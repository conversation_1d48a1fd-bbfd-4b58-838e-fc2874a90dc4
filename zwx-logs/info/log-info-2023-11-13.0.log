2023-11-13 08:27:01 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19852 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 08:27:01 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 08:27:14 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$9b7c22f9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 08:27:14 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 08:27:14 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 08:27:14 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 08:27:14 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 08:27:14 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 08:27:14 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 12837 ms
2023-11-13 08:27:15 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 08:27:15 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 08:27:21 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 08:27:21 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 08:27:21 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 08:27:21 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 21.314 seconds (JVM running for 24.29)
2023-11-13 08:27:38 [Thread-42] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 08:30:45 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 8960 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 08:30:45 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 08:30:53 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f6b40720] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 08:30:53 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 08:30:53 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 08:30:53 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 08:30:53 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 08:30:53 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 08:30:53 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7709 ms
2023-11-13 08:30:54 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 08:30:54 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 08:30:59 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 08:30:59 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 08:30:59 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 08:30:59 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.981 seconds (JVM running for 15.611)
2023-11-13 08:42:44 [Thread-30] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 08:42:57 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 3100 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 08:42:57 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 08:43:03 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$cfef4a27] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 08:43:03 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 08:43:03 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 08:43:04 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 08:43:04 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 08:43:04 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 08:43:04 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6165 ms
2023-11-13 08:43:04 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 08:43:04 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 08:43:09 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 08:43:09 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 08:43:09 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 08:43:09 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 11.944 seconds (JVM running for 13.231)
2023-11-13 08:53:33 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 08:53:40 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19888 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 08:53:40 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 08:53:46 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$335438dd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 08:53:47 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 08:53:47 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 08:53:47 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 08:53:47 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 08:53:47 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 08:53:47 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6316 ms
2023-11-13 08:53:47 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 08:53:47 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 08:53:52 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 08:53:52 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 08:53:52 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 08:53:52 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.178 seconds (JVM running for 13.244)
2023-11-13 09:02:27 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 09:02:34 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 9744 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 09:02:34 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 09:02:40 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$5bf1d357] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 09:02:41 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 09:02:41 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 09:02:41 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 09:02:41 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 09:02:41 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 09:02:41 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6312 ms
2023-11-13 09:02:41 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 09:02:41 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 09:02:46 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 09:02:46 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 09:02:46 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 09:02:46 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.338 seconds (JVM running for 13.625)
2023-11-13 09:03:30 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 09:03:35 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19040 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 09:03:35 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 09:03:41 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$e50d552c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 09:03:41 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 09:03:41 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 09:03:42 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 09:03:42 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 09:03:42 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 09:03:42 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6137 ms
2023-11-13 09:03:42 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 09:03:42 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 09:03:47 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 09:03:47 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 09:03:47 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 09:03:47 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 11.869 seconds (JVM running for 12.94)
2023-11-13 09:05:08 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 09:05:13 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 1244 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 09:05:13 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 09:05:19 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$7559fa7a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 09:05:19 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 09:05:19 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 09:05:19 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 09:05:19 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 09:05:20 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 09:05:20 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6348 ms
2023-11-13 09:05:20 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 09:05:20 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 09:05:24 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 09:05:24 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 09:05:24 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 09:05:24 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.169 seconds (JVM running for 13.333)
2023-11-13 09:09:08 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 09:09:13 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20396 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 09:09:13 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 09:09:19 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$b41c2c55] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 09:09:19 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 09:09:19 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 09:09:19 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 09:09:19 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 09:09:19 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 09:09:19 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6358 ms
2023-11-13 09:09:20 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 09:09:20 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 09:09:25 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 09:09:25 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 09:09:25 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 09:09:25 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.616 seconds (JVM running for 13.744)
2023-11-13 09:11:50 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 09:11:54 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 17952 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 09:11:54 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 09:12:00 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$63e4a5dc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 09:12:01 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 09:12:01 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 09:12:01 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 09:12:01 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 09:12:01 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 09:12:01 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6446 ms
2023-11-13 09:12:01 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 09:12:01 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 09:12:06 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 09:12:06 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 09:12:06 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 09:12:06 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.362 seconds (JVM running for 13.496)
2023-11-13 09:20:03 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 09:20:10 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19724 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 09:20:10 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 09:20:16 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$2fdb97ce] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 09:20:16 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 09:20:16 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 09:20:16 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 09:20:16 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 09:20:16 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 09:20:16 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6130 ms
2023-11-13 09:20:16 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 09:20:17 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 09:20:21 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 09:20:21 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 09:20:21 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 09:20:21 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 11.797 seconds (JVM running for 12.856)
2023-11-13 09:21:37 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 09:21:44 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 17392 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 09:21:44 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 09:21:51 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$a431e17c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 09:21:51 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 09:21:51 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 09:21:51 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 09:21:51 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 09:21:52 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 09:21:52 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6786 ms
2023-11-13 09:21:52 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 09:21:52 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 09:21:57 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 09:21:57 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 09:21:57 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 09:21:57 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.863 seconds (JVM running for 13.866)
2023-11-13 09:25:02 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 09:44:46 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 14448 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 09:44:46 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 09:44:54 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f4cafac1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 09:44:54 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 09:44:54 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 09:44:54 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 09:44:54 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 09:44:54 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 09:44:54 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7774 ms
2023-11-13 09:44:54 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 09:44:55 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 09:44:59 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 09:44:59 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 09:44:59 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 09:44:59 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.856 seconds (JVM running for 14.897)
2023-11-13 09:45:21 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 09:57:34 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 8912 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 09:57:34 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 09:57:40 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$249210ad] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 09:57:40 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 09:57:40 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 09:57:40 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 09:57:40 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 09:57:41 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 09:57:41 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6545 ms
2023-11-13 09:57:41 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 09:57:41 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 09:57:46 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 09:57:46 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 09:57:46 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 09:57:46 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.406 seconds (JVM running for 13.282)
2023-11-13 10:04:43 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 10:06:17 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19384 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 10:06:17 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 10:06:23 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$390fa490] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 10:06:23 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 10:06:23 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 10:06:23 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 10:06:23 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 10:06:24 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 10:06:24 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6621 ms
2023-11-13 10:06:24 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 10:06:24 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 10:06:29 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 10:06:29 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 10:06:29 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 10:06:29 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.082 seconds (JVM running for 14.098)
2023-11-13 10:09:54 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 10:15:02 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20044 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 10:15:02 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 10:15:08 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$9c7e5b35] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 10:15:08 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 10:15:08 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 10:15:08 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 10:15:08 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 10:15:08 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 10:15:08 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6104 ms
2023-11-13 10:15:08 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 10:15:09 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 10:15:13 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 10:15:13 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 10:15:13 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 10:15:13 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 11.885 seconds (JVM running for 12.892)
2023-11-13 10:17:25 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 10:18:00 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 13240 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 10:18:00 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 10:18:49 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 6952 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 10:18:49 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 10:18:55 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$90a29de0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 10:18:55 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 10:18:55 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 10:18:55 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 10:18:55 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 10:18:55 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 10:18:55 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6102 ms
2023-11-13 10:18:55 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 10:18:56 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 10:19:00 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 10:19:00 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 10:19:00 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 10:19:00 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 11.81 seconds (JVM running for 12.699)
2023-11-13 10:33:05 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 10:33:18 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 17716 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 10:33:18 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 10:33:25 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$899ded1a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 10:33:25 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 10:33:25 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 10:33:25 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 10:33:25 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 10:33:25 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 10:33:25 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6742 ms
2023-11-13 10:33:25 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 10:33:26 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 10:33:30 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 10:33:30 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 10:33:30 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 10:33:30 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.812 seconds (JVM running for 14.795)
2023-11-13 10:38:08 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 10:38:15 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 18592 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 10:38:15 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 10:38:27 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 18668 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 10:38:27 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 10:38:33 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$5dfaf08b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 10:38:34 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 10:38:34 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 10:38:34 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 10:38:34 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 10:38:34 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 10:38:34 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6475 ms
2023-11-13 10:38:34 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 10:38:34 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 10:38:39 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 10:38:39 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 10:38:39 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 10:38:39 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.356 seconds (JVM running for 13.596)
2023-11-13 10:44:47 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 10:45:10 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20412 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 10:45:10 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 10:45:16 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$16d933b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 10:45:16 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 10:45:16 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 10:45:16 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 10:45:16 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 10:45:17 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 10:45:17 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6636 ms
2023-11-13 10:45:17 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 10:45:17 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 10:45:22 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 10:45:22 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 10:45:22 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 10:45:22 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.783 seconds (JVM running for 13.984)
2023-11-13 10:48:13 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 10:48:25 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20884 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 10:48:25 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 10:48:31 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$e075a5e4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 10:48:31 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 10:48:31 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 10:48:31 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 10:48:31 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 10:48:31 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 10:48:31 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6217 ms
2023-11-13 10:48:32 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 10:48:32 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 10:48:36 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 10:48:36 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 10:48:36 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 10:48:36 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.093 seconds (JVM running for 13.013)
2023-11-13 10:57:52 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 10:58:10 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 12104 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 10:58:10 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 10:58:16 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$39d6fc1c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 10:58:16 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 10:58:16 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 10:58:16 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 10:58:16 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 10:58:17 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 10:58:17 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6674 ms
2023-11-13 10:58:17 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 10:58:17 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 10:58:22 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 10:58:22 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 10:58:22 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 10:58:22 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.733 seconds (JVM running for 13.689)
2023-11-13 11:00:18 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 11:00:42 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 11648 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 11:00:42 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 11:00:48 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$2f778fc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 11:00:49 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 11:00:49 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 11:00:49 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 11:00:49 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 11:00:49 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 11:00:49 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6769 ms
2023-11-13 11:00:49 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 11:00:49 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 11:00:55 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 11:00:55 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 11:00:55 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 11:00:55 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.821 seconds (JVM running for 15.5)
2023-11-13 11:06:57 [Thread-30] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 11:07:07 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 10868 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 11:07:07 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 11:07:14 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$5a9d7b1b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 11:07:14 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 11:07:14 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 11:07:14 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 11:07:14 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 11:07:14 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 11:07:14 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6633 ms
2023-11-13 11:07:14 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 11:07:15 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 11:07:19 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 11:07:19 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 11:07:19 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 11:07:19 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.815 seconds (JVM running for 14.148)
2023-11-13 11:19:10 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 8268 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 11:19:10 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 11:19:16 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$51ee4565] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 11:19:17 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 11:19:17 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 11:19:17 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 11:19:17 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 11:19:17 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 11:19:17 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6206 ms
2023-11-13 11:19:17 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 11:19:17 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 11:19:22 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 11:19:22 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 11:19:22 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 11:19:22 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.093 seconds (JVM running for 13.166)
2023-11-13 11:24:27 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 11:24:35 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19964 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 11:24:35 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 11:24:41 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$7fc70a70] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 11:24:41 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 11:24:41 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 11:24:41 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 11:24:41 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 11:24:42 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 11:24:42 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6631 ms
2023-11-13 11:24:42 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 11:24:42 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 11:24:47 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 11:24:47 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 11:24:47 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 11:24:47 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.124 seconds (JVM running for 14.502)
2023-11-13 11:36:42 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 11:36:52 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20716 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 11:36:52 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 11:36:58 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$38223a25] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 11:36:58 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 11:36:58 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 11:36:58 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 11:36:58 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 11:36:59 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 11:36:59 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6377 ms
2023-11-13 11:36:59 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 11:36:59 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 11:37:04 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 11:37:04 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 11:37:04 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 11:37:04 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.21 seconds (JVM running for 13.337)
2023-11-13 11:37:42 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 11:37:47 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 11444 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 11:37:47 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 11:37:54 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$cec674dd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 11:37:54 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 11:37:54 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 11:37:54 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 11:37:54 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 11:37:54 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 11:37:54 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6726 ms
2023-11-13 11:37:55 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 11:37:55 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 11:38:00 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 11:38:00 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 11:38:00 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 11:38:00 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.812 seconds (JVM running for 13.91)
2023-11-13 11:39:24 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 11:40:21 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 11028 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 11:40:21 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 11:40:27 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$93689355] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 11:40:28 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 11:40:28 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 11:40:28 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 11:40:28 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 11:40:28 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 11:40:28 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6644 ms
2023-11-13 11:40:28 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 11:40:29 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 11:40:33 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 11:40:33 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 11:40:33 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 11:40:33 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.641 seconds (JVM running for 13.969)
2023-11-13 11:55:48 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 11:55:54 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 4668 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 11:55:54 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 11:56:00 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$e71ac027] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 11:56:01 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 11:56:01 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 11:56:01 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 11:56:01 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 11:56:01 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 11:56:01 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6505 ms
2023-11-13 11:56:01 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 11:56:01 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 11:56:06 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 11:56:06 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 11:56:06 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 11:56:06 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.542 seconds (JVM running for 13.557)
2023-11-13 11:59:21 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 13:33:17 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 21168 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 13:33:17 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 13:33:23 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$1379300b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 13:33:23 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 13:33:23 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 13:33:23 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 13:33:23 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 13:33:23 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 13:33:23 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6206 ms
2023-11-13 13:33:23 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 13:33:24 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 13:33:28 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 13:33:28 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 13:33:28 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 13:33:28 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.089 seconds (JVM running for 13.136)
2023-11-13 13:33:41 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 13:33:56 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 4904 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 13:33:56 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 13:34:01 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$acc342ce] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 13:34:02 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 13:34:02 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 13:34:02 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 13:34:02 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 13:34:02 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 13:34:02 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6192 ms
2023-11-13 13:34:02 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 13:34:02 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 13:34:07 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 13:34:07 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 13:34:07 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 13:34:07 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 11.933 seconds (JVM running for 13.004)
2023-11-13 13:36:40 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 14:11:33 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 6368 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 14:11:33 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 14:11:40 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$6078cc1a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 14:11:40 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 14:11:40 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 14:11:40 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 14:11:40 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 14:11:40 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 14:11:40 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6602 ms
2023-11-13 14:11:40 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 14:11:41 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 14:11:41 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2023-11-13 14:11:41 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2023-11-13 14:12:01 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 14404 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 14:12:01 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 14:12:11 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$b2563b23] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 14:12:11 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 14:12:11 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 14:12:11 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 14:12:11 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 14:12:11 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 14:12:11 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 9877 ms
2023-11-13 14:12:12 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 14:12:12 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 14:12:16 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 14:12:16 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 14:12:16 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 14:12:16 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 15.859 seconds (JVM running for 16.782)
2023-11-13 14:13:08 [Thread-30] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 14:14:17 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 3988 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 14:14:17 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 14:14:23 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$cc4b8a71] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 14:14:24 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 14:14:24 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 14:14:24 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 14:14:24 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 14:14:24 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 14:14:24 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6384 ms
2023-11-13 14:14:24 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 14:14:24 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 14:14:29 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 14:14:29 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 14:14:29 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 14:14:29 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.42 seconds (JVM running for 13.801)
2023-11-13 14:16:44 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 14:16:52 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19816 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 14:16:52 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 14:16:58 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$174a5cac] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 14:16:58 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 14:16:58 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 14:16:58 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 14:16:58 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 14:16:58 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 14:16:58 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6366 ms
2023-11-13 14:16:58 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 14:16:59 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 14:17:03 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 14:17:03 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 14:17:03 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 14:17:03 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.231 seconds (JVM running for 13.33)
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196402140834==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510232197310100917==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510232197107305212==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500382198601088534==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51023219770603571X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510232198204155011==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197011167517==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 50022319840129061X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510212196904302815==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510215197204125014==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513523196910050018==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301196610179514==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330329196411294211==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330329197208124211==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330329197507094219==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 352224196807203310==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 352224197704155410==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 433027196808184819==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196609093730==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197002024119==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197610114222==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197303064130==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196805255717==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512224196712076013==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197011060258==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512324196811211773==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510202195901231815==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510229197009031598==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510229197212191597==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500229198410094635==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196910249013==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510281197912217212==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510525196702061898==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216197208282056==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216197206211633==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197208223033==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197308012639==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197301234212==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 422802197010055303==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512923197607244018==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225196803098730==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196503014538==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 422823196903013036==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225196612078630==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 420625196902115639==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196710044319==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225197506068636==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512221196807159234==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216197309171232==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196909171293==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510229196301298180==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500381198501263511==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197202190414==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197212230416==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512530196501225799==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512530196808045776==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512930196512035271==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510227197812207043==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511224197503033918==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512229196609114079==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510212196806276473==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510212196903096415==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196903153419==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512221196905293998==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510231197010123790==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022219650123681X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225196504258730==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510625196902145979==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510215197311065723==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500222198509277453==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301196304096139==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221198010223427==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323198103204233==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197107190817==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197706250218==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500233198603302378==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51021219660614541X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223196901028276==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510522197608100032==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196411205913==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196411205913==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197103053916==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512226197601130758==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197305277436==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196411185215==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196510254810==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196711193217==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196811017851==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51232319700331561X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51232319691010423X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196909125017==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196710077812==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196901041812==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197606061313==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197105244613==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197105244613==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225197505136254==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221196308103816==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022519641014883X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197103030210==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510224196401067572==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510227195706033933==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022819750112786X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 33032719670918467X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330327197112194634==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330327197609204631==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510321196412128330==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511028196405164818==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511226198308084557==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511227197402123577==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511227197708111018==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512228197201163510==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512228197201163510==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197404086258==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197105240131==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197304102911==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197304102911==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197711012112==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196306124037==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509244119==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196601114114==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196910294139==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197102184155==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197303078693==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197311154112==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510232197109087116==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510228197302030010==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513030196611076417==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522131196901190018==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500106198710226417==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500382198311144120==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500382198311144120==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512927195910252017==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196112195053==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196501021655==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510213198001147514==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510282198306138013==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524196702148674==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197212011578==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330327197506214490==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522198110064174==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197209098153==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524198206221296==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196807135815==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197204087015==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510230197002153870==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510230197508048090==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513030197304296115==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225197103096464==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500241199110026314==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196709056331==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197306024911==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197306024911==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197306215718==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197506274915==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512324197010296214==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500237198606157933==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197001190311==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51303019700910693X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196706244011==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500241198503143912==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196108243916==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196505275719==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196506285716==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196804274919==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219690622631X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197010113915==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219730928441X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197603134316==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196706084417==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197406294417==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197211250178==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197507096632==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197209245281==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197301075828==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500241199104290338==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196606165711==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196801085717==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197001285715==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197403185311==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197409245792==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522198002104917==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524196804011951==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 332623195205310714==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196302154319==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197105024915==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197012264012==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196811285712==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522195701305714==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197202281522==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513521196501021811==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352119730803181X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352119730803181X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223197305047117==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513521196401041911==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197310060735==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51302119731012185X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196708264171==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196803064119==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196306183010==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196410036918==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196806090312==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022119660918391X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022119660918391X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196904054331==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197502174116==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500241198801034116==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197807184139==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197807184139==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196810294115==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196901234118==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197007154150==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197201104114==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197205154119==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219720813413X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197301044112==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197301264318==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197405134315==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197203121391==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197404081293==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197512031293==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524198103041292==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509244311==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196907104111==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522229196803294618==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522229197009194614==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196605274115==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196605274115==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219661207391X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196701043915==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196809124354==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196905054114==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197205031293==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197205031293==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352419740814129X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002253926==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510217197311201118==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196712189510==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197308277871==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301196901083993==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510902197511090911==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 532722198212041348==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51302919830105235X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197102171756==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197203131753==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197306271732==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197201063021==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512324197409126073==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512322197504270839==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216198210063623==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500110199109210835==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223195610250892==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510224197108168934==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196501130118==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197301170171==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196303070317==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022319731016501X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 420222196610053754==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511112196904100530==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522321198910204052==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197004065213==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513031197605070212==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225196906192551==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197202086997==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196610233015==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196305233714==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301196308232855==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196708035413==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510227197104102910==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513029197604294714==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197003045111==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197003045111==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 532122197112081425==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510228196509114831==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510921198807201936==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510522198610031106==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216196709073215==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197007097842==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510213197112134017==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510213197303094070==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510231195101260919==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223197410033438==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225197403093652==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022119770511701X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022919750604279X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196309045916==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196607225958==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197310076319==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522198012124110==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219630412261X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509063318==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197210083319==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522195706082935==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196502033319==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509063318==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509063318==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196909052917==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219720408573X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197201044019==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197012124116==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196310064110==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196712034117==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196809083919==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197202044117==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196712034117==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197202044117==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002104111==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197011252710==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522123196606205037==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512225196609040078==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197104234972==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 430725197701021013==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197106010355==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197212150212==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509090244==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 433130196310014453==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196908035699==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 433130196801094419==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522195904190216==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196208290218==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196405030212==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197003070232==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197104240050==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 433022197007073943==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196402120212==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196409210210==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509090244==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219651024022X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197212150212==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197212150212==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 50038119831230541X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197307315838==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196509171535==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510224195806066231==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510224196806216636==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223197108139098==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51052219680827805X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196805115216==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197310052700==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197005023014==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197412104751==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197303117033==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510212197102034518==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197103271513==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512221196608174095==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512222197412220230==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512222196907104499==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510213196806250229==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511028197210094025==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51062219691201691X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196903122213==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197109222211==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510215196302183037==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196812121335==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197207181318==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512221197002194731==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197008124034==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197112249419==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197202150370==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197511200140==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216197408140810==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512224197711183399==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512225196310140154==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500108198309052310==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197109237612==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197009056017==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512322197502076231==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51122419811013101X==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500223198509136657==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513023196907278073==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196302220093==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500222198705215436==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196306111236==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196306111236==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196404150511==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002254013==========================================
2023-11-13 14:17:08 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002254013==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196402140834==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510232197310100917==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510232197107305212==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500382198601088534==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51023219770603571X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510232198204155011==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197011167517==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 50022319840129061X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510212196904302815==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510215197204125014==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513523196910050018==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301196610179514==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330329196411294211==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330329197208124211==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330329197507094219==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 352224196807203310==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 352224197704155410==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 433027196808184819==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196609093730==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197002024119==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197610114222==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197303064130==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196805255717==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512224196712076013==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197011060258==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512324196811211773==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510202195901231815==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510229197009031598==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510229197212191597==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500229198410094635==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196910249013==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510281197912217212==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510525196702061898==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216197208282056==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216197206211633==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197208223033==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197308012639==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197301234212==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 422802197010055303==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512923197607244018==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225196803098730==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196503014538==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 422823196903013036==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225196612078630==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 420625196902115639==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196710044319==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225197506068636==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512221196807159234==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216197309171232==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196909171293==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510229196301298180==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500381198501263511==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197202190414==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197212230416==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512530196501225799==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512530196808045776==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512930196512035271==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510227197812207043==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511224197503033918==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512229196609114079==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510212196806276473==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510212196903096415==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196903153419==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512221196905293998==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510231197010123790==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022219650123681X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225196504258730==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510625196902145979==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510215197311065723==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500222198509277453==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301196304096139==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221198010223427==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323198103204233==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197107190817==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197706250218==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500233198603302378==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51021219660614541X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223196901028276==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510522197608100032==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196411205913==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196411205913==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197103053916==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512226197601130758==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197305277436==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196411185215==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196510254810==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196711193217==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196811017851==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51232319700331561X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51232319691010423X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196909125017==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196710077812==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196901041812==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197606061313==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197105244613==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197105244613==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225197505136254==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221196308103816==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022519641014883X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197103030210==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510224196401067572==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510227195706033933==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022819750112786X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 33032719670918467X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330327197112194634==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330327197609204631==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510321196412128330==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511028196405164818==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511226198308084557==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511227197402123577==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511227197708111018==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512228197201163510==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512228197201163510==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197404086258==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197105240131==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197304102911==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197304102911==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197711012112==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196306124037==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509244119==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196601114114==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196910294139==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197102184155==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197303078693==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197311154112==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510232197109087116==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510228197302030010==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513030196611076417==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522131196901190018==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500106198710226417==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500382198311144120==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500382198311144120==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512927195910252017==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196112195053==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196501021655==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510213198001147514==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510282198306138013==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524196702148674==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197212011578==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330327197506214490==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522198110064174==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197209098153==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524198206221296==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196807135815==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197204087015==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510230197002153870==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510230197508048090==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513030197304296115==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225197103096464==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500241199110026314==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196709056331==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197306024911==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197306024911==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197306215718==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197506274915==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512324197010296214==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500237198606157933==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197001190311==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51303019700910693X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196706244011==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500241198503143912==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196108243916==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196505275719==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196506285716==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196804274919==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219690622631X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197010113915==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219730928441X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197603134316==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196706084417==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197406294417==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197211250178==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197507096632==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197209245281==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197301075828==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500241199104290338==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196606165711==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196801085717==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197001285715==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197403185311==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197409245792==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522198002104917==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524196804011951==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 332623195205310714==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196302154319==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197105024915==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197012264012==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196811285712==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522195701305714==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197202281522==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513521196501021811==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352119730803181X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352119730803181X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223197305047117==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513521196401041911==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197310060735==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51302119731012185X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196708264171==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196803064119==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196306183010==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196410036918==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196806090312==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022119660918391X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022119660918391X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196904054331==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197502174116==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500241198801034116==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197807184139==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197807184139==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196810294115==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196901234118==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197007154150==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197201104114==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197205154119==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219720813413X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197301044112==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197301264318==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197405134315==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197203121391==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197404081293==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197512031293==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524198103041292==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509244311==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196907104111==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522229196803294618==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522229197009194614==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196605274115==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196605274115==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219661207391X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196701043915==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196809124354==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196905054114==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197205031293==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197205031293==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352419740814129X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002253926==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510217197311201118==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196712189510==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197308277871==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301196901083993==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510902197511090911==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 532722198212041348==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51302919830105235X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197102171756==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197203131753==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197306271732==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197201063021==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512324197409126073==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512322197504270839==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216198210063623==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500110199109210835==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223195610250892==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510224197108168934==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196501130118==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197301170171==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196303070317==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022319731016501X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 420222196610053754==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511112196904100530==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522321198910204052==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197004065213==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513031197605070212==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225196906192551==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197202086997==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196610233015==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196305233714==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301196308232855==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196708035413==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510227197104102910==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513029197604294714==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197003045111==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197003045111==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 532122197112081425==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510228196509114831==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510921198807201936==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510522198610031106==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216196709073215==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197007097842==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510213197112134017==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510213197303094070==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510231195101260919==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223197410033438==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225197403093652==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022119770511701X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022919750604279X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196309045916==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196607225958==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197310076319==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522198012124110==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219630412261X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509063318==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197210083319==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522195706082935==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196502033319==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509063318==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509063318==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196909052917==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219720408573X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197201044019==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197012124116==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196310064110==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196712034117==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196809083919==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197202044117==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196712034117==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197202044117==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002104111==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197011252710==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522123196606205037==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512225196609040078==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197104234972==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 430725197701021013==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197106010355==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197212150212==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509090244==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 433130196310014453==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196908035699==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 433130196801094419==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522195904190216==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196208290218==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196405030212==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197003070232==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197104240050==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 433022197007073943==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196402120212==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196409210210==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509090244==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219651024022X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197212150212==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197212150212==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 50038119831230541X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197307315838==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196509171535==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510224195806066231==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510224196806216636==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223197108139098==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51052219680827805X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196805115216==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197310052700==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197005023014==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197412104751==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197303117033==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510212197102034518==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197103271513==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512221196608174095==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512222197412220230==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512222196907104499==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510213196806250229==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511028197210094025==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51062219691201691X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196903122213==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197109222211==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510215196302183037==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196812121335==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197207181318==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512221197002194731==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197008124034==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197112249419==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197202150370==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197511200140==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216197408140810==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512224197711183399==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512225196310140154==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500108198309052310==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197109237612==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197009056017==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512322197502076231==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51122419811013101X==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500223198509136657==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513023196907278073==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196302220093==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500222198705215436==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196306111236==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196306111236==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196404150511==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002254013==========================================
2023-11-13 14:17:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002254013==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196402140834==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510232197310100917==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510232197107305212==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500382198601088534==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51023219770603571X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510232198204155011==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197011167517==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 50022319840129061X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510212196904302815==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510215197204125014==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513523196910050018==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301196610179514==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330329196411294211==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330329197208124211==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330329197507094219==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 352224196807203310==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 352224197704155410==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 433027196808184819==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196609093730==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197002024119==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197610114222==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197303064130==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196805255717==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512224196712076013==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197011060258==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512324196811211773==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510202195901231815==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510229197009031598==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510229197212191597==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500229198410094635==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196910249013==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510281197912217212==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510525196702061898==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216197208282056==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216197206211633==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197208223033==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197308012639==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197301234212==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 422802197010055303==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512923197607244018==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225196803098730==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196503014538==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 422823196903013036==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225196612078630==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 420625196902115639==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196710044319==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225197506068636==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512221196807159234==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216197309171232==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196909171293==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510229196301298180==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500381198501263511==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197202190414==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197212230416==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512530196501225799==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512530196808045776==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512930196512035271==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510227197812207043==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511224197503033918==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512229196609114079==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510212196806276473==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510212196903096415==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196903153419==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512221196905293998==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510231197010123790==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022219650123681X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225196504258730==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510625196902145979==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510215197311065723==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500222198509277453==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301196304096139==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221198010223427==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323198103204233==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197107190817==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197706250218==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500233198603302378==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51021219660614541X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223196901028276==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510522197608100032==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196411205913==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196411205913==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197103053916==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512226197601130758==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197305277436==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196411185215==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196510254810==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196711193217==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196811017851==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51232319700331561X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51232319691010423X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196909125017==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196710077812==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196901041812==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197606061313==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197105244613==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197105244613==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225197505136254==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221196308103816==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022519641014883X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197103030210==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510224196401067572==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510227195706033933==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022819750112786X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 33032719670918467X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330327197112194634==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330327197609204631==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510321196412128330==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511028196405164818==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511226198308084557==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511227197402123577==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511227197708111018==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512228197201163510==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512228197201163510==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197404086258==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197105240131==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197304102911==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197304102911==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513525197711012112==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196306124037==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509244119==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196601114114==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196910294139==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197102184155==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197303078693==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197311154112==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510232197109087116==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510228197302030010==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513030196611076417==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522131196901190018==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500106198710226417==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500382198311144120==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500382198311144120==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512927195910252017==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196112195053==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196501021655==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510213198001147514==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510282198306138013==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524196702148674==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197212011578==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 330327197506214490==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522198110064174==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197209098153==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524198206221296==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196807135815==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197204087015==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510230197002153870==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510230197508048090==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513030197304296115==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225197103096464==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500241199110026314==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196709056331==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197306024911==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197306024911==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197306215718==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197506274915==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512324197010296214==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500237198606157933==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197001190311==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51303019700910693X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196706244011==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500241198503143912==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196108243916==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196505275719==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196506285716==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196804274919==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219690622631X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197010113915==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219730928441X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197603134316==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196706084417==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197406294417==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197211250178==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197507096632==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197209245281==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197301075828==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500241199104290338==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196606165711==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196801085717==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197001285715==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197403185311==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197409245792==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522198002104917==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524196804011951==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 332623195205310714==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196302154319==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197105024915==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197012264012==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196811285712==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522195701305714==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197202281522==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513521196501021811==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352119730803181X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352119730803181X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223197305047117==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513521196401041911==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197310060735==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51302119731012185X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196708264171==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196803064119==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196306183010==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196410036918==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196806090312==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022119660918391X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022119660918391X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196904054331==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197502174116==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500241198801034116==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197807184139==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197807184139==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196810294115==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196901234118==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197007154150==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197201104114==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197205154119==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219720813413X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197301044112==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197301264318==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197405134315==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197203121391==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197404081293==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197512031293==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524198103041292==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509244311==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196907104111==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522229196803294618==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522229197009194614==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196605274115==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196605274115==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219661207391X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196701043915==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196809124354==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196905054114==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197205031293==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197205031293==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352419740814129X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002253926==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510217197311201118==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196712189510==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197308277871==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301196901083993==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510902197511090911==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 532722198212041348==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51302919830105235X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197102171756==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197203131753==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197306271732==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197201063021==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512324197409126073==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512322197504270839==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216198210063623==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500110199109210835==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223195610250892==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510224197108168934==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196501130118==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197301170171==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196303070317==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022319731016501X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 420222196610053754==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511112196904100530==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522321198910204052==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197004065213==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513031197605070212==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225196906192551==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197202086997==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226196610233015==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196305233714==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301196308232855==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196708035413==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510227197104102910==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513029197604294714==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197003045111==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197003045111==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 532122197112081425==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510228196509114831==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510921198807201936==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510522198610031106==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216196709073215==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197007097842==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510213197112134017==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510213197303094070==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510231195101260919==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223197410033438==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510225197403093652==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022119770511701X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51022919750604279X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196309045916==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196607225958==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197310076319==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522198012124110==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219630412261X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509063318==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197210083319==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522195706082935==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196502033319==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509063318==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509063318==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196909052917==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219720408573X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197201044019==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197012124116==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196310064110==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196712034117==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196809083919==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197202044117==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196712034117==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197202044117==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002104111==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197011252710==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 522123196606205037==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512225196609040078==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513524197104234972==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 430725197701021013==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197106010355==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197212150212==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509090244==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 433130196310014453==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196908035699==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 433130196801094419==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522195904190216==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196208290218==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196405030212==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197003070232==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197104240050==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 433022197007073943==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196402120212==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196409210210==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196509090244==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51352219651024022X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197212150212==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197212150212==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 50038119831230541X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197307315838==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196509171535==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510224195806066231==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510224196806216636==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512223197108139098==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51052219680827805X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222196805115216==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197310052700==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197005023014==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197412104751==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510221197303117033==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510212197102034518==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512301197103271513==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512221196608174095==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512222197412220230==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512222196907104499==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510213196806250229==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 511028197210094025==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51062219691201691X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223196903122213==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197109222211==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510215196302183037==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196812121335==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323197207181318==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512221197002194731==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197008124034==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510222197112249419==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197202150370==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510226197511200140==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510216197408140810==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512224197711183399==========================================
2023-11-13 14:19:09 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512225196310140154==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500108198309052310==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512326197109237612==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 510223197009056017==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512322197502076231==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 51122419811013101X==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500223198509136657==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513023196907278073==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 512323196302220093==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 500222198705215436==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196306111236==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196306111236==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522196404150511==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002254013==========================================
2023-11-13 14:19:09 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002254013==========================================
2023-11-13 14:19:14 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 16120 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 14:19:14 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 14:19:20 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$e585f492] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 14:19:21 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 14:19:21 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 14:19:21 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 14:19:21 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 14:19:21 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 14:19:21 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6213 ms
2023-11-13 14:19:21 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 14:19:21 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 14:19:26 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 14:19:26 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 14:19:26 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 14:19:26 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.028 seconds (JVM running for 13.049)
2023-11-13 14:31:37 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002254013==========================================
2023-11-13 14:31:37 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.OccDisCaseLateTimeJob - 513522197002254013==========================================
2023-11-13 14:31:37 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 15:15:11 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19452 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 15:15:11 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 15:15:18 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ae2fd1f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 15:15:18 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 15:15:18 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 15:15:18 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 15:15:18 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 15:15:18 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 15:15:18 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6465 ms
2023-11-13 15:15:18 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 15:15:19 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 15:15:23 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 15:15:23 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 15:15:23 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 15:15:23 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.469 seconds (JVM running for 13.411)
2023-11-13 15:15:31 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 15:28:38 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 5380 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 15:28:38 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 15:28:45 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$343ce6ea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 15:28:45 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 15:28:45 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 15:28:45 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 15:28:45 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 15:28:46 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 15:28:46 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6940 ms
2023-11-13 15:28:46 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 15:28:46 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 15:28:52 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 15:28:52 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 15:28:52 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 15:28:52 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 15.092 seconds (JVM running for 16.125)
2023-11-13 15:32:08 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 15:33:18 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 5564 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 15:33:18 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 15:33:24 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$a644224b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 15:33:24 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 15:33:24 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 15:33:24 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 15:33:24 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 15:33:24 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 15:33:24 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6213 ms
2023-11-13 15:33:24 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 15:33:25 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 15:33:29 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 15:33:29 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 15:33:29 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 15:33:29 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.169 seconds (JVM running for 13.256)
2023-11-13 15:34:42 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 15:34:47 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 18812 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 15:34:47 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 15:34:54 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ece37587] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 15:34:54 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 15:34:54 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 15:34:54 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 15:34:54 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 15:34:54 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 15:34:54 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7077 ms
2023-11-13 15:34:54 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 15:34:55 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 15:35:00 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 15:35:00 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 15:35:00 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 15:35:00 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.611 seconds (JVM running for 14.851)
2023-11-13 15:35:40 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 15:35:48 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 16760 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 15:35:48 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 15:35:54 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$7dfd937e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 15:35:54 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 15:35:54 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 15:35:54 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 15:35:54 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 15:35:54 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 15:35:54 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6207 ms
2023-11-13 15:35:54 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 15:35:55 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 15:35:59 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 15:35:59 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 15:36:00 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 15:36:00 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.575 seconds (JVM running for 13.7)
2023-11-13 15:47:51 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 15:53:28 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 10040 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 15:53:28 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 15:53:34 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f2aa752b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 15:53:34 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 15:53:34 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 15:53:34 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 15:53:34 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 15:53:34 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 15:53:34 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6476 ms
2023-11-13 15:53:35 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 15:53:35 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 15:53:40 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 15:53:40 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 15:53:40 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 15:53:40 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.318 seconds (JVM running for 14.259)
2023-11-13 16:01:13 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 16:01:20 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 7976 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 16:01:20 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 16:01:26 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$73df8db2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 16:01:26 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 16:01:26 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 16:01:26 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 16:01:26 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 16:01:27 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 16:01:27 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6433 ms
2023-11-13 16:01:27 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 16:01:27 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 16:01:32 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 16:01:32 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 16:01:32 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 16:01:32 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.741 seconds (JVM running for 13.801)
2023-11-13 16:03:35 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 16:03:43 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 17664 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 16:03:43 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 16:03:50 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3747959e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 16:03:50 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 16:03:50 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 16:03:50 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 16:03:50 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 16:03:51 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 16:03:51 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6939 ms
2023-11-13 16:03:51 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 16:03:51 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 16:03:56 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 16:03:56 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 16:03:56 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 16:03:56 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.33 seconds (JVM running for 15.127)
2023-11-13 16:12:33 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 16:12:56 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 17784 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 16:12:56 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 16:13:02 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$174a5cac] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 16:13:02 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 16:13:02 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 16:13:02 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 16:13:02 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 16:13:02 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 16:13:02 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6423 ms
2023-11-13 16:13:03 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 16:13:03 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 16:13:07 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 16:13:07 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 16:13:07 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 16:13:07 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.421 seconds (JVM running for 13.596)
2023-11-13 16:36:42 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 16:36:50 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 8460 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 16:36:50 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 16:36:57 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$bfdf6f76] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 16:36:57 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 16:36:57 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 16:36:57 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 16:36:57 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 16:36:57 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 16:36:57 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6988 ms
2023-11-13 16:36:58 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 16:36:58 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 16:37:03 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 16:37:03 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 16:37:03 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 16:37:03 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 14.215 seconds (JVM running for 15.511)
2023-11-13 16:40:16 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 16:40:57 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 6272 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 16:40:57 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 16:41:03 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$56e78922] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 16:41:03 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 16:41:03 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 16:41:03 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 16:41:03 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 16:41:03 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 16:41:03 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6325 ms
2023-11-13 16:41:03 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 16:41:04 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 16:41:08 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 16:41:08 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 16:41:08 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 16:41:08 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.17 seconds (JVM running for 13.126)
2023-11-13 16:47:24 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 16:47:42 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 2132 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 16:47:42 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 16:47:48 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$55655669] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 16:47:48 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 16:47:48 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 16:47:48 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 16:47:48 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 16:47:49 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 16:47:49 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6458 ms
2023-11-13 16:47:49 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 16:47:49 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 16:47:54 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 16:47:54 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 16:47:54 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 16:47:54 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.449 seconds (JVM running for 13.76)
2023-11-13 16:54:09 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 16:54:51 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19380 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 16:54:51 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 16:54:57 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$d4484e2d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 16:54:57 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 16:54:57 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 16:54:57 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 16:54:57 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 16:54:57 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 16:54:57 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6227 ms
2023-11-13 16:54:58 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 16:54:58 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 16:55:03 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 16:55:03 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 16:55:03 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 16:55:03 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.318 seconds (JVM running for 13.308)
2023-11-13 16:56:01 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 16:56:46 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 1812 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 16:56:46 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 16:56:52 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$7371ebc2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 16:56:52 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 16:56:52 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 16:56:52 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 16:56:52 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 16:56:52 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 16:56:52 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6308 ms
2023-11-13 16:56:52 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 16:56:53 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 16:56:57 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 16:56:57 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 16:56:57 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 16:56:57 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.095 seconds (JVM running for 13.101)
2023-11-13 17:28:31 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 17:28:48 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 4304 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 17:28:48 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 17:28:55 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$756babb0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 17:28:56 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 17:28:56 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 17:28:56 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 17:28:56 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 17:28:56 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 17:28:56 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7137 ms
2023-11-13 17:28:56 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 17:28:56 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 17:29:02 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 17:29:02 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 17:29:02 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 17:29:02 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.991 seconds (JVM running for 15.032)
2023-11-13 17:33:59 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 17:35:39 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20552 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 17:35:39 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 17:35:45 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$d23b5613] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 17:35:46 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 17:35:46 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 17:35:46 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 17:35:46 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 17:35:46 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 17:35:46 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6209 ms
2023-11-13 17:35:46 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 17:35:46 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 17:35:52 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 17:35:52 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 17:35:52 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 17:35:52 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.848 seconds (JVM running for 13.994)
2023-11-13 17:36:10 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 18:09:53 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 18224 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 18:09:53 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 18:10:00 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$199e2143] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 18:10:00 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 18:10:00 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 18:10:00 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 18:10:00 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 18:10:01 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 18:10:01 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7602 ms
2023-11-13 18:10:01 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 18:10:01 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 18:10:06 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 18:10:06 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 18:10:06 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 18:10:06 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.696 seconds (JVM running for 14.629)
2023-11-13 18:10:07 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 18:10:23 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 15780 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 18:10:23 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 18:10:29 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$79f58f78] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 18:10:29 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 18:10:29 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 18:10:29 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 18:10:29 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 18:10:30 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 18:10:30 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6700 ms
2023-11-13 18:10:30 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 18:10:30 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 18:10:35 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 18:10:35 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 18:10:35 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 18:10:35 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.653 seconds (JVM running for 14.75)
2023-11-13 18:11:09 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 18:11:21 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 13924 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 18:11:21 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 18:11:27 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$431062d5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 18:11:27 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 18:11:27 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 18:11:27 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 18:11:27 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 18:11:27 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 18:11:27 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6283 ms
2023-11-13 18:11:28 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 18:11:28 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 18:11:33 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 18:11:33 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 18:11:33 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 18:11:33 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.997 seconds (JVM running for 14.19)
2023-11-13 22:15:44 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 22:16:02 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 14208 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 22:16:02 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 22:16:08 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$d06e7232] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 22:16:09 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 22:16:09 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 22:16:09 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 22:16:09 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 22:16:09 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 22:16:09 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6712 ms
2023-11-13 22:16:09 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 22:16:10 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 22:16:15 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 22:16:15 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 22:16:15 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 22:16:15 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.387 seconds (JVM running for 14.595)
2023-11-13 22:21:13 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 22:24:11 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 18896 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 22:24:11 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 22:24:18 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ce69a8b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 22:24:19 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 22:24:19 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 22:24:19 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 22:24:19 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 22:24:19 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 22:24:19 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7370 ms
2023-11-13 22:24:19 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 22:24:19 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 22:24:25 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 22:24:25 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 22:24:25 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 22:24:25 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 14.418 seconds (JVM running for 15.598)
2023-11-13 22:28:51 [Thread-31] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-13 22:29:11 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 21136 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-13 22:29:11 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-13 22:29:18 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$77b645fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-13 22:29:18 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-13 22:29:18 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-13 22:29:18 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-13 22:29:18 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-13 22:29:19 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-13 22:29:19 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6999 ms
2023-11-13 22:29:19 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-13 22:29:19 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-13 22:29:26 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-13 22:29:26 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-13 22:29:26 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-13 22:29:26 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 15.682 seconds (JVM running for 16.954)
2023-11-13 22:38:43 [Thread-34] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed

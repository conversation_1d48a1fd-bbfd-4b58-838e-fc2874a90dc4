2023-11-11 15:18:04 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 1932 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 15:18:04 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 15:18:14 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f770742c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 15:18:15 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 15:18:15 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 15:18:15 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 15:18:15 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 15:18:15 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 15:18:15 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 10773 ms
2023-11-11 15:18:15 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 15:18:16 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 15:18:16 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 15:18:16 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2023-11-11 15:18:16 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2023-11-11 15:19:55 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 3448 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 15:19:55 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 15:20:01 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$e168ea67] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 15:20:01 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 15:20:01 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 15:20:01 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 15:20:01 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 15:20:01 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 15:20:01 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6178 ms
2023-11-11 15:20:02 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 15:20:02 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 15:20:02 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 15:20:02 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2023-11-11 15:20:02 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2023-11-11 15:21:02 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 8116 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 15:21:02 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 15:21:08 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f5298212] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 15:21:08 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 15:21:08 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 15:21:08 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 15:21:08 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 15:21:08 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 15:21:08 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6197 ms
2023-11-11 15:21:09 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 15:21:09 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 15:21:09 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 15:21:09 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2023-11-11 15:21:09 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2023-11-11 15:22:43 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 1600 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 15:22:43 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 15:22:50 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$40e83c74] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 15:22:50 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 15:22:51 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 15:22:51 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 15:22:51 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 15:22:51 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 15:22:51 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7360 ms
2023-11-11 15:22:51 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 15:22:51 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 15:22:55 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 15:22:55 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2023-11-11 15:22:55 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2023-11-11 15:25:54 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20352 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 15:25:54 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 15:26:16 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 2380 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 15:26:16 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 15:26:23 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$c8956ce1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 15:26:23 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 15:26:23 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 15:26:23 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 15:26:23 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 15:26:24 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 15:26:24 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7315 ms
2023-11-11 15:26:24 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 15:26:25 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 15:26:31 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 15:26:31 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2023-11-11 15:26:31 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2023-11-11 15:27:09 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 8488 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 15:27:09 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 15:27:15 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3ee9f7bb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 15:27:15 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 15:27:15 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 15:27:15 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 15:27:15 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 15:27:15 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 15:27:15 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6282 ms
2023-11-11 15:27:16 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 15:27:16 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 15:27:19 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 15:27:19 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2023-11-11 15:27:19 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2023-11-11 15:35:00 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20796 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 15:35:00 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 15:35:08 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$eb53892c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 15:35:08 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 15:35:08 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 15:35:08 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 15:35:08 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 15:35:08 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 15:35:08 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7836 ms
2023-11-11 15:35:09 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 15:35:09 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 15:35:12 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 15:35:12 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2023-11-11 15:35:12 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2023-11-11 15:36:52 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 14568 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 15:36:52 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 15:36:58 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3677fa6f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 15:36:58 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 15:36:58 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 15:36:58 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 15:36:58 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 15:36:58 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 15:36:58 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6228 ms
2023-11-11 15:36:58 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 15:36:59 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 15:37:05 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 15:37:05 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 15:37:05 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 15:37:05 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.837 seconds (JVM running for 14.963)
2023-11-11 15:37:59 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 15:38:04 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19344 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 15:38:04 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 15:38:10 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$26de117e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 15:38:10 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 15:38:10 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 15:38:10 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 15:38:10 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 15:38:10 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 15:38:10 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6112 ms
2023-11-11 15:38:11 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 15:38:11 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 15:38:15 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 15:38:15 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 15:38:15 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 15:38:15 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 11.865 seconds (JVM running for 12.813)
2023-11-11 16:26:25 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 18:06:35 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 17464 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 18:06:35 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 18:06:42 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3bc5f974] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 18:06:42 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 18:06:42 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 18:06:42 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 18:06:42 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 18:06:43 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 18:06:43 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7382 ms
2023-11-11 18:06:43 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 18:06:43 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 18:06:48 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 18:06:48 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 18:06:48 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 18:06:48 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.903 seconds (JVM running for 15.029)
2023-11-11 18:17:04 [Thread-31] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 18:17:12 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 16308 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 18:17:12 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 18:17:18 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$272d3b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 18:17:18 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 18:17:18 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 18:17:18 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 18:17:18 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 18:17:18 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 18:17:18 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6371 ms
2023-11-11 18:17:19 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 18:17:19 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 18:17:24 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 18:17:24 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 18:17:24 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 18:17:24 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.541 seconds (JVM running for 13.647)
2023-11-11 18:18:07 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 18:18:14 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 9540 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 18:18:14 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 18:18:20 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f85f4815] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 18:18:21 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 18:18:21 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 18:18:21 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 18:18:21 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 18:18:21 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 18:18:21 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6293 ms
2023-11-11 18:18:21 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 18:18:22 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 18:18:26 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 18:18:26 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 18:18:26 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 18:18:26 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.325 seconds (JVM running for 13.509)
2023-11-11 18:21:30 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 18:21:35 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 9184 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 18:21:35 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 18:21:40 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$fa17810c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 18:21:41 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 18:21:41 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 18:21:41 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 18:21:41 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 18:21:41 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 18:21:41 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6282 ms
2023-11-11 18:21:41 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 18:21:42 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 18:21:50 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 18:21:50 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 18:21:50 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 18:21:50 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 15.961 seconds (JVM running for 17.094)
2023-11-11 18:23:14 [Thread-32] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 18:23:19 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 13188 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 18:23:19 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 18:23:25 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$6a4e839] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 18:23:25 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 18:23:25 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 18:23:25 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 18:23:25 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 18:23:25 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 18:23:25 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6396 ms
2023-11-11 18:23:26 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 18:23:26 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 18:23:30 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 18:23:30 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 18:23:30 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 18:23:30 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.303 seconds (JVM running for 13.324)
2023-11-11 18:38:15 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 18:38:23 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 4908 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 18:38:23 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 18:38:29 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f4157344] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 18:38:29 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 18:38:29 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 18:38:29 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 18:38:29 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 18:38:29 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 18:38:29 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6212 ms
2023-11-11 18:38:30 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 18:38:30 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 18:38:34 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 18:38:34 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 18:38:34 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 18:38:34 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.087 seconds (JVM running for 13.089)
2023-11-11 18:42:25 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 18:42:38 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 14252 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 18:42:38 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 18:42:44 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3f814a47] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 18:42:44 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 18:42:44 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 18:42:44 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 18:42:44 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 18:42:44 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 18:42:44 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6275 ms
2023-11-11 18:42:45 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 18:42:45 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 18:42:49 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 18:42:49 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 18:42:49 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 18:42:49 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.139 seconds (JVM running for 13.174)
2023-11-11 18:49:08 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 18:49:19 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 5856 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 18:49:19 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 18:49:25 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$fae41554] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 18:49:26 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 18:49:26 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 18:49:26 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 18:49:26 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 18:49:26 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 18:49:26 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6266 ms
2023-11-11 18:49:26 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 18:49:27 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 18:49:31 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 18:49:31 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 18:49:31 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 18:49:31 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.149 seconds (JVM running for 13.208)
2023-11-11 18:51:25 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 18:51:32 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 17996 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 18:51:32 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 18:51:38 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$2c6b027e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 18:51:38 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 18:51:38 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 18:51:38 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 18:51:38 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 18:51:39 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 18:51:39 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6242 ms
2023-11-11 18:51:39 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 18:51:39 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 18:51:44 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 18:51:44 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 18:51:44 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 18:51:44 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.105 seconds (JVM running for 13.151)
2023-11-11 18:53:58 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 1484 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 18:53:58 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 18:54:03 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$c4d13fb1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 18:54:04 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 18:54:04 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 18:54:04 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 18:54:04 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 18:54:04 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 18:54:04 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6222 ms
2023-11-11 18:54:04 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 18:54:05 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 18:54:09 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 18:54:09 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 18:54:09 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 18:54:09 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.183 seconds (JVM running for 13.223)
2023-11-11 18:56:06 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 18:56:12 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 18696 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 18:56:12 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 18:56:18 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$2240114b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 18:56:19 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 18:56:19 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 18:56:19 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 18:56:19 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 18:56:19 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 18:56:19 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6474 ms
2023-11-11 18:56:19 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 18:56:19 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 18:56:24 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 18:56:24 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 18:56:24 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 18:56:24 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.521 seconds (JVM running for 13.797)
2023-11-11 18:57:37 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 18:57:45 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 7596 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 18:57:45 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 18:57:51 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$7042016a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 18:57:51 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 18:57:51 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 18:57:51 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 18:57:51 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 18:57:51 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 18:57:51 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6390 ms
2023-11-11 18:57:51 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 18:57:52 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 18:57:56 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 18:57:56 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 18:57:56 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 18:57:56 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.512 seconds (JVM running for 13.567)
2023-11-11 18:58:20 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 18:58:28 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19744 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 18:58:28 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 18:58:35 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f6b40720] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 18:58:35 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 18:58:35 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 18:58:35 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 18:58:35 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 18:58:35 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 18:58:35 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6321 ms
2023-11-11 18:58:35 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 18:58:36 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 18:58:40 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 18:58:40 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 18:58:40 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 18:58:40 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.221 seconds (JVM running for 13.24)
2023-11-11 19:00:16 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 19:00:21 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 9976 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 19:00:21 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 19:00:27 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$51ee4565] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 19:00:27 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 19:00:27 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 19:00:27 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 19:00:27 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 19:00:27 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 19:00:27 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6420 ms
2023-11-11 19:00:28 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 19:00:28 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 19:00:32 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 19:00:32 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 19:00:32 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 19:00:32 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.393 seconds (JVM running for 13.492)
2023-11-11 19:03:36 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 19:03:43 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 16688 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 19:03:43 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 19:03:49 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$4ee9c742] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 19:03:50 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 19:03:50 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 19:03:50 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 19:03:50 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 19:03:50 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 19:03:50 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6258 ms
2023-11-11 19:03:50 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 19:03:50 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 19:03:55 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 19:03:55 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 19:03:55 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 19:03:55 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.227 seconds (JVM running for 13.29)
2023-11-11 19:04:04 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 19:04:42 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20036 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 19:04:42 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 19:04:48 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$74d62e2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 19:04:48 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 19:04:48 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 19:04:48 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 19:04:48 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 19:04:48 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 19:04:48 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6272 ms
2023-11-11 19:04:48 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 19:04:49 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 19:04:53 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 19:04:53 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 19:04:53 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 19:04:53 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.096 seconds (JVM running for 13.104)
2023-11-11 19:04:58 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 19:08:38 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20388 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 19:08:38 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 19:08:45 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$272d3b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 19:08:45 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 19:08:45 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 19:08:45 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 19:08:45 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 19:08:45 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 19:08:45 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6630 ms
2023-11-11 19:08:45 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 19:08:46 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 19:08:50 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 19:08:50 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 19:08:50 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 19:08:50 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.721 seconds (JVM running for 13.802)
2023-11-11 19:17:42 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 19:17:50 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 3544 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 19:17:50 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 19:17:56 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3ad7aeb2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 19:17:57 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 19:17:57 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 19:17:57 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 19:17:57 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 19:17:57 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 19:17:57 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6473 ms
2023-11-11 19:17:57 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 19:17:57 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 19:18:02 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 19:18:02 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 19:18:02 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 19:18:02 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.369 seconds (JVM running for 13.561)
2023-11-11 19:25:19 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 19:25:29 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19640 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 19:25:29 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 19:25:36 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$4bc89f86] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 19:25:36 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 19:25:36 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 19:25:36 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 19:25:36 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 19:25:36 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 19:25:36 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6629 ms
2023-11-11 19:25:36 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 19:25:37 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 19:25:42 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 19:25:42 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 19:25:42 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 19:25:42 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.513 seconds (JVM running for 14.508)
2023-11-11 19:37:18 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 19:37:40 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 7052 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 19:37:40 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 19:37:46 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$c54750e6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 19:37:46 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 19:37:46 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 19:37:46 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 19:37:46 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 19:37:47 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 19:37:47 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6377 ms
2023-11-11 19:37:47 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 19:37:47 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 19:37:52 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 19:37:52 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 19:37:52 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 19:37:52 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.278 seconds (JVM running for 13.262)
2023-11-11 19:44:19 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 19:46:24 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 22272 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 19:46:24 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 19:46:31 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$2856aad7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 19:46:31 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 19:46:31 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 19:46:31 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 19:46:31 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 19:46:31 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 19:46:31 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7303 ms
2023-11-11 19:46:31 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 19:46:32 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 19:46:37 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 19:46:37 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 19:46:37 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 19:46:37 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 14.193 seconds (JVM running for 15.371)
2023-11-11 19:50:43 [Thread-30] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 19:50:49 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 15520 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 19:50:49 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 19:50:55 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$c60b7e22] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 19:50:56 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 19:50:56 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 19:50:56 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 19:50:56 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 19:50:56 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 19:50:56 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6752 ms
2023-11-11 19:50:56 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 19:50:56 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 19:51:01 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 19:51:01 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 19:51:01 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 19:51:01 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.466 seconds (JVM running for 13.601)
2023-11-11 19:52:09 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 19:53:37 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19872 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 19:53:37 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 19:53:44 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$332e1bcf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 19:53:44 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 19:53:44 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 19:53:44 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 19:53:44 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 19:53:44 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 19:53:44 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7035 ms
2023-11-11 19:53:44 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 19:53:45 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 19:53:49 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 19:53:49 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 19:53:49 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 19:53:49 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.318 seconds (JVM running for 14.771)
2023-11-11 19:57:24 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 19:57:34 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19088 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 19:57:34 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 19:57:40 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$cdb542c2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 19:57:40 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 19:57:40 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 19:57:40 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 19:57:40 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 19:57:40 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 19:57:40 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6220 ms
2023-11-11 19:57:40 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 19:57:41 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 19:57:45 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 19:57:45 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 19:57:45 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 19:57:45 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 11.97 seconds (JVM running for 12.971)
2023-11-11 20:11:45 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 20:11:49 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19964 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 20:11:49 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 20:11:55 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$665f8010] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 20:11:55 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 20:11:55 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 20:11:55 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 20:11:55 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 20:11:56 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 20:11:56 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6146 ms
2023-11-11 20:11:56 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 20:11:56 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 20:12:00 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 20:12:00 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 20:12:00 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 20:12:00 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 11.922 seconds (JVM running for 12.897)
2023-11-11 20:12:57 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 20:21:55 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 17948 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 20:21:55 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 20:22:01 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$25d4dd3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 20:22:01 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 20:22:01 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 20:22:01 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 20:22:01 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 20:22:02 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 20:22:02 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6553 ms
2023-11-11 20:22:02 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 20:22:02 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 20:22:06 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 20:22:06 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 20:22:06 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 20:22:06 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.329 seconds (JVM running for 13.358)
2023-11-11 20:25:37 [Thread-26] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-11 20:25:52 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20180 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 20:25:52 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 20:25:59 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$1ddb740e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 20:25:59 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 20:25:59 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 20:25:59 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 20:25:59 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 20:26:00 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 20:26:00 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7289 ms
2023-11-11 20:26:00 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 20:26:21 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 20:26:21 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2023-11-11 20:26:21 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2023-11-11 20:26:52 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 22108 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-11 20:26:52 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-11 20:26:59 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$16d933b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-11 20:26:59 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-11 20:26:59 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-11 20:26:59 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-11 20:26:59 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-11 20:27:00 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-11 20:27:00 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7385 ms
2023-11-11 20:27:00 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-11 20:27:00 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-11 20:27:05 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-11 20:27:05 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-11 20:27:05 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-11 20:27:05 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.601 seconds (JVM running for 14.568)
2023-11-11 20:34:41 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed

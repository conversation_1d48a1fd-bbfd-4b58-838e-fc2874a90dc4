2024-01-30 09:48:10 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 18248 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2024-01-30 09:48:10 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2024-01-30 09:48:18 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ee98018c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-01-30 09:48:18 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2024-01-30 09:48:18 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2024-01-30 09:48:18 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-01-30 09:48:18 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2024-01-30 09:48:18 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-01-30 09:48:18 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7331 ms
2024-01-30 09:48:19 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2024-01-30 09:48:40 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2024-01-30 09:48:40 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2024-01-30 09:48:40 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-01-30 09:49:04 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19288 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2024-01-30 09:49:04 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2024-01-30 09:49:12 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$9e2d41b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-01-30 09:49:12 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2024-01-30 09:49:13 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2024-01-30 09:49:13 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-01-30 09:49:13 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2024-01-30 09:49:13 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-01-30 09:49:13 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7625 ms
2024-01-30 09:49:13 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2024-01-30 09:49:13 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2024-01-30 09:49:18 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-01-30 09:49:18 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2024-01-30 09:49:22 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2024-01-30 09:49:22 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 18.407 seconds (JVM running for 19.41)
2024-01-30 09:49:47 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2024-01-30 09:51:57 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 17908 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2024-01-30 09:51:57 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2024-01-30 09:52:05 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$9bc0dff0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-01-30 09:52:05 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2024-01-30 09:52:05 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2024-01-30 09:52:05 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-01-30 09:52:05 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2024-01-30 09:52:06 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-01-30 09:52:06 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 8240 ms
2024-01-30 09:52:06 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2024-01-30 09:52:06 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2024-01-30 09:52:11 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-01-30 09:52:11 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2024-01-30 09:52:11 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2024-01-30 09:52:11 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 14.108 seconds (JVM running for 16.105)
2024-01-30 09:56:10 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2024-01-30 15:00:13 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 18928 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2024-01-30 15:00:13 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2024-01-30 15:00:19 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$b91b38a4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-01-30 15:00:19 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2024-01-30 15:00:19 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2024-01-30 15:00:19 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-01-30 15:00:19 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2024-01-30 15:00:19 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-01-30 15:00:19 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6301 ms
2024-01-30 15:00:20 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2024-01-30 15:00:20 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2024-01-30 15:00:24 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-01-30 15:00:24 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2024-01-30 15:00:24 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2024-01-30 15:00:24 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.25 seconds (JVM running for 13.519)
2024-01-30 15:04:14 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2024-01-30 15:21:16 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20916 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2024-01-30 15:21:16 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2024-01-30 15:21:24 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$850d2bca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-01-30 15:21:24 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2024-01-30 15:21:24 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2024-01-30 15:21:24 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-01-30 15:21:24 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2024-01-30 15:21:25 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-01-30 15:21:25 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7711 ms
2024-01-30 15:21:25 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2024-01-30 15:21:25 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2024-01-30 15:21:30 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-01-30 15:21:30 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2024-01-30 15:21:30 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2024-01-30 15:21:30 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 14.639 seconds (JVM running for 19.088)
2024-01-30 15:23:03 [Thread-30] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2024-01-30 16:42:10 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20556 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2024-01-30 16:42:10 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2024-01-30 16:42:21 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$77f8f2a7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-01-30 16:42:22 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2024-01-30 16:42:22 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2024-01-30 16:42:22 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-01-30 16:42:22 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2024-01-30 16:42:22 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-01-30 16:42:22 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 11335 ms
2024-01-30 16:42:22 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2024-01-30 16:42:23 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2024-01-30 16:42:27 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-01-30 16:42:27 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2024-01-30 16:42:27 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2024-01-30 16:42:27 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 17.461 seconds (JVM running for 19.062)
2024-01-30 16:55:49 [Schedule-Task-1] INFO  com.chis.modules.timer.heth.job.DangerValToolJob - 危急值工具开始时间：2024-01-30 16:42:28 结束时间：2024-01-30 16:55:49 耗时: 801574 毫秒
2024-01-30 16:56:59 [Schedule-Task-1] INFO  com.chis.modules.timer.heth.job.DangerValToolJob - 危急值工具开始时间：2024-01-30 16:55:50 结束时间：2024-01-30 16:56:59 耗时: 69044 毫秒
2024-01-30 16:56:59 [Thread-32] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2024-01-30 16:57:07 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 21512 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2024-01-30 16:57:07 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2024-01-30 16:57:14 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f770270] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-01-30 16:57:14 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2024-01-30 16:57:14 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2024-01-30 16:57:14 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-01-30 16:57:14 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2024-01-30 16:57:15 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-01-30 16:57:15 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7353 ms
2024-01-30 16:57:15 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2024-01-30 16:57:15 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2024-01-30 16:57:21 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2024-01-30 16:57:21 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2024-01-30 16:57:21 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2024-01-30 16:57:21 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 14.616 seconds (JVM running for 15.607)

2023-08-23 09:41:58 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 18700 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-08-23 09:41:58 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-08-23 09:42:09 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3b1d80db] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-08-23 09:42:10 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-08-23 09:42:10 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-08-23 09:42:10 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-08-23 09:42:10 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-08-23 09:42:10 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-08-23 09:42:10 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 11561 ms
2023-08-23 09:42:10 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-08-23 09:42:10 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-08-23 09:42:15 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-08-23 09:42:15 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-08-23 09:42:15 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-08-23 09:42:15 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 17.685 seconds (JVM running for 18.84)
2023-08-23 09:46:04 [Thread-37] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-08-23 09:46:31 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19672 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-08-23 09:46:31 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-08-23 09:46:38 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$edf2a6fd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-08-23 09:46:39 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-08-23 09:46:39 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-08-23 09:46:39 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-08-23 09:46:39 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-08-23 09:46:39 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-08-23 09:46:39 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7394 ms
2023-08-23 09:46:39 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-08-23 09:46:39 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-08-23 09:46:43 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-08-23 09:46:43 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-08-23 09:46:43 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-08-23 09:46:43 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.345 seconds (JVM running for 13.205)
2023-08-23 09:47:36 [Thread-27] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed

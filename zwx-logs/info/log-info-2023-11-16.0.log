2023-11-16 08:30:47 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 2188 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-16 08:30:47 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-16 08:31:00 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$1266ce1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-16 08:31:00 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-16 08:31:00 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-16 08:31:00 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-16 08:31:00 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-16 08:31:00 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-16 08:31:00 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 12387 ms
2023-11-16 08:31:01 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-16 08:31:01 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-16 08:31:07 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-16 08:31:07 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-16 08:31:07 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-16 08:31:07 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 20.306 seconds (JVM running for 22.425)
2023-11-16 08:33:44 [Thread-43] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-16 08:34:17 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20388 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-16 08:34:17 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-16 08:34:23 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$8de7ab2c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-16 08:34:23 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-16 08:34:23 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-16 08:34:24 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-16 08:34:24 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-16 08:34:24 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-16 08:34:24 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6655 ms
2023-11-16 08:34:24 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-16 08:34:24 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-16 08:34:29 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-16 08:34:29 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-16 08:34:29 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-16 08:34:29 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.875 seconds (JVM running for 14.273)
2023-11-16 08:35:43 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-16 08:36:54 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 12724 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-16 08:36:54 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-16 08:37:00 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$17c24c3e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-16 08:37:01 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-16 08:37:01 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-16 08:37:01 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-16 08:37:01 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-16 08:37:01 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-16 08:37:01 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6699 ms
2023-11-16 08:37:01 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-16 08:37:01 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-16 08:37:06 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-16 08:37:06 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-16 08:37:06 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-16 08:37:06 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.026 seconds (JVM running for 15.461)
2023-11-16 08:42:11 [Thread-30] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-16 08:42:20 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 11288 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-16 08:42:20 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-16 08:42:26 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$b118cabd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-16 08:42:26 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-16 08:42:26 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-16 08:42:26 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-16 08:42:26 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-16 08:42:26 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-16 08:42:26 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6606 ms
2023-11-16 08:42:27 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-16 08:42:27 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-16 08:42:32 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-16 08:42:32 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-16 08:42:32 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-16 08:42:32 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.751 seconds (JVM running for 13.956)
2023-11-16 08:50:12 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-16 09:00:32 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 9872 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-16 09:00:32 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-16 09:00:39 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$aafeb3c5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-16 09:00:39 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-16 09:00:39 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-16 09:00:39 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-16 09:00:39 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-16 09:00:39 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-16 09:00:39 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7240 ms
2023-11-16 09:00:40 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-16 09:00:40 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-16 09:00:44 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-16 09:00:44 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2023-11-16 09:00:44 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2023-11-16 09:01:39 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20104 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-16 09:01:39 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-16 09:01:45 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$e71ac027] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-16 09:01:45 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-16 09:01:46 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-16 09:01:46 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-16 09:01:46 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-16 09:01:46 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-16 09:01:46 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6656 ms
2023-11-16 09:01:46 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-16 09:01:46 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-16 09:01:51 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-16 09:01:51 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-16 09:01:51 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-16 09:01:51 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.782 seconds (JVM running for 14.02)
2023-11-16 09:03:45 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed

2023-11-07 23:21:08 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 23668 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-07 23:21:08 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-07 23:21:17 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$5c8d99cc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-07 23:21:17 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-07 23:21:17 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-07 23:21:17 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-07 23:21:17 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-07 23:21:17 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-07 23:21:17 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 8646 ms
2023-11-07 23:21:17 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2023-11-07 23:21:17 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2023-11-07 23:22:32 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20168 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-07 23:22:32 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-07 23:22:40 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3654e5f2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-07 23:22:40 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-07 23:22:40 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-07 23:22:40 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-07 23:22:40 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-07 23:22:41 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-07 23:22:41 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 8454 ms
2023-11-07 23:22:41 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-07 23:22:41 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Loaded jar:file:/F:/repository/com/atomikos/transactions/4.0.6/transactions-4.0.6.jar!/transactions-defaults.properties
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_max_wait_time_on_shutdown = 9223372036854775807
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.allow_subtransactions = true
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.recovery_delay = 10000
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.automatic_resource_registration = true
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_max_retries = 5
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.client_demarcation = false
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.threaded_2pc = false
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.serial_jta_transactions = true
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_dir = F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\transaction-logs
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.rmi_export_class = none
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_actives = 50
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.checkpoint_interval = 500
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.enable_logging = true
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_name = tmlog
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_timeout = 300000
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.trust_client_tm = false
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.factory.initial = com.sun.jndi.rmi.registry.RegistryContextFactory
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.tm_unique_name = ***********.tm
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.forget_orphaned_log_entries_delay = 86400000
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_retry_interval = 10000
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.provider.url = rmi://localhost:1099
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.force_shutdown_on_vm_exit = false
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_jta_timeout = 10000
2023-11-07 23:22:46 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Using default (local) logging and recovery...
2023-11-07 23:22:47 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-07 23:22:47 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-07 23:22:47 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-07 23:22:47 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 15.96 seconds (JVM running for 17.215)
2023-11-07 23:22:48 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:23:16 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:23:44 [Thread-31] INFO  com.atomikos.icatch.imp.TransactionServiceImp - Transaction Service: Entering shutdown (false, 9223372036854775807)...
2023-11-07 23:23:44 [Thread-31] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-07 23:23:50 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 16880 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-07 23:23:50 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-07 23:23:56 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f7e43518] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-07 23:23:57 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-07 23:23:57 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-07 23:23:57 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-07 23:23:57 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-07 23:23:57 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-07 23:23:57 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6885 ms
2023-11-07 23:23:57 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-07 23:23:58 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Loaded jar:file:/F:/repository/com/atomikos/transactions/4.0.6/transactions-4.0.6.jar!/transactions-defaults.properties
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_max_wait_time_on_shutdown = 9223372036854775807
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.allow_subtransactions = true
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.recovery_delay = 10000
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.automatic_resource_registration = true
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_max_retries = 5
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.client_demarcation = false
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.threaded_2pc = false
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.serial_jta_transactions = true
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_dir = F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\transaction-logs
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.rmi_export_class = none
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_actives = 50
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.checkpoint_interval = 500
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.enable_logging = true
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_name = tmlog
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_timeout = 300000
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.trust_client_tm = false
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.factory.initial = com.sun.jndi.rmi.registry.RegistryContextFactory
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.tm_unique_name = ***********.tm
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.forget_orphaned_log_entries_delay = 86400000
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_retry_interval = 10000
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.provider.url = rmi://localhost:1099
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.force_shutdown_on_vm_exit = false
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_jta_timeout = 10000
2023-11-07 23:24:02 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Using default (local) logging and recovery...
2023-11-07 23:24:03 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-07 23:24:03 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-07 23:24:03 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-07 23:24:03 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.695 seconds (JVM running for 14.767)
2023-11-07 23:24:04 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:24:39 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:24:50 [Thread-30] INFO  com.atomikos.icatch.imp.TransactionServiceImp - Transaction Service: Entering shutdown (false, 9223372036854775807)...
2023-11-07 23:24:50 [Thread-30] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-07 23:24:56 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20880 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-07 23:24:56 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-07 23:25:03 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$44c74ad] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-07 23:25:04 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-07 23:25:04 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-07 23:25:04 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-07 23:25:04 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-07 23:25:04 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-07 23:25:04 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7829 ms
2023-11-07 23:25:04 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-07 23:25:04 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Loaded jar:file:/F:/repository/com/atomikos/transactions/4.0.6/transactions-4.0.6.jar!/transactions-defaults.properties
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_max_wait_time_on_shutdown = 9223372036854775807
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.allow_subtransactions = true
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.recovery_delay = 10000
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.automatic_resource_registration = true
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_max_retries = 5
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.client_demarcation = false
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.threaded_2pc = false
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.serial_jta_transactions = true
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_dir = F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\transaction-logs
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.rmi_export_class = none
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_actives = 50
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.checkpoint_interval = 500
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.enable_logging = true
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_name = tmlog
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_timeout = 300000
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.trust_client_tm = false
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.factory.initial = com.sun.jndi.rmi.registry.RegistryContextFactory
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.tm_unique_name = ***********.tm
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.forget_orphaned_log_entries_delay = 86400000
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_retry_interval = 10000
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.provider.url = rmi://localhost:1099
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.force_shutdown_on_vm_exit = false
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_jta_timeout = 10000
2023-11-07 23:25:09 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Using default (local) logging and recovery...
2023-11-07 23:25:09 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-07 23:25:09 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-07 23:25:09 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-07 23:25:09 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 14.59 seconds (JVM running for 15.841)
2023-11-07 23:25:10 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:25:14 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:25:16 [Schedule-Task-2] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:26:55 [Thread-31] INFO  com.atomikos.icatch.imp.TransactionServiceImp - Transaction Service: Entering shutdown (false, 9223372036854775807)...
2023-11-07 23:26:55 [Thread-31] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-07 23:27:02 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 15108 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-07 23:27:02 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-07 23:27:09 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$1646a9dd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-07 23:27:09 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-07 23:27:09 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-07 23:27:09 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-07 23:27:09 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-07 23:27:09 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-07 23:27:09 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6740 ms
2023-11-07 23:27:10 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-07 23:27:10 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Loaded jar:file:/F:/repository/com/atomikos/transactions/4.0.6/transactions-4.0.6.jar!/transactions-defaults.properties
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_max_wait_time_on_shutdown = 9223372036854775807
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.allow_subtransactions = true
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.recovery_delay = 10000
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.automatic_resource_registration = true
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_max_retries = 5
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.client_demarcation = false
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.threaded_2pc = false
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.serial_jta_transactions = true
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_dir = F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\transaction-logs
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.rmi_export_class = none
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_actives = 50
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.checkpoint_interval = 500
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.enable_logging = true
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_name = tmlog
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_timeout = 300000
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.trust_client_tm = false
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.factory.initial = com.sun.jndi.rmi.registry.RegistryContextFactory
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.tm_unique_name = ***********.tm
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.forget_orphaned_log_entries_delay = 86400000
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_retry_interval = 10000
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.provider.url = rmi://localhost:1099
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.force_shutdown_on_vm_exit = false
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_jta_timeout = 10000
2023-11-07 23:27:15 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Using default (local) logging and recovery...
2023-11-07 23:27:15 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-07 23:27:15 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-07 23:27:15 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-07 23:27:15 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.371 seconds (JVM running for 14.436)
2023-11-07 23:27:16 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:27:37 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 调用死因接口，URL:https://public.cqcdc.org/api/oauth/token 参数:{password=rqk6$gOX0lRb, grant_type=password, scope=all, username=2023004} 
2023-11-07 23:27:57 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:44:12 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 调用死因接口，URL:https://public.cqcdc.org/api/oauth/token 参数:{password=rqk6$gOX0lRb, grant_type=password, scope=all, username=2023004} 
2023-11-07 23:44:12 [Thread-29] INFO  com.atomikos.icatch.imp.TransactionServiceImp - Transaction Service: Entering shutdown (false, 9223372036854775807)...
2023-11-07 23:44:12 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-07 23:44:21 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 21124 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-07 23:44:21 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-07 23:44:28 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$93d05c90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-07 23:44:29 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-07 23:44:29 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-07 23:44:29 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-07 23:44:29 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-07 23:44:29 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-07 23:44:29 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7415 ms
2023-11-07 23:44:29 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-07 23:44:30 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Loaded jar:file:/F:/repository/com/atomikos/transactions/4.0.6/transactions-4.0.6.jar!/transactions-defaults.properties
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_max_wait_time_on_shutdown = 9223372036854775807
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.allow_subtransactions = true
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.recovery_delay = 10000
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.automatic_resource_registration = true
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_max_retries = 5
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.client_demarcation = false
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.threaded_2pc = false
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.serial_jta_transactions = true
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_dir = F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\transaction-logs
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.rmi_export_class = none
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_actives = 50
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.checkpoint_interval = 500
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.enable_logging = true
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_name = tmlog
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_timeout = 300000
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.trust_client_tm = false
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.factory.initial = com.sun.jndi.rmi.registry.RegistryContextFactory
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.tm_unique_name = ***********.tm
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.forget_orphaned_log_entries_delay = 86400000
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_retry_interval = 10000
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.provider.url = rmi://localhost:1099
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.force_shutdown_on_vm_exit = false
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_jta_timeout = 10000
2023-11-07 23:44:35 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Using default (local) logging and recovery...
2023-11-07 23:44:35 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-07 23:44:35 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-07 23:44:35 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-07 23:44:35 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 14.631 seconds (JVM running for 15.842)
2023-11-07 23:44:36 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:45:25 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 调用死因接口，URL:https://public.cqcdc.org/api/oauth/token 参数:{password=rqk6$gOX0lRb, grant_type=password, scope=all, username=2023004} 
2023-11-07 23:47:05 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 调用死因接口返回：<html>
<head><title>301 Moved Permanently</title></head>
<body>
<center><h1>301 Moved Permanently</h1></center>
<hr><center>nginx</center>
</body>
</html>

2023-11-07 23:47:05 [Thread-31] INFO  com.atomikos.icatch.imp.TransactionServiceImp - Transaction Service: Entering shutdown (false, 9223372036854775807)...
2023-11-07 23:47:06 [Thread-31] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-07 23:47:13 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 21972 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-07 23:47:13 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-07 23:47:20 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$6c94a565] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-07 23:47:20 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-07 23:47:20 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-07 23:47:20 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-07 23:47:20 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-07 23:47:20 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-07 23:47:20 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6655 ms
2023-11-07 23:47:20 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-07 23:47:21 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Loaded jar:file:/F:/repository/com/atomikos/transactions/4.0.6/transactions-4.0.6.jar!/transactions-defaults.properties
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_max_wait_time_on_shutdown = 9223372036854775807
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.allow_subtransactions = true
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.recovery_delay = 10000
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.automatic_resource_registration = true
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_max_retries = 5
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.client_demarcation = false
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.threaded_2pc = false
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.serial_jta_transactions = true
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_dir = F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\transaction-logs
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.rmi_export_class = none
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_actives = 50
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.checkpoint_interval = 500
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.enable_logging = true
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_name = tmlog
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_timeout = 300000
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.trust_client_tm = false
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.factory.initial = com.sun.jndi.rmi.registry.RegistryContextFactory
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.tm_unique_name = ***********.tm
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.forget_orphaned_log_entries_delay = 86400000
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_retry_interval = 10000
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.provider.url = rmi://localhost:1099
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.force_shutdown_on_vm_exit = false
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_jta_timeout = 10000
2023-11-07 23:47:25 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Using default (local) logging and recovery...
2023-11-07 23:47:26 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-07 23:47:26 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-07 23:47:26 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-07 23:47:26 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 13.021 seconds (JVM running for 14.203)
2023-11-07 23:47:27 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:47:39 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 调用死因接口，URL:https://public.cqcdc.org/api/oauth/token 参数:{password=rqk6$gOX0lRb, grant_type=password, scope=all, username=2023004} 
2023-11-07 23:47:45 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:49:10 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 调用死因接口，URL:https://public.cqcdc.org/api/oauth/token 参数:{password=rqk6$gOX0lRb, grant_type=password, scope=all, username=2023004} 
2023-11-07 23:49:10 [Thread-29] INFO  com.atomikos.icatch.imp.TransactionServiceImp - Transaction Service: Entering shutdown (false, 9223372036854775807)...
2023-11-07 23:49:10 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-11-07 23:49:19 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 21412 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-11-07 23:49:19 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-11-07 23:49:26 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$4a2556bf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-07 23:49:26 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-11-07 23:49:26 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-11-07 23:49:26 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-11-07 23:49:26 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-11-07 23:49:26 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-11-07 23:49:26 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6746 ms
2023-11-07 23:49:26 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-11-07 23:49:27 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Loaded jar:file:/F:/repository/com/atomikos/transactions/4.0.6/transactions-4.0.6.jar!/transactions-defaults.properties
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_max_wait_time_on_shutdown = 9223372036854775807
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.allow_subtransactions = true
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.recovery_delay = 10000
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.automatic_resource_registration = true
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_max_retries = 5
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.client_demarcation = false
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.threaded_2pc = false
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.serial_jta_transactions = true
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_dir = F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\transaction-logs
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.rmi_export_class = none
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_actives = 50
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.checkpoint_interval = 500
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.enable_logging = true
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.log_base_name = tmlog
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.max_timeout = 300000
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.trust_client_tm = false
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.factory.initial = com.sun.jndi.rmi.registry.RegistryContextFactory
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.tm_unique_name = ***********.tm
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.forget_orphaned_log_entries_delay = 86400000
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.oltp_retry_interval = 10000
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: java.naming.provider.url = rmi://localhost:1099
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.force_shutdown_on_vm_exit = false
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - USING: com.atomikos.icatch.default_jta_timeout = 10000
2023-11-07 23:49:33 [main] INFO  com.atomikos.icatch.provider.imp.AssemblerImp - Using default (local) logging and recovery...
2023-11-07 23:49:33 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-11-07 23:49:34 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-11-07 23:49:34 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:49:34 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-11-07 23:49:34 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 15.214 seconds (JVM running for 16.951)
2023-11-07 23:49:47 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 调用死因接口，URL:https://public.cqcdc.org/api/oauth/token 参数:{password=rqk6$gOX0lRb, grant_type=password, scope=all, username=2023004} 
2023-11-07 23:49:54 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 同步开始。。。
2023-11-07 23:49:54 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.DeathDataConnectedDB2DBJob - 调用死因接口，URL:https://public.cqcdc.org/api/oauth/token 参数:{password=rqk6$gOX0lRb, grant_type=password, scope=all, username=2023004} 

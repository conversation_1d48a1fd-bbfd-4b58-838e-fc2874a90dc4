2025-02-13 11:28:19 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 17568 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2025-02-13 11:28:19 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2025-02-13 11:28:36 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$b448ba05] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-13 11:28:36 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2025-02-13 11:28:36 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2025-02-13 11:28:36 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-13 11:28:36 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2025-02-13 11:28:37 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-13 11:28:37 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 17385 ms
2025-02-13 11:28:37 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-13 11:28:37 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-13 11:28:44 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-02-13 11:28:44 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2025-02-13 11:28:44 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2025-02-13 11:28:44 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 27.179 seconds (JVM running for 29.425)
2025-02-13 11:46:50 [Thread-58] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-02-13 11:47:02 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19520 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2025-02-13 11:47:02 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2025-02-13 11:47:18 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$4b235929] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-13 11:47:18 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2025-02-13 11:47:18 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2025-02-13 11:47:18 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-13 11:47:18 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2025-02-13 11:47:18 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-13 11:47:18 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 15637 ms
2025-02-13 11:47:19 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-13 11:47:19 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-13 11:47:25 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-02-13 11:47:25 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2025-02-13 11:47:25 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2025-02-13 11:47:25 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 23.594 seconds (JVM running for 26.363)
2025-02-13 13:40:15 [Thread-40] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-02-13 13:40:25 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 20924 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2025-02-13 13:40:25 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2025-02-13 13:40:35 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$cdf69359] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-13 13:40:35 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2025-02-13 13:40:35 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2025-02-13 13:40:35 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-13 13:40:35 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2025-02-13 13:40:35 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-13 13:40:35 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 9742 ms
2025-02-13 13:40:36 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-13 13:40:36 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-13 13:40:42 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-02-13 13:40:42 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2025-02-13 13:40:42 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2025-02-13 13:40:42 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 17.182 seconds (JVM running for 19.119)
2025-02-13 13:41:42 [Thread-38] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-02-13 13:47:12 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 9844 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2025-02-13 13:47:12 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2025-02-13 13:47:23 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$b8467cad] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-13 13:47:23 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2025-02-13 13:47:23 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2025-02-13 13:47:23 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-13 13:47:23 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2025-02-13 13:47:23 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-13 13:47:23 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 10721 ms
2025-02-13 13:47:24 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-13 13:47:24 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-13 13:47:29 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-02-13 13:47:29 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2025-02-13 13:47:29 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2025-02-13 13:47:29 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 17.779 seconds (JVM running for 19.555)
2025-02-13 13:49:17 [Thread-37] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-02-13 13:49:44 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 9044 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2025-02-13 13:49:44 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2025-02-13 13:49:53 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$1646a9dd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-13 13:49:54 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2025-02-13 13:49:54 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2025-02-13 13:49:54 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-13 13:49:54 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2025-02-13 13:49:54 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-13 13:49:54 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 9973 ms
2025-02-13 13:49:54 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-13 13:49:55 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-13 13:50:00 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-02-13 13:50:00 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2025-02-13 13:50:00 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2025-02-13 13:50:00 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 16.837 seconds (JVM running for 18.741)
2025-02-13 13:57:58 [Thread-36] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-02-13 13:58:32 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 2788 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2025-02-13 13:58:32 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2025-02-13 13:58:42 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f0cac43b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-13 13:58:42 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2025-02-13 13:58:42 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2025-02-13 13:58:42 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-13 13:58:42 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2025-02-13 13:58:42 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-13 13:58:42 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 9727 ms
2025-02-13 13:58:42 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-13 13:58:43 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-13 13:58:48 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-02-13 13:58:48 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2025-02-13 13:58:48 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2025-02-13 13:58:48 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 16.539 seconds (JVM running for 18.367)
2025-02-13 14:03:58 [Thread-36] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-02-13 14:06:49 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 13352 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2025-02-13 14:06:49 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2025-02-13 14:06:59 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$75c2afce] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-13 14:07:00 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2025-02-13 14:07:00 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2025-02-13 14:07:00 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-13 14:07:00 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2025-02-13 14:07:00 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-13 14:07:00 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 10596 ms
2025-02-13 14:07:00 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-13 14:07:01 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-13 14:07:07 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-02-13 14:07:07 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2025-02-13 14:07:07 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2025-02-13 14:07:07 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 18.379 seconds (JVM running for 21.093)
2025-02-13 14:12:44 [Thread-38] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-02-13 14:12:51 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19612 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2025-02-13 14:12:51 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2025-02-13 14:13:02 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$fdd07624] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-13 14:13:02 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2025-02-13 14:13:02 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2025-02-13 14:13:03 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-13 14:13:03 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2025-02-13 14:13:03 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-13 14:13:03 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 11195 ms
2025-02-13 14:13:03 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-13 14:13:03 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-13 14:13:11 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-02-13 14:13:11 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2025-02-13 14:13:11 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2025-02-13 14:13:11 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 20.24 seconds (JVM running for 22.333)
2025-02-13 14:13:22 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.NotInTimeWarnBase - 职业病报告不及时 工具启动，当前版本：20231113 当前时间：Thu Feb 13 14:13:22 CST 2025
2025-02-13 14:14:23 [Schedule-Task-1] INFO  c.c.m.timer.heth.job.zw.warn.NotInTimeWarnBase - 职业病报告不及时 工具启动，当前版本：20231113 当前时间：Thu Feb 13 14:14:23 CST 2025
2025-02-13 14:14:23 [Thread-43] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed

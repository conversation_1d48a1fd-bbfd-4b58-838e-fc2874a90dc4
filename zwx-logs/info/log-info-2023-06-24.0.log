2023-06-24 13:38:18 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 5836 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-06-24 13:38:18 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-06-24 13:38:25 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$63aa86a7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-06-24 13:38:25 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-06-24 13:38:25 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-06-24 13:38:25 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-06-24 13:38:25 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-06-24 13:38:26 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-06-24 13:38:26 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7106 ms
2023-06-24 13:38:26 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-06-24 13:38:26 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-06-24 13:38:30 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-06-24 13:38:30 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-06-24 13:38:30 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-06-24 13:38:30 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 12.708 seconds (JVM running for 13.602)
2023-06-24 14:37:23 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-06-24 14:37:47 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19460 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-06-24 14:37:47 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-06-24 14:37:53 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3644ae92] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-06-24 14:37:53 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-06-24 14:37:53 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-06-24 14:37:53 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-06-24 14:37:53 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-06-24 14:37:53 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-06-24 14:37:53 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6086 ms
2023-06-24 14:37:54 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-06-24 14:37:54 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-06-24 14:37:58 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-06-24 14:37:58 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-06-24 14:37:58 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-06-24 14:37:58 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 11.204 seconds (JVM running for 12.101)
2023-06-24 14:40:45 [Thread-25] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-06-24 14:40:52 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 19676 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2023-06-24 14:40:52 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2023-06-24 14:40:57 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$adf62dca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-06-24 14:40:58 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9600 (http)
2023-06-24 14:40:58 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9600"]
2023-06-24 14:40:58 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2023-06-24 14:40:58 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.21]
2023-06-24 14:40:58 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-06-24 14:40:58 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6070 ms
2023-06-24 14:40:58 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2023-06-24 14:40:58 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2023-06-24 14:41:02 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2023-06-24 14:41:02 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9600"]
2023-06-24 14:41:02 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9600 (http) with context path ''
2023-06-24 14:41:02 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 11.099 seconds (JVM running for 12.052)
2023-06-24 14:47:04 [Schedule-Task-1] INFO  c.c.m.t.h.j.c.HealthReportCardUpdateFileAndSubmitJob - 职业/放射卫生技术服务信息报送卡附件上传&提交-apiData: {"reportId":"1111111","ocode":"1250000045038921XY","securityKey":"0ac196a70885e9b1df10f181e924229b"}
2023-06-24 14:47:28 [Thread-25] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed

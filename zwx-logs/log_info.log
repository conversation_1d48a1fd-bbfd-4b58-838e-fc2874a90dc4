2025-07-18 10:22:18 [main] INFO  com.SpringbootTimerApplication - Starting SpringbootTimerApplication on W10-20230323986 with PID 9268 (F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes started by Administrator in F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst)
2025-07-18 10:22:18 [main] INFO  com.SpringbootTimerApplication - No active profile set, falling back to default profiles: default
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件夹名称路径：null
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-timer-heth-badrsn-rst/target/classes/chiscdc-batch-check-back.yml
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-timer-heth-badrsn-rst/target/classes/chiscdc-bhk-all-check.yml
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-timer-heth-badrsn-rst/target/classes/chiscdc-cq-mtl-timer.yml
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-timer-heth-badrsn-rst/target/classes/chiscdc-crpt-merge.yml
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-timer-heth-badrsn-rst/target/classes/chiscdc-crpt-warning.yml
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-timer-heth-badrsn-rst/target/classes/chiscdc-online-timer.yml
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-timer-heth-badrsn-rst/target/classes/chiscdc-sx-cfbkf-timer.yml
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-timer-heth-badrsn-rst/target/classes/chiscdc-sx-train-timer.yml
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-timer-heth-badrsn-rst/target/classes/chiscdc-timer.yml
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-timer-heth-badrsn-rst/target/classes/chiscdc-warn-timer.yml
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-timer-heth-badrsn-rst/target/classes/chiscdc-zyws-comm-timer.yml
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-timer-heth-badrsn-rst/target/classes/chiscdc-zyws-rpt-timer.yml
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-jdbc/target/classes/chiscdc-jdbc.yml
2025-07-18 10:22:31 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件：file:/F:/IdeaProjects/spring-timer-heth-badrsn-rst/spring-webmvc/target/classes/chiscdc-webmvc.yml
2025-07-18 10:22:32 [main] INFO  com.chis.modules.webmvc.config.WebMvcConfig - 加载配置文件夹名称路径：null
2025-07-18 10:22:32 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$4041f669] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 10:22:34 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9601 (http)
2025-07-18 10:22:34 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9601"]
2025-07-18 10:22:34 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 10:22:34 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.96]
2025-07-18 10:22:34 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 10:22:34 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 15478 ms
2025-07-18 10:22:35 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-18 10:22:37 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-18 10:22:47 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-07-18 10:22:47 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9601"]
2025-07-18 10:22:47 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9601 (http) with context path ''
2025-07-18 10:22:47 [main] INFO  com.SpringbootTimerApplication - Started SpringbootTimerApplication in 31.184 seconds (JVM running for 33.475)
2025-07-18 10:43:12 [Thread-63] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed

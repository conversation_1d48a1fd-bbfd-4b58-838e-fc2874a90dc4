2024-01-30 09:48:40 [main] ERROR com.alibaba.druid.pool.DruidDataSource - init datasource error, url: ****************************************
java.sql.SQLRecoverableException: IO 错误: The Network Adapter could not establish the connection
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:489)
	at oracle.jdbc.driver.PhysicalConnection.<init>(PhysicalConnection.java:553)
	at oracle.jdbc.driver.T4CConnection.<init>(T4CConnection.java:254)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:32)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:528)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:149)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:218)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:143)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1515)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:859)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1904)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1846)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1774)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1251)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1171)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:857)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:760)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1160)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1251)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1171)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1500)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:592)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1251)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1171)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:593)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:90)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:374)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1411)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:592)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1251)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1171)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:593)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:90)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:374)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1411)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:592)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:845)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:877)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:742)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:389)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1213)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1202)
	at com.SpringbootTimerApplication.main(SpringbootTimerApplication.java:12)
Caused by: oracle.net.ns.NetException: The Network Adapter could not establish the connection
	at oracle.net.nt.ConnStrategy.execute(ConnStrategy.java:439)
	at oracle.net.resolver.AddrResolution.resolveAndExecute(AddrResolution.java:454)
	at oracle.net.ns.NSProtocol.establishConnection(NSProtocol.java:693)
	at oracle.net.ns.NSProtocol.connect(NSProtocol.java:251)
	at oracle.jdbc.driver.T4CConnection.connect(T4CConnection.java:1140)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:340)
	... 84 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at oracle.net.nt.TcpNTAdapter.connect(TcpNTAdapter.java:149)
	at oracle.net.nt.ConnOption.connect(ConnOption.java:133)
	at oracle.net.nt.ConnStrategy.execute(ConnStrategy.java:405)
	... 89 common frames omitted
2024-01-30 09:48:40 [main] ERROR com.alibaba.druid.pool.DruidDataSource - {dataSource-1} init error
java.sql.SQLRecoverableException: IO 错误: The Network Adapter could not establish the connection
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:489)
	at oracle.jdbc.driver.PhysicalConnection.<init>(PhysicalConnection.java:553)
	at oracle.jdbc.driver.T4CConnection.<init>(T4CConnection.java:254)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:32)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:528)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:149)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:218)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:143)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1515)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:859)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1904)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1846)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1774)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1251)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1171)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:857)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:760)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1160)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1251)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1171)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1500)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:592)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1251)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1171)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:593)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:90)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:374)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1411)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:592)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1251)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1171)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:593)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:90)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:374)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1411)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:592)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:845)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:877)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:742)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:389)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1213)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1202)
	at com.SpringbootTimerApplication.main(SpringbootTimerApplication.java:12)
Caused by: oracle.net.ns.NetException: The Network Adapter could not establish the connection
	at oracle.net.nt.ConnStrategy.execute(ConnStrategy.java:439)
	at oracle.net.resolver.AddrResolution.resolveAndExecute(AddrResolution.java:454)
	at oracle.net.ns.NSProtocol.establishConnection(NSProtocol.java:693)
	at oracle.net.ns.NSProtocol.connect(NSProtocol.java:251)
	at oracle.jdbc.driver.T4CConnection.connect(T4CConnection.java:1140)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:340)
	... 84 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at oracle.net.nt.TcpNTAdapter.connect(TcpNTAdapter.java:149)
	at oracle.net.nt.ConnOption.connect(ConnOption.java:133)
	at oracle.net.nt.ConnStrategy.execute(ConnStrategy.java:405)
	... 89 common frames omitted
2024-01-30 09:48:40 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sxFsTokenController': Unsatisfied dependency expressed through field 'tsSystemParamService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'tsSystemParamService': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'tsSystemParamMapper' defined in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-jdbc\target\classes\com\chis\modules\sys\mapper\TsSystemParamMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is java.sql.SQLRecoverableException: IO 错误: The Network Adapter could not establish the connection
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:596)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:90)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:374)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1411)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:592)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:845)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:877)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:742)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:389)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1213)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1202)
	at com.SpringbootTimerApplication.main(SpringbootTimerApplication.java:12)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'tsSystemParamService': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'tsSystemParamMapper' defined in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-jdbc\target\classes\com\chis\modules\sys\mapper\TsSystemParamMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is java.sql.SQLRecoverableException: IO 错误: The Network Adapter could not establish the connection
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:596)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:90)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:374)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1411)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:592)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1251)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1171)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:593)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'tsSystemParamMapper' defined in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-jdbc\target\classes\com\chis\modules\sys\mapper\TsSystemParamMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is java.sql.SQLRecoverableException: IO 错误: The Network Adapter could not establish the connection
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1515)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:592)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1251)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1171)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:593)
	... 32 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is java.sql.SQLRecoverableException: IO 错误: The Network Adapter could not establish the connection
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:769)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1160)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1251)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1171)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1500)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is java.sql.SQLRecoverableException: IO 错误: The Network Adapter could not establish the connection
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1778)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1251)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1171)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:857)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:760)
	... 56 common frames omitted
Caused by: java.sql.SQLRecoverableException: IO 错误: The Network Adapter could not establish the connection
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:489)
	at oracle.jdbc.driver.PhysicalConnection.<init>(PhysicalConnection.java:553)
	at oracle.jdbc.driver.T4CConnection.<init>(T4CConnection.java:254)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:32)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:528)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:149)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:218)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:143)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1515)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:859)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1904)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1846)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1774)
	... 67 common frames omitted
Caused by: oracle.net.ns.NetException: The Network Adapter could not establish the connection
	at oracle.net.nt.ConnStrategy.execute(ConnStrategy.java:439)
	at oracle.net.resolver.AddrResolution.resolveAndExecute(AddrResolution.java:454)
	at oracle.net.ns.NSProtocol.establishConnection(NSProtocol.java:693)
	at oracle.net.ns.NSProtocol.connect(NSProtocol.java:251)
	at oracle.jdbc.driver.T4CConnection.connect(T4CConnection.java:1140)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:340)
	... 84 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at oracle.net.nt.TcpNTAdapter.connect(TcpNTAdapter.java:149)
	at oracle.net.nt.ConnOption.connect(ConnOption.java:133)
	at oracle.net.nt.ConnStrategy.execute(ConnStrategy.java:405)
	... 89 common frames omitted
2024-01-30 09:49:25 [Schedule-Task-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1864 millis. select
         t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.PERSON_ID,t.PERSON_NAME,t.SEX,
        t.IDC,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,
        t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,
        t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.MHKDCTNO,t.MHKDCT,t.JDGDAT,t.BADRSN,t.IF_LACKITM,
        t.IF_TARGETDIS,t.IF_WRKTABU,t.IF_INTEITM_LACK,t.IF_RHK,t.PROCESS_LACK,t.LACK_MSG,t.PSN_TYPE,
        t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.CRPT_NAME,t.UUID,t.RPT_PRINT_DATE,t.IF_ITEM_SET_STD,
        t.SET_STD_MSG,t.IF_REPORT_INTIME,t.IF_INTO_ZDZYB_ANALY,t.CHECK_STATE,t.BACK_RSN,t.NOT_CHECK_STATE,
        t.NOT_CHECK_RSN,t.WORK_TYPE_ID,t.WORK_OTHER,t.CARD_TYPE_ID,t.HARM_START_DATE,t.DEL_RSN,t.JC_TYPE,
        t.OTHER_BADRSN,t.IF_INTO_CHECK,t.IF_INDUS_TYPE_NOSTD,t.IF_CRPT_SIZE_NOSTD,t.IF_ABNOMAL,t.STATE,
        t.COUNTY_SMT_DATE,t.COUNTY_RST,t.COUNTY_AUDIT_ADV,t.COUNTY_CHK_ORGID,t.CITY_SMT_DATE,t.CITY_RST,
        t.CITY_AUDIT_ADV,t.CIYT_CHK_ORGID,t.PRO_SMT_DATE,t.CITY_RST2,t.PRO_AUDIT_ADV,t.PRO_CHK_ORGID,
        t.DEAL_COMPLETE_DATE,t.IF_WRK_AGE_NOSTD,t.ENTRUST_CRPT_ID 
        from TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON t.ENTRUST_CRPT_ID = T1.RID
        LEFT JOIN TD_ZWYJ_DANGER_LOG T2 ON t.RID = T2.BHK_ID
        WHERE T1.INTER_PRC_TAG = 1
           AND t.BHK_TYPE IN (3,4)
           AND T2.RID IS NULL
         
            AND t.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND ROWNUM <=  ?
         
         
        ORDER BY t.RID["2022-01-01",900]
2024-01-30 09:49:47 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - Could not open JDBC Connection for transaction; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 09:49:47 CST 2024
2024-01-30 09:49:47 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - Could not open JDBC Connection for transaction; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 09:49:47 CST 2024
2024-01-30 09:49:47 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - Could not open JDBC Connection for transaction; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 09:49:47 CST 2024
2024-01-30 09:49:47 [Schedule-Task-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task.
org.springframework.transaction.CannotCreateTransactionException: Could not open JDBC Connection for transaction; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 09:49:47 CST 2024
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:305)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:378)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:475)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:289)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.chis.modules.timer.heth.service.TdTjBhkService$$EnhancerBySpringCGLIB$$6e0a13f2.selectDangerValToolBhks(<generated>)
	at com.chis.modules.timer.heth.job.DangerValToolJob.findTdtjBhks(DangerValToolJob.java:193)
	at com.chis.modules.timer.heth.job.DangerValToolJob.excuteData(DangerValToolJob.java:51)
	at com.chis.modules.timer.heth.job.DangerValToolJob.excuteData(DangerValToolJob.java:58)
	at com.chis.modules.timer.heth.job.DangerValToolJob.start(DangerValToolJob.java:45)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 09:49:47 CST 2024
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1356)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1253)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4619)
	at com.alibaba.druid.filter.stat.StatFilter.dataSource_getConnection(StatFilter.java:680)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4615)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1231)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1223)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:90)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:262)
	... 26 common frames omitted
2024-01-30 09:52:40 [Schedule-Task-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1691 millis. select
         t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.PERSON_ID,t.PERSON_NAME,t.SEX,
        t.IDC,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,
        t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,
        t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.MHKDCTNO,t.MHKDCT,t.JDGDAT,t.BADRSN,t.IF_LACKITM,
        t.IF_TARGETDIS,t.IF_WRKTABU,t.IF_INTEITM_LACK,t.IF_RHK,t.PROCESS_LACK,t.LACK_MSG,t.PSN_TYPE,
        t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.CRPT_NAME,t.UUID,t.RPT_PRINT_DATE,t.IF_ITEM_SET_STD,
        t.SET_STD_MSG,t.IF_REPORT_INTIME,t.IF_INTO_ZDZYB_ANALY,t.CHECK_STATE,t.BACK_RSN,t.NOT_CHECK_STATE,
        t.NOT_CHECK_RSN,t.WORK_TYPE_ID,t.WORK_OTHER,t.CARD_TYPE_ID,t.HARM_START_DATE,t.DEL_RSN,t.JC_TYPE,
        t.OTHER_BADRSN,t.IF_INTO_CHECK,t.IF_INDUS_TYPE_NOSTD,t.IF_CRPT_SIZE_NOSTD,t.IF_ABNOMAL,t.STATE,
        t.COUNTY_SMT_DATE,t.COUNTY_RST,t.COUNTY_AUDIT_ADV,t.COUNTY_CHK_ORGID,t.CITY_SMT_DATE,t.CITY_RST,
        t.CITY_AUDIT_ADV,t.CIYT_CHK_ORGID,t.PRO_SMT_DATE,t.CITY_RST2,t.PRO_AUDIT_ADV,t.PRO_CHK_ORGID,
        t.DEAL_COMPLETE_DATE,t.IF_WRK_AGE_NOSTD,t.ENTRUST_CRPT_ID 
            ,T1.CRPT_NAME as entrustCrptName
        from TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON t.ENTRUST_CRPT_ID = T1.RID
        LEFT JOIN TD_ZWYJ_DANGER_LOG T2 ON t.RID = T2.BHK_ID
        WHERE T1.INTER_PRC_TAG = 1
           AND t.BHK_TYPE IN (3,4)
           AND T2.RID IS NULL
         
            AND t.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND ROWNUM <=  ?
         
         
        ORDER BY t.RID["2022-01-01",900]
2024-01-30 09:56:10 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - Could not open JDBC Connection for transaction; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 09:56:10 CST 2024
2024-01-30 09:56:10 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - Could not open JDBC Connection for transaction; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 09:56:10 CST 2024
2024-01-30 09:56:10 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - Could not open JDBC Connection for transaction; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 09:56:10 CST 2024
2024-01-30 09:56:10 [Schedule-Task-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task.
org.springframework.transaction.CannotCreateTransactionException: Could not open JDBC Connection for transaction; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 09:56:10 CST 2024
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:305)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:378)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:475)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:289)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.chis.modules.timer.heth.service.TdTjBhkService$$EnhancerBySpringCGLIB$$b07bbc58.selectDangerValToolBhks(<generated>)
	at com.chis.modules.timer.heth.job.DangerValToolJob.findTdtjBhks(DangerValToolJob.java:193)
	at com.chis.modules.timer.heth.job.DangerValToolJob.excuteData(DangerValToolJob.java:51)
	at com.chis.modules.timer.heth.job.DangerValToolJob.excuteData(DangerValToolJob.java:58)
	at com.chis.modules.timer.heth.job.DangerValToolJob.start(DangerValToolJob.java:45)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 09:56:10 CST 2024
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1356)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1253)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4619)
	at com.alibaba.druid.filter.stat.StatFilter.dataSource_getConnection(StatFilter.java:680)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4615)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1231)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1223)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:90)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:262)
	... 26 common frames omitted
2024-01-30 15:00:40 [Schedule-Task-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1704 millis. select
         t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.PERSON_ID,t.PERSON_NAME,t.SEX,
        t.IDC,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,
        t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,
        t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.MHKDCTNO,t.MHKDCT,t.JDGDAT,t.BADRSN,t.IF_LACKITM,
        t.IF_TARGETDIS,t.IF_WRKTABU,t.IF_INTEITM_LACK,t.IF_RHK,t.PROCESS_LACK,t.LACK_MSG,t.PSN_TYPE,
        t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.CRPT_NAME,t.UUID,t.RPT_PRINT_DATE,t.IF_ITEM_SET_STD,
        t.SET_STD_MSG,t.IF_REPORT_INTIME,t.IF_INTO_ZDZYB_ANALY,t.CHECK_STATE,t.BACK_RSN,t.NOT_CHECK_STATE,
        t.NOT_CHECK_RSN,t.WORK_TYPE_ID,t.WORK_OTHER,t.CARD_TYPE_ID,t.HARM_START_DATE,t.DEL_RSN,t.JC_TYPE,
        t.OTHER_BADRSN,t.IF_INTO_CHECK,t.IF_INDUS_TYPE_NOSTD,t.IF_CRPT_SIZE_NOSTD,t.IF_ABNOMAL,t.STATE,
        t.COUNTY_SMT_DATE,t.COUNTY_RST,t.COUNTY_AUDIT_ADV,t.COUNTY_CHK_ORGID,t.CITY_SMT_DATE,t.CITY_RST,
        t.CITY_AUDIT_ADV,t.CIYT_CHK_ORGID,t.PRO_SMT_DATE,t.CITY_RST2,t.PRO_AUDIT_ADV,t.PRO_CHK_ORGID,
        t.DEAL_COMPLETE_DATE,t.IF_WRK_AGE_NOSTD,t.ENTRUST_CRPT_ID 
            ,T1.RID as ENTRUST_CRPT_ID
        from TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON t.ENTRUST_CRPT_ID = T1.RID
        LEFT JOIN TD_ZWYJ_DANGER_LOG T2 ON t.RID = T2.BHK_ID
        WHERE T1.INTER_PRC_TAG = 1
           AND t.BHK_TYPE IN (3,4)
           AND T2.RID IS NULL
         
            AND t.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND ROWNUM <=  ?
         
         
        ORDER BY t.RID["2022-01-01",900]
2024-01-30 15:01:56 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:01:56 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:01:56 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:01:59 [Schedule-Task-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1428 millis. select
         t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.PERSON_ID,t.PERSON_NAME,t.SEX,
        t.IDC,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,
        t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,
        t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.MHKDCTNO,t.MHKDCT,t.JDGDAT,t.BADRSN,t.IF_LACKITM,
        t.IF_TARGETDIS,t.IF_WRKTABU,t.IF_INTEITM_LACK,t.IF_RHK,t.PROCESS_LACK,t.LACK_MSG,t.PSN_TYPE,
        t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.CRPT_NAME,t.UUID,t.RPT_PRINT_DATE,t.IF_ITEM_SET_STD,
        t.SET_STD_MSG,t.IF_REPORT_INTIME,t.IF_INTO_ZDZYB_ANALY,t.CHECK_STATE,t.BACK_RSN,t.NOT_CHECK_STATE,
        t.NOT_CHECK_RSN,t.WORK_TYPE_ID,t.WORK_OTHER,t.CARD_TYPE_ID,t.HARM_START_DATE,t.DEL_RSN,t.JC_TYPE,
        t.OTHER_BADRSN,t.IF_INTO_CHECK,t.IF_INDUS_TYPE_NOSTD,t.IF_CRPT_SIZE_NOSTD,t.IF_ABNOMAL,t.STATE,
        t.COUNTY_SMT_DATE,t.COUNTY_RST,t.COUNTY_AUDIT_ADV,t.COUNTY_CHK_ORGID,t.CITY_SMT_DATE,t.CITY_RST,
        t.CITY_AUDIT_ADV,t.CIYT_CHK_ORGID,t.PRO_SMT_DATE,t.CITY_RST2,t.PRO_AUDIT_ADV,t.PRO_CHK_ORGID,
        t.DEAL_COMPLETE_DATE,t.IF_WRK_AGE_NOSTD,t.ENTRUST_CRPT_ID 
            ,T1.RID as ENTRUST_CRPT_ID
        from TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON t.ENTRUST_CRPT_ID = T1.RID
        LEFT JOIN TD_ZWYJ_DANGER_LOG T2 ON t.RID = T2.BHK_ID
        WHERE T1.INTER_PRC_TAG = 1
           AND t.BHK_TYPE IN (3,4)
           AND T2.RID IS NULL
         
            AND t.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND ROWNUM <=  ?
         
         
        ORDER BY t.RID["2022-01-01",900]
2024-01-30 15:01:59 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:01:59 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:01:59 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:02:03 [Schedule-Task-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1463 millis. select
         t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.PERSON_ID,t.PERSON_NAME,t.SEX,
        t.IDC,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,
        t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,
        t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.MHKDCTNO,t.MHKDCT,t.JDGDAT,t.BADRSN,t.IF_LACKITM,
        t.IF_TARGETDIS,t.IF_WRKTABU,t.IF_INTEITM_LACK,t.IF_RHK,t.PROCESS_LACK,t.LACK_MSG,t.PSN_TYPE,
        t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.CRPT_NAME,t.UUID,t.RPT_PRINT_DATE,t.IF_ITEM_SET_STD,
        t.SET_STD_MSG,t.IF_REPORT_INTIME,t.IF_INTO_ZDZYB_ANALY,t.CHECK_STATE,t.BACK_RSN,t.NOT_CHECK_STATE,
        t.NOT_CHECK_RSN,t.WORK_TYPE_ID,t.WORK_OTHER,t.CARD_TYPE_ID,t.HARM_START_DATE,t.DEL_RSN,t.JC_TYPE,
        t.OTHER_BADRSN,t.IF_INTO_CHECK,t.IF_INDUS_TYPE_NOSTD,t.IF_CRPT_SIZE_NOSTD,t.IF_ABNOMAL,t.STATE,
        t.COUNTY_SMT_DATE,t.COUNTY_RST,t.COUNTY_AUDIT_ADV,t.COUNTY_CHK_ORGID,t.CITY_SMT_DATE,t.CITY_RST,
        t.CITY_AUDIT_ADV,t.CIYT_CHK_ORGID,t.PRO_SMT_DATE,t.CITY_RST2,t.PRO_AUDIT_ADV,t.PRO_CHK_ORGID,
        t.DEAL_COMPLETE_DATE,t.IF_WRK_AGE_NOSTD,t.ENTRUST_CRPT_ID 
            ,T1.RID as ENTRUST_CRPT_ID
        from TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON t.ENTRUST_CRPT_ID = T1.RID
        LEFT JOIN TD_ZWYJ_DANGER_LOG T2 ON t.RID = T2.BHK_ID
        WHERE T1.INTER_PRC_TAG = 1
           AND t.BHK_TYPE IN (3,4)
           AND T2.RID IS NULL
         
            AND t.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND ROWNUM <=  ?
         
         
        ORDER BY t.RID["2022-01-01",900]
2024-01-30 15:02:10 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:02:10 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:02:10 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:02:53 [Schedule-Task-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1393 millis. select
         t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.PERSON_ID,t.PERSON_NAME,t.SEX,
        t.IDC,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,
        t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,
        t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.MHKDCTNO,t.MHKDCT,t.JDGDAT,t.BADRSN,t.IF_LACKITM,
        t.IF_TARGETDIS,t.IF_WRKTABU,t.IF_INTEITM_LACK,t.IF_RHK,t.PROCESS_LACK,t.LACK_MSG,t.PSN_TYPE,
        t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.CRPT_NAME,t.UUID,t.RPT_PRINT_DATE,t.IF_ITEM_SET_STD,
        t.SET_STD_MSG,t.IF_REPORT_INTIME,t.IF_INTO_ZDZYB_ANALY,t.CHECK_STATE,t.BACK_RSN,t.NOT_CHECK_STATE,
        t.NOT_CHECK_RSN,t.WORK_TYPE_ID,t.WORK_OTHER,t.CARD_TYPE_ID,t.HARM_START_DATE,t.DEL_RSN,t.JC_TYPE,
        t.OTHER_BADRSN,t.IF_INTO_CHECK,t.IF_INDUS_TYPE_NOSTD,t.IF_CRPT_SIZE_NOSTD,t.IF_ABNOMAL,t.STATE,
        t.COUNTY_SMT_DATE,t.COUNTY_RST,t.COUNTY_AUDIT_ADV,t.COUNTY_CHK_ORGID,t.CITY_SMT_DATE,t.CITY_RST,
        t.CITY_AUDIT_ADV,t.CIYT_CHK_ORGID,t.PRO_SMT_DATE,t.CITY_RST2,t.PRO_AUDIT_ADV,t.PRO_CHK_ORGID,
        t.DEAL_COMPLETE_DATE,t.IF_WRK_AGE_NOSTD,t.ENTRUST_CRPT_ID 
            ,T1.RID as ENTRUST_CRPT_ID
        from TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON t.ENTRUST_CRPT_ID = T1.RID
        LEFT JOIN TD_ZWYJ_DANGER_LOG T2 ON t.RID = T2.BHK_ID
        WHERE T1.INTER_PRC_TAG = 1
           AND t.BHK_TYPE IN (3,4)
           AND T2.RID IS NULL
         
            AND t.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND ROWNUM <=  ?
         
         
        ORDER BY t.RID["2022-01-01",900]
2024-01-30 15:03:00 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:00 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:00 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:03 [Schedule-Task-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1492 millis. select
         t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.PERSON_ID,t.PERSON_NAME,t.SEX,
        t.IDC,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,
        t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,
        t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.MHKDCTNO,t.MHKDCT,t.JDGDAT,t.BADRSN,t.IF_LACKITM,
        t.IF_TARGETDIS,t.IF_WRKTABU,t.IF_INTEITM_LACK,t.IF_RHK,t.PROCESS_LACK,t.LACK_MSG,t.PSN_TYPE,
        t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.CRPT_NAME,t.UUID,t.RPT_PRINT_DATE,t.IF_ITEM_SET_STD,
        t.SET_STD_MSG,t.IF_REPORT_INTIME,t.IF_INTO_ZDZYB_ANALY,t.CHECK_STATE,t.BACK_RSN,t.NOT_CHECK_STATE,
        t.NOT_CHECK_RSN,t.WORK_TYPE_ID,t.WORK_OTHER,t.CARD_TYPE_ID,t.HARM_START_DATE,t.DEL_RSN,t.JC_TYPE,
        t.OTHER_BADRSN,t.IF_INTO_CHECK,t.IF_INDUS_TYPE_NOSTD,t.IF_CRPT_SIZE_NOSTD,t.IF_ABNOMAL,t.STATE,
        t.COUNTY_SMT_DATE,t.COUNTY_RST,t.COUNTY_AUDIT_ADV,t.COUNTY_CHK_ORGID,t.CITY_SMT_DATE,t.CITY_RST,
        t.CITY_AUDIT_ADV,t.CIYT_CHK_ORGID,t.PRO_SMT_DATE,t.CITY_RST2,t.PRO_AUDIT_ADV,t.PRO_CHK_ORGID,
        t.DEAL_COMPLETE_DATE,t.IF_WRK_AGE_NOSTD,t.ENTRUST_CRPT_ID 
            ,T1.RID as ENTRUST_CRPT_ID
        from TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON t.ENTRUST_CRPT_ID = T1.RID
        LEFT JOIN TD_ZWYJ_DANGER_LOG T2 ON t.RID = T2.BHK_ID
        WHERE T1.INTER_PRC_TAG = 1
           AND t.BHK_TYPE IN (3,4)
           AND T2.RID IS NULL
         
            AND t.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND ROWNUM <=  ?
         
         
        ORDER BY t.RID["2022-01-01",900]
2024-01-30 15:03:05 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:05 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:05 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:07 [Schedule-Task-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1415 millis. select
         t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.PERSON_ID,t.PERSON_NAME,t.SEX,
        t.IDC,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,
        t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,
        t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.MHKDCTNO,t.MHKDCT,t.JDGDAT,t.BADRSN,t.IF_LACKITM,
        t.IF_TARGETDIS,t.IF_WRKTABU,t.IF_INTEITM_LACK,t.IF_RHK,t.PROCESS_LACK,t.LACK_MSG,t.PSN_TYPE,
        t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.CRPT_NAME,t.UUID,t.RPT_PRINT_DATE,t.IF_ITEM_SET_STD,
        t.SET_STD_MSG,t.IF_REPORT_INTIME,t.IF_INTO_ZDZYB_ANALY,t.CHECK_STATE,t.BACK_RSN,t.NOT_CHECK_STATE,
        t.NOT_CHECK_RSN,t.WORK_TYPE_ID,t.WORK_OTHER,t.CARD_TYPE_ID,t.HARM_START_DATE,t.DEL_RSN,t.JC_TYPE,
        t.OTHER_BADRSN,t.IF_INTO_CHECK,t.IF_INDUS_TYPE_NOSTD,t.IF_CRPT_SIZE_NOSTD,t.IF_ABNOMAL,t.STATE,
        t.COUNTY_SMT_DATE,t.COUNTY_RST,t.COUNTY_AUDIT_ADV,t.COUNTY_CHK_ORGID,t.CITY_SMT_DATE,t.CITY_RST,
        t.CITY_AUDIT_ADV,t.CIYT_CHK_ORGID,t.PRO_SMT_DATE,t.CITY_RST2,t.PRO_AUDIT_ADV,t.PRO_CHK_ORGID,
        t.DEAL_COMPLETE_DATE,t.IF_WRK_AGE_NOSTD,t.ENTRUST_CRPT_ID 
            ,T1.RID as ENTRUST_CRPT_ID
        from TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON t.ENTRUST_CRPT_ID = T1.RID
        LEFT JOIN TD_ZWYJ_DANGER_LOG T2 ON t.RID = T2.BHK_ID
        WHERE T1.INTER_PRC_TAG = 1
           AND t.BHK_TYPE IN (3,4)
           AND T2.RID IS NULL
         
            AND t.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND ROWNUM <=  ?
         
         
        ORDER BY t.RID["2022-01-01",900]
2024-01-30 15:03:07 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:07 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:07 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:09 [Schedule-Task-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1419 millis. select
         t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.PERSON_ID,t.PERSON_NAME,t.SEX,
        t.IDC,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,
        t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,
        t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.MHKDCTNO,t.MHKDCT,t.JDGDAT,t.BADRSN,t.IF_LACKITM,
        t.IF_TARGETDIS,t.IF_WRKTABU,t.IF_INTEITM_LACK,t.IF_RHK,t.PROCESS_LACK,t.LACK_MSG,t.PSN_TYPE,
        t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.CRPT_NAME,t.UUID,t.RPT_PRINT_DATE,t.IF_ITEM_SET_STD,
        t.SET_STD_MSG,t.IF_REPORT_INTIME,t.IF_INTO_ZDZYB_ANALY,t.CHECK_STATE,t.BACK_RSN,t.NOT_CHECK_STATE,
        t.NOT_CHECK_RSN,t.WORK_TYPE_ID,t.WORK_OTHER,t.CARD_TYPE_ID,t.HARM_START_DATE,t.DEL_RSN,t.JC_TYPE,
        t.OTHER_BADRSN,t.IF_INTO_CHECK,t.IF_INDUS_TYPE_NOSTD,t.IF_CRPT_SIZE_NOSTD,t.IF_ABNOMAL,t.STATE,
        t.COUNTY_SMT_DATE,t.COUNTY_RST,t.COUNTY_AUDIT_ADV,t.COUNTY_CHK_ORGID,t.CITY_SMT_DATE,t.CITY_RST,
        t.CITY_AUDIT_ADV,t.CIYT_CHK_ORGID,t.PRO_SMT_DATE,t.CITY_RST2,t.PRO_AUDIT_ADV,t.PRO_CHK_ORGID,
        t.DEAL_COMPLETE_DATE,t.IF_WRK_AGE_NOSTD,t.ENTRUST_CRPT_ID 
            ,T1.RID as ENTRUST_CRPT_ID
        from TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON t.ENTRUST_CRPT_ID = T1.RID
        LEFT JOIN TD_ZWYJ_DANGER_LOG T2 ON t.RID = T2.BHK_ID
        WHERE T1.INTER_PRC_TAG = 1
           AND t.BHK_TYPE IN (3,4)
           AND T2.RID IS NULL
         
            AND t.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND ROWNUM <=  ?
         
         
        ORDER BY t.RID["2022-01-01",900]
2024-01-30 15:03:10 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:10 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:10 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:11 [Schedule-Task-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1515 millis. select
         t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.PERSON_ID,t.PERSON_NAME,t.SEX,
        t.IDC,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,
        t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,
        t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.MHKDCTNO,t.MHKDCT,t.JDGDAT,t.BADRSN,t.IF_LACKITM,
        t.IF_TARGETDIS,t.IF_WRKTABU,t.IF_INTEITM_LACK,t.IF_RHK,t.PROCESS_LACK,t.LACK_MSG,t.PSN_TYPE,
        t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.CRPT_NAME,t.UUID,t.RPT_PRINT_DATE,t.IF_ITEM_SET_STD,
        t.SET_STD_MSG,t.IF_REPORT_INTIME,t.IF_INTO_ZDZYB_ANALY,t.CHECK_STATE,t.BACK_RSN,t.NOT_CHECK_STATE,
        t.NOT_CHECK_RSN,t.WORK_TYPE_ID,t.WORK_OTHER,t.CARD_TYPE_ID,t.HARM_START_DATE,t.DEL_RSN,t.JC_TYPE,
        t.OTHER_BADRSN,t.IF_INTO_CHECK,t.IF_INDUS_TYPE_NOSTD,t.IF_CRPT_SIZE_NOSTD,t.IF_ABNOMAL,t.STATE,
        t.COUNTY_SMT_DATE,t.COUNTY_RST,t.COUNTY_AUDIT_ADV,t.COUNTY_CHK_ORGID,t.CITY_SMT_DATE,t.CITY_RST,
        t.CITY_AUDIT_ADV,t.CIYT_CHK_ORGID,t.PRO_SMT_DATE,t.CITY_RST2,t.PRO_AUDIT_ADV,t.PRO_CHK_ORGID,
        t.DEAL_COMPLETE_DATE,t.IF_WRK_AGE_NOSTD,t.ENTRUST_CRPT_ID 
            ,T1.RID as ENTRUST_CRPT_ID
        from TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON t.ENTRUST_CRPT_ID = T1.RID
        LEFT JOIN TD_ZWYJ_DANGER_LOG T2 ON t.RID = T2.BHK_ID
        WHERE T1.INTER_PRC_TAG = 1
           AND t.BHK_TYPE IN (3,4)
           AND T2.RID IS NULL
         
            AND t.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND ROWNUM <=  ?
         
         
        ORDER BY t.RID["2022-01-01",900]
2024-01-30 15:03:12 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:12 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:12 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:03:14 [Schedule-Task-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1477 millis. select
         t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.PERSON_ID,t.PERSON_NAME,t.SEX,
        t.IDC,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,
        t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,
        t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.MHKDCTNO,t.MHKDCT,t.JDGDAT,t.BADRSN,t.IF_LACKITM,
        t.IF_TARGETDIS,t.IF_WRKTABU,t.IF_INTEITM_LACK,t.IF_RHK,t.PROCESS_LACK,t.LACK_MSG,t.PSN_TYPE,
        t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.CRPT_NAME,t.UUID,t.RPT_PRINT_DATE,t.IF_ITEM_SET_STD,
        t.SET_STD_MSG,t.IF_REPORT_INTIME,t.IF_INTO_ZDZYB_ANALY,t.CHECK_STATE,t.BACK_RSN,t.NOT_CHECK_STATE,
        t.NOT_CHECK_RSN,t.WORK_TYPE_ID,t.WORK_OTHER,t.CARD_TYPE_ID,t.HARM_START_DATE,t.DEL_RSN,t.JC_TYPE,
        t.OTHER_BADRSN,t.IF_INTO_CHECK,t.IF_INDUS_TYPE_NOSTD,t.IF_CRPT_SIZE_NOSTD,t.IF_ABNOMAL,t.STATE,
        t.COUNTY_SMT_DATE,t.COUNTY_RST,t.COUNTY_AUDIT_ADV,t.COUNTY_CHK_ORGID,t.CITY_SMT_DATE,t.CITY_RST,
        t.CITY_AUDIT_ADV,t.CIYT_CHK_ORGID,t.PRO_SMT_DATE,t.CITY_RST2,t.PRO_AUDIT_ADV,t.PRO_CHK_ORGID,
        t.DEAL_COMPLETE_DATE,t.IF_WRK_AGE_NOSTD,t.ENTRUST_CRPT_ID 
            ,T1.RID as ENTRUST_CRPT_ID
        from TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON t.ENTRUST_CRPT_ID = T1.RID
        LEFT JOIN TD_ZWYJ_DANGER_LOG T2 ON t.RID = T2.BHK_ID
        WHERE T1.INTER_PRC_TAG = 1
           AND t.BHK_TYPE IN (3,4)
           AND T2.RID IS NULL
         
            AND t.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND ROWNUM <=  ?
         
         
        ORDER BY t.RID["2022-01-01",900]
2024-01-30 15:03:59 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:04:14 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?                                 , ?                                 , ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:04:14 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdZwyjDangerBhkMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select          t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,         t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,             t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,             t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,             t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,             t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,             t.LAST_FST_BHK_CODE,t.UUID,t.ENTRUST_CRPT_ID          from  TD_ZWYJ_DANGER_BHK t         where t.BHKORG_ID = ?                       AND t.BHK_CODE IN              (                    ?               )
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."ENTRUST_CRPT_ID": 标识符无效

2024-01-30 15:21:34 [Schedule-Task-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1757 millis. select
         t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.PERSON_ID,t.PERSON_NAME,t.SEX,
        t.IDC,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,
        t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,
        t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.MHKDCTNO,t.MHKDCT,t.JDGDAT,t.BADRSN,t.IF_LACKITM,
        t.IF_TARGETDIS,t.IF_WRKTABU,t.IF_INTEITM_LACK,t.IF_RHK,t.PROCESS_LACK,t.LACK_MSG,t.PSN_TYPE,
        t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.CRPT_NAME,t.UUID,t.RPT_PRINT_DATE,t.IF_ITEM_SET_STD,
        t.SET_STD_MSG,t.IF_REPORT_INTIME,t.IF_INTO_ZDZYB_ANALY,t.CHECK_STATE,t.BACK_RSN,t.NOT_CHECK_STATE,
        t.NOT_CHECK_RSN,t.WORK_TYPE_ID,t.WORK_OTHER,t.CARD_TYPE_ID,t.HARM_START_DATE,t.DEL_RSN,t.JC_TYPE,
        t.OTHER_BADRSN,t.IF_INTO_CHECK,t.IF_INDUS_TYPE_NOSTD,t.IF_CRPT_SIZE_NOSTD,t.IF_ABNOMAL,t.STATE,
        t.COUNTY_SMT_DATE,t.COUNTY_RST,t.COUNTY_AUDIT_ADV,t.COUNTY_CHK_ORGID,t.CITY_SMT_DATE,t.CITY_RST,
        t.CITY_AUDIT_ADV,t.CIYT_CHK_ORGID,t.PRO_SMT_DATE,t.CITY_RST2,t.PRO_AUDIT_ADV,t.PRO_CHK_ORGID,
        t.DEAL_COMPLETE_DATE,t.IF_WRK_AGE_NOSTD,t.ENTRUST_CRPT_ID 
            ,T1.RID as ENTRUST_CRPT_ID
        from TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON t.ENTRUST_CRPT_ID = T1.RID
        LEFT JOIN TD_ZWYJ_DANGER_LOG T2 ON t.RID = T2.BHK_ID
        WHERE T1.INTER_PRC_TAG = 1
           AND t.BHK_TYPE IN (3,4)
           AND T2.RID IS NULL
         
            AND t.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND ROWNUM <=  ?
         
         
        ORDER BY t.RID["2022-01-01",900]
2024-01-30 15:23:03 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - Could not open JDBC Connection for transaction; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 15:23:03 CST 2024
2024-01-30 15:23:03 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - Could not open JDBC Connection for transaction; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 15:23:03 CST 2024
2024-01-30 15:23:03 [Schedule-Task-1] ERROR com.chis.modules.timer.heth.job.DangerValToolJob - Could not open JDBC Connection for transaction; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 15:23:03 CST 2024
2024-01-30 15:23:03 [Schedule-Task-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task.
org.springframework.transaction.CannotCreateTransactionException: Could not open JDBC Connection for transaction; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 15:23:03 CST 2024
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:305)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:378)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:475)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:289)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.chis.modules.timer.heth.service.TdTjBhkService$$EnhancerBySpringCGLIB$$1986befe.selectDangerValToolBhks(<generated>)
	at com.chis.modules.timer.heth.job.DangerValToolJob.findTdtjBhks(DangerValToolJob.java:193)
	at com.chis.modules.timer.heth.job.DangerValToolJob.excuteData(DangerValToolJob.java:51)
	at com.chis.modules.timer.heth.job.DangerValToolJob.excuteData(DangerValToolJob.java:58)
	at com.chis.modules.timer.heth.job.DangerValToolJob.start(DangerValToolJob.java:45)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Jan 30 15:23:03 CST 2024
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1356)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1253)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4619)
	at com.alibaba.druid.filter.stat.StatFilter.dataSource_getConnection(StatFilter.java:680)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4615)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1231)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1223)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:90)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:262)
	... 26 common frames omitted

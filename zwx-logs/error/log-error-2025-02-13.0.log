2025-02-13 13:40:44 [Schedule-Task-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task.
java.lang.NullPointerException: null
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.lambda$getCrptWarnRecords$4(OccDisCaseLateTimeJob.java:318)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.getCrptWarnRecords(OccDisCaseLateTimeJob.java:315)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.getWarnMap(OccDisCaseLateTimeJob.java:112)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.start(OccDisCaseLateTimeJob.java:73)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-13 13:40:45 [Schedule-Task-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task.
java.lang.NullPointerException: null
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.lambda$getCrptWarnRecords$4(OccDisCaseLateTimeJob.java:318)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.getCrptWarnRecords(OccDisCaseLateTimeJob.java:315)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.getWarnMap(OccDisCaseLateTimeJob.java:112)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.start(OccDisCaseLateTimeJob.java:73)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-13 13:41:42 [Schedule-Task-2] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task.
java.lang.NullPointerException: null
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.lambda$getCrptWarnRecords$4(OccDisCaseLateTimeJob.java:318)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.getCrptWarnRecords(OccDisCaseLateTimeJob.java:315)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.getWarnMap(OccDisCaseLateTimeJob.java:112)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.start(OccDisCaseLateTimeJob.java:73)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-13 13:48:23 [Schedule-Task-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task.
java.lang.NullPointerException: null
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.lambda$getCrptWarnRecords$4(OccDisCaseLateTimeJob.java:318)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.getCrptWarnRecords(OccDisCaseLateTimeJob.java:315)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.getWarnMap(OccDisCaseLateTimeJob.java:112)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.start(OccDisCaseLateTimeJob.java:73)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-13 13:51:18 [Schedule-Task-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task.
java.lang.NullPointerException: null
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.lambda$getCrptWarnRecords$4(OccDisCaseLateTimeJob.java:318)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.getCrptWarnRecords(OccDisCaseLateTimeJob.java:315)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.getWarnMap(OccDisCaseLateTimeJob.java:112)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.start(OccDisCaseLateTimeJob.java:73)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-13 13:57:58 [Schedule-Task-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task.
java.lang.NullPointerException: null
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.lambda$getCrptWarnRecords$4(OccDisCaseLateTimeJob.java:318)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.getCrptWarnRecords(OccDisCaseLateTimeJob.java:315)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.getWarnMap(OccDisCaseLateTimeJob.java:112)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseLateTimeJob.start(OccDisCaseLateTimeJob.java:73)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-13 14:07:16 [Schedule-Task-1] ERROR c.c.m.timer.heth.job.zw.warn.NotInTimeWarnBase - 今年节假日未配置,当前日期：二〇二五年二月十三日
2025-02-13 14:07:19 [Schedule-Task-1] ERROR c.c.m.timer.heth.job.zw.warn.NotInTimeWarnBase - 今年节假日未配置,当前日期：二〇二五年二月十三日
2025-02-13 14:07:29 [Schedule-Task-2] ERROR c.c.m.timer.heth.job.zw.warn.NotInTimeWarnBase - 今年节假日未配置,当前日期：二〇二五年二月十三日
2025-02-13 14:12:23 [Schedule-Task-1] ERROR c.c.m.timer.heth.job.zw.warn.NotInTimeWarnBase - 今年节假日未配置,当前日期：二〇二五年二月十三日
2025-02-13 14:12:31 [Schedule-Task-3] ERROR c.c.m.timer.heth.job.zw.warn.NotInTimeWarnBase - 今年节假日未配置,当前日期：二〇二五年二月十三日
2025-02-13 14:14:23 [Schedule-Task-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task.
org.springframework.transaction.CannotCreateTransactionException: Could not open JDBC Connection for transaction; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Thu Feb 13 14:14:23 CST 2025
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:305)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:378)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:475)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:289)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.chis.modules.timer.heth.service.DisCaseJobService$$EnhancerBySpringCGLIB$$a3de2aab.findDisCaseNotInTimeDataList(<generated>)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseNotInTimeJob.queryDataList(OccDisCaseNotInTimeJob.java:45)
	at com.chis.modules.timer.heth.job.zw.warn.NotInTimeWarnBase.executeData(NotInTimeWarnBase.java:136)
	at com.chis.modules.timer.heth.job.zw.warn.OccDisCaseNotInTimeJob.start(OccDisCaseNotInTimeJob.java:27)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Thu Feb 13 14:14:23 CST 2025
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1356)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1253)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4619)
	at com.alibaba.druid.filter.stat.StatFilter.dataSource_getConnection(StatFilter.java:680)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4615)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1231)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1223)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:90)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:262)
	... 25 common frames omitted

2025-07-04 11:03:41 [pool-3-thread-2] ERROR c.c.modules.timer.heth.job.ChestAndHearingRstJob - 计算电测听结论-失败: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."RID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdTjBhksubMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: WITH ID_TABLE AS (         SELECT RID         FROM (         SELECT ZWX.RID, ROWNUM AS RN         FROM (         SELECT B.RID         FROM TD_TJ_BHKSUB BS         INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID         WHERE B.HEARING_RST_ID IS NULL and T.rid in(444566,338145)                           AND BS.ITEM_RST IS NOT NULL                       AND NVL(BS.IF_LACK, 0) = 0         AND BS.ITEM_ID IN          (               ?          ,              ?          )                                            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')                                 AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')                   GROUP BY B.RID         ) ZWX         )         WHERE RN BETWEEN 0 AND ?         )         SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst,BS.RGLTAG AS rgltag         FROM TD_TJ_BHKSUB BS         INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID         INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID         WHERE I.RID IN          (               ?          ,              ?          )                            AND BS.ITEM_RST IS NOT NULL
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."RID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."RID": 标识符无效

org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."RID": 标识符无效

### The error may exist in file [F:\IdeaProjects\spring-timer-heth-badrsn-rst\spring-timer-heth-badrsn-rst\target\classes\mapper\oracle\TdTjBhksubMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: WITH ID_TABLE AS (         SELECT RID         FROM (         SELECT ZWX.RID, ROWNUM AS RN         FROM (         SELECT B.RID         FROM TD_TJ_BHKSUB BS         INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID         WHERE B.HEARING_RST_ID IS NULL and T.rid in(444566,338145)                           AND BS.ITEM_RST IS NOT NULL                       AND NVL(BS.IF_LACK, 0) = 0         AND BS.ITEM_ID IN          (               ?          ,              ?          )                                            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')                                 AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')                   GROUP BY B.RID         ) ZWX         )         WHERE RN BETWEEN 0 AND ?         )         SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst,BS.RGLTAG AS rgltag         FROM TD_TJ_BHKSUB BS         INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID         INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID         WHERE I.RID IN          (               ?          ,              ?          )                            AND BS.ITEM_RST IS NOT NULL
### Cause: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."RID": 标识符无效

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-00904: "T"."RID": 标识符无效

	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:234)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:73)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy71.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230)
	at com.baomidou.mybatisplus.core.override.PageMapperMethod.executeForMany(PageMapperMethod.java:173)
	at com.baomidou.mybatisplus.core.override.PageMapperMethod.execute(PageMapperMethod.java:86)
	at com.baomidou.mybatisplus.core.override.PageMapperProxy.invoke(PageMapperProxy.java:64)
	at com.sun.proxy.$Proxy107.selectHearingRstList(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy108.selectHearingRstList(Unknown Source)
	at com.chis.modules.timer.heth.service.TdTjBhksubService.selectHearingRstList(TdTjBhksubService.java:125)
	at com.chis.modules.timer.heth.service.TdTjBhksubService$$FastClassBySpringCGLIB$$fb4e4ae0.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:295)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.chis.modules.timer.heth.service.TdTjBhksubService$$EnhancerBySpringCGLIB$$3bdc77f6.selectHearingRstList(<generated>)
	at com.chis.modules.timer.heth.service.ChestAndHearingRstService.dealHearingRst(ChestAndHearingRstService.java:120)
	at com.chis.modules.timer.heth.job.ChestAndHearingRstJob.dealHearingRst(ChestAndHearingRstJob.java:292)
	at com.chis.modules.timer.heth.job.ChestAndHearingRstJob.lambda$start$0(ChestAndHearingRstJob.java:80)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1626)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: ORA-00904: "T"."RID": 标识符无效

	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:447)
	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:396)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:951)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:513)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:227)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:531)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:208)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:886)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1175)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1296)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3613)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3714)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1378)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:63)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy336.query(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.PerformanceInterceptor.intercept(PerformanceInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy336.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:326)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy335.query(Unknown Source)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:143)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy335.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 35 common frames omitted
2025-07-04 11:24:35 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1201477 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 11:28:05 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1532 millis. UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN
         (  
            ?
         , 
            ?
         )[9116636,444566,338145]
2025-07-04 11:42:21 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1333 millis. UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN
         (  
            ?
         )[9116637,444566]
2025-07-04 14:00:37 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1737438 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:16:34 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 953876 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:29:38 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 782964 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:33:36 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 236972 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:34:31 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 54779 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:35:07 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 34116 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:35:08 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1461 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[2,35459280,35459348,35459356,35459360,35459381,35459386,35459419,35459727,35459856,35459875,35459886,35459898,35459994,35459998,35459999,35460009,35460039,35460140,35460155,35460187,35460359,35460372,35460395,35460461,35460471,35460486,35460492,35460540,35460546,35460555,35460556,35460557,35460558,35460571,35460585,35460588,35460594,35460599,35460600,35460602,35460607,35460612,35460880,35460977,35460987,35460998,35461046,35461053,35461065,35461068,35461083,35461114,35461138,35461140,35461170,35461190,35461191,35461194,35461322,35461330,35461331,35461338,35461352,35461358,35461362,35461380,35461381,35461383,35461387,35461390,35461391,35461399,35461416,35461427,35461428,35461430,35461431,35461436,35461440,35461445,35461451]
2025-07-04 14:35:42 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33804 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:36:17 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33602 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:36:52 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33716 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:37:28 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33662 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:38:01 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33168 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:38:03 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1396 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35468272,35468274,35468281,35468282,35468283,35468284,35468285,35468286,35468287,35468288,35468289,35468290,35468292,35468293,35468295,35468296,35468297,35468298,35468299,35468300,35468302,35468303,35468305,35468306,35468307,35468311,35468312,35468314,35468315,35468316,35468319,35468320,35468321,35468322,35468325,35468326,35468327,35468330,35468331,35468332,35468334,35468335,35468336,35468337,35468339,35468340,35468341,35468342,35468343,35468345,35468346,35468347,35468348,35468349,35468350,35468352,35468354,35468365,35468366,35468367,35468368,35468369,35468370,35468371,35468373,35468374,35468375,35468376,35468377,35468378,35468379,35468381,35468382,35468384,35468385,35468386,35468391,35468393,35468396,35468398,35468400,35468402,35468403,35468405,35468406,35468407,35468408,35468409,35468410,35468413,35468414,35468416,35468417,35468427,35468428,35468429,35468430,35468431,35468432,35468433,35468434,35468435,35468436,35468437,35468438,35468439,35468440,35468441,35468442,35468443,35468444,35468445,35468446,35468448,35468449,35468451,35468453,35468455,35468456,35468457,35468458,35468459,35468460,35468461,35468462,35468468,35468469,35468470,35468471,35468472,35468473,35468474,35468475,35468476,35468477,35468478,35468479,35468480,35468481,35468482,35468483,35468485,35468486,35468487,35468489,35468490,35468491,35468494,35468495,35468496,35468497,35468498,35468499,35468510,35468511,35468512,35468513,35468514,35468515,35468516,35468517,35468519,35468521]
2025-07-04 14:38:37 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33137 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:39:11 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33433 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:39:45 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33559 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:40:18 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33189 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:40:20 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1180 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35475847,35475848,35475849,35475850,35475851,35475852,35475853,35475854,35475855,35475856,35475857,35475858,35475859,35475860,35475861,35475862,35475863,35475864,35475866,35475867,35475868,35475869,35475870,35475871,35475872,35475873,35475874,35475875,35475876,35475877,35475878,35475879,35475880,35475881,35475882,35475888,35475898,35475901,35475902,35475903,35475904,35475905,35475906,35475907,35475908,35475909,35475910,35475911,35475912,35475913,35475914,35475915,35475916,35475917,35475918,35475919,35475920,35475922,35475923,35475924,35475925,35475926,35475927,35475928,35475929,35475930,35475931,35475932,35475933,35475934,35475935,35475936,35475937,35475938,35475939,35475940,35475941,35475942,35475944,35475945,35475946,35475947,35475948,35475949,35475950,35475951,35475952,35475953,35475954,35475955,35475956,35475957,35475959,35475962,35475963,35475964,35475966,35475967,35475968,35475969,35475971,35475972,35475973,35475974,35475975,35475976,35475977,35475978,35475979,35475980,35475981,35475982,35475983,35475984,35475985,35475986,35475987,35475988,35475989,35475990,35475991,35475992,35475993,35475994,35475995,35475996,35475997,35475998,35475999,35476000,35476001,35476002,35476003,35476004,35476005,35476006,35476007,35476008,35476009,35476010,35476011,35476012,35476013,35476014,35476015,35476016,35476017,35476018,35476019,35476020,35476021,35476022,35476023,35476024,35476025,35476026,35476027,35476028,35476030,35476031,35476032,35476033,35476034,35476035]
2025-07-04 14:40:54 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33622 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:41:28 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33030 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:42:02 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33140 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:42:03 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1123 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35479769,35479772,35479773,35479775,35479777,35479779,35479780,35479783,35479785,35479786,35479789,35479791,35479793,35479795,35479797,35479798,35479800,35479804,35479807,35479808,35479810,35479814,35479817,35479820,35479823,35479825,35479827,35479830,35479831,35479833,35479835,35479838,35479840,35479841,35479843,35479845,35479846,35479850,35479851,35479852,35479853,35479854,35479856,35479857,35479858,35479859,35479860,35479862,35479863,35479864,35479865,35479866,35479867,35479869,35479870,35479872,35479873,35479874,35479875,35479878,35479879,35479880,35479881,35479882,35479883,35479884,35479885,35479886,35479887,35479888,35479891,35479892,35479893,35479894,35479896,35479897,35479898,35479899,35479900,35479903,35479904,35479905,35479906,35479908,35479909,35479912,35479950,35479951,35479952,35479956,35479957,35479958,35479959,35479960,35479961,35479962,35479963,35479964,35479965,35479966,35479967,35479968,35479969,35479970,35479971,35479972,35479973,35479974,35479975,35479976,35479977,35479978,35479979,35479980,35479981,35479982,35479983,35479984,35479985,35479986,35479987,35479988,35479989,35479990,35479991,35479992,35479993,35479994,35479997,35479998,35479999,35480001,35480002,35480003,35480004,35480005,35480006,35480007,35480008,35480009,35480010,35480011,35480012,35480013,35480014,35480015,35480016,35480017,35480018,35480019,35480020,35480021,35480022,35480027,35480028,35480029,35480032,35480033,35480037,35480039,35480040,35480041,35480045,35480046,35480047,35480048,35480057,35480058,35480059,35480060,35480062,35480063,35480084,35480085,35480086,35480087,35480088,35480089,35480090,35480091,35480092,35480093,35480094,35480095,35480096,35480097,35480098,35480099,35480100,35480101,35480111,35480116,35480120,35480121,35480122,35480124,35480125,35480126,35480127]
2025-07-04 14:42:37 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33408 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:43:11 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33254 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:43:45 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33173 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:44:19 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33241 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:44:21 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1492 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[2,35484850,35484853,35484887,35484888,35484908,35484919,35484937,35484944,35484945,35484956,35484966,35484979,35484989,35484990,35484997,35485016,35485027,35485030,35485031,35485036,35485037,35485049,35485050,35485068,35485082,35485086,35485088,35485094,35485095,35485096,35485100,35485104,35485107,35485108,35485111,35485118,35485123,35485126,35485129,35485139,35485150,35485151,35485156,35485163,35485166,35485182,35485188,35485196,35485198,35485209,35485218,35485219,35485222,35485227,35485233,35485235,35485239,35485241,35485244,35485250,35485252,35485254,35485256,35485258,35485259,35485274,35485275,35485277,35485279,35485280,35485282,35485285,35485286,35485288,35485289,35485294,35485296,35485306,35485307,35485312,35485317,35485320,35485322,35485325,35485330,35485342,35485353,35485398,35485402,35485404,35485406,35485410,35485424,35485428,35485439,35485441,35485442,35485449,35485455,35485482,35485491,35485495,35485502,35485508,35485509,35485517,35485533,35485538,35485539,35485541,35485544,35485546,35485557,35485561,35485571,35485577,35485580,35485585,35485588,35485613,35485619,35485626,35485630,35485631,35485635,35485640,35485641,35485650,35485655,35485664,35485665,35485669,35485671,35485673,35485674,35485687,35485693,35485703,35485707,35485709,35485718,35485737,35485747,35485752,35485772,35485780,35485789,35485791,35485794,35485799,35485806,35485808,35485815,35485818,35485820,35485823,35485828,35485861,35485871,35485883,35485893,35485897,35485901,35485906,35485910,35485912,35485981,35486011,35486029,35486030,35486035,35486037,35486046,35486052,35486087,35486160]
2025-07-04 14:44:54 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33670 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:45:29 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33404 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:46:03 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33080 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:46:04 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1048 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[2,35490125,35490144,35490310,35490319,35490414,35490458,35490461,35490565,35490626,35490650,35490689,35490714,35490728,35490743,35490781,35490823,35490845,35490849,35490853,35490868,35490873,35490875,35490901,35490926,35490927,35490933,35490934,35490935,35490937,35490938,35490939,35490945,35490952,35490955,35490956,35490961,35490964,35490970,35490971,35490974,35490977,35490981,35490982,35490984,35490985,35490987,35490989,35490991,35490992,35490994,35490995,35490996,35490999,35491001,35491005,35491007,35491008,35491011,35491012,35491021,35491027,35491028,35491031,35491036,35491096,35491115,35491118,35491186,35491187,35491213,35491237,35491240,35491242,35491244,35491245,35491246,35491248,35491249,35491250,35491251,35491252,35491257,35491259,35491260,35491262,35491263,35491266,35491267,35491268,35491269,35491270,35491271,35491272,35491273,35491274,35491276,35491278,35491279,35491281,35491285,35491287,35491288,35491289,35491290,35491291,35491292,35491293,35491294,35491295,35491297,35491298,35491299,35491301,35491304,35491307,35491309,35491310,35491311,35491312,35491313,35491315,35491318,35491322,35491323,35491326,35491327,35491328,35491335,35491337,35491341,35491354,35491357,35491358,35491360,35491362,35491363,35491365,35491367,35491368,35491369,35491406,35491407,35491411,35491412,35491413,35491416,35491418,35491421,35491439,35491442,35491444,35491445,35491446,35491448,35491456,35491457,35491465,35491466,35491467,35491469,35491471,35491472,35491491,35491495,35491506,35491508,35491510,35491525,35491534,35491545,35491571,35491604,35491645,35491657,35491658,35491660,35491661,35491784,35491794]
2025-07-04 14:46:37 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32841 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:47:10 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32539 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:47:15 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 3293 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[2,35492993,35493020,35493022,35493061,35493063,35493064,35493089,35493137,35493178,35493236,35493253,35493315,35493528,35493530,35493675,35493696,35493704,35493852,35493865,35493866,35493868,35493875,35493876,35493877,35493887,35493888,35493891,35493897,35493900,35493903,35493917,35493933,35493944,35493972,35494014,35494015,35494173,35494175,35494194,35494245,35494246,35494249,35494274,35494309,35494322,35494373,35494396,35494401,35494406,35494530,35494535,35494537,35494539,35494549,35494550,35494552,35494553,35494555,35494559,35494563,35494568,35494569,35494573,35494574,35494575,35494576,35494578,35494579,35494581,35494583,35494585,35494598,35494600,35494606,35494607,35494608,35494612,35494613,35494615,35494617,35494635,35494636,35494639,35494652,35494653,35494655,35494663,35494665,35494666,35494669,35494678,35494679,35494680,35494681,35494688,35494692,35494694,35494722,35494723]
2025-07-04 14:47:47 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32847 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:47:49 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1130 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35496032,35496036,35496037,35496038,35496039,35496065,35496068,35496073,35496074,35496076,35496078,35496081,35496082,35496086,35496090,35496091,35496092,35496093,35496094,35496096,35496105,35496106,35496107,35496108,35496109,35496110,35496111,35496112,35496113,35496114,35496116,35496118,35496119,35496122,35496123,35496124,35496125,35496126,35496128,35496129,35496130,35496132,35496133,35496134,35496135,35496137,35496138,35496139,35496140,35496141,35496142,35496143,35496144,35496145,35496146,35496147,35496148,35496149,35496150,35496152,35496153,35496154,35496155,35496156,35496157,35496158,35496159,35496161,35496163,35496164,35496165,35496166,35496167,35496168,35496169,35496170,35496171,35496172,35496174,35496175,35496176,35496177,35496178,35496179,35496180,35496181,35496183,35496184,35496185,35496186,35496187,35496188,35496189,35496191,35496193,35496194,35496219,35496220,35496242,35496265]
2025-07-04 14:48:22 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32833 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:48:56 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32830 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:49:30 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32817 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:49:31 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1338 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35502309,35502310,35502311,35502312,35502313,35502314,35502315,35502316,35502317,35502318,35502319,35502320,35502321,35502322,35502323,35502324,35502325,35502327,35502328,35502329,35502330,35502331,35502332,35502333,35502334,35502335,35502336,35502337,35502338,35502339,35502340,35502342,35502343,35502344,35502345,35502346,35502347,35502348,35502349,35502350,35502351,35502352,35502353,35502354,35502355,35502356,35502357,35502358,35502359,35502360,35502361,35502362,35502363,35502364,35502366,35502368,35502369,35502370,35502371,35502372,35502373,35502374,35502376,35502377,35502379,35502380,35502381,35502382,35502383,35502384,35502385,35502386,35502387,35502388,35502389,35502390,35502391,35502392,35502393,35502394,35502395,35502396,35502397,35502398,35502399,35502400,35502401,35502402,35502404,35502405,35502406,35502407,35502408,35502409,35502410,35502416,35502417,35502418,35502419,35502420,35502421,35502422,35502423,35502424,35502426,35502427,35502428,35502429,35502430,35502431,35502432,35502433,35502434,35502436,35502437,35502438,35502439,35502440,35502441,35502442,35502443,35502444,35502446,35502447,35502448,35502449,35502450,35502451,35502452,35502454,35502455,35502456,35502457,35502458,35502459,35502460,35502461,35502462,35502463,35502464,35502465,35502466,35502467,35502468,35502469,35502470,35502471,35502472,35502473,35502474,35502475,35502476,35502477,35502478,35502479,35502481,35502482,35502483,35502484,35502485,35502486,35502487,35502488,35502489,35502496]
2025-07-04 14:49:35 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 3127 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[2,35500559,35500560,35500563,35500596,35500598,35500599,35500600,35500601,35500602,35500603,35500604,35500606,35500700,35500716,35500915,35500917,35500935,35500985,35501070,35501079,35501107,35501135,35501137,35501143,35501209,35501211,35501222,35501223,35501243,35501360,35501371,35501468,35501609,35501653,35501711,35501713,35501715,35501716,35501722,35501733,35501738,35501750,35501752,35501769,35501805,35501811,35501821,35501846,35501854,35501855,35501863,35501867,35501895,35501901,35501903,35501905,35501908,35501919,35501925,35501932,35501934,35501936,35501939,35501969,35501970,35501972,35501974,35501988,35502000,35502002,35502003,35502006,35502022,35502034,35502038,35502304,35502326,35502341,35502365,35502375,35502378,35502403,35502425,35502453,35502480]
2025-07-04 14:50:07 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32815 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:50:09 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1004 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35504017,35504018,35504019,35504020,35504021,35504022,35504023,35504024,35504025,35504026,35504028,35504029,35504031,35504032,35504033,35504034,35504035,35504036,35504037,35504038,35504039,35504040,35504041,35504042,35504043,35504044,35504045,35504046,35504047,35504048,35504049,35504050,35504051,35504052,35504054,35504055,35504056,35504057,35504058,35504059,35504061,35504063,35504064,35504065,35504066,35504067,35504068,35504069,35504070,35504072,35504073,35504074,35504075,35504076,35504077,35504078,35504080,35504082,35504084,35504085,35504086,35504087,35504088,35504089,35504090,35504091,35504092,35504093,35504094,35504095,35504096,35504097,35504098,35504099,35504100,35504101,35504102,35504103,35504104,35504105,35504106,35504107,35504108,35504109,35504110,35504111,35504112,35504113,35504177,35504229,35504249,35504250,35504251,35504254,35504255,35504256,35504257,35504258,35504259,35504260,35504261,35504262,35504263,35504264,35504265,35504267,35504268,35504269,35504270,35504271,35504272,35504273,35504274,35504275,35504276,35504277,35504278,35504279,35504280,35504281,35504283,35504284,35504288,35504290,35504291,35504293,35504294,35504295,35504297,35504298,35504299,35504300,35504303,35504307,35504308,35504310]
2025-07-04 14:50:43 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33257 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:51:24 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 40771 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:53:30 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 126559 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:55:10 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 99079 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:56:26 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 76349 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:57:00 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33253 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:57:33 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32434 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:57:34 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1155 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35518140,35518141,35518142,35518143,35518144,35518146,35518147,35518148,35518149,35518150,35518151,35518152,35518153,35518154,35518155,35518156,35518157,35518158,35518159,35518161,35518162,35518163,35518164,35518165,35518169,35518170,35518173,35518174,35518175,35518176,35518177,35518178,35518179,35518180,35518181,35518183,35518184,35518185,35518187,35518188,35518191,35518192,35518193,35518194,35518195,35518196,35518197,35518199,35518201,35518202,35518203,35518204,35518206,35518207,35518208,35518209,35518210,35518211,35518212,35518213,35518214,35518215,35518216,35518217,35518218,35518219,35518220,35518221,35518222,35518223,35518225,35518226,35518227,35518228,35518229,35518235,35518236,35518237,35518238,35518239,35518240,35518246,35518247,35518252,35518253,35518255,35518264,35518266,35518268,35518270,35518271,35518272,35518273,35518276,35518277,35518279,35518280,35518282,35518284,35518287,35518290,35518295,35518296,35518297]
2025-07-04 14:58:07 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32665 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:58:41 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32580 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:59:14 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32703 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 14:59:47 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32812 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:00:21 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32320 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:00:54 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32211 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:00:56 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 2125 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35535578,35535579,35535580,35535581,35535582,35535583,35535584,35535585,35535586,35535587,35535592,35535594,35535596,35535597,35535598,35535602,35535608,35535613,35535614,35535615,35535617,35535620,35535621,35535623,35535624,35535625,35535626,35535627,35535628,35535629,35535630,35535632,35535633,35535634,35535635,35535636,35535637,35535638,35535639,35535640,35535641,35535642,35535643,35535644,35535645,35535646,35535647,35535648,35535649,35535651,35535654,35535655,35535656,35535657,35535658,35535661,35535666,35535671,35535672,35535673,35535674,35535675,35535676,35535677,35535678,35535686,35535687,35535688,35535689,35535690,35535691,35535692,35535693,35535694,35535695,35535696,35535697,35535698,35535699,35535700,35535701,35535702,35535703,35535704,35535705,35535706,35535708,35535709,35535712,35535713,35535714,35535715,35535716,35535717,35535718,35535719,35535720,35535721,35535722,35535724,35535725,35535726,35535728,35535729,35535730,35535731,35535732,35535733,35535734,35535735,35535736,35535737,35535738,35535739,35535740,35535741,35535742,35535743,35535744,35535745,35535746,35535747,35535748,35535749,35535750,35535751,35535752,35535753,35535754,35535755,35535756,35535757,35535758,35535759,35535760,35535761,35535762,35535763,35535764,35535765,35535766,35535767,35535768,35535769,35535770,35535771,35535772,35535773,35535774,35535775,35535776,35535777,35535778,35535779,35535780,35535781,35535782,35535783,35535784,35535785,35535786,35535787,35535788,35535789,35535790,35535791,35535792,35535793,35535794,35535795,35535796,35535797,35535798,35535799,35535800,35535801,35535802]
2025-07-04 15:01:29 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32283 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:02:01 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32243 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:02:34 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32388 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:03:07 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32542 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:03:40 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32132 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:04:13 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32322 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:04:14 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1088 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[2,35542106,35542110,35542116,35542119,35542128,35542130,35542132,35542133,35542141,35542142,35542155,35542170,35542174,35542175,35542181,35542186,35542189,35542194,35542197,35542198,35542219,35542224,35542248,35542263,35542264,35542300,35542312,35542323,35542333,35542341,35542343,35542354,35542356,35542366,35542371,35542378,35542396,35542407,35542408,35542410,35542424,35542428,35542433,35542435,35542438,35542444,35542446,35542447,35542466,35542467,35542469,35542474,35542475,35542487,35542494,35542496,35542512,35542516,35542519,35542522,35542524,35542532,35542533,35542572,35542583,35542592,35542605,35542607,35542609,35542621,35542627,35542628,35542645,35542668,35542680,35542682,35542686,35542691,35542697,35542701,35542714,35542716,35542724,35542740,35542745,35542794,35542823,35542830,35542831,35542861,35542870,35542877,35542881,35542886,35542892,35542902,35542904,35542925,35542926,35542928,35542944,35542945,35542947,35542951,35542964,35542967,35542971,35542977,35542980,35542984,35542987,35542988,35542994,35542995,35542998,35543003,35543007,35543020,35543023,35543027,35543030,35543033,35543046,35543050,35543055,35543056,35543060,35543063,35543064,35543065,35543070,35543071,35543074,35543075,35543078,35543090,35543100,35543102,35543103,35543113,35543117]
2025-07-04 15:04:47 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32447 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:05:19 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32258 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:05:53 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32264 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:05:59 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 5727 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[2,35546183,35546186,35546221,35546226,35546235,35546247,35546281,35546287,35546390,35546513,35546519,35546525,35546534,35546540,35546552,35546557,35546558,35546560,35546569,35546577,35546583,35546605,35546702,35546734,35546768,35546905,35547039,35547075,35547133,35547156,35547378,35547391,35547641,35547643,35547654,35547661,35547668,35547699,35547700,35547849,35547850,35547851,35547896,35547930,35547932,35547933,35547935,35547950,35547977,35547978,35547980,35547981,35547986,35547987,35547989,35547991,35547992,35548004,35548007,35548011,35548015,35548023,35548048,35548050,35548051,35548052,35548053,35548054,35548055,35548056,35548057,35548063,35548159,35548164,35548231,35548359,35548441,35548447,35548449,35548451,35548453,35548460,35548548,35548989]
2025-07-04 15:06:31 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32196 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:07:03 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 31999 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:07:35 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 31950 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:08:08 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32183 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:08:40 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32563 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:09:12 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 31910 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:09:45 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32110 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:10:18 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33240 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:10:52 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 33350 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:17:33 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 400824 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:18:40 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 66980 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:19:15 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 34418 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:19:45 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30416 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:19:49 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 3169 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35567572,35567573,35567574,35567575,35567576,35567577,35567578,35567579,35567580,35567581,35567582,35567583,35567584,35567585,35567586,35567587,35567588,35567589,35567590,35567591,35567592,35567593,35567594,35567595,35567596,35567597,35567598,35567599,35567600,35567601,35567602,35567603,35567604,35567605,35567606,35567607,35567608,35567609,35567610,35567611,35567612,35567613,35567614,35567615,35567616,35567617,35567618,35567619,35567620,35567621,35567622,35567623,35567624,35567625,35567626,35567627,35567628,35567629,35567630,35567631,35567632,35567633,35567634,35567635,35567636,35567637,35567638,35567639,35567640,35567641,35567642,35567643,35567644,35567645,35567646,35567647,35567648,35567649,35567650,35567651,35567652,35567653,35567654,35567655,35567656,35567657,35567658,35567659,35567660,35567661,35567662,35567663,35567664,35567665,35567666,35567667,35567668,35567669,35567670,35567671,35567672,35567673,35567674,35567675,35567676,35567677,35567678,35567679,35567680,35567681,35567682,35567683,35567684,35567685,35567686,35567687,35567688,35567689,35567690,35567691,35567692,35567693,35567694,35567695,35567696,35567697,35567698,35567699,35567700,35567701,35567702,35567703,35567704,35567705,35567706,35567707,35567708,35567709,35567710,35567711,35567712,35567713,35567714,35567715,35567716,35567717,35567718,35567719,35567720,35567721,35567722,35567723,35567724,35567725,35567726,35567727,35567728,35567729,35567730,35567731,35567732,35567733,35567734,35567735,35567736,35567737,35567738,35567739,35567740,35567741,35567742,35567743,35567744,35567745,35567746,35567747,35567748,35567749,35567750,35567751,35567752,35567753,35567754,35567755,35567756,35567757,35567758,35567759,35567760,35567761,35567762,35567763,35567764,35567765,35567766,35567767,35567768,35567769,35567770,35567771,35567772,35567773,35567774,35567775,35567776,35567777,35567778,35567779,35567780,35567781,35567782,35567783,35567784,35567785,35567786,35567787,35567788,35567789,35567790,35567791,35567792,35567793,35567794,35567795,35567796,35567797,35567798]
2025-07-04 15:20:20 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30622 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:20:51 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30419 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:20:52 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1322 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35569997,35569998,35569999,35570000,35570001,35570002,35570003,35570004,35570005,35570006,35570010,35570022,35570136,35570150,35570310,35570314,35570317,35570323,35570324,35570327,35570329,35570566,35570567,35570568,35570572,35570575,35570578,35570580,35570582,35570585,35570588,35570594,35570766,35570767,35570769,35570770,35570775,35571529,35571530,35571531,35571533,35571535,35571536,35571545,35571548,35571550,35571552,35571553,35571554,35571556,35571557,35571558,35571559,35571560,35571561,35571563,35571564,35571566,35571567,35571568,35571571,35571572,35571573,35571574,35571575,35571576,35571577,35571578,35571579,35571580,35571581,35571582,35571583,35571584,35571585,35571586,35571587,35571588,35571589,35571590,35571591,35571592,35571593,35571594,35571595,35571596,35571597,35571598,35571599,35571600,35571601,35571602,35571603,35571604,35571605,35571606,35571607,35571608,35571609,35571610,35571611,35571612,35571613,35571614,35571615,35571616,35571617,35571618,35571619,35571620,35571621,35571622,35571623,35571624,35571625,35571626,35571627,35571628,35571629,35571630,35571631,35571632,35571633,35571634,35571635,35571637,35571639,35571640,35571641,35571642,35571643,35571644,35571645,35571646,35571647,35571648,35571649,35571650,35571651,35571652,35571654,35571655,35571656,35571657,35571658,35571659,35571660,35571661,35571662,35571664,35571665,35571666,35571667,35571673,35571674,35571675,35571679,35571681,35571682,35571683,35571685,35571686,35571687,35571689,35571690,35571692,35571693,35571695,35571696,35571698,35571700,35571702,35571703,35571704,35571705,35571706,35571707,35571708,35571709,35571710,35571711,35571713,35571714,35571715,35571716,35571717,35571718,35571719,35571720,35571721,35571722,35571723,35571724,35571725,35571726,35571727,35571728,35571729,35571730,35571731,35571733,35571734,35571735,35571736,35571737,35571738,35571739,35571740,35571741,35571742,35571743,35571744,35571751,35571753,35571754,35571755,35571756]
2025-07-04 15:21:23 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30605 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:21:53 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30096 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:22:25 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30427 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:22:55 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30292 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:23:26 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30281 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:23:27 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1046 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35580381,35580383,35580385,35580387,35580389,35580390,35580392,35580396,35580400,35580401,35580402,35580404,35580406,35580408,35580409,35580411,35580412,35580415,35580416,35580418,35580419,35580421,35580423,35580425,35580427,35580429,35580431,35580432,35580435,35580436,35580438,35580440,35580441,35580445,35580446,35580447,35580449,35580451,35580453,35580455,35580456,35580457,35580460,35580462,35580464,35580465,35580467,35580469,35580470,35580472,35580474,35580476,35580478,35580479,35580481,35580483,35580484,35580486,35580487,35580489,35580491,35580492,35580495,35580496,35580498,35580500,35580501,35580503,35580553,35580554,35580557,35580558,35580566,35580567,35580569,35580572,35580574,35580575,35580577,35580578,35580579,35580581,35580584,35580585,35580587,35580588,35580590,35580591,35580592,35580593,35580594,35580595,35580596,35580597,35580598,35580599,35580600,35580601,35580602,35580603,35580604,35580605,35580606,35580608,35580610,35580611,35580612,35580715,35580716,35580717,35580718,35580719,35580720,35580723,35580725,35580726,35580727,35580728,35580729,35580731,35580732,35580733,35580734,35580773,35580775,35580779,35580782,35580784,35580785,35580787,35580788,35580789,35580790,35580792,35580793,35580800,35580801,35580802,35580803,35580805,35580808,35580810,35580813,35580816,35580823,35580827,35580833,35580836,35580837,35580838,35580841,35580842,35580843,35580847,35580851,35580856,35580865,35580867,35580878,35580899,35580900,35580901,35580929,35580933,35580935,35580936,35580937,35580943,35580945,35580946,35580948,35580949,35580950,35580951,35580952,35580953,35580954,35580955,35580956,35580957,35580959,35580960,35580961,35580962,35580963]
2025-07-04 15:23:57 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30193 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:24:29 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30059 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:24:59 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30092 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:25:31 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30080 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:26:01 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29848 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:26:31 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29902 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:26:33 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1254 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35590015,35590021,35590031,35590032,35590035,35590039,35590054,35590057,35590060,35590064,35590068,35590086,35590091,35590092,35590093,35590094,35590095,35590096,35590097,35590098,35590100,35590101,35590102,35590107,35590108,35590110,35590111,35590113,35590114,35590115,35590116,35590117,35590118,35590119,35590120,35590121,35590122,35590124,35590130,35590131,35590132,35590133,35590136,35590137,35590139,35590140,35590141,35590142,35590143,35590144,35590145,35590147,35590148,35590149,35590150,35590151,35590152,35590153,35590154,35590156,35590157,35590158,35590159,35590160,35590161,35590162,35590163,35590164,35590165,35590166,35590241,35590243,35590245,35590263,35590291,35590292,35590295,35590301,35590308,35590309,35590310,35590311,35590313,35590314,35590317,35590318,35590319,35590321,35590322,35590327,35590338,35590340,35590341,35590342,35590343,35590344,35590346,35590347,35590348,35590349,35590350,35590351,35590352,35590354,35590355,35590356,35590357,35590358,35590359,35590361,35590363,35590366,35590367,35590368,35590369,35590370,35590371,35590374,35590375,35590377,35590378,35590379,35590380,35590381,35590382,35590383,35590384,35590385,35590386,35590387,35590388,35590390,35590391,35590395,35590396,35590397,35590398,35590399,35590400,35590402,35590403,35590404,35590409,35590411,35590412,35590413,35590414,35590415,35590417,35590418,35590419,35590420,35590424,35590425,35590427,35590428,35590429,35590431,35590432,35590433,35590435,35590436,35590437,35590448,35590449,35590450,35590451,35590452,35590453,35590455,35590458,35590459,35590461,35590463,35590465,35590466,35590468,35590470,35590471,35590473,35590475,35590477,35590479,35590481,35590482,35590484,35590485,35590488,35590489,35590491,35590492,35590494,35590495,35590498]
2025-07-04 15:27:03 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30074 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:27:33 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30118 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:28:03 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29852 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:28:33 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29897 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:29:03 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29856 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:29:34 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29781 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:30:05 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29828 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:30:35 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30002 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:31:05 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29952 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:31:35 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29968 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:31:36 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1222 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35605422,35605423,35605424,35605425,35605426,35605427,35605428,35605429,35605430,35605431,35605432,35605433,35605434,35605435,35605436,35605437,35605438,35605439,35605440,35605441,35605442,35605443,35605444,35605445,35605446,35605447,35605448,35605449,35605450,35605451,35605452,35605453,35605454,35605455,35605457,35605458,35605459,35605460,35605461,35605462,35605463,35605464,35605465,35605468,35605469,35605470,35605472,35605473,35605475,35605477,35605478,35605479,35605480,35605481,35605482,35605483,35605484,35605485,35605486,35605487,35605488,35605489,35605490,35605491,35605492,35605493,35605494,35605495,35605496,35605497,35605498,35605499,35605500,35605501,35605502,35605503,35605504,35605505,35605506,35605508,35605512,35605513,35605514,35605516,35605517,35605518,35605519,35605520,35605521,35605522,35605524,35605525,35605526,35605530,35605531,35605532,35605533,35605534,35605535,35605536,35605537,35605538,35605540,35605541,35605542,35605543,35605544,35605545,35605546,35605547,35605548,35605551,35605552,35605553,35605554,35605555,35605556,35605557,35605558,35605559,35605560,35605561,35605562,35605563,35605564,35605565,35605566,35605567,35605568,35605569,35605571,35605572,35605574,35605575,35605576,35605577,35605578,35605579,35605581,35605582,35605583,35605584,35605585,35605586,35605587,35605588,35605589,35605590,35605591,35605592,35605593,35605594,35605595,35605597,35605598,35605599,35605600,35605601,35605604,35605605,35605607,35605609,35605613,35605614,35605615,35605616,35605617,35605618,35605624,35605625,35605626,35605627,35605628,35605634,35605635,35605636,35605639,35605641,35605644,35605645,35605646,35605647,35605648,35605649,35605650,35605651,35605652,35605653,35605654,35605655,35605656,35605657,35605658,35605659,35605660,35605661,35605662,35605663,35605664,35605665,35605666,35605667,35605668,35605669,35605670,35605671,35605672,35605673,35605674]
2025-07-04 15:32:06 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29660 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:32:08 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1423 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35607248,35607249,35607250,35607251,35607252,35607253,35607254,35607255,35607256,35607257,35607258,35607259,35607260,35607261,35607262,35607263,35607264,35607265,35607267,35607268,35607269,35607270,35607271,35607272,35607274,35607275,35607276,35607278,35607279,35607281,35607283,35607284,35607286,35607288,35607289,35607290,35607292,35607293,35607294,35607295,35607296,35607297,35607298,35607299,35607300,35607301,35607302,35607303,35607304,35607305,35607306,35607307,35607308,35607309,35607310,35607311,35607312,35607313,35607314,35607316,35607317,35607318,35607319,35607320,35607321,35607322,35607323,35607324,35607326,35607327,35607328,35607329,35607330,35607331,35607333,35607334,35607335,35607336,35607338,35607339,35607340,35607341,35607342,35607343,35607344,35607345,35607346,35607347,35607348,35607349,35607350,35607351,35607352,35607353,35607354,35607355,35607356,35607357,35607358,35607359,35607360,35607361,35607362,35607363,35607364,35607365,35607366,35607367,35607368,35607369,35607370,35607371,35607372,35607373,35607374,35607375,35607376,35607378,35607379,35607380,35607381,35607383,35607384,35607385,35607386,35607387,35607389,35607390,35607392,35607393,35607394,35607395,35607396,35607397,35607398,35607399,35607400,35607401,35607402,35607403,35607404,35607406,35607407,35607408,35607409,35607410,35607411,35607412,35607413,35607414,35607415,35607417,35607418,35607419,35607420,35607421,35607422,35607423,35607424,35607425,35607426,35607427,35607428,35607430,35607431,35607432,35607433,35607434,35607435,35607436,35607437,35607438,35607439,35607440,35607441]
2025-07-04 15:32:37 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29613 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:33:07 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29592 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:33:37 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29522 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:34:06 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29703 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:34:37 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30343 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:35:07 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29891 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:35:38 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29760 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]
2025-07-04 15:36:08 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 29640 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
         
         
         
            AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
         
         
            AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
         
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-04",1000]

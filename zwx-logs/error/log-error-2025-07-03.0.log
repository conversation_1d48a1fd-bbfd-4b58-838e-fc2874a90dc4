2025-07-03 14:45:51 [main] ERROR org.apache.catalina.util.LifecycleBase - Failed to start component [Connector["http-nio-9600"]]
org.apache.catalina.LifecycleException: Protocol handler start failed
	at org.apache.catalina.connector.Connector.startInternal(Connector.java:1058)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
	at org.apache.catalina.core.StandardService.addConnector(StandardService.java:219)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.addPreviouslyRemovedConnectors(TomcatWebServer.java:263)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:195)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.startWebServer(ServletWebServerApplicationContext.java:296)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:162)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:552)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:742)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:389)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1213)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1202)
	at com.SpringbootTimerApplication.main(SpringbootTimerApplication.java:12)
Caused by: java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method)
	at sun.nio.ch.Net.bind(Net.java:433)
	at sun.nio.ch.Net.bind(Net.java:425)
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223)
	at org.apache.tomcat.util.net.NioEndpoint.initServerSocket(NioEndpoint.java:268)
	at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:223)
	at org.apache.tomcat.util.net.AbstractEndpoint.bindWithCleanup(AbstractEndpoint.java:1373)
	at org.apache.tomcat.util.net.AbstractEndpoint.start(AbstractEndpoint.java:1459)
	at org.apache.coyote.AbstractProtocol.start(AbstractProtocol.java:674)
	at org.apache.catalina.connector.Connector.startInternal(Connector.java:1055)
	... 14 common frames omitted
2025-07-03 14:45:51 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The Tomcat connector configured to listen on port 9600 failed to start. The port may already be in use or the connector may be misconfigured.

Action:

Verify the connector's configuration, identify and stop any process that's listening on port 9600, or configure this application to listen on another port.

2025-07-03 15:25:25 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 518400 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:25:33 [pool-2-thread-2] ERROR c.c.m.timer.heth.service.ChestAndHearingRstService - 批量更新结论出错，
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

### The error may involve com.chis.modules.timer.heth.mapper.TdTjBhkMapper.updateHearingRstBatch-Inline
### The error occurred while setting parameters
### SQL: UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN          (               ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          )
### Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

### The error may involve com.chis.modules.timer.heth.mapper.TdTjBhkMapper.updateHearingRstBatch-Inline
### The error occurred while setting parameters
### SQL: UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN          (               ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          )
### Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:73)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy71.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:294)
	at com.baomidou.mybatisplus.core.override.PageMapperMethod.execute(PageMapperMethod.java:73)
	at com.baomidou.mybatisplus.core.override.PageMapperProxy.invoke(PageMapperProxy.java:64)
	at com.sun.proxy.$Proxy81.updateHearingRstBatch(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy82.updateHearingRstBatch(Unknown Source)
	at com.chis.modules.timer.heth.service.TdTjBhkService.updateRstBatch(TdTjBhkService.java:168)
	at com.chis.modules.timer.heth.service.TdTjBhkService$$FastClassBySpringCGLIB$$588b2fea.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:295)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.chis.modules.timer.heth.service.TdTjBhkService$$EnhancerBySpringCGLIB$$4ae25f28.updateRstBatch(<generated>)
	at com.chis.modules.timer.heth.service.ChestAndHearingRstService.lambda$updateRstBatch$2(ChestAndHearingRstService.java:226)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
	at java.util.concurrent.ConcurrentHashMap$EntrySpliterator.forEachRemaining(ConcurrentHashMap.java:3606)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at com.chis.modules.timer.heth.service.ChestAndHearingRstService.updateRstBatch(ChestAndHearingRstService.java:222)
	at com.chis.modules.timer.heth.service.ChestAndHearingRstService.dealHearingRst(ChestAndHearingRstService.java:144)
	at com.chis.modules.timer.heth.job.ChestAndHearingRstJob.dealHearingRst(ChestAndHearingRstJob.java:246)
	at com.chis.modules.timer.heth.job.ChestAndHearingRstJob.lambda$start$0(ChestAndHearingRstJob.java:80)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1626)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:447)
	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:396)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:951)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:513)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:227)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:531)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:208)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:1046)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1336)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3613)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3714)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1378)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:46)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy336.update(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.PerformanceInterceptor.intercept(PerformanceInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy336.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.OptimisticLockerInterceptor.intercept(OptimisticLockerInterceptor.java:141)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy335.update(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy335.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:198)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 45 common frames omitted
2025-07-03 15:25:33 [pool-2-thread-2] ERROR c.c.m.timer.heth.service.ChestAndHearingRstService - 批量更新结论出错，
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

### The error may involve com.chis.modules.timer.heth.mapper.TdTjBhkMapper.updateHearingRstBatch-Inline
### The error occurred while setting parameters
### SQL: UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN          (               ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          )
### Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

### The error may involve com.chis.modules.timer.heth.mapper.TdTjBhkMapper.updateHearingRstBatch-Inline
### The error occurred while setting parameters
### SQL: UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN          (               ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          )
### Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:73)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy71.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:294)
	at com.baomidou.mybatisplus.core.override.PageMapperMethod.execute(PageMapperMethod.java:73)
	at com.baomidou.mybatisplus.core.override.PageMapperProxy.invoke(PageMapperProxy.java:64)
	at com.sun.proxy.$Proxy81.updateHearingRstBatch(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy82.updateHearingRstBatch(Unknown Source)
	at com.chis.modules.timer.heth.service.TdTjBhkService.lambda$updateRstBatch$0(TdTjBhkService.java:179)
	at java.util.stream.Streams$RangeIntSpliterator.forEachRemaining(Streams.java:110)
	at java.util.stream.IntPipeline$Head.forEach(IntPipeline.java:557)
	at com.chis.modules.timer.heth.service.TdTjBhkService.updateRstBatch(TdTjBhkService.java:173)
	at com.chis.modules.timer.heth.service.TdTjBhkService$$FastClassBySpringCGLIB$$588b2fea.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:295)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.chis.modules.timer.heth.service.TdTjBhkService$$EnhancerBySpringCGLIB$$4ae25f28.updateRstBatch(<generated>)
	at com.chis.modules.timer.heth.service.ChestAndHearingRstService.lambda$updateRstBatch$2(ChestAndHearingRstService.java:226)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
	at java.util.concurrent.ConcurrentHashMap$EntrySpliterator.forEachRemaining(ConcurrentHashMap.java:3606)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at com.chis.modules.timer.heth.service.ChestAndHearingRstService.updateRstBatch(ChestAndHearingRstService.java:222)
	at com.chis.modules.timer.heth.service.ChestAndHearingRstService.dealHearingRst(ChestAndHearingRstService.java:144)
	at com.chis.modules.timer.heth.job.ChestAndHearingRstJob.dealHearingRst(ChestAndHearingRstJob.java:246)
	at com.chis.modules.timer.heth.job.ChestAndHearingRstJob.lambda$start$0(ChestAndHearingRstJob.java:80)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1626)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:447)
	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:396)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:951)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:513)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:227)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:531)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:208)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:1046)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1336)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3613)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3714)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1378)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:46)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy336.update(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.PerformanceInterceptor.intercept(PerformanceInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy336.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.OptimisticLockerInterceptor.intercept(OptimisticLockerInterceptor.java:141)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy335.update(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy335.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:198)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 48 common frames omitted
2025-07-03 15:26:35 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 590963 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:26:36 [pool-2-thread-1] ERROR c.c.m.timer.heth.service.ChestAndHearingRstService - 批量更新结论出错，
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

### The error may involve com.chis.modules.timer.heth.mapper.TdTjBhkMapper.updateChestRstBatch-Inline
### The error occurred while setting parameters
### SQL: UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN          (               ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          )
### Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

### The error may involve com.chis.modules.timer.heth.mapper.TdTjBhkMapper.updateChestRstBatch-Inline
### The error occurred while setting parameters
### SQL: UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN          (               ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          )
### Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:73)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy71.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:294)
	at com.baomidou.mybatisplus.core.override.PageMapperMethod.execute(PageMapperMethod.java:73)
	at com.baomidou.mybatisplus.core.override.PageMapperProxy.invoke(PageMapperProxy.java:64)
	at com.sun.proxy.$Proxy81.updateChestRstBatch(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy82.updateChestRstBatch(Unknown Source)
	at com.chis.modules.timer.heth.service.TdTjBhkService.lambda$updateRstBatch$0(TdTjBhkService.java:177)
	at java.util.stream.Streams$RangeIntSpliterator.forEachRemaining(Streams.java:110)
	at java.util.stream.IntPipeline$Head.forEach(IntPipeline.java:557)
	at com.chis.modules.timer.heth.service.TdTjBhkService.updateRstBatch(TdTjBhkService.java:173)
	at com.chis.modules.timer.heth.service.TdTjBhkService$$FastClassBySpringCGLIB$$588b2fea.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:295)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.chis.modules.timer.heth.service.TdTjBhkService$$EnhancerBySpringCGLIB$$4ae25f28.updateRstBatch(<generated>)
	at com.chis.modules.timer.heth.service.ChestAndHearingRstService.lambda$updateRstBatch$2(ChestAndHearingRstService.java:226)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
	at java.util.concurrent.ConcurrentHashMap$EntrySpliterator.forEachRemaining(ConcurrentHashMap.java:3606)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at com.chis.modules.timer.heth.service.ChestAndHearingRstService.updateRstBatch(ChestAndHearingRstService.java:222)
	at com.chis.modules.timer.heth.service.ChestAndHearingRstService.dealChestRst(ChestAndHearingRstService.java:80)
	at com.chis.modules.timer.heth.job.ChestAndHearingRstJob.dealChestRst(ChestAndHearingRstJob.java:226)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1626)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:447)
	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:396)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:951)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:513)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:227)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:531)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:208)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:1046)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1336)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3613)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3714)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1378)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:46)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy336.update(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.PerformanceInterceptor.intercept(PerformanceInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy336.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.OptimisticLockerInterceptor.intercept(OptimisticLockerInterceptor.java:141)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy335.update(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy335.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:198)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 47 common frames omitted
2025-07-03 15:26:36 [pool-2-thread-1] ERROR c.c.m.timer.heth.service.ChestAndHearingRstService - 批量更新结论出错，
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

### The error may involve com.chis.modules.timer.heth.mapper.TdTjBhkMapper.updateChestRstBatch-Inline
### The error occurred while setting parameters
### SQL: UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN          (               ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          )
### Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

### The error may involve com.chis.modules.timer.heth.mapper.TdTjBhkMapper.updateChestRstBatch-Inline
### The error occurred while setting parameters
### SQL: UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN          (               ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          ,              ?          )
### Cause: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:93)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:73)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy71.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:294)
	at com.baomidou.mybatisplus.core.override.PageMapperMethod.execute(PageMapperMethod.java:73)
	at com.baomidou.mybatisplus.core.override.PageMapperProxy.invoke(PageMapperProxy.java:64)
	at com.sun.proxy.$Proxy81.updateChestRstBatch(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy82.updateChestRstBatch(Unknown Source)
	at com.chis.modules.timer.heth.service.TdTjBhkService.updateRstBatch(TdTjBhkService.java:166)
	at com.chis.modules.timer.heth.service.TdTjBhkService$$FastClassBySpringCGLIB$$588b2fea.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:295)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.chis.modules.timer.heth.service.TdTjBhkService$$EnhancerBySpringCGLIB$$4ae25f28.updateRstBatch(<generated>)
	at com.chis.modules.timer.heth.service.ChestAndHearingRstService.lambda$updateRstBatch$2(ChestAndHearingRstService.java:226)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
	at java.util.concurrent.ConcurrentHashMap$EntrySpliterator.forEachRemaining(ConcurrentHashMap.java:3606)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at com.chis.modules.timer.heth.service.ChestAndHearingRstService.updateRstBatch(ChestAndHearingRstService.java:222)
	at com.chis.modules.timer.heth.service.ChestAndHearingRstService.dealChestRst(ChestAndHearingRstService.java:80)
	at com.chis.modules.timer.heth.job.ChestAndHearingRstJob.dealChestRst(ChestAndHearingRstJob.java:226)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1626)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: ORA-04098: 触发器 'ZYBSCS.TRG_TD_TJ_BHK' 无效且未通过重新验证

	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:447)
	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:396)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:951)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:513)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:227)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:531)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:208)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:1046)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1336)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3613)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3714)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1378)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:46)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy336.update(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.PerformanceInterceptor.intercept(PerformanceInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy336.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.OptimisticLockerInterceptor.intercept(OptimisticLockerInterceptor.java:141)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy335.update(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy335.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:198)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 44 common frames omitted
2025-07-03 15:35:34 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 331204 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:35:35 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1309 millis. UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[9116637,35408420,35408525,35408526,35408527,35410569,35410564,35408564,35408562,35408593,35408595,35408590,35408582,35408636,35408637,35408634,35408663,35408659,35408655,35408648,35408649,35408647,35408712,35410825,26540092,35408803,35410900,35410896,35410897,35408892,35408894,35408883,35408873,35410922,35410916,35410914,26405661,35408132,35410424,35410426,35410421,35410416,7893888,35411516,35411514,35411515,35411508,35411509,35411502,35411491,35411538,35411527,35411522,35411613,7894221,27524478,35410961,35411057,35411059,35411047,35411132,35411133,35411134,35411135,35411161,35411185,35411187,35411173,35411223,35411217,35411212,35411213,35411215,35411203,35411263,35411373,35411374,35411370,35411441]
2025-07-03 15:35:37 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1667 millis. UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[9116636,35408412,35408413,35408414,35394079,35408415,35408408,35410456,35408409,35408410,35408411,35408404,35410452,35408405,35410453,35408406,35410454,35408407,35408400,35408401,35408402,35410450,35408403,35410451,35408398,35408399,21254501,21254502,21254503,35408432,35408428,35408430,35408431,35408424,35408426,35408427,35408421,35408422,35408423,35408416,35408417,35408418,35408419,35410524,35410526,35410527,27613665,35410520,27613664,35410521,35410522,27613666,35410523,27613676,27136468,26636765,27136469,27613686,35410556,35410557,35410558,35410559,35410555,35410540,27613652,35410541,35410542,35410543,27613649,27613648,27613651,27613650,35410539,27613660,27613662,35410528,35410529,35410588,35410589,35410590,35410584,35410585,35410586,35410587,35410580,35410581,35410583,35410576,35410577,35410579,35410572,35410573,35410574,35410575,35410568,35410570,35410565,35410566,29477210,35410567,35410561,35410562,35410563,29477217,35410620,35410621,35410622,35410623,35410616,35410617,35410618,26904882,35410612,35410613,35410614,26904881,35410615,35408560,35410608,35410609,35410610,35408563,35410604,35408557,35410605,35408558,35410606,35410607,35410600,35410601,26904876,35410602,26904877,35410603,35410597,35410598,35410599,35408604,35408605,35410653,35410654,35408607,35410655,383169,35408600,35410648,35410649,35408602,35410650,35408603,35410644,35408597,35410645,35410646,35410647,35410641,35408594,35410642,35410643,35410636,35410637,35410638,35410639,35408584,35410632,35410633,35408586,35410634,35408587,35410635,338141,35410628,35410629,35410631,35410624,35410625,35410626,35410627,35410684,35408638,35410687,35410680,35408633,35410681,35410682,35408635,35410683,35408628,35410676,35408629,35408630,35408624,35410672,35408625,35410673,35408626,35410674,35410675,35408620,35408621,35410669,35408622,35408623,35408616,35410664,35408617,35410665,35408618,35410666,35408619,35410667,35408612,35410660,35408613,35410661,35408614,35410662,35408615,35408608,35410656,35408609,35410657,35408610,35410658,35408611,35410659,35408669,35408671,35408660,35408656,35408657,35408658,35408652,35410700,35408653,35410701,35408654,35410702,35410703,35410696,35410697,35408650,35410698,35408651,35410699,35408644,35408645,35410693,35408646,35410694,35410695]
2025-07-03 15:38:00 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 477573 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:38:03 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 2479 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,7893886,7893888,7894221,21224419,21254501,21254503,21255899,21255900,21255901,21255903,24566428,25140948,25140949,25933162,26405473,26405647,26405684,26405798,26623910,26623975,26623976,26623986,26633331,26634079,26636765,26692643,26692676,27136548,27136549,27136551,27136825,27136872,27136888,27136890,27136891,27136892,27136916,27136917,27136918,27136919,27136978,27136979,27136980,27137018,27137150,27137181,27137182,27137191,27137192,27137193,27137194,27137208,27137209,27137210,27137445,27137446,27137462,27137465,27137469,27137470,27137479,27137485,27137491,27137493,27137496,27137497,27137498,27137499,27137500,27137559,27137561,27137562,27137563,27137569,27137570,27137571,27137572,27137593,27137594,27137595,27137596,27137607,27137608,27137609,27137611,27137612,27137613,27137614,27137615,27137616,27137617,27137618,27137657,27137658,27137659,27137660,27137665,27137683,27137760,27137812,27137814,27145453,27623979,28511540,29477210,29477217,32032225,32032843,32032869,32079412,32083492,32083534,32106293,32244673,32490320,32866016,32866307,33162075,33189080,33210618,35394079,35394662,35394663,35394664,35408145,35408165,35408166,35408168,35408169,35408171,35408175,35408184,35408187,35408188,35408233,35408234,35408235,35408236,35408237,35408238,35408239,35408240,35408241,35408242,35408243,35408244,35408245,35408246,35408247,35408248,35408249,35408251,35408252,35408253,35408254,35408256,35408257,35408258,35408259,35408260,35408261,35408262,35408263,35408264,35408265,35408266,35408267,35408268,35408269,35408270,35408271,35408272,35408273,35408274,35408275,35408276,35408277,35408278,35408279,35408280,35408281,35408282,35408283,35408284,35408285,35408286,35408287,35408288,35408289,35408290,35408291,35408292,35408293,35408294,35408295,35408296,35408297,35408298,35408299,35408300,35408301,35408302,35408303,35408304,35408305,35408306,35408307,35408308,35408309,35408310,35408311,35408312,35408313,35408314,35408315,35408316,35408317,35408318,35408319,35408320,35408321,35408322,35408323,35408325,35408327,35408328,35408329,35408330,35408331,35408332,35408333,35408334,35408335,35408336,35408337,35408338,35408339,35408340,35408341,35408342,35408343,35408344,35408345,35408347,35408348,35408350,35408351,35408352,35408353,35408354]
2025-07-03 15:39:28 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 85138 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:39:31 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1751 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35412638,35412639,35412640,35412641,35412642,35412643,35412644,35412645,35412646,35412647,35412648,35412649,35412650,35412651,35412652,35412653,35412654,35412655,35412656,35412657,35412658,35412659,35412660,35412661,35412662,35412663,35412664,35412665,35412666,35412667,35412668,35412669,35412670,35412671,35412672,35412673,35412674,35412675,35412676,35412677,35412678,35412679,35412680,35412681,35412682,35412683,35412684,35412685,35412686,35412688,35412689,35412690,35412691,35412692,35412693,35412694,35412697,35412699,35412700,35412701,35412702,35412703,35412704,35412705,35412707,35412710,35412712,35412713,35412714,35412716,35412717,35412718,35412721,35412722,35412723,35412724,35412725,35412726,35412727,35412729,35412733,35412736,35412738,35412739,35412740,35412741,35412742,35412743,35412744,35412745,35412746,35412747,35412748,35412749,35412750,35412751,35412752,35412753,35412754,35412755,35412756,35412757,35412758,35412759,35412760,35412761,35412762,35412763,35412764,35412765,35412766,35412767,35412768,35412770,35412771,35412773,35412774,35412775,35412776,35412777,35412778,35412779,35412780,35412781,35412783,35412786,35412791,35412795,35412800,35412801,35412802,35412803,35412804,35412807,35412809,35412810,35412811,35412812,35412817,35412818,35412819,35412820,35412822,35412823,35412825,35412826,35412827,35412829,35412831,35412835,35412837,35412838,35412840,35412841,35412845,35412846,35412847,35412848,35412849,35412859,35412860,35412861,35412866,35412867,35412868,35412869,35412873,35412874,35412875,35412877,35412879,35412880,35412881,35412882,35412883,35412886,35412887,35412888,35412890,35412891,35412892,35412893,35412894,35412896,35412897,35412898,35412901,35412902,35412903,35412905,35412911,35412913,35412914,35412916,35412917,35412919,35412921]
2025-07-03 15:40:07 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 35797 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:40:43 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 35531 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:41:18 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 34637 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:41:27 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 348405 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:41:51 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 31915 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:42:24 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 32219 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:42:26 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 2058 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35419837,35419838,35419839,35419840,35419841,35419842,35419843,35419844,35419845,35419846,35419847,35419848,35419849,35419850,35419851,35419852,35419853,35419854,35419855,35419856,35419857,35419859,35419860,35419861,35419862,35419863,35419864,35419865,35419867,35419870,35419871,35419873,35419875,35419879,35419881,35419885,35419886,35419887,35419889,35419890,35419891,35419892,35419893,35419894,35419895,35419896,35419897,35419898,35419899,35419900,35419901,35419902,35419903,35419904,35419906,35419907,35419909,35419910,35419911,35419912,35419913,35419914,35419915,35419916,35419917,35419918,35419919,35419920,35419922,35419923,35419924,35419925,35419926,35419927,35419929,35419930,35419931,35419932,35419933,35419935,35419936,35419937,35419938,35419941,35419942,35419943,35419944,35419945,35419946,35419947,35419948,35419949,35419950,35419951,35419952,35419953,35419954,35419955,35419957,35419958,35419961,35419962,35419970,35419971,35419972,35419974,35419975,35419993,35419994,35419995,35419996,35419997,35419998,35419999,35420000,35420001,35420002,35420003,35420004,35420005,35420006,35420007,35420008,35420009,35420010,35420011,35420012,35420014,35420015,35420016,35420017,35420018,35420019,35420020,35420021,35420040,35420044,35420045,35420047,35420048,35420049,35420050,35420051,35420052,35420053,35420054,35420055,35420056,35420058,35420059,35420060,35420061,35420062,35420063,35420064,35420065,35420066,35420067,35420068,35420069,35420070,35420071,35420072,35420073,35420074,35420075,35420076,35420077,35420078,35420079,35420080,35420082,35420083,35420084,35420086,35420087,35420088,35420090,35420091,35420092,35420093,35420094,35420095,35420096,35420097,35420098,35420099,35420100,35420101,35420102,35420104,35420105,35420106,35420107,35420108,35420109]
2025-07-03 15:43:00 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 34004 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:43:16 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 108911 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:43:30 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 13907 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:43:37 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 36477 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:43:46 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 13687 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:44:01 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 14127 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:44:08 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 30690 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:44:10 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1042 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35426278,35426279,35426280,35426281,35426282,35426283,35426284,35426285,35426286,35426287,35426288,35426289,35426290,35426291,35426292,35426293,35426294,35426295,35426296,35426297,35426298,35426299,35426300,35426301,35426302,35426303,35426304,35426305,35426306,35426307,35426308,35426309,35426310,35426311,35426312,35426313,35426314,35426316,35426317,35426318,35426319,35426320,35426321,35426322,35426323,35426324,35426325,35426326,35426327,35426328,35426329,35426330,35426331,35426332,35426334,35426335,35426336,35426337,35426338,35426339,35426340,35426341,35426342,35426343,35426344,35426346,35426348,35426349,35426350,35426351,35426352,35426353,35426354,35426355,35426356,35426357,35426358,35426359,35426360,35426361,35426362,35426363,35426364,35426365,35426366,35426367,35426368,35426369,35426370,35426371,35426372,35426373,35426374,35426375,35426376,35426377,35426378,35426379,35426380,35426381,35426382,35426383,35426384,35426385,35426386,35426387,35426388,35426389,35426390,35426391,35426392,35426393,35426394,35426395,35426396,35426397,35426398,35426399,35426400,35426401,35426402,35426403,35426404,35426406,35426407,35426408,35426409,35426410,35426411,35426412,35426413,35426414,35426415,35426416,35426417]
2025-07-03 15:44:16 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 14557 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:44:32 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 14021 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:44:41 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 31301 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:44:47 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 13980 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:45:02 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 14666 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:45:14 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 31314 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:45:16 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 13870 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:45:53 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 35299 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:46:32 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 39111 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:46:59 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 26821 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:47:11 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 115637 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:47:17 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 17190 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:47:33 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 13869 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:47:34 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1302 millis. UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[9116636,35447575,35447569,35447570,35443475,35447571,35443468,35447564,35443469,35447565,35443470,35447566,35443471,35447567,35443464,35447560,35443465,35447561,35443466,35447562,35443467,35447563,35443460,35447556,35443461,35447557,35443462,35447558,35443463,35447559,35447552,35447553,35447554,35447555,35443516,35447612,35443517,35443518,35443519,35443512,35447608,35443509,35447605,35443510,35443506,35443496,35447592,35443497,35447593,35443499,35443492,35443493,35447589,35443494,35447590,35443495,35447591,35443488,35443489,35443490,35443491,35447633,35447634,35447635,35447630,35447631,35443528,35447624,35447627,35443524,35443525,35443526,35443527,35447623,35443520,35443521,35443522,35445630,35443582,35445631,35443583,35443577,35445625,35445626,35443578,35443579,35443574,35443575,35443612,35443613,35443614,35443615,35443608,35443609,35443610,35443611,35445652,35443604,35445653,35443605,35443606,35443607,35443600,35445650,35443602,35445651,35443603,35443596,35443597,35445646,35445647,35443599,35443592,35445641,35443593,35443594,35443595,35445636,35443588,35443589,35443590,35443591,35445632,35443584,35443585,35443586,35445635,35443587,35443644,35443645,35443646,35443647,35443640,35443641,35443642,35443643,35443636,35443637,35443638,35443639,35443632,35443633,35443634,35443635,35443628,35443629,35443630,35443631,35443624,35443625,35443626,35443627,35443620,35443621,35443622,35443623,35443616,35443617,35443618,35443619,35443676,35443677,35443678,35443679,35443672,35443673,35443674,35443675,35443668,35443669,35443670,35443671,35443664,35443665,35443666,35443667,35443660,35443661,35443662,35443663,35443656,35443657,35443658,35443659,35443652,35443653,35443654,35443655,35443648,35443649,35443650,35443651,35443708,35443709,35443710,35443711,35443704,35443706,35443707,35443700,35443701,35443702,35443703,35443696,35443697,35443698,35443699,35443692,35443693,35443694,35443688,35443689,35443690,35443691,35443684,35443685,35443686,35443687,35443680,35443681,35443682,35443683,35446812,35446813,35446814,35446815,35446808,35446809,35446810,35446811,35446804,35446805,35446806,35446807,35446800,35446801,35446802,35446803,35446796,35446797,35446798,35446792,35446793,35446794,35446795,35446788,35446789,35446791,35446784,35446785,35446787]
2025-07-03 15:47:35 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1249 millis. UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[9116636,35446844,35446845,35446847,35446840,35446841,35446843,35446836,35446837,35446838,35446839,35446832,35446833,35446834,35446835,35444780,35446829,35446830,35446831,35446824,35446825,35446826,35446827,35446820,35446821,35446822,35446823,35446816,35446818,35446819,35444828,35446877,35446878,35446879,35444824,35446872,35444825,35446873,35446874,35444827,35444821,35446869,35444822,35446870,35446871,35444816,35446864,35444818,35444819,35446860,35446861,35446862,35446856,35446857,35446858,35446859,35446852,35446853,35446854,35446855,35446849,35446850,35446851,35446908,35446909,35446910,35446911,35446904,35446905,35446907,35446900,35446901,35446902,35446903,35446896,35446897,35446898,35446899,35446892,35446893,35446894,35444841,35444842,35446884,35444837,35444832,35446880,35446881,35446882,35444835,35446883,35446943,35446933,35446929,35446924,35446925,35446926,35446921,35446922,35446923,35446913,35446968,35446969,35446971,35446964,35446966,35446950,35447004,35447007,35447000,35447001,35446977,35447031,35447024,35447022,35447011,35447068,35447069,35447070,35447071,35447064,35447065,35447066,35447067,35447061,35447062,35447063,35447056,35447057,35447058,35447059,35447052,35447053,35447054,35447055,35447048,35447049,35447050,35447051,35447046,35447047,35447101,35447102,35447103,35447096,35447097,35447098,35447099,35447092,35447093,35447094,35447095,35447088,35447089,35447090,35447091,35447084,35447085,35447086,35447082,35447083,35447077,35447072,35447073,35447132,35447133,35447121,35447113,35447114,35447108,35447104,35447105,35447106,35447107,35447166,35447167,35447161,35447162,35447163,35447154,35447146,35447196,35447197,35447198,35447199,35447192,35447194,35447195,35447188,35447189,35447190,35447191,35447184,35447185,35447186,35447187,35447180,35447181,35447182,35447183,35447176,35447177,35447178,35447179,35447172,35447173,35447174,35447175,35447168,35447169,35447170,35447171,35447229,35447230,35447231,35447224,35447225,35447226,35447227,35447220,35447221,35447223,35447212,35447213,35447208,35447209,35447210,35447211,35447204,35447205,35447206,35447207,35447200,35447201,35447202,35447260,35447262,35447263,35447258,35447252,35447253,35447254,35447255,35447250,35447251,35447245,35447246,35447247,35447240,35447242,35447243]
2025-07-03 15:47:43 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 31645 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:47:50 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 14202 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:47:53 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 3208 millis. UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[9116637,35449371,35449532,35449519,35449573,35449571,35449614,35449633,35449693,35449690,35447677,35449723,35447668,35449751,35447695,35449772,35449773,35449768,35449769,35449770,35449771,35449763,35447766,35449796,35449794,35447780,35448976,35449167,35449213,35449202,35449241,35449237,35449276,35449270,35449288,35449285,35449286,35449280,35449331,35448351,35448344,35448336,35448332,35448327,35448321,35448372,35448373,35448370,35448371,35448366,35448367,35448360,35448363,35448356,35448357,35448358,35448387,35448478,35448475,35448483,35448541,35448554,35448582,35448583,35448577,35448620,35448696,35448697,35448728,35448780,35448783,35448770,35448771,35447819,35448091,35448084,35448081,35448150,35448168,35448316,35448310,35448298,35448295]
2025-07-03 15:47:55 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1943 millis. UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[9116636,35448805,35448806,35448807,35448800,35448801,35448803,35447830,35447826,35447820,35447821,35447816,35447817,35447818,35447813,35447814,35447808,35447810,35447811,35447934,35447928,35447929,35447930,35447931,35447925,35447993,35447980,35447981,35447982,35447976,35447977,35447978,35447979,35447972,35447973,35447974,35447975,35447968,35447969,35447971,35448029,35448030,35448031,35448020,35448060,35448061,35448062,35448063,35448056,35448057,35448059,35448052,35448053,35448054,35448055,35448048,35448049,35448050,35448051,35448044,35448045,35448040,35448041,35448042,35448043,35448036,35448037,35448038,35448039,35448032,35448033,35448034,35448035,35448092,35448093,35448094,35448095,35448088,35448089,35448090,35448085,35448086,35448087,35448080,35448082,35448083,35448076,35448077,35448078,35448072,35448073,35448074,35448075,35448068,35448069,35448070,35448071,35448064,35448065,35448066,35448067,35448124,35448126,35448122,35448123,35448104,35448105,35448106,35448107,35448100,35448101,35448102,35448103,35448096,35448097,35448098,35448099,35448156,35448157,35448158,35448159,35448152,35448153,35448154,35448155,35448151,35448146,35448136,35448137,35448135,35448172,35448173,35448169,35448170,35448171,35448167,35448160,35448252,35448253,35448250,35448245,35448246,35448247,35448241,35448242,35448243,35448317,35448318,35448312,35448313,35448314,35448315,35448308,35448309,35448311,35448304,35448305,35448306,35448300,35448301,35448302,35448303,35448296,35448297,35448299,35448292,35448293,35448294,35448291]
2025-07-03 15:48:09 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 13844 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:48:16 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 31363 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:48:24 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 14803 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:48:39 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 13676 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:48:50 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 31623 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:48:53 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 13852 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:49:07 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 13674 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:49:10 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 2833 millis. UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[9116637,35459604,35459601,35459590,35459644,35459681,35459831,35459852,35458046,35459113,35459107,35459157,35459152,35459143,35459189,35459168,35459224,35459205,35459237,35459236,35459239,35459351,35459346,35459388,35459375,35459360,35459405,35459399,35459446,35459442,35459485,35459483,35459476,35459468,35459461,35459514,35459506,35459500,35459499,35459493,35459489,35458570,35458716,35458720,35458764,35458796,35458840,35458826,35458879,35458867,35458863,35458849,35458851,35458909,35458919,35458915,35458951,35459034,35459029,35459014,35459049,35459045,35458137,35458136,35458138,35458133,35458132,35458127,35458126,35458235,35458272,35458413,35458490,35458535]
2025-07-03 15:49:12 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1980 millis. UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[9116636,35459058,35459053,35459052,35459055,35459054,35459048,35459051,35459050,35459044,35459047,35459046,35459041,35459040,35459043,35458077,35458079,35458075,35458068,35458071,35458070,35458065,35458064,35458067,35458061,35458056,35458059,35458058,35458053,35458054,35458109,35458104,35458107,35458106,35458101,35458100,35458103,35458102,35458097,35458099,35458092,35458095,35458094,35458089,35458088,35458091,35458087,35458080,35458083,35458082,35458134,35458129,35458131,35458125,35458124,35458121,35458120,35458122,35458116,35458119,35458118,35458112,35458115,35458114,35458175,35458169,35458162,35458187,35458181,35458177,35458237,35458234,35458231,35458230,35458226,35458223,35458257,35458255,35458254,35458250,35458244,35458242,35458273,35458397,35458373,35458375,35458374,35458369,35458371,35458431,35458426,35458421,35458422,35458417,35458418,35458412,35458414,35458411,35458400,35458461,35458460,35458463,35458462,35458456,35458459,35458458,35458453,35458455,35458454,35458449,35458448,35458450,35458447,35458433,35458493,35458492,35458495,35458488,35458484,35458487,35458483,35458482,35458476,35458479,35458478,35458473,35458472,35458475,35458469,35458471,35458470,35458465,35458464,35458467,35458466,35458525,35458527,35458521,35458520,35458523,35458517,35458516,35458519,35458513,35458514,35458508,35458511,35458510,35458505,35458507,35458500,35458503,35458502,35458497,35458496,35458499,35458556,35458558,35458553,35458552,35458555,35458554,35458549,35458548,35458551,35458545,35458544,35458546,35458540,35458542,35458537,35458536,35458539,35458538,35458533,35458528,35458531,35458530]
2025-07-03 15:49:21 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 31362 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:49:26 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 4489 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[2,35441939,35441984,35441997,35442000,35442012,35442069,35442082,35442088,35442094,35442095,35442097,35442108,35442111,35442121,35442122,35442125,35442126,35442153,35442195,35442197,35442202,35442204,35442205,35442206,35442207,35442209,35442210,35442212,35442213,35442216,35442217,35442218,35442219,35442221,35442223,35442224,35442226,35442228,35442240,35442242,35442244,35442250,35442251,35442263,35442267,35442283,35442286,35442293,35442296,35442308,35442312,35442317,35442353,35442390,35442396,35442408,35442411,35442412,35442415,35442419,35442422,35442427,35442431,35442432,35442434,35442437,35442447,35442450,35442452,35442454,35442460,35442462,35442551,35442552,35442553,35442554,35442555,35442556,35442557,35442558,35442559,35442560,35442561,35442562,35442563,35442564,35442565,35442566,35442574,35442600,35442605,35442610,35442611,35442641,35442651,35442656,35442658,35442673,35442680,35442708,35442715,35442717,35442718,35442727,35442741,35442763,35442764,35442766,35442779,35442784,35442815,35442899,35442904,35442911,35442934,35442943,35442968,35442971,35442972,35442995,35443009,35443031,35443034,35443055,35443069,35443072,35443075,35443076,35443136,35443137,35443138,35443139,35443140,35443141,35443148,35443149,35443150,35443153,35443154]
2025-07-03 15:49:26 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 13957 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:49:38 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 11533 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:49:52 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 14032 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:49:57 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 31188 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:49:58 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1063 millis. UPDATE TD_TJ_BHK SET CHEST_RESULT = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[0,35443667,35443668,35443669,35443670,35443671,35443672,35443673,35443674,35443675,35443676,35443677,35443678,35443679,35443680,35443681,35443682,35443683,35443684,35443685,35443686,35443687,35443688,35443689,35443690,35443691,35443692,35443693,35443694,35443695,35443696,35443697,35443698,35443699,35443700,35443701,35443702,35443703,35443704,35443706,35443707,35443708,35443709,35443710,35443711,35443712,35443713,35443714,35443715,35443716,35443717,35443718,35443755,35443756,35443757,35443758,35443759,35443760,35443761,35443762,35443763,35443764,35443765,35443766,35443767,35443768,35443769,35443770,35443771,35443772,35443773,35443774,35443775,35443776,35443777,35443778,35443779,35443780,35443781,35443782,35443783,35443784,35443811,35443812,35443814,35443815,35443816,35443817,35443818,35444370,35444371,35444372,35444377,35444379,35444380,35444381,35444383,35444388,35444390,35444394,35444401,35444408,35444411,35444419,35444420,35444432,35444435,35444440,35444447,35444458,35444480,35444526,35444546,35444555,35444559,35444564,35444579,35444582,35444583,35444584,35444713,35444717,35444720,35444722,35444727,35444816,35444818,35444819,35444821,35444824,35444825,35444827,35445625,35445647,35445650,35446322,35446324,35446646,35446647,35446655,35446659,35446664,35446673,35446675,35446676,35446679,35446681,35446683,35446686,35446687,35446689,35446694,35446696,35446698,35446699,35446701,35446702,35446706,35446716,35446717,35446718,35446725,35446727,35446732,35446733,35446736,35446737,35446738,35446740,35446741,35446744,35446746,35446747,35446748,35446749,35446750,35446751,35446752,35446753,35446754,35446755,35446756,35446757,35446758,35446759,35446774,35446782,35446786,35446787,35446789,35446790,35446791,35446806,35446817,35446825,35446828,35446836,35446842,35446846,35446848,35446865,35446866,35446867,35446868,35446869,35446870,35446871,35446872,35446873,35446874,35446875,35446876,35446877,35446878,35446879,35446880,35446881,35446882,35446883,35446884,35446885,35446886,35446887,35446888,35446889,35446890,35446891,35446892,35446893,35446894,35446895,35446896,35446898,35446899,35446900,35446901,35446902,35446903,35446904,35446905,35446906,35446907,35446908,35446909,35446910,35446911,35446912,35446913,35446914,35446915,35446916]
2025-07-03 15:50:29 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 37227 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:54:17 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 226918 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:56:00 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 102903 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:56:32 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 31799 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:56:49 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 16963 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:57:04 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 13799 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:57:19 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 14078 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:57:33 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 14328 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:57:35 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1286 millis. UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[9116636,35480677,35480676,35480679,35480673,35480675,35480674,35480733,35480732,35480735,35480729,35480728,35480731,35480730,35480725,35480724,35480727,35480726,35480721,35480720,35480723,35480722,35480717,35480718,35480712,35480709,35480708,35480711,35480710,35480705,35480704,35480707,35480706,35480765,35480764,35480767,35480766,35480761,35480760,35480763,35480762,35480757,35480756,35480759,35480753,35480752,35480755,35480754,35480749,35480748,35480751,35480750,35480745,35480744,35480747,35480746,35480743,35480742,35480737,35480736,35480739,35480792,35480794,35480789,35480788,35480791,35480790,35480785,35480784,35480787,35480786,35480781,35480783,35480777,35480776,35480779,35480778,35480773,35480768,35480771,35480770,35480809,35480811,35480810,35480804,35480807,35480803,35480802,35478841,35478877,35478876,35478879,35478878,35478872,35478875,35478874,35478869,35478864,35478866,35478861,35478860,35478863,35478862,35478859,35478858,35478853,35478854,35478849,35478848,35478909,35478908,35478911,35478904,35478907,35478906,35478901,35478900,35478903,35478897,35478899,35478898,35478893,35478892,35478895,35478894,35478889,35478888,35478891,35478890,35478887,35478881,35478880,35478882,35478941,35478942,35478936,35478939,35478938,35478933,35478932,35478935,35478929,35478928,35478931,35478930,35478925,35478924,35478927,35478926,35478922,35478917,35478919,35478914,35478974,35478966,35478961,35478958,35478951,35478947,35479006,35479001,35479003,35478997,35478996,35478999,35478998,35478993,35478992,35478994,35478991,35478984,35478981,35478982,35479039,35479033,35479032,35479029,35479031,35479030,35479025,35479024,35479020,35479023,35479022,35479017,35479016,35479018,35479013,35479012,35479011,35479010]
2025-07-03 15:57:49 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 13546 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:58:03 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 13554 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:58:19 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 14019 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:58:20 [pool-2-thread-1] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 501619 millis. SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
            FROM (SELECT
                    B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
                FROM TD_TJ_BHKSUB BS
                    INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                    INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
                    INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
                WHERE SC.EXTENDS3 = '30'
                    AND B.CHEST_RESULT IS NULL
                    AND NVL(BS.IF_LACK, 0) = 0
                     
                     
                     
                        AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                     
                     
                        AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                     
                GROUP BY B.RID) ZWX
            WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND ?["2024-01-01","2025-07-03",1000]
2025-07-03 15:58:41 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 21631 millis. WITH ID_TABLE AS (
            SELECT RID
            FROM (
                SELECT ZWX.RID, ROWNUM AS RN
                FROM (
                    SELECT B.RID
                        FROM TD_TJ_BHKSUB BS
                            INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
                        WHERE B.HEARING_RST_ID IS NULL
                            AND BS.ITEM_RST IS NOT NULL
                            AND NVL(BS.IF_LACK, 0) = 0
                            AND BS.ITEM_ID IN
                             (  
                                ?
                             ) 
                             
                             
                             
                                AND B.RPT_PRINT_DATE >= TO_DATE(?,'yyyy-MM-dd')
                             
                             
                                AND B.RPT_PRINT_DATE <= TO_DATE(?||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
                             
                        GROUP BY B.RID
                ) ZWX
            )
            WHERE RN BETWEEN 0 AND ?
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst
        FROM TD_TJ_BHKSUB BS
            INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
            INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
             (  
                ?
             ) 
            AND BS.ITEM_RST IS NOT NULL[11244,"2024-01-01","2025-07-03",1000,11244]
2025-07-03 15:58:42 [pool-2-thread-2] ERROR com.alibaba.druid.filter.stat.StatFilter - slow sql 1202 millis. UPDATE TD_TJ_BHK SET HEARING_RST_ID = ? WHERE RID IN
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[9116636,35489787,35489781,35489769,35489765,35489764,35489767,35489766,35489761,35489760,35489762,35490841,35490840,35490837,35490836,35490839,35490838,35490833,35490832,35490835,35490834,35490829,35490828,35490831,35490830,35490825,35490827,35490826,35490821,35490823,35490822,35490817,35490816,35490819,35488828,35488823,35488813,35488812,35488815,35488814,35488809,35488807,35488806,35488803,35488860,35488863,35488853,35488848,35488851,35488840,35488842,35488837,35488839,35488867,35488956,35489021,35489020,35489022,35489017,35489019,35489012,35489014,35489005,35489006,35489001,35489002,35488999,35488992,35488994,35489055,35489051,35489047,35489046,35489085,35489084,35489086,35489081,35489080,35489069,35489068,35489064,35489066,35489063,35489057,35489059,35489104,35489107,35489106,35489101,35489100,35489102,35489097,35489093,35489092,35489094,35489089,35489091,35489090,35489177,35489176,35489173,35489175,35489213,35489209,35489208,35489210,35489205,35489207,35489206,35489201,35489203,35489202,35489247,35489241,35489240,35489243,35489237,35489236,35489239,35489238,35489233,35489235,35489234,35489227,35489226,35489221,35489220,35489223,35489222,35489217,35489218,35489267]

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>chis-spr</groupId>
    <artifactId>spring-timer-heth-badrsn-rst</artifactId>
    <version>4.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    <parent>
        <artifactId>springboot-pro</artifactId>
        <groupId>chis-spr</groupId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <zwx.spring-jdbc.version>2.0-SNAPSHOT</zwx.spring-jdbc.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>zyws.sdszw</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${zwx.spring-jdbc.version}</version>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.6.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
         <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
           <version>3.7.0</version>
           <configuration>
              <source>8</source>
              <target>8</target>
           </configuration> 
      </plugin>  
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>JxcDomain</id>
            <name>JxcDomainName</name>
            <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url>
        </repository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>JxcDomain</id>
            <name>JxcDomainName</name>
            <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>JxcDomain</id>
            <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>
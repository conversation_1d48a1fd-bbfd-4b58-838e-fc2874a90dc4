server:
  port: 9601
  tomcat:
    uri-encoding: UTF-8
    #最大线程数（并发限制数）
    max-threads: 1000
    #最大连接数（并发限制数）
    max-connections: 900
    min-spare-threads: 100
    accesslog:
      pattern: '%{X-Forwarded-For}i %h %l %u %t "%r" %s %b %T'
      enabled: true
      directory: ${user.dir}/logs
      prefix: access.${HOST:127.0.0.1}.${server.port:9800}
      
logging.file: E:/es/logs

##################【spring】########################
spring:
  # 加密
  encrypt:
    #AES加密秘钥
    key: afmi5pjDeKpSUaLU
    # AES加密，为true则不加密(注意：现场必须配置为false，否则会检测到druid漏洞)
    debug: false
    # 此接口无需加密 如：responseNoEncryptUriList: ["/payCallback","/refundCallback"]
    responseNoEncryptUriList: ["/csClient/rcdState","/easyExcel/fileupload","/easyExcel/cardImport","/easyExcel/cfPatientFlowupImport","/easyExcel/sampRsnUpload","/easyExcel/siteSurveysImport","/easyExcel/srvorgCardUpload","/easyExcel/zwBskInfoImport","/easyExcelUpload/uploadSlowCaserst","/pushShort","/lungTrain/pushTrainData","/easyExcel/bhkDataUpload","/easyExcel/tjorgRptInvestUpload","/easyExcel/notDiagInvestUpload","/easyExcel/diagOrgLateRptImport"]
    requestNoDecyptUriList: ["/csClient/rcdState","/easyExcel/fileupload","/easyExcel/cardImport","/easyExcel/cfPatientFlowupImport","/easyExcel/sampRsnUpload","/easyExcel/siteSurveysImport","/easyExcel/srvorgCardUpload","/easyExcel/zwBskInfoImport","/easyExcelUpload/uploadSlowCaserst","/pushShort","/lungTrain/pushTrainData","/easyExcel/bhkDataUpload","/easyExcel/tjorgRptInvestUpload","/easyExcel/notDiagInvestUpload","/easyExcel/diagOrgLateRptImport"]
    #DES是否加密 为true则不加密
    ifDesEnctrypt: false
    #DES密钥
    desEnctryptKey: chiscdc_zyws_@%^

heth-timer:
  #默认查询待处理数据，重点危害因素结论判定工具、是否超范围工具
  dataSize: 100
  #起始日期，重点危害因素结论判定工具（报告打印日期）、是否超范围工具（报告打印日期）
  start-date: "2022-01-01"
  #超范围服务
  outRange:
    #超范围服务预警统计工具默认一次处理数据量
    warnDataSize: 900
  #个案审核计算工具
  calculat:
    #一次处理数据量
    dataSize: 1000
    #处理数据体检日期的起始日期，体检日期
    startDate: "2022-01-01"
    #体检危害因素都是“放射类”时，贵州：1，不纳入个案审核，其他平台：非1或空，纳入审核。
    ifNotCheckFsBadRsn: 0
  #个案异步导出
  calculat-export:
    #一次查询处理任务数，建议大于100，不能大于1000
    dataSize: 500
    #限制一次处理最大导出任务数，如不配置则默认dataSize的五倍，不能大于10000
    executeDataSizeLimit: 1000
    #单个excel文件最大数据条数,不能超过100万否则Excel生成异常，导出数据超过配置值会生成zip包
    #参数值必须是1000的倍数 否则生成的文件序号不连续
    maxDataNum: 10000
    #导出是否需要显示【质控编号】
    ifExportZkBhkCode: false
    #个案异步导出线程池配置
    thread-pool:
      #同时处理导出任务线程数，根据服务器性能配置，避免数据库连接数不够
      corePoolSize: 2
      maximumPoolSize: 200
      keepAliveTime: 0
      queueCapacity: 10240
      #单个导出任务线程内同时进行的数据查询子线程数(每个子线程内一次查询1000条数据)
      #参数配置越大查询越快但占用内存和CPU越高，避免内存溢出建议设置5，最大10
      childPoolSize: 5
  #危急值工具
  danger:
    #危急值工具默认一次处理数据量
    dataSize: 900
    #危急值工具起始日期 两边引号不可以去掉 否则会解析成日期，报告打印日期
    startDate: "2022-01-01"
  sche-cron:
    #重点危害因素判定定时器 如果需要任务不执行 "-"
    badrsn-rst: "-"
    #超范围服务计算工具定时规则（吉林特有）
    outRangeTime: "-"
    #超范围服务预警统计工具定时规则（吉林特有，注意：必须设置在凌晨0点后，比如每天凌晨两点执行：0 0 2 * * ?）
    warnStatiscal: "-"
    #危急值工具定时规则（吉林特有）
    dangerCron: "-"
    #个案审核工具定时规则
    calculatCron: "-"
    #个案异步导出定时规则，不建议配置几秒钟执行一次，参数参考配置10分钟循环：0 */10 * * * ?
    calculatExportCron: "-"
  #体检报告抽取-计算胸片/电测听结果
  chest-hearing-rst:
    #定时规则
    cron: "-"
    #单次查询条数
    size: 2000
    bhk-date:
      #处理数据体检日期的起始日期（yyyy-MM-dd）
      start: ""
      #处理数据体检日期的结束日期（yyyy-MM-dd）
      end: ""
    rpt-print-date:
      #处理数据报告打印日期的起始日期（建议配置）（yyyy-MM-dd）
      start: "2023-01-01"
      #处理数据报告打印日期的结束日期（为空默认当天）（yyyy-MM-dd）
      end: ""
  #导出文件下载定时清理
  file-clean-time:
    #建议每天凌晨两点执行：0 0 2 * * ?
    checkCron: "-"
    #每次查询数据条数（建议1000）
    dataSize: "1000"
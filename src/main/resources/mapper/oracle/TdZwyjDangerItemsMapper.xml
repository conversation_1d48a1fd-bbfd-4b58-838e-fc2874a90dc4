<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwyjDangerItemsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwyjDangerItems">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="ITEM_ID" property="fkByItemId.rid" />
        <result column="DANGER_NAME" property="dangerName" />
        <result column="MSRUNT_ID" property="fkByMsruntId.rid" />
        <result column="GE_VAL" property="geVal" />
        <result column="GT_VAL" property="gtVal" />
        <result column="LE_VAL" property="leVal" />
        <result column="LT_VAL" property="ltVal" />
        <result column="IF_REVEAL" property="ifReveal" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.ITEM_ID,t.DANGER_NAME,t.MSRUNT_ID,t.GE_VAL,t.GT_VAL,t.LE_VAL,t.LT_VAL,t.IF_REVEAL,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByItemId != null and ${joiner}fkByItemId.rid != null">
            and ${mAlias}ITEM_ID = #{${joiner}fkByItemId.rid}
        </if>
        <if test="${joiner}dangerName != null and ${joiner}dangerName != ''">
            and ${mAlias}DANGER_NAME = #{${joiner}dangerName}
        </if>
        <if test="${joiner}fkByMsruntId != null and ${joiner}fkByMsruntId.rid != null">
            and ${mAlias}MSRUNT_ID = #{${joiner}fkByMsruntId.rid}
        </if>
        <if test="${joiner}geVal != null and ${joiner}geVal != ''">
            and ${mAlias}GE_VAL = #{${joiner}geVal}
        </if>
        <if test="${joiner}gtVal != null and ${joiner}gtVal != ''">
            and ${mAlias}GT_VAL = #{${joiner}gtVal}
        </if>
        <if test="${joiner}leVal != null and ${joiner}leVal != ''">
            and ${mAlias}LE_VAL = #{${joiner}leVal}
        </if>
        <if test="${joiner}ltVal != null and ${joiner}ltVal != ''">
            and ${mAlias}LT_VAL = #{${joiner}ltVal}
        </if>
        <if test="${joiner}ifReveal != null and ${joiner}ifReveal != ''">
            and ${mAlias}IF_REVEAL = #{${joiner}ifReveal}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByItemId != null and ${joiner}fkByItemId.rid != null">
                t.ITEM_ID = #{${joiner}fkByItemId.rid},
            </when>
            <otherwise>
                t.ITEM_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dangerName != null and ${joiner}dangerName != ''">
                t.DANGER_NAME = #{${joiner}dangerName},
            </when>
            <otherwise>
                t.DANGER_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMsruntId != null and ${joiner}fkByMsruntId.rid != null">
                t.MSRUNT_ID = #{${joiner}fkByMsruntId.rid},
            </when>
            <otherwise>
                t.MSRUNT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}geVal != null and ${joiner}geVal != ''">
                t.GE_VAL = #{${joiner}geVal},
            </when>
            <otherwise>
                t.GE_VAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}gtVal != null and ${joiner}gtVal != ''">
                t.GT_VAL = #{${joiner}gtVal},
            </when>
            <otherwise>
                t.GT_VAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}leVal != null and ${joiner}leVal != ''">
                t.LE_VAL = #{${joiner}leVal},
            </when>
            <otherwise>
                t.LE_VAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ltVal != null and ${joiner}ltVal != ''">
                t.LT_VAL = #{${joiner}ltVal},
            </when>
            <otherwise>
                t.LT_VAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifReveal != null and ${joiner}ifReveal != ''">
                t.IF_REVEAL = #{${joiner}ifReveal},
            </when>
            <otherwise>
                t.IF_REVEAL = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_DANGER_ITEMS t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_DANGER_ITEMS t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_DANGER_ITEMS t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwyjDangerItems" >
        update TD_ZWYJ_DANGER_ITEMS t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZWYJ_DANGER_ITEMS t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZwyjDangerItems">
        delete from TD_ZWYJ_DANGER_ITEMS
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->

    <!-- 通过项目rid集合 获取危急值项目维护 -->
    <select id="selectListByItemIds" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_DANGER_ITEMS t
        where t.IF_REVEAL = 1
        <if test="null != itemIdList and itemIdList.size > 0">
            AND t.ITEM_ID IN
            <foreach collection="itemIdList" item="item" index="index" separator="," open="(" close=")">
                <trim suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>


</mapper>

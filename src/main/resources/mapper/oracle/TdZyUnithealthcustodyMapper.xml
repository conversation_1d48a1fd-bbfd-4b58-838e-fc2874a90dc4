<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZyUnithealthcustodyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZyUnithealthcustody">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="IFHEA" property="ifhea" />
        <result column="CHECK_UNIT_NAMES" property="checkUnitNames" />
        <result column="CHECK_REPORT_NOS" property="checkReportNos" />
        <result column="IFHEA_DUST" property="ifheaDust" />
        <result column="HEA_DUST_PEOPLES" property="heaDustPeoples" />
        <result column="IFHEA_CHEMISTRY" property="ifheaChemistry" />
        <result column="HEA_CHEMISTRY_PEOPLES" property="heaChemistryPeoples" />
        <result column="HEA_CHEMISTRY_LEAD_PEOPLES" property="heaChemistryLeadPeoples" />
        <result column="HEA_TCHEMISTRY_BENZENE_PEOPLES" property="heaTchemistryBenzenePeoples" />
        <result column="IFHEA_PHYSICS" property="ifheaPhysics" />
        <result column="HEA_PHYSICS_PEOPLES" property="heaPhysicsPeoples" />
        <result column="HEA_PHYSICS_NOISE_PEOPLES" property="heaPhysicsNoisePeoples" />
        <result column="IFHEA_RADIOACTIVITY" property="ifheaRadioactivity" />
        <result column="HEA_RADIOACTIVITY_PEOPLES" property="heaRadioactivityPeoples" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MAIN_ID,t.IFHEA,t.CHECK_UNIT_NAMES,t.CHECK_REPORT_NOS,t.IFHEA_DUST,t.HEA_DUST_PEOPLES,t.IFHEA_CHEMISTRY,t.HEA_CHEMISTRY_PEOPLES,t.HEA_CHEMISTRY_LEAD_PEOPLES,t.HEA_TCHEMISTRY_BENZENE_PEOPLES,t.IFHEA_PHYSICS,t.HEA_PHYSICS_PEOPLES,t.HEA_PHYSICS_NOISE_PEOPLES,t.IFHEA_RADIOACTIVITY,t.HEA_RADIOACTIVITY_PEOPLES,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}ifhea != null">
            and ${mAlias}IFHEA = #{${joiner}ifhea}
        </if>
        <if test="${joiner}checkUnitNames != null">
            and ${mAlias}CHECK_UNIT_NAMES = #{${joiner}checkUnitNames}
        </if>
        <if test="${joiner}checkReportNos != null">
            and ${mAlias}CHECK_REPORT_NOS = #{${joiner}checkReportNos}
        </if>
        <if test="${joiner}ifheaDust != null">
            and ${mAlias}IFHEA_DUST = #{${joiner}ifheaDust}
        </if>
        <if test="${joiner}heaDustPeoples != null">
            and ${mAlias}HEA_DUST_PEOPLES = #{${joiner}heaDustPeoples}
        </if>
        <if test="${joiner}ifheaChemistry != null">
            and ${mAlias}IFHEA_CHEMISTRY = #{${joiner}ifheaChemistry}
        </if>
        <if test="${joiner}heaChemistryPeoples != null">
            and ${mAlias}HEA_CHEMISTRY_PEOPLES = #{${joiner}heaChemistryPeoples}
        </if>
        <if test="${joiner}heaChemistryLeadPeoples != null">
            and ${mAlias}HEA_CHEMISTRY_LEAD_PEOPLES = #{${joiner}heaChemistryLeadPeoples}
        </if>
        <if test="${joiner}heaTchemistryBenzenePeoples != null">
            and ${mAlias}HEA_TCHEMISTRY_BENZENE_PEOPLES = #{${joiner}heaTchemistryBenzenePeoples}
        </if>
        <if test="${joiner}ifheaPhysics != null">
            and ${mAlias}IFHEA_PHYSICS = #{${joiner}ifheaPhysics}
        </if>
        <if test="${joiner}heaPhysicsPeoples != null">
            and ${mAlias}HEA_PHYSICS_PEOPLES = #{${joiner}heaPhysicsPeoples}
        </if>
        <if test="${joiner}heaPhysicsNoisePeoples != null">
            and ${mAlias}HEA_PHYSICS_NOISE_PEOPLES = #{${joiner}heaPhysicsNoisePeoples}
        </if>
        <if test="${joiner}ifheaRadioactivity != null">
            and ${mAlias}IFHEA_RADIOACTIVITY = #{${joiner}ifheaRadioactivity}
        </if>
        <if test="${joiner}heaRadioactivityPeoples != null">
            and ${mAlias}HEA_RADIOACTIVITY_PEOPLES = #{${joiner}heaRadioactivityPeoples}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifhea != null">
                t.IFHEA = #{${joiner}ifhea},
            </when>
            <otherwise>
                t.IFHEA = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}checkUnitNames != null">
                t.CHECK_UNIT_NAMES = #{${joiner}checkUnitNames},
            </when>
            <otherwise>
                t.CHECK_UNIT_NAMES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}checkReportNos != null">
                t.CHECK_REPORT_NOS = #{${joiner}checkReportNos},
            </when>
            <otherwise>
                t.CHECK_REPORT_NOS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifheaDust != null">
                t.IFHEA_DUST = #{${joiner}ifheaDust},
            </when>
            <otherwise>
                t.IFHEA_DUST = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}heaDustPeoples != null">
                t.HEA_DUST_PEOPLES = #{${joiner}heaDustPeoples},
            </when>
            <otherwise>
                t.HEA_DUST_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifheaChemistry != null">
                t.IFHEA_CHEMISTRY = #{${joiner}ifheaChemistry},
            </when>
            <otherwise>
                t.IFHEA_CHEMISTRY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}heaChemistryPeoples != null">
                t.HEA_CHEMISTRY_PEOPLES = #{${joiner}heaChemistryPeoples},
            </when>
            <otherwise>
                t.HEA_CHEMISTRY_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}heaChemistryLeadPeoples != null">
                t.HEA_CHEMISTRY_LEAD_PEOPLES = #{${joiner}heaChemistryLeadPeoples},
            </when>
            <otherwise>
                t.HEA_CHEMISTRY_LEAD_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}heaTchemistryBenzenePeoples != null">
                t.HEA_TCHEMISTRY_BENZENE_PEOPLES = #{${joiner}heaTchemistryBenzenePeoples},
            </when>
            <otherwise>
                t.HEA_TCHEMISTRY_BENZENE_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifheaPhysics != null">
                t.IFHEA_PHYSICS = #{${joiner}ifheaPhysics},
            </when>
            <otherwise>
                t.IFHEA_PHYSICS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}heaPhysicsPeoples != null">
                t.HEA_PHYSICS_PEOPLES = #{${joiner}heaPhysicsPeoples},
            </when>
            <otherwise>
                t.HEA_PHYSICS_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}heaPhysicsNoisePeoples != null">
                t.HEA_PHYSICS_NOISE_PEOPLES = #{${joiner}heaPhysicsNoisePeoples},
            </when>
            <otherwise>
                t.HEA_PHYSICS_NOISE_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifheaRadioactivity != null">
                t.IFHEA_RADIOACTIVITY = #{${joiner}ifheaRadioactivity},
            </when>
            <otherwise>
                t.IFHEA_RADIOACTIVITY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}heaRadioactivityPeoples != null">
                t.HEA_RADIOACTIVITY_PEOPLES = #{${joiner}heaRadioactivityPeoples},
            </when>
            <otherwise>
                t.HEA_RADIOACTIVITY_PEOPLES = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITHEALTHCUSTODY t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITHEALTHCUSTODY t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITHEALTHCUSTODY t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdZyUnithealthcustody">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_ZY_UNITHEALTHCUSTODY_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_ZY_UNITHEALTHCUSTODY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            IFHEA,
            CHECK_UNIT_NAMES,
            CHECK_REPORT_NOS,
            IFHEA_DUST,
            HEA_DUST_PEOPLES,
            IFHEA_CHEMISTRY,
            HEA_CHEMISTRY_PEOPLES,
            HEA_CHEMISTRY_LEAD_PEOPLES,
            HEA_TCHEMISTRY_BENZENE_PEOPLES,
            IFHEA_PHYSICS,
            HEA_PHYSICS_PEOPLES,
            HEA_PHYSICS_NOISE_PEOPLES,
            IFHEA_RADIOACTIVITY,
            HEA_RADIOACTIVITY_PEOPLES,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{fkByMainId.rid},
            #{ifhea},
            #{checkUnitNames},
            #{checkReportNos},
            #{ifheaDust},
            #{heaDustPeoples},
            #{ifheaChemistry},
            #{heaChemistryPeoples},
            #{heaChemistryLeadPeoples},
            #{heaTchemistryBenzenePeoples},
            #{ifheaPhysics},
            #{heaPhysicsPeoples},
            #{heaPhysicsNoisePeoples},
            #{ifheaRadioactivity},
            #{heaRadioactivityPeoples},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_ZY_UNITHEALTHCUSTODY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            IFHEA,
            CHECK_UNIT_NAMES,
            CHECK_REPORT_NOS,
            IFHEA_DUST,
            HEA_DUST_PEOPLES,
            IFHEA_CHEMISTRY,
            HEA_CHEMISTRY_PEOPLES,
            HEA_CHEMISTRY_LEAD_PEOPLES,
            HEA_TCHEMISTRY_BENZENE_PEOPLES,
            IFHEA_PHYSICS,
            HEA_PHYSICS_PEOPLES,
            HEA_PHYSICS_NOISE_PEOPLES,
            IFHEA_RADIOACTIVITY,
            HEA_RADIOACTIVITY_PEOPLES,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_ZY_UNITHEALTHCUSTODY_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByMainId != null and item.fkByMainId.rid != null">
                            #{item.fkByMainId.rid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifhea != null">
                            #{item.ifhea} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.checkUnitNames != null">
                            #{item.checkUnitNames} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.checkReportNos != null">
                            #{item.checkReportNos} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifheaDust != null">
                            #{item.ifheaDust} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.heaDustPeoples != null">
                            #{item.heaDustPeoples} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifheaChemistry != null">
                            #{item.ifheaChemistry} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.heaChemistryPeoples != null">
                            #{item.heaChemistryPeoples} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.heaChemistryLeadPeoples != null">
                            #{item.heaChemistryLeadPeoples} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.heaTchemistryBenzenePeoples != null">
                            #{item.heaTchemistryBenzenePeoples} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifheaPhysics != null">
                            #{item.ifheaPhysics} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.heaPhysicsPeoples != null">
                            #{item.heaPhysicsPeoples} AS C16,
                        </when>
                        <otherwise>
                            NULL AS C16,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.heaPhysicsNoisePeoples != null">
                            #{item.heaPhysicsNoisePeoples} AS C17,
                        </when>
                        <otherwise>
                            NULL AS C17,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifheaRadioactivity != null">
                            #{item.ifheaRadioactivity} AS C18,
                        </when>
                        <otherwise>
                            NULL AS C18,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.heaRadioactivityPeoples != null">
                            #{item.heaRadioactivityPeoples} AS C19,
                        </when>
                        <otherwise>
                            NULL AS C19,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZyUnithealthcustody" >
        update TD_ZY_UNITHEALTHCUSTODY t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_ZY_UNITHEALTHCUSTODY t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZyUnithealthcustody">
        delete from TD_ZY_UNITHEALTHCUSTODY
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

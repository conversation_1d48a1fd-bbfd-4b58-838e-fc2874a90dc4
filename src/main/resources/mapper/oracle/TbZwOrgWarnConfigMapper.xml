<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TbZwOrgWarnConfigMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chis.modules.timer.heth.entity.TbZwOrgWarnConfig">
        <result column="RID" property="rid"/>
        <result column="CREATE_DATE" property="createDate"/>
        <result column="CREATE_MANID" property="createManid"/>
        <result column="MODIFY_DATE" property="modifyDate"/>
        <result column="MODIFY_MANID" property="modifyManid"/>
        <result column="BUS_TYPE" property="busType"/>
        <result column="WARN_TYPE" property="warnType"/>
        <result column="ITEM_ID" property="fkByItemId.rid"/>
        <result column="WARN_TYPE_DESC" property="warnTypeDesc"/>
        <result column="XH" property="xh"/>
        <result column="POST_ID" property="fkByPostId.rid"/>
        <result column="INST_TYPE_ID" property="fkByInstTypeId.rid"/>
        <result column="MIN_NUMS" property="minNums"/>
        <result column="TRAIN_YEAR" property="trainYear"/>
        <result column="TITLE_ID" property="fkByTitleId.rid"/>
        <result column="TITLE_EXTENDS2" property="fkByTitleId.extends2"/>
        <result column="ORG_NUMS" property="orgNums"/>
        <result column="IF_TEST_INST" property="ifTestInst"/>
        <result column="IF_DEL" property="ifDel"/>
        <result column="WARN_TYPE_ID" property="warnTypeId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BUS_TYPE,t.WARN_TYPE,t.ITEM_ID,t.WARN_TYPE_DESC,t.XH,t.POST_ID,t.INST_TYPE_ID,t.MIN_NUMS,t.TRAIN_YEAR,t.TITLE_ID,t.ORG_NUMS,t.IF_TEST_INST,t.IF_DEL,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}busType != null and ${joiner}busType != ''">
            and ${mAlias}BUS_TYPE = #{${joiner}busType}
        </if>
        <if test="${joiner}warnType != null and ${joiner}warnType != ''">
            and ${mAlias}WARN_TYPE = #{${joiner}warnType}
        </if>
        <if test="${joiner}fkByItemId != null and ${joiner}fkByItemId.rid != null">
            and ${mAlias}ITEM_ID = #{${joiner}fkByItemId.rid}
        </if>
        <if test="${joiner}warnTypeDesc != null and ${joiner}warnTypeDesc != ''">
            and ${mAlias}WARN_TYPE_DESC = #{${joiner}warnTypeDesc}
        </if>
        <if test="${joiner}xh != null and ${joiner}xh != ''">
            and ${mAlias}XH = #{${joiner}xh}
        </if>
        <if test="${joiner}fkByPostId != null and ${joiner}fkByPostId.rid != null">
            and ${mAlias}POST_ID = #{${joiner}fkByPostId.rid}
        </if>
        <if test="${joiner}fkByInstTypeId != null and ${joiner}fkByInstTypeId.rid != null">
            and ${mAlias}INST_TYPE_ID = #{${joiner}fkByInstTypeId.rid}
        </if>
        <if test="${joiner}minNums != null and ${joiner}minNums != ''">
            and ${mAlias}MIN_NUMS = #{${joiner}minNums}
        </if>
        <if test="${joiner}trainYear != null and ${joiner}trainYear != ''">
            and ${mAlias}TRAIN_YEAR = #{${joiner}trainYear}
        </if>
        <if test="${joiner}fkByTitleId != null and ${joiner}fkByTitleId.rid != null">
            and ${mAlias}TITLE_ID = #{${joiner}fkByTitleId.rid}
        </if>
        <if test="${joiner}orgNums != null and ${joiner}orgNums != ''">
            and ${mAlias}ORG_NUMS = #{${joiner}orgNums}
        </if>
        <if test="${joiner}ifTestInst != null and ${joiner}ifTestInst != ''">
            and ${mAlias}IF_TEST_INST = #{${joiner}ifTestInst}
        </if>
        <if test="${joiner}ifDel != null and ${joiner}ifDel != ''">
            and ${mAlias}IF_DEL = #{${joiner}ifDel}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
        t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}busType != null and ${joiner}busType != ''">
                t.BUS_TYPE = #{${joiner}busType},
            </when>
            <otherwise>
                t.BUS_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}warnType != null and ${joiner}warnType != ''">
                t.WARN_TYPE = #{${joiner}warnType},
            </when>
            <otherwise>
                t.WARN_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByItemId != null and ${joiner}fkByItemId.rid != null">
                t.ITEM_ID = #{${joiner}fkByItemId.rid},
            </when>
            <otherwise>
                t.ITEM_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}warnTypeDesc != null and ${joiner}warnTypeDesc != ''">
                t.WARN_TYPE_DESC = #{${joiner}warnTypeDesc},
            </when>
            <otherwise>
                t.WARN_TYPE_DESC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}xh != null and ${joiner}xh != ''">
                t.XH = #{${joiner}xh},
            </when>
            <otherwise>
                t.XH = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByPostId != null and ${joiner}fkByPostId.rid != null">
                t.POST_ID = #{${joiner}fkByPostId.rid},
            </when>
            <otherwise>
                t.POST_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByInstTypeId != null and ${joiner}fkByInstTypeId.rid != null">
                t.INST_TYPE_ID = #{${joiner}fkByInstTypeId.rid},
            </when>
            <otherwise>
                t.INST_TYPE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}minNums != null and ${joiner}minNums != ''">
                t.MIN_NUMS = #{${joiner}minNums},
            </when>
            <otherwise>
                t.MIN_NUMS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}trainYear != null and ${joiner}trainYear != ''">
                t.TRAIN_YEAR = #{${joiner}trainYear},
            </when>
            <otherwise>
                t.TRAIN_YEAR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByTitleId != null and ${joiner}fkByTitleId.rid != null">
                t.TITLE_ID = #{${joiner}fkByTitleId.rid},
            </when>
            <otherwise>
                t.TITLE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}orgNums != null and ${joiner}orgNums != ''">
                t.ORG_NUMS = #{${joiner}orgNums},
            </when>
            <otherwise>
                t.ORG_NUMS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifTestInst != null and ${joiner}ifTestInst != ''">
                t.IF_TEST_INST = #{${joiner}ifTestInst},
            </when>
            <otherwise>
                t.IF_TEST_INST = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifDel != null and ${joiner}ifDel != ''">
                t.IF_DEL = #{${joiner}ifDel},
            </when>
            <otherwise>
                t.IF_DEL = null,
            </otherwise>
        </choose>
    </sql>


    <!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from TB_ZW_ORG_WARN_CONFIG t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX
        ) WHERE 1=1
        <if test="first != null and pageSize != null">
            AND RN BETWEEN #{first} AND #{pageSize}
        </if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from TB_ZW_ORG_WARN_CONFIG t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from TB_ZW_ORG_WARN_CONFIG t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TbZwOrgWarnConfig">
        update TB_ZW_ORG_WARN_CONFIG t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TB_ZW_ORG_WARN_CONFIG t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TbZwOrgWarnConfig">
        delete from TB_ZW_ORG_WARN_CONFIG
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

    <!-- ========================自定义方法====================================== -->

    <select id="dataAndSubList" resultMap="BaseResultMap">
        SELECT C.RID,
               C.BUS_TYPE,
               C.WARN_TYPE,
               C.ITEM_ID,
               C.WARN_TYPE_DESC,
               C.XH,
               C.POST_ID,
               C.INST_TYPE_ID,
               NVL(C.MIN_NUMS, 0) AS MIN_NUMS,
               C.TRAIN_YEAR,
               C.TITLE_ID,
               SC.EXTENDS2 AS TITLE_EXTENDS2,
               C.ORG_NUMS,
               C.IF_TEST_INST,
               C.IF_DEL,
               S.WARN_TYPE_ID
        FROM TB_ZW_ORG_WARN_CONFIG C
                 LEFT JOIN TB_ZW_ORG_WARN_SUB S ON C.RID = S.MAIN_ID
                 LEFT JOIN TS_SIMPLE_CODE SC ON C.TITLE_ID = SC.RID
        WHERE NVL(C.IF_DEL, 0) = 0
          AND C.BUS_TYPE = #{busType}
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdTjBhkAbnomalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdTjBhkAbnomal">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="BHK_ID" property="bhkId" />
        <result column="TYPE" property="type" />
        <result column="ABNOMAL_INFO" property="abnomalInfo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_ID,t.TYPE,t.ABNOMAL_INFO,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}bhkId != null">
            and ${mAlias}BHK_ID = #{${joiner}bhkId}
        </if>
        <if test="${joiner}type != null">
            and ${mAlias}TYPE = #{${joiner}type}
        </if>
        <if test="${joiner}abnomalInfo != null">
            and ${mAlias}ABNOMAL_INFO = #{${joiner}abnomalInfo}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}bhkId != null">
                t.BHK_ID = #{${joiner}bhkId},
            </when>
            <otherwise>
                t.BHK_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}type != null">
                t.TYPE = #{${joiner}type},
            </when>
            <otherwise>
                t.TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}abnomalInfo != null">
                t.ABNOMAL_INFO = #{${joiner}abnomalInfo},
            </when>
            <otherwise>
                t.ABNOMAL_INFO = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_BHK_ABNOMAL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_BHK_ABNOMAL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_BHK_ABNOMAL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdTjBhkAbnomal">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_TJ_BHK_ABNOMAL_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_TJ_BHK_ABNOMAL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            BHK_ID,
            TYPE,
            ABNOMAL_INFO,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{bhkId},
            #{type},
            #{abnomalInfo},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_TJ_BHK_ABNOMAL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            BHK_ID,
            TYPE,
            ABNOMAL_INFO,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_TJ_BHK_ABNOMAL_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.bhkId != null">
                            #{item.bhkId} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.type != null">
                            #{item.type} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.abnomalInfo != null">
                            #{item.abnomalInfo} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdTjBhkAbnomal" >
        update TD_TJ_BHK_ABNOMAL t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_TJ_BHK_ABNOMAL t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdTjBhkAbnomal">
        delete from TD_TJ_BHK_ABNOMAL
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <delete id="deleteBhkAbnomalByBhkIdList" >
        delete from TD_TJ_BHK_ABNOMAL
        <where>
            <if test="null != bhkIdList and bhkIdList.size > 0">
                AND BHK_ID IN
                <foreach collection="bhkIdList" item="item" index="index" separator="," open="(" close=")">
                    <trim  suffixOverrides=",">
                        #{item}
                    </trim>
                </foreach>
            </if>
        </where>
    </delete>

    <select id="findBhkAbnomals" resultType="com.chis.modules.timer.heth.entity.TdTjBhkAbnomal">
        SELECT
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_BHK_ABNOMAL t
        left join TD_TJ_BHK T1 on t.BHK_ID=T1.RID
        WHERE 1=1
        <if test="ridList != null and ridList.size > 0">
            AND t.BHK_ID IN
            <foreach collection="ridList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
        order by t.TYPE
    </select>


</mapper>

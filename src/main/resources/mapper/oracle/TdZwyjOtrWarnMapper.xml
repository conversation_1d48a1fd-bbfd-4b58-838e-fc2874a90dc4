<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwyjOtrWarnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwyjOtrWarn">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="CITY_ZONE_ID" property="fkByCityZoneId.rid" />
        <result column="WARN_TYPE" property="warnType" />
        <result column="BHKORG_ID" property="fkByBhkorgId.rid" />
        <result column="BHKORG_ZONE_ID" property="fkByBhkorgZoneId.rid" />
        <result column="WARN_CONT" property="warnCont" />
        <result column="HAPPEN_DATE" property="happenDate" />
        <result column="OTR_PSNS" property="otrPsns" />
        <result column="DEAL_ORG_ID" property="fkByDealOrgId.rid" />
        <result column="IF_OUT_RANGE" property="ifOutRange" />
        <result column="DEAL_CONT" property="dealCont" />
        <result column="DEAL_DATE" property="dealDate" />
        <result column="CHECK_ORG_ID" property="fkByCheckOrgId.rid" />
        <result column="CHECK_RST" property="checkRst" />
        <result column="CHECK_CONT" property="checkCont" />
        <result column="CHECK_DATE" property="checkDate" />
        <result column="STATE_MARK" property="stateMark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.CITY_ZONE_ID,t.WARN_TYPE,t.BHKORG_ID,t.BHKORG_ZONE_ID,t.WARN_CONT,t.HAPPEN_DATE,t.OTR_PSNS,t.DEAL_ORG_ID,t.IF_OUT_RANGE,t.DEAL_CONT,t.DEAL_DATE,t.CHECK_ORG_ID,t.CHECK_RST,t.CHECK_CONT,t.CHECK_DATE,t.STATE_MARK,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByCityZoneId != null and ${joiner}fkByCityZoneId.rid != null">
            and ${mAlias}CITY_ZONE_ID = #{${joiner}fkByCityZoneId.rid}
        </if>
        <if test="${joiner}warnType != null and ${joiner}warnType != ''">
            and ${mAlias}WARN_TYPE = #{${joiner}warnType}
        </if>
        <if test="${joiner}fkByBhkorgId != null and ${joiner}fkByBhkorgId.rid != null">
            and ${mAlias}BHKORG_ID = #{${joiner}fkByBhkorgId.rid}
        </if>
        <if test="${joiner}fkByBhkorgZoneId != null and ${joiner}fkByBhkorgZoneId.rid != null">
            and ${mAlias}BHKORG_ZONE_ID = #{${joiner}fkByBhkorgZoneId.rid}
        </if>
        <if test="${joiner}warnCont != null and ${joiner}warnCont != ''">
            and ${mAlias}WARN_CONT = #{${joiner}warnCont}
        </if>
        <if test="${joiner}happenDate != null">
            and ${mAlias}HAPPEN_DATE = #{${joiner}happenDate}
        </if>
        <if test="${joiner}otrPsns != null and ${joiner}otrPsns != ''">
            and ${mAlias}OTR_PSNS = #{${joiner}otrPsns}
        </if>
        <if test="${joiner}fkByDealOrgId != null and ${joiner}fkByDealOrgId.rid != null">
            and ${mAlias}DEAL_ORG_ID = #{${joiner}fkByDealOrgId.rid}
        </if>
        <if test="${joiner}ifOutRange != null and ${joiner}ifOutRange != ''">
            and ${mAlias}IF_OUT_RANGE = #{${joiner}ifOutRange}
        </if>
        <if test="${joiner}dealCont != null and ${joiner}dealCont != ''">
            and ${mAlias}DEAL_CONT = #{${joiner}dealCont}
        </if>
        <if test="${joiner}dealDate != null">
            and ${mAlias}DEAL_DATE = #{${joiner}dealDate}
        </if>
        <if test="${joiner}fkByCheckOrgId != null and ${joiner}fkByCheckOrgId.rid != null">
            and ${mAlias}CHECK_ORG_ID = #{${joiner}fkByCheckOrgId.rid}
        </if>
        <if test="${joiner}checkRst != null and ${joiner}checkRst != ''">
            and ${mAlias}CHECK_RST = #{${joiner}checkRst}
        </if>
        <if test="${joiner}checkCont != null and ${joiner}checkCont != ''">
            and ${mAlias}CHECK_CONT = #{${joiner}checkCont}
        </if>
        <if test="${joiner}checkDate != null">
            and ${mAlias}CHECK_DATE = #{${joiner}checkDate}
        </if>
        <if test="${joiner}stateMark != null and ${joiner}stateMark != ''">
            and ${mAlias}STATE_MARK = #{${joiner}stateMark}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCityZoneId != null and ${joiner}fkByCityZoneId.rid != null">
                t.CITY_ZONE_ID = #{${joiner}fkByCityZoneId.rid},
            </when>
            <otherwise>
                t.CITY_ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}warnType != null and ${joiner}warnType != ''">
                t.WARN_TYPE = #{${joiner}warnType},
            </when>
            <otherwise>
                t.WARN_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByBhkorgId != null and ${joiner}fkByBhkorgId.rid != null">
                t.BHKORG_ID = #{${joiner}fkByBhkorgId.rid},
            </when>
            <otherwise>
                t.BHKORG_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByBhkorgZoneId != null and ${joiner}fkByBhkorgZoneId.rid != null">
                t.BHKORG_ZONE_ID = #{${joiner}fkByBhkorgZoneId.rid},
            </when>
            <otherwise>
                t.BHKORG_ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}warnCont != null and ${joiner}warnCont != ''">
                t.WARN_CONT = #{${joiner}warnCont},
            </when>
            <otherwise>
                t.WARN_CONT = null,
            </otherwise>
        </choose>
            t.HAPPEN_DATE = #{${joiner}happenDate},
        <choose>
            <when test="${joiner}otrPsns != null and ${joiner}otrPsns != ''">
                t.OTR_PSNS = #{${joiner}otrPsns},
            </when>
            <otherwise>
                t.OTR_PSNS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByDealOrgId != null and ${joiner}fkByDealOrgId.rid != null">
                t.DEAL_ORG_ID = #{${joiner}fkByDealOrgId.rid},
            </when>
            <otherwise>
                t.DEAL_ORG_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifOutRange != null and ${joiner}ifOutRange != ''">
                t.IF_OUT_RANGE = #{${joiner}ifOutRange},
            </when>
            <otherwise>
                t.IF_OUT_RANGE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dealCont != null and ${joiner}dealCont != ''">
                t.DEAL_CONT = #{${joiner}dealCont},
            </when>
            <otherwise>
                t.DEAL_CONT = null,
            </otherwise>
        </choose>
            t.DEAL_DATE = #{${joiner}dealDate},
        <choose>
            <when test="${joiner}fkByCheckOrgId != null and ${joiner}fkByCheckOrgId.rid != null">
                t.CHECK_ORG_ID = #{${joiner}fkByCheckOrgId.rid},
            </when>
            <otherwise>
                t.CHECK_ORG_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}checkRst != null and ${joiner}checkRst != ''">
                t.CHECK_RST = #{${joiner}checkRst},
            </when>
            <otherwise>
                t.CHECK_RST = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}checkCont != null and ${joiner}checkCont != ''">
                t.CHECK_CONT = #{${joiner}checkCont},
            </when>
            <otherwise>
                t.CHECK_CONT = null,
            </otherwise>
        </choose>
            t.CHECK_DATE = #{${joiner}checkDate},
        <choose>
            <when test="${joiner}stateMark != null and ${joiner}stateMark != ''">
                t.STATE_MARK = #{${joiner}stateMark},
            </when>
            <otherwise>
                t.STATE_MARK = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_OTR_WARN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_OTR_WARN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_OTR_WARN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwyjOtrWarn" >
        update TD_ZWYJ_OTR_WARN t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZWYJ_OTR_WARN t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZwyjOtrWarn">
        delete from TD_ZWYJ_OTR_WARN
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

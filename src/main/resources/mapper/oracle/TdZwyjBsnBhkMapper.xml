<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwyjBsnBhkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwyjBsnBhk">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="BHK_CODE" property="bhkCode" />
        <result column="BHKORG_ID" property="fkByBhkorgId.rid" />
        <result column="CRPT_ID" property="fkByCrptId.rid" />
        <result column="CRPT_NAME" property="crptName" />
        <result column="PERSON_NAME" property="personName" />
        <result column="SEX" property="sex" />
        <result column="CARD_TYPE_ID" property="fkByCardTypeId.rid" />
        <result column="IDC" property="idc" />
        <result column="PSN_TYPE" property="psnType" />
        <result column="BRTH" property="brth" />
        <result column="AGE" property="age" />
        <result column="ISXMRD" property="isxmrd" />
        <result column="LNKTEL" property="lnktel" />
        <result column="DPT" property="dpt" />
        <result column="WRKNUM" property="wrknum" />
        <result column="WRKLNT" property="wrklnt" />
        <result column="WRKLNTMONTH" property="wrklntmonth" />
        <result column="TCHBADRSNTIM" property="tchbadrsntim" />
        <result column="TCHBADRSNMONTH" property="tchbadrsnmonth" />
        <result column="WORK_NAME" property="workName" />
        <result column="BHK_TYPE" property="bhkType" />
        <result column="ONGUARD_STATEID" property="fkByOnguardStateid.rid" />
        <result column="BHK_DATE" property="bhkDate" />
        <result column="BHKRST" property="bhkrst" />
        <result column="MHKADV" property="mhkadv" />
        <result column="OCP_BHKRSTDES" property="ocpBhkrstdes" />
        <result column="JDGDAT" property="jdgdat" />
        <result column="BADRSN" property="badrsn" />
        <result column="RPT_PRINT_DATE" property="rptPrintDate" />
        <result column="IF_RHK" property="ifRhk" />
        <result column="LAST_BHK_CODE" property="lastBhkCode" />
        <result column="LAST_FST_BHK_CODE" property="lastFstBhkCode" />
        <result column="DATA_UP_DATE" property="dataUpDate" />
        <result column="UUID" property="uuid" />
        <result column="ENTRUST_CRPT_ID" property="entrustId.rid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.CRPT_NAME,t.PERSON_NAME,t.SEX,t.CARD_TYPE_ID,t.IDC,t.PSN_TYPE,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.JDGDAT,t.BADRSN,t.RPT_PRINT_DATE,t.IF_RHK,t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.DATA_UP_DATE,t.UUID,
             t.ENTRUST_CRPT_ID,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}bhkCode != null and ${joiner}bhkCode != ''">
            and ${mAlias}BHK_CODE = #{${joiner}bhkCode}
        </if>
        <if test="${joiner}fkByBhkorgId != null and ${joiner}fkByBhkorgId.rid != null">
            and ${mAlias}BHKORG_ID = #{${joiner}fkByBhkorgId.rid}
        </if>
        <if test="${joiner}fkByCrptId != null and ${joiner}fkByCrptId.rid != null">
            and ${mAlias}CRPT_ID = #{${joiner}fkByCrptId.rid}
        </if>
        <if test="${joiner}crptName != null and ${joiner}crptName != ''">
            and ${mAlias}CRPT_NAME = #{${joiner}crptName}
        </if>
        <if test="${joiner}personName != null and ${joiner}personName != ''">
            and ${mAlias}PERSON_NAME = #{${joiner}personName}
        </if>
        <if test="${joiner}sex != null and ${joiner}sex != ''">
            and ${mAlias}SEX = #{${joiner}sex}
        </if>
        <if test="${joiner}fkByCardTypeId != null and ${joiner}fkByCardTypeId.rid != null">
            and ${mAlias}CARD_TYPE_ID = #{${joiner}fkByCardTypeId.rid}
        </if>
        <if test="${joiner}idc != null and ${joiner}idc != ''">
            and ${mAlias}IDC = #{${joiner}idc}
        </if>
        <if test="${joiner}psnType != null and ${joiner}psnType != ''">
            and ${mAlias}PSN_TYPE = #{${joiner}psnType}
        </if>
        <if test="${joiner}brth != null">
            and ${mAlias}BRTH = #{${joiner}brth}
        </if>
        <if test="${joiner}age != null and ${joiner}age != ''">
            and ${mAlias}AGE = #{${joiner}age}
        </if>
        <if test="${joiner}isxmrd != null and ${joiner}isxmrd != ''">
            and ${mAlias}ISXMRD = #{${joiner}isxmrd}
        </if>
        <if test="${joiner}lnktel != null and ${joiner}lnktel != ''">
            and ${mAlias}LNKTEL = #{${joiner}lnktel}
        </if>
        <if test="${joiner}dpt != null and ${joiner}dpt != ''">
            and ${mAlias}DPT = #{${joiner}dpt}
        </if>
        <if test="${joiner}wrknum != null and ${joiner}wrknum != ''">
            and ${mAlias}WRKNUM = #{${joiner}wrknum}
        </if>
        <if test="${joiner}wrklnt != null and ${joiner}wrklnt != ''">
            and ${mAlias}WRKLNT = #{${joiner}wrklnt}
        </if>
        <if test="${joiner}wrklntmonth != null and ${joiner}wrklntmonth != ''">
            and ${mAlias}WRKLNTMONTH = #{${joiner}wrklntmonth}
        </if>
        <if test="${joiner}tchbadrsntim != null and ${joiner}tchbadrsntim != ''">
            and ${mAlias}TCHBADRSNTIM = #{${joiner}tchbadrsntim}
        </if>
        <if test="${joiner}tchbadrsnmonth != null and ${joiner}tchbadrsnmonth != ''">
            and ${mAlias}TCHBADRSNMONTH = #{${joiner}tchbadrsnmonth}
        </if>
        <if test="${joiner}workName != null and ${joiner}workName != ''">
            and ${mAlias}WORK_NAME = #{${joiner}workName}
        </if>
        <if test="${joiner}bhkType != null and ${joiner}bhkType != ''">
            and ${mAlias}BHK_TYPE = #{${joiner}bhkType}
        </if>
        <if test="${joiner}fkByOnguardStateid != null and ${joiner}fkByOnguardStateid.rid != null">
            and ${mAlias}ONGUARD_STATEID = #{${joiner}fkByOnguardStateid.rid}
        </if>
        <if test="${joiner}bhkDate != null">
            and ${mAlias}BHK_DATE = #{${joiner}bhkDate}
        </if>
        <if test="${joiner}bhkrst != null">
            and ${mAlias}BHKRST = #{${joiner}bhkrst}
        </if>
        <if test="${joiner}mhkadv != null">
            and ${mAlias}MHKADV = #{${joiner}mhkadv}
        </if>
        <if test="${joiner}ocpBhkrstdes != null and ${joiner}ocpBhkrstdes != ''">
            and ${mAlias}OCP_BHKRSTDES = #{${joiner}ocpBhkrstdes}
        </if>
        <if test="${joiner}jdgdat != null">
            and ${mAlias}JDGDAT = #{${joiner}jdgdat}
        </if>
        <if test="${joiner}badrsn != null and ${joiner}badrsn != ''">
            and ${mAlias}BADRSN = #{${joiner}badrsn}
        </if>
        <if test="${joiner}rptPrintDate != null">
            and ${mAlias}RPT_PRINT_DATE = #{${joiner}rptPrintDate}
        </if>
        <if test="${joiner}ifRhk != null and ${joiner}ifRhk != ''">
            and ${mAlias}IF_RHK = #{${joiner}ifRhk}
        </if>
        <if test="${joiner}lastBhkCode != null and ${joiner}lastBhkCode != ''">
            and ${mAlias}LAST_BHK_CODE = #{${joiner}lastBhkCode}
        </if>
        <if test="${joiner}lastFstBhkCode != null and ${joiner}lastFstBhkCode != ''">
            and ${mAlias}LAST_FST_BHK_CODE = #{${joiner}lastFstBhkCode}
        </if>
        <if test="${joiner}dataUpDate != null">
            and ${mAlias}DATA_UP_DATE = #{${joiner}dataUpDate}
        </if>
        <if test="${joiner}uuid != null and ${joiner}uuid != ''">
            and ${mAlias}UUID = #{${joiner}uuid}
        </if>
        <if test="${joiner}entrustId != null and ${joiner}entrustId.rid != null">
            and ${mAlias}ENTRUST_CRPT_ID = #{${joiner}entrustId.rid}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}bhkCode != null and ${joiner}bhkCode != ''">
                t.BHK_CODE = #{${joiner}bhkCode},
            </when>
            <otherwise>
                t.BHK_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByBhkorgId != null and ${joiner}fkByBhkorgId.rid != null">
                t.BHKORG_ID = #{${joiner}fkByBhkorgId.rid},
            </when>
            <otherwise>
                t.BHKORG_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCrptId != null and ${joiner}fkByCrptId.rid != null">
                t.CRPT_ID = #{${joiner}fkByCrptId.rid},
            </when>
            <otherwise>
                t.CRPT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}crptName != null and ${joiner}crptName != ''">
                t.CRPT_NAME = #{${joiner}crptName},
            </when>
            <otherwise>
                t.CRPT_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}personName != null and ${joiner}personName != ''">
                t.PERSON_NAME = #{${joiner}personName},
            </when>
            <otherwise>
                t.PERSON_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}sex != null and ${joiner}sex != ''">
                t.SEX = #{${joiner}sex},
            </when>
            <otherwise>
                t.SEX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCardTypeId != null and ${joiner}fkByCardTypeId.rid != null">
                t.CARD_TYPE_ID = #{${joiner}fkByCardTypeId.rid},
            </when>
            <otherwise>
                t.CARD_TYPE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}idc != null and ${joiner}idc != ''">
                t.IDC = #{${joiner}idc},
            </when>
            <otherwise>
                t.IDC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}psnType != null and ${joiner}psnType != ''">
                t.PSN_TYPE = #{${joiner}psnType},
            </when>
            <otherwise>
                t.PSN_TYPE = null,
            </otherwise>
        </choose>
            t.BRTH = #{${joiner}brth},
        <choose>
            <when test="${joiner}age != null and ${joiner}age != ''">
                t.AGE = #{${joiner}age},
            </when>
            <otherwise>
                t.AGE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}isxmrd != null and ${joiner}isxmrd != ''">
                t.ISXMRD = #{${joiner}isxmrd},
            </when>
            <otherwise>
                t.ISXMRD = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lnktel != null and ${joiner}lnktel != ''">
                t.LNKTEL = #{${joiner}lnktel},
            </when>
            <otherwise>
                t.LNKTEL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dpt != null and ${joiner}dpt != ''">
                t.DPT = #{${joiner}dpt},
            </when>
            <otherwise>
                t.DPT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}wrknum != null and ${joiner}wrknum != ''">
                t.WRKNUM = #{${joiner}wrknum},
            </when>
            <otherwise>
                t.WRKNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}wrklnt != null and ${joiner}wrklnt != ''">
                t.WRKLNT = #{${joiner}wrklnt},
            </when>
            <otherwise>
                t.WRKLNT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}wrklntmonth != null and ${joiner}wrklntmonth != ''">
                t.WRKLNTMONTH = #{${joiner}wrklntmonth},
            </when>
            <otherwise>
                t.WRKLNTMONTH = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}tchbadrsntim != null and ${joiner}tchbadrsntim != ''">
                t.TCHBADRSNTIM = #{${joiner}tchbadrsntim},
            </when>
            <otherwise>
                t.TCHBADRSNTIM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}tchbadrsnmonth != null and ${joiner}tchbadrsnmonth != ''">
                t.TCHBADRSNMONTH = #{${joiner}tchbadrsnmonth},
            </when>
            <otherwise>
                t.TCHBADRSNMONTH = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}workName != null and ${joiner}workName != ''">
                t.WORK_NAME = #{${joiner}workName},
            </when>
            <otherwise>
                t.WORK_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}bhkType != null and ${joiner}bhkType != ''">
                t.BHK_TYPE = #{${joiner}bhkType},
            </when>
            <otherwise>
                t.BHK_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByOnguardStateid != null and ${joiner}fkByOnguardStateid.rid != null">
                t.ONGUARD_STATEID = #{${joiner}fkByOnguardStateid.rid},
            </when>
            <otherwise>
                t.ONGUARD_STATEID = null,
            </otherwise>
        </choose>
            t.BHK_DATE = #{${joiner}bhkDate},
            t.BHKRST = #{${joiner}bhkrst},
            t.MHKADV = #{${joiner}mhkadv},
        <choose>
            <when test="${joiner}ocpBhkrstdes != null and ${joiner}ocpBhkrstdes != ''">
                t.OCP_BHKRSTDES = #{${joiner}ocpBhkrstdes},
            </when>
            <otherwise>
                t.OCP_BHKRSTDES = null,
            </otherwise>
        </choose>
            t.JDGDAT = #{${joiner}jdgdat},
        <choose>
            <when test="${joiner}badrsn != null and ${joiner}badrsn != ''">
                t.BADRSN = #{${joiner}badrsn},
            </when>
            <otherwise>
                t.BADRSN = null,
            </otherwise>
        </choose>
            t.RPT_PRINT_DATE = #{${joiner}rptPrintDate},
        <choose>
            <when test="${joiner}ifRhk != null and ${joiner}ifRhk != ''">
                t.IF_RHK = #{${joiner}ifRhk},
            </when>
            <otherwise>
                t.IF_RHK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lastBhkCode != null and ${joiner}lastBhkCode != ''">
                t.LAST_BHK_CODE = #{${joiner}lastBhkCode},
            </when>
            <otherwise>
                t.LAST_BHK_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lastFstBhkCode != null and ${joiner}lastFstBhkCode != ''">
                t.LAST_FST_BHK_CODE = #{${joiner}lastFstBhkCode},
            </when>
            <otherwise>
                t.LAST_FST_BHK_CODE = null,
            </otherwise>
        </choose>
            t.DATA_UP_DATE = #{${joiner}dataUpDate},
        <choose>
            <when test="${joiner}uuid != null and ${joiner}uuid != ''">
                t.UUID = #{${joiner}uuid},
            </when>
            <otherwise>
                t.UUID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}entrustId.rid != null and ${joiner}entrustId.rid != ''">
                t.ENTRUST_CRPT_ID = #{${joiner}entrustId.rid},
            </when>
            <otherwise>
                t.ENTRUST_CRPT_ID = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_BSN_BHK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_BSN_BHK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_BSN_BHK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwyjBsnBhk" >
        update TD_ZWYJ_BSN_BHK t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZWYJ_BSN_BHK t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZwyjBsnBhk">
        delete from TD_ZWYJ_BSN_BHK
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

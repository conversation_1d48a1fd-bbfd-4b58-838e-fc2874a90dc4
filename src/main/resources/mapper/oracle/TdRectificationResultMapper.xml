<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdRectificationResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdRectificationResult">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="FACTOR_ID" property="fkByFactorId.rid" />
        <result column="ITEM_ID" property="fkByItemId.rid" />
        <result column="JOB_NAME" property="jobName" />
        <result column="JUDGEMENT_RESULT" property="judgementResult" />
        <result column="COMPOSITIVE_RESULT" property="compositiveResult" />
        <result column="PREVIOUS_TWA" property="previousTwa" />
        <result column="CURRENT_TWA" property="currentTwa" />
        <result column="FINAL_TWA" property="finalTwa" />
        <result column="PREVIOUS_STEL" property="previousStel" />
        <result column="CURRENT_STEL" property="currentStel" />
        <result column="FINAL_STEL" property="finalStel" />
        <result column="PREVIOUS_MAC" property="previousMac" />
        <result column="CURRENT_MAC" property="currentMac" />
        <result column="FINAL_MAC" property="finalMac" />
        <result column="PREVIOUS_PE" property="previousPe" />
        <result column="CURRENT_PE" property="currentPe" />
        <result column="FINAL_PE" property="finalPe" />
        <result column="PREVIOUS_LEX" property="previousLex" />
        <result column="CURRENT_LEX" property="currentLex" />
        <result column="FINAL_LEX" property="finalLex" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MAIN_ID,t.FACTOR_ID,t.ITEM_ID,t.JOB_NAME,t.JUDGEMENT_RESULT,t.COMPOSITIVE_RESULT,t.PREVIOUS_TWA,t.CURRENT_TWA,t.FINAL_TWA,t.PREVIOUS_STEL,t.CURRENT_STEL,t.FINAL_STEL,t.PREVIOUS_MAC,t.CURRENT_MAC,t.FINAL_MAC,t.PREVIOUS_PE,t.CURRENT_PE,t.FINAL_PE,t.PREVIOUS_LEX,t.CURRENT_LEX,t.FINAL_LEX,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}fkByFactorId != null and ${joiner}fkByFactorId.rid != null">
            and ${mAlias}FACTOR_ID = #{${joiner}fkByFactorId.rid}
        </if>
        <if test="${joiner}fkByItemId != null and ${joiner}fkByItemId.rid != null">
            and ${mAlias}ITEM_ID = #{${joiner}fkByItemId.rid}
        </if>
        <if test="${joiner}jobName != null">
            and ${mAlias}JOB_NAME = #{${joiner}jobName}
        </if>
        <if test="${joiner}judgementResult != null">
            and ${mAlias}JUDGEMENT_RESULT = #{${joiner}judgementResult}
        </if>
        <if test="${joiner}compositiveResult != null">
            and ${mAlias}COMPOSITIVE_RESULT = #{${joiner}compositiveResult}
        </if>
        <if test="${joiner}previousTwa != null">
            and ${mAlias}PREVIOUS_TWA = #{${joiner}previousTwa}
        </if>
        <if test="${joiner}currentTwa != null">
            and ${mAlias}CURRENT_TWA = #{${joiner}currentTwa}
        </if>
        <if test="${joiner}finalTwa != null">
            and ${mAlias}FINAL_TWA = #{${joiner}finalTwa}
        </if>
        <if test="${joiner}previousStel != null">
            and ${mAlias}PREVIOUS_STEL = #{${joiner}previousStel}
        </if>
        <if test="${joiner}currentStel != null">
            and ${mAlias}CURRENT_STEL = #{${joiner}currentStel}
        </if>
        <if test="${joiner}finalStel != null">
            and ${mAlias}FINAL_STEL = #{${joiner}finalStel}
        </if>
        <if test="${joiner}previousMac != null">
            and ${mAlias}PREVIOUS_MAC = #{${joiner}previousMac}
        </if>
        <if test="${joiner}currentMac != null">
            and ${mAlias}CURRENT_MAC = #{${joiner}currentMac}
        </if>
        <if test="${joiner}finalMac != null">
            and ${mAlias}FINAL_MAC = #{${joiner}finalMac}
        </if>
        <if test="${joiner}previousPe != null">
            and ${mAlias}PREVIOUS_PE = #{${joiner}previousPe}
        </if>
        <if test="${joiner}currentPe != null">
            and ${mAlias}CURRENT_PE = #{${joiner}currentPe}
        </if>
        <if test="${joiner}finalPe != null">
            and ${mAlias}FINAL_PE = #{${joiner}finalPe}
        </if>
        <if test="${joiner}previousLex != null">
            and ${mAlias}PREVIOUS_LEX = #{${joiner}previousLex}
        </if>
        <if test="${joiner}currentLex != null">
            and ${mAlias}CURRENT_LEX = #{${joiner}currentLex}
        </if>
        <if test="${joiner}finalLex != null">
            and ${mAlias}FINAL_LEX = #{${joiner}finalLex}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByFactorId != null and ${joiner}fkByFactorId.rid != null">
                t.FACTOR_ID = #{${joiner}fkByFactorId.rid},
            </when>
            <otherwise>
                t.FACTOR_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByItemId != null and ${joiner}fkByItemId.rid != null">
                t.ITEM_ID = #{${joiner}fkByItemId.rid},
            </when>
            <otherwise>
                t.ITEM_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}jobName != null">
                t.JOB_NAME = #{${joiner}jobName},
            </when>
            <otherwise>
                t.JOB_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}judgementResult != null">
                t.JUDGEMENT_RESULT = #{${joiner}judgementResult},
            </when>
            <otherwise>
                t.JUDGEMENT_RESULT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}compositiveResult != null">
                t.COMPOSITIVE_RESULT = #{${joiner}compositiveResult},
            </when>
            <otherwise>
                t.COMPOSITIVE_RESULT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}previousTwa != null">
                t.PREVIOUS_TWA = #{${joiner}previousTwa},
            </when>
            <otherwise>
                t.PREVIOUS_TWA = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}currentTwa != null">
                t.CURRENT_TWA = #{${joiner}currentTwa},
            </when>
            <otherwise>
                t.CURRENT_TWA = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}finalTwa != null">
                t.FINAL_TWA = #{${joiner}finalTwa},
            </when>
            <otherwise>
                t.FINAL_TWA = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}previousStel != null">
                t.PREVIOUS_STEL = #{${joiner}previousStel},
            </when>
            <otherwise>
                t.PREVIOUS_STEL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}currentStel != null">
                t.CURRENT_STEL = #{${joiner}currentStel},
            </when>
            <otherwise>
                t.CURRENT_STEL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}finalStel != null">
                t.FINAL_STEL = #{${joiner}finalStel},
            </when>
            <otherwise>
                t.FINAL_STEL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}previousMac != null">
                t.PREVIOUS_MAC = #{${joiner}previousMac},
            </when>
            <otherwise>
                t.PREVIOUS_MAC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}currentMac != null">
                t.CURRENT_MAC = #{${joiner}currentMac},
            </when>
            <otherwise>
                t.CURRENT_MAC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}finalMac != null">
                t.FINAL_MAC = #{${joiner}finalMac},
            </when>
            <otherwise>
                t.FINAL_MAC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}previousPe != null">
                t.PREVIOUS_PE = #{${joiner}previousPe},
            </when>
            <otherwise>
                t.PREVIOUS_PE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}currentPe != null">
                t.CURRENT_PE = #{${joiner}currentPe},
            </when>
            <otherwise>
                t.CURRENT_PE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}finalPe != null">
                t.FINAL_PE = #{${joiner}finalPe},
            </when>
            <otherwise>
                t.FINAL_PE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}previousLex != null">
                t.PREVIOUS_LEX = #{${joiner}previousLex},
            </when>
            <otherwise>
                t.PREVIOUS_LEX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}currentLex != null">
                t.CURRENT_LEX = #{${joiner}currentLex},
            </when>
            <otherwise>
                t.CURRENT_LEX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}finalLex != null">
                t.FINAL_LEX = #{${joiner}finalLex},
            </when>
            <otherwise>
                t.FINAL_LEX = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_RECTIFICATION_RESULT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_RECTIFICATION_RESULT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_RECTIFICATION_RESULT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdRectificationResult">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_RECTIFICATION_RESULT_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_RECTIFICATION_RESULT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            FACTOR_ID,
            ITEM_ID,
            JOB_NAME,
            JUDGEMENT_RESULT,
            COMPOSITIVE_RESULT,
            PREVIOUS_TWA,
            CURRENT_TWA,
            FINAL_TWA,
            PREVIOUS_STEL,
            CURRENT_STEL,
            FINAL_STEL,
            PREVIOUS_MAC,
            CURRENT_MAC,
            FINAL_MAC,
            PREVIOUS_PE,
            CURRENT_PE,
            FINAL_PE,
            PREVIOUS_LEX,
            CURRENT_LEX,
            FINAL_LEX,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{fkByMainId.rid},
            #{fkByFactorId.rid},
            #{fkByItemId.rid},
            #{jobName},
            #{judgementResult},
            #{compositiveResult},
            #{previousTwa},
            #{currentTwa},
            #{finalTwa},
            #{previousStel},
            #{currentStel},
            #{finalStel},
            #{previousMac},
            #{currentMac},
            #{finalMac},
            #{previousPe},
            #{currentPe},
            #{finalPe},
            #{previousLex},
            #{currentLex},
            #{finalLex},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_RECTIFICATION_RESULT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            FACTOR_ID,
            ITEM_ID,
            JOB_NAME,
            JUDGEMENT_RESULT,
            COMPOSITIVE_RESULT,
            PREVIOUS_TWA,
            CURRENT_TWA,
            FINAL_TWA,
            PREVIOUS_STEL,
            CURRENT_STEL,
            FINAL_STEL,
            PREVIOUS_MAC,
            CURRENT_MAC,
            FINAL_MAC,
            PREVIOUS_PE,
            CURRENT_PE,
            FINAL_PE,
            PREVIOUS_LEX,
            CURRENT_LEX,
            FINAL_LEX,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_RECTIFICATION_RESULT_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByMainId != null and item.fkByMainId.rid != null">
                            #{item.fkByMainId.rid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByFactorId != null and item.fkByFactorId.rid != null">
                            #{item.fkByFactorId.rid} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByItemId != null and item.fkByItemId.rid != null">
                            #{item.fkByItemId.rid} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.jobName != null">
                            #{item.jobName} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.judgementResult != null">
                            #{item.judgementResult} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.compositiveResult != null">
                            #{item.compositiveResult} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.previousTwa != null">
                            #{item.previousTwa} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.currentTwa != null">
                            #{item.currentTwa} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.finalTwa != null">
                            #{item.finalTwa} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.previousStel != null">
                            #{item.previousStel} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.currentStel != null">
                            #{item.currentStel} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.finalStel != null">
                            #{item.finalStel} AS C16,
                        </when>
                        <otherwise>
                            NULL AS C16,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.previousMac != null">
                            #{item.previousMac} AS C17,
                        </when>
                        <otherwise>
                            NULL AS C17,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.currentMac != null">
                            #{item.currentMac} AS C18,
                        </when>
                        <otherwise>
                            NULL AS C18,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.finalMac != null">
                            #{item.finalMac} AS C19,
                        </when>
                        <otherwise>
                            NULL AS C19,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.previousPe != null">
                            #{item.previousPe} AS C20,
                        </when>
                        <otherwise>
                            NULL AS C20,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.currentPe != null">
                            #{item.currentPe} AS C21,
                        </when>
                        <otherwise>
                            NULL AS C21,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.finalPe != null">
                            #{item.finalPe} AS C22,
                        </when>
                        <otherwise>
                            NULL AS C22,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.previousLex != null">
                            #{item.previousLex} AS C23,
                        </when>
                        <otherwise>
                            NULL AS C23,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.currentLex != null">
                            #{item.currentLex} AS C24,
                        </when>
                        <otherwise>
                            NULL AS C24,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.finalLex != null">
                            #{item.finalLex} AS C25,
                        </when>
                        <otherwise>
                            NULL AS C25,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdRectificationResult" >
        update TD_RECTIFICATION_RESULT t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_RECTIFICATION_RESULT t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 注意 joiner 需要加入 -->
    <delete id="removeByEntity" parameterType="TdRectificationResult">
        delete from TD_RECTIFICATION_RESULT
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

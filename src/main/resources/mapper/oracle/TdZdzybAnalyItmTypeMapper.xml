<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZdzybAnalyItmTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZdzybAnalyItmType">
        <result column="RID" property="rid" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="ITEM_NAME" property="itemName" />
        <result column="SHOW_NAME" property="showName" />
        <result column="XH" property="xh" />
        <result column="RULE_LEVEL" property="ruleLevel" />
        <result column="ITEM_CODE" property="itemCode" />
        <result column="IF_JUDGE_RESULT" property="ifJudgeResult" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_MANID,t.CREATE_DATE,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MAIN_ID,t.ITEM_NAME,t.SHOW_NAME,t.XH,t.RULE_LEVEL,t.ITEM_CODE,t.IF_JUDGE_RESULT,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}itemName != null and ${joiner}itemName != ''">
            and ${mAlias}ITEM_NAME = #{${joiner}itemName}
        </if>
        <if test="${joiner}showName != null and ${joiner}showName != ''">
            and ${mAlias}SHOW_NAME = #{${joiner}showName}
        </if>
        <if test="${joiner}xh != null and ${joiner}xh != ''">
            and ${mAlias}XH = #{${joiner}xh}
        </if>
        <if test="${joiner}ruleLevel != null and ${joiner}ruleLevel != ''">
            and ${mAlias}RULE_LEVEL = #{${joiner}ruleLevel}
        </if>
        <if test="${joiner}itemCode != null and ${joiner}itemCode != ''">
            and ${mAlias}ITEM_CODE = #{${joiner}itemCode}
        </if>
        <if test="${joiner}ifJudgeResult != null and ${joiner}ifJudgeResult != ''">
            and ${mAlias}IF_JUDGE_RESULT = #{${joiner}ifJudgeResult}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.CREATE_DATE = #{${joiner}createDate},
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}itemName != null and ${joiner}itemName != ''">
                t.ITEM_NAME = #{${joiner}itemName},
            </when>
            <otherwise>
                t.ITEM_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}showName != null and ${joiner}showName != ''">
                t.SHOW_NAME = #{${joiner}showName},
            </when>
            <otherwise>
                t.SHOW_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}xh != null and ${joiner}xh != ''">
                t.XH = #{${joiner}xh},
            </when>
            <otherwise>
                t.XH = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ruleLevel != null and ${joiner}ruleLevel != ''">
                t.RULE_LEVEL = #{${joiner}ruleLevel},
            </when>
            <otherwise>
                t.RULE_LEVEL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}itemCode != null and ${joiner}itemCode != ''">
                t.ITEM_CODE = #{${joiner}itemCode},
            </when>
            <otherwise>
                t.ITEM_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifJudgeResult != null and ${joiner}ifJudgeResult != ''">
                t.IF_JUDGE_RESULT = #{${joiner}ifJudgeResult},
            </when>
            <otherwise>
                t.IF_JUDGE_RESULT = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZDZYB_ANALY_ITM_TYPE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZDZYB_ANALY_ITM_TYPE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZDZYB_ANALY_ITM_TYPE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZdzybAnalyItmType" >
        update TD_ZDZYB_ANALY_ITM_TYPE t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZDZYB_ANALY_ITM_TYPE t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZdzybAnalyItmType">
        delete from TD_ZDZYB_ANALY_ITM_TYPE
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

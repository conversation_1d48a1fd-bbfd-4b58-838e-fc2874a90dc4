<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TbZwWarnModelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TbZwWarnModel">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="WARN_TYPE" property="warnType" />
        <result column="WARN_LEVEL" property="warnLevel" />
        <result column="WARN_INDEX" property="warnIndex" />
        <result column="GE_IND" property="geInd" />
        <result column="GT_IND" property="gtInd" />
        <result column="LE_IND" property="leInd" />
        <result column="LT_IND" property="ltInd" />
        <result column="IF_CYCLE_TIME" property="ifCycleTime" />
        <result column="CYCLE_DAYS" property="cycleDays" />
        <result column="RMK" property="rmk" />
        <result column="STATE_MARK" property="stateMark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.WARN_TYPE,t.WARN_LEVEL,t.WARN_INDEX,t.GE_IND,t.GT_IND,t.LE_IND,t.LT_IND,t.IF_CYCLE_TIME,t.CYCLE_DAYS,t.RMK,t.STATE_MARK,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}warnType != null">
            and ${mAlias}WARN_TYPE = #{${joiner}warnType}
        </if>
        <if test="${joiner}warnLevel != null">
            and ${mAlias}WARN_LEVEL = #{${joiner}warnLevel}
        </if>
        <if test="${joiner}warnIndex != null">
            and ${mAlias}WARN_INDEX = #{${joiner}warnIndex}
        </if>
        <if test="${joiner}geInd != null">
            and ${mAlias}GE_IND = #{${joiner}geInd}
        </if>
        <if test="${joiner}gtInd != null">
            and ${mAlias}GT_IND = #{${joiner}gtInd}
        </if>
        <if test="${joiner}leInd != null">
            and ${mAlias}LE_IND = #{${joiner}leInd}
        </if>
        <if test="${joiner}ltInd != null">
            and ${mAlias}LT_IND = #{${joiner}ltInd}
        </if>
        <if test="${joiner}ifCycleTime != null">
            and ${mAlias}IF_CYCLE_TIME = #{${joiner}ifCycleTime}
        </if>
        <if test="${joiner}cycleDays != null">
            and ${mAlias}CYCLE_DAYS = #{${joiner}cycleDays}
        </if>
        <if test="${joiner}rmk != null">
            and ${mAlias}RMK = #{${joiner}rmk}
        </if>
        <if test="${joiner}stateMark != null">
            and ${mAlias}STATE_MARK = #{${joiner}stateMark}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}warnType != null">
                t.WARN_TYPE = #{${joiner}warnType},
            </when>
            <otherwise>
                t.WARN_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}warnLevel != null">
                t.WARN_LEVEL = #{${joiner}warnLevel},
            </when>
            <otherwise>
                t.WARN_LEVEL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}warnIndex != null">
                t.WARN_INDEX = #{${joiner}warnIndex},
            </when>
            <otherwise>
                t.WARN_INDEX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}geInd != null">
                t.GE_IND = #{${joiner}geInd},
            </when>
            <otherwise>
                t.GE_IND = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}gtInd != null">
                t.GT_IND = #{${joiner}gtInd},
            </when>
            <otherwise>
                t.GT_IND = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}leInd != null">
                t.LE_IND = #{${joiner}leInd},
            </when>
            <otherwise>
                t.LE_IND = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ltInd != null">
                t.LT_IND = #{${joiner}ltInd},
            </when>
            <otherwise>
                t.LT_IND = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifCycleTime != null">
                t.IF_CYCLE_TIME = #{${joiner}ifCycleTime},
            </when>
            <otherwise>
                t.IF_CYCLE_TIME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}cycleDays != null">
                t.CYCLE_DAYS = #{${joiner}cycleDays},
            </when>
            <otherwise>
                t.CYCLE_DAYS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rmk != null">
                t.RMK = #{${joiner}rmk},
            </when>
            <otherwise>
                t.RMK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}stateMark != null">
                t.STATE_MARK = #{${joiner}stateMark},
            </when>
            <otherwise>
                t.STATE_MARK = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_ZW_WARN_MODEL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_ZW_WARN_MODEL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_ZW_WARN_MODEL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TbZwWarnModel">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TB_ZW_WARN_MODEL_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TB_ZW_WARN_MODEL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            WARN_TYPE,
            WARN_LEVEL,
            WARN_INDEX,
            GE_IND,
            GT_IND,
            LE_IND,
            LT_IND,
            IF_CYCLE_TIME,
            CYCLE_DAYS,
            RMK,
            STATE_MARK,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{warnType},
            #{warnLevel},
            #{warnIndex},
            #{geInd},
            #{gtInd},
            #{leInd},
            #{ltInd},
            #{ifCycleTime},
            #{cycleDays},
            #{rmk},
            #{stateMark},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TB_ZW_WARN_MODEL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            WARN_TYPE,
            WARN_LEVEL,
            WARN_INDEX,
            GE_IND,
            GT_IND,
            LE_IND,
            LT_IND,
            IF_CYCLE_TIME,
            CYCLE_DAYS,
            RMK,
            STATE_MARK,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TB_ZW_WARN_MODEL_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.warnType != null">
                            #{item.warnType} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.warnLevel != null">
                            #{item.warnLevel} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.warnIndex != null">
                            #{item.warnIndex} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.geInd != null">
                            #{item.geInd} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.gtInd != null">
                            #{item.gtInd} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.leInd != null">
                            #{item.leInd} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ltInd != null">
                            #{item.ltInd} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifCycleTime != null">
                            #{item.ifCycleTime} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.cycleDays != null">
                            #{item.cycleDays} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rmk != null">
                            #{item.rmk} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.stateMark != null">
                            #{item.stateMark} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TbZwWarnModel" >
        update TB_ZW_WARN_MODEL t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TB_ZW_WARN_MODEL t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 注意 joiner 需要加入 -->
    <delete id="removeByEntity" parameterType="TbZwWarnModel">
        delete from TB_ZW_WARN_MODEL
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->

    <select id="selectListByWarnType" resultMap="BaseResultMap">
        SELECT M.RID,
               M.WARN_TYPE,
               M.WARN_LEVEL,
               M.WARN_INDEX,
               M.GE_IND,
               M.GT_IND,
               M.IF_CYCLE_TIME,
               M.CYCLE_DAYS
        FROM TB_ZW_WARN_MODEL M
        WHERE M.WARN_TYPE = #{warnType} AND M.STATE_MARK = 1
    </select>

</mapper>

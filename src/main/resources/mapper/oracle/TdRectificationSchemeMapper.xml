<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdRectificationSchemeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdRectificationScheme">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="RECTIFICATION_MEASURE" property="rectificationMeasure" />
        <result column="CAPITAL_INVESTMENT" property="capitalInvestment" />
        <result column="START_DATE" property="startDate" />
        <result column="END_DATE" property="endDate" />
        <result column="PRINCIPAL" property="principal" />
        <result column="CONTACT" property="contact" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MAIN_ID,t.RECTIFICATION_MEASURE,t.CAPITAL_INVESTMENT,t.START_DATE,t.END_DATE,t.PRINCIPAL,t.CONTACT,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}rectificationMeasure != null">
            and ${mAlias}RECTIFICATION_MEASURE = #{${joiner}rectificationMeasure}
        </if>
        <if test="${joiner}capitalInvestment != null">
            and ${mAlias}CAPITAL_INVESTMENT = #{${joiner}capitalInvestment}
        </if>
        <if test="${joiner}startDate != null">
            and ${mAlias}START_DATE = #{${joiner}startDate}
        </if>
        <if test="${joiner}endDate != null">
            and ${mAlias}END_DATE = #{${joiner}endDate}
        </if>
        <if test="${joiner}principal != null">
            and ${mAlias}PRINCIPAL = #{${joiner}principal}
        </if>
        <if test="${joiner}contact != null">
            and ${mAlias}CONTACT = #{${joiner}contact}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rectificationMeasure != null">
                t.RECTIFICATION_MEASURE = #{${joiner}rectificationMeasure},
            </when>
            <otherwise>
                t.RECTIFICATION_MEASURE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}capitalInvestment != null">
                t.CAPITAL_INVESTMENT = #{${joiner}capitalInvestment},
            </when>
            <otherwise>
                t.CAPITAL_INVESTMENT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}startDate != null">
                t.START_DATE = #{${joiner}startDate},
            </when>
            <otherwise>
                t.START_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}endDate != null">
                t.END_DATE = #{${joiner}endDate},
            </when>
            <otherwise>
                t.END_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}principal != null">
                t.PRINCIPAL = #{${joiner}principal},
            </when>
            <otherwise>
                t.PRINCIPAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}contact != null">
                t.CONTACT = #{${joiner}contact},
            </when>
            <otherwise>
                t.CONTACT = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_RECTIFICATION_SCHEME t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_RECTIFICATION_SCHEME t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_RECTIFICATION_SCHEME t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdRectificationScheme">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_RECTIFICATION_SCHEME_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_RECTIFICATION_SCHEME
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            RECTIFICATION_MEASURE,
            CAPITAL_INVESTMENT,
            START_DATE,
            END_DATE,
            PRINCIPAL,
            CONTACT,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{fkByMainId.rid},
            #{rectificationMeasure},
            #{capitalInvestment},
            #{startDate},
            #{endDate},
            #{principal},
            #{contact},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_RECTIFICATION_SCHEME
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            RECTIFICATION_MEASURE,
            CAPITAL_INVESTMENT,
            START_DATE,
            END_DATE,
            PRINCIPAL,
            CONTACT,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_RECTIFICATION_SCHEME_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByMainId != null and item.fkByMainId.rid != null">
                            #{item.fkByMainId.rid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rectificationMeasure != null">
                            #{item.rectificationMeasure} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.capitalInvestment != null">
                            #{item.capitalInvestment} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.startDate != null">
                            #{item.startDate} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.endDate != null">
                            #{item.endDate} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.principal != null">
                            #{item.principal} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.contact != null">
                            #{item.contact} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdRectificationScheme" >
        update TD_RECTIFICATION_SCHEME t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_RECTIFICATION_SCHEME t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 注意 joiner 需要加入 -->
    <delete id="removeByEntity" parameterType="TdRectificationScheme">
        delete from TD_RECTIFICATION_SCHEME
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

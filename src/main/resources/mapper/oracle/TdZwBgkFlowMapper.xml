<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwBgkFlowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwBgkFlow">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="CART_TYPE" property="cartType" />
        <result column="BUS_ID" property="busId" />
        <result column="OPER_FLAG" property="operFlag" />
        <result column="RCV_DATE" property="rcvDate" />
        <result column="SMT_PSN_ID" property="fkBySmtPsnId.rid" />
        <result column="OPER_DATE" property="operDate" />
        <result column="OPER_PSN_ID" property="fkByOperPsnId.rid" />
        <result column="BACK_RSN" property="backRsn" />
        <result column="IF_IN_TIME" property="ifInTime" />
        <result column="AUDIT_ADV" property="auditAdv" />
        <result column="AUDIT_MAN" property="auditMan" />
        <result column="BHK_CODE" property="bhkCode" />
        <result column="BHKORG_ID" property="fkByBhkorgId.rid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.CART_TYPE,t.BUS_ID,t.OPER_FLAG,t.RCV_DATE,t.SMT_PSN_ID,t.OPER_DATE,t.OPER_PSN_ID,t.BACK_RSN,t.IF_IN_TIME,t.AUDIT_ADV,t.AUDIT_MAN,t.BHK_CODE,t.BHKORG_ID,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}cartType != null">
            and ${mAlias}CART_TYPE = #{${joiner}cartType}
        </if>
        <if test="${joiner}busId != null">
            and ${mAlias}BUS_ID = #{${joiner}busId}
        </if>
        <if test="${joiner}operFlag != null">
            and ${mAlias}OPER_FLAG = #{${joiner}operFlag}
        </if>
        <if test="${joiner}rcvDate != null">
            and ${mAlias}RCV_DATE = #{${joiner}rcvDate}
        </if>
        <if test="${joiner}fkBySmtPsnId != null and ${joiner}fkBySmtPsnId.rid != null">
            and ${mAlias}SMT_PSN_ID = #{${joiner}fkBySmtPsnId.rid}
        </if>
        <if test="${joiner}operDate != null">
            and ${mAlias}OPER_DATE = #{${joiner}operDate}
        </if>
        <if test="${joiner}fkByOperPsnId != null and ${joiner}fkByOperPsnId.rid != null">
            and ${mAlias}OPER_PSN_ID = #{${joiner}fkByOperPsnId.rid}
        </if>
        <if test="${joiner}backRsn != null">
            and ${mAlias}BACK_RSN = #{${joiner}backRsn}
        </if>
        <if test="${joiner}ifInTime != null">
            and ${mAlias}IF_IN_TIME = #{${joiner}ifInTime}
        </if>
        <if test="${joiner}auditAdv != null">
            and ${mAlias}AUDIT_ADV = #{${joiner}auditAdv}
        </if>
        <if test="${joiner}auditMan != null">
            and ${mAlias}AUDIT_MAN = #{${joiner}auditMan}
        </if>
        <if test="${joiner}bhkCode != null">
            and ${mAlias}BHK_CODE = #{${joiner}bhkCode}
        </if>
        <if test="${joiner}fkByBhkorgId != null and ${joiner}fkByBhkorgId.rid != null">
            and ${mAlias}BHKORG_ID = #{${joiner}fkByBhkorgId.rid}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}cartType != null">
                t.CART_TYPE = #{${joiner}cartType},
            </when>
            <otherwise>
                t.CART_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}busId != null">
                t.BUS_ID = #{${joiner}busId},
            </when>
            <otherwise>
                t.BUS_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}operFlag != null">
                t.OPER_FLAG = #{${joiner}operFlag},
            </when>
            <otherwise>
                t.OPER_FLAG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rcvDate != null">
                t.RCV_DATE = #{${joiner}rcvDate},
            </when>
            <otherwise>
                t.RCV_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkBySmtPsnId != null and ${joiner}fkBySmtPsnId.rid != null">
                t.SMT_PSN_ID = #{${joiner}fkBySmtPsnId.rid},
            </when>
            <otherwise>
                t.SMT_PSN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}operDate != null">
                t.OPER_DATE = #{${joiner}operDate},
            </when>
            <otherwise>
                t.OPER_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByOperPsnId != null and ${joiner}fkByOperPsnId.rid != null">
                t.OPER_PSN_ID = #{${joiner}fkByOperPsnId.rid},
            </when>
            <otherwise>
                t.OPER_PSN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}backRsn != null">
                t.BACK_RSN = #{${joiner}backRsn},
            </when>
            <otherwise>
                t.BACK_RSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifInTime != null">
                t.IF_IN_TIME = #{${joiner}ifInTime},
            </when>
            <otherwise>
                t.IF_IN_TIME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}auditAdv != null">
                t.AUDIT_ADV = #{${joiner}auditAdv},
            </when>
            <otherwise>
                t.AUDIT_ADV = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}auditMan != null">
                t.AUDIT_MAN = #{${joiner}auditMan},
            </when>
            <otherwise>
                t.AUDIT_MAN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}bhkCode != null">
                t.BHK_CODE = #{${joiner}bhkCode},
            </when>
            <otherwise>
                t.BHK_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByBhkorgId != null and ${joiner}fkByBhkorgId.rid != null">
                t.BHKORG_ID = #{${joiner}fkByBhkorgId.rid},
            </when>
            <otherwise>
                t.BHKORG_ID = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZW_BGK_FLOW t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZW_BGK_FLOW t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZW_BGK_FLOW t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdZwBgkFlow">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_ZW_BGK_FLOW_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_ZW_BGK_FLOW
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            CART_TYPE,
            BUS_ID,
            OPER_FLAG,
            RCV_DATE,
            SMT_PSN_ID,
            OPER_DATE,
            OPER_PSN_ID,
            BACK_RSN,
            IF_IN_TIME,
            AUDIT_ADV,
            AUDIT_MAN,
            BHK_CODE,
            BHKORG_ID,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{cartType},
            #{busId},
            #{operFlag},
            #{rcvDate},
            #{fkBySmtPsnId.rid},
            #{operDate},
            #{fkByOperPsnId.rid},
            #{backRsn},
            #{ifInTime},
            #{auditAdv},
            #{auditMan},
            #{bhkCode},
            #{fkByBhkorgId.rid},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_ZW_BGK_FLOW
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            CART_TYPE,
            BUS_ID,
            OPER_FLAG,
            RCV_DATE,
            SMT_PSN_ID,
            OPER_DATE,
            OPER_PSN_ID,
            BACK_RSN,
            IF_IN_TIME,
            AUDIT_ADV,
            AUDIT_MAN,
            BHK_CODE,
            BHKORG_ID,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_ZW_BGK_FLOW_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.cartType != null">
                            #{item.cartType} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.busId != null">
                            #{item.busId} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.operFlag != null">
                            #{item.operFlag} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rcvDate != null">
                            #{item.rcvDate} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkBySmtPsnId != null and item.fkBySmtPsnId.rid != null">
                            #{item.fkBySmtPsnId.rid} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.operDate != null">
                            #{item.operDate} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByOperPsnId != null and item.fkByOperPsnId.rid != null">
                            #{item.fkByOperPsnId.rid} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.backRsn != null">
                            #{item.backRsn} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifInTime != null">
                            #{item.ifInTime} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.auditAdv != null">
                            #{item.auditAdv} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.auditMan != null">
                            #{item.auditMan} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.bhkCode != null">
                            #{item.bhkCode} AS C16,
                        </when>
                        <otherwise>
                            NULL AS C16,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByBhkorgId != null and item.fkByBhkorgId.rid != null">
                            #{item.fkByBhkorgId.rid} AS C17,
                        </when>
                        <otherwise>
                            NULL AS C17,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwBgkFlow" >
        update TD_ZW_BGK_FLOW t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_ZW_BGK_FLOW t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZwBgkFlow">
        delete from TD_ZW_BGK_FLOW
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <insert id="batchInsertBgkFlow" parameterType="java.util.List">
        INSERT INTO TD_ZW_BGK_FLOW (RID, CART_TYPE, BUS_ID, OPER_FLAG, RCV_DATE,SMT_PSN_ID, AUDIT_ADV, AUDIT_MAN, CREATE_DATE, CREATE_MANID, BHK_CODE,
        BHKORG_ID)
        SELECT TD_ZW_BGK_FLOW_SEQ.NEXTVAL, T.* FROM (
        SELECT 9 t1,
        B.RID t2,
        #{operationFlag} t3,
        <choose>
            <when test="smtPsnId != null and smtPsnId != null">
                sysdate t4,
            </when>
            <otherwise>
                B.DEAL_COMPLETE_DATE t4,
            </otherwise>
        </choose>
        <choose>
            <when test="smtPsnId != null and smtPsnId != null">
                #{smtPsnId} AS C9,
            </when>
            <otherwise>
                NULL AS C9,
            </otherwise>
        </choose>
        <choose>
            <when test="auditAdv != null and auditAdv != null">
                #{auditAdv} AS C10,
            </when>
            <otherwise>
                NULL AS C10,
            </otherwise>
        </choose>
        <choose>
            <when test="auditMan != null and auditMan != null">
                #{auditMan} AS C11,
            </when>
            <otherwise>
                NULL AS C11,
            </otherwise>
        </choose>
        sysdate t5,
        #{psnId} t6,
        B.BHK_CODE t7,
        B.BHKORG_ID t8
        FROM TD_TJ_BHK B
        WHERE B.RID IN
        <foreach collection="rids" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ) T
    </insert>

    <select id="selectBgkFlowByLastMark" resultType="com.chis.modules.timer.heth.entity.TdZwBgkFlow">
        SELECT T.BUS_ID,T.RCV_DATE,T.RID
        FROM TD_ZW_BGK_FLOW T
        WHERE CART_TYPE = 9
        AND OPER_FLAG =#{lastMark}
        AND EXISTS (
        SELECT 1
        FROM TD_ZW_BGK_FLOW T1
        WHERE T1.BUS_ID = T.BUS_ID
        AND T1.BUS_ID IN
        <foreach collection="rids" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        )
        ORDER BY T.BUS_ID,T.CREATE_DATE DESC
    </select>

    <update id="updateBgkFlow">
        UPDATE TD_ZW_BGK_FLOW T
        SET T.OPER_DATE = sysdate,
        T.OPER_PSN_ID =#{checkRsnId},
        T.IF_IN_TIME =#{timely},
        T.MODIFY_DATE=sysdate,
        T.MODIFY_MANID=#{psnId}
        WHERE T.RID IN
        <foreach collection="rids" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <insert id="batchInsertBgkFlowBack">
        INSERT INTO TD_ZW_BGK_FLOW (RID, CART_TYPE, BUS_ID, OPER_FLAG, RCV_DATE, SMT_PSN_ID, AUDIT_ADV, AUDIT_MAN, CREATE_DATE, CREATE_MANID, BHK_CODE, BHKORG_ID)
        SELECT TD_ZW_BGK_FLOW_SEQ.NEXTVAL, T.* FROM (
        select B.t1, B.t2, B.t3, B.t4, B.C9, P.AUDIT_ADV, B.C11, B.t5, B.t6, B.t7, B.t8 from(
        <foreach collection="params" item="param" index="index" separator="UNION ALL">
            SELECT
            9 t1,
            B.RID t2,
            #{operationFlag} t3,
            <choose>
                <when test="smtPsnId != null and smtPsnId != null">
                    sysdate t4,
                </when>
                <otherwise>
                    B.DEAL_COMPLETE_DATE t4,
                </otherwise>
            </choose>
            <choose>
                <when test="smtPsnId != null and smtPsnId != null">
                    #{smtPsnId} AS C9,
                </when>
                <otherwise>
                    NULL AS C9,
                </otherwise>
            </choose>
            NULL AS C10,
            <choose>
                <when test="auditMan != null and auditMan != null">
                    #{auditMan} AS C11,
                </when>
                <otherwise>
                    NULL AS C11,
                </otherwise>
            </choose>
            sysdate t5,
            #{psnId} t6,
            B.BHK_CODE t7,
            B.BHKORG_ID t8
            FROM TD_TJ_BHK B where B.RID=#{param.rid}
        </foreach>
        ) B
        JOIN (
        <foreach collection="params" item="param" index="index" separator="UNION ALL">
            SELECT #{param.rid} AS RID, #{param.auditAdv} AS AUDIT_ADV FROM DUAL
        </foreach>
        ) P ON B.t2 = P.RID
        ) T


    </insert>

</mapper>

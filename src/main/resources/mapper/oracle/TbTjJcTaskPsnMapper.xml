<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TbTjJcTaskPsnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TbTjJcTaskPsn">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="PSN_NAME" property="psnName" />
        <result column="CARD_TYPE_ID" property="fkByCardTypeId.rid" />
        <result column="IDC" property="idc" />
        <result column="ONGUARD_STATEID" property="fkByOnguardStateid.rid" />
        <result column="POST_ID" property="fkByPostId.rid" />
        <result column="RMK" property="rmk" />
        <result column="BHK_CODE" property="bhkCode" />
        <result column="WORK_OTHER" property="workOther" />
        <result column="PROTECT_EQU_ID" property="fkByProtectEquId.rid" />
        <result column="PLAN_TYPE" property="planType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MAIN_ID,t.PSN_NAME,t.CARD_TYPE_ID,t.IDC,t.ONGUARD_STATEID,t.POST_ID,t.RMK,t.BHK_CODE,t.WORK_OTHER,t.PROTECT_EQU_ID,t.PLAN_TYPE,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}psnName != null">
            and ${mAlias}PSN_NAME = #{${joiner}psnName}
        </if>
        <if test="${joiner}fkByCardTypeId != null and ${joiner}fkByCardTypeId.rid != null">
            and ${mAlias}CARD_TYPE_ID = #{${joiner}fkByCardTypeId.rid}
        </if>
        <if test="${joiner}idc != null">
            and ${mAlias}IDC = #{${joiner}idc}
        </if>
        <if test="${joiner}fkByOnguardStateid != null and ${joiner}fkByOnguardStateid.rid != null">
            and ${mAlias}ONGUARD_STATEID = #{${joiner}fkByOnguardStateid.rid}
        </if>
        <if test="${joiner}fkByPostId != null and ${joiner}fkByPostId.rid != null">
            and ${mAlias}POST_ID = #{${joiner}fkByPostId.rid}
        </if>
        <if test="${joiner}rmk != null">
            and ${mAlias}RMK = #{${joiner}rmk}
        </if>
        <if test="${joiner}bhkCode != null">
            and ${mAlias}BHK_CODE = #{${joiner}bhkCode}
        </if>
        <if test="${joiner}workOther != null">
            and ${mAlias}WORK_OTHER = #{${joiner}workOther}
        </if>
        <if test="${joiner}fkByProtectEquId != null and ${joiner}fkByProtectEquId.rid != null">
            and ${mAlias}PROTECT_EQU_ID = #{${joiner}fkByProtectEquId.rid}
        </if>
        <if test="${joiner}planType != null">
            and ${mAlias}PLAN_TYPE = #{${joiner}planType}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}psnName != null">
                t.PSN_NAME = #{${joiner}psnName},
            </when>
            <otherwise>
                t.PSN_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCardTypeId != null and ${joiner}fkByCardTypeId.rid != null">
                t.CARD_TYPE_ID = #{${joiner}fkByCardTypeId.rid},
            </when>
            <otherwise>
                t.CARD_TYPE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}idc != null">
                t.IDC = #{${joiner}idc},
            </when>
            <otherwise>
                t.IDC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByOnguardStateid != null and ${joiner}fkByOnguardStateid.rid != null">
                t.ONGUARD_STATEID = #{${joiner}fkByOnguardStateid.rid},
            </when>
            <otherwise>
                t.ONGUARD_STATEID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByPostId != null and ${joiner}fkByPostId.rid != null">
                t.POST_ID = #{${joiner}fkByPostId.rid},
            </when>
            <otherwise>
                t.POST_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rmk != null">
                t.RMK = #{${joiner}rmk},
            </when>
            <otherwise>
                t.RMK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}bhkCode != null">
                t.BHK_CODE = #{${joiner}bhkCode},
            </when>
            <otherwise>
                t.BHK_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}workOther != null">
                t.WORK_OTHER = #{${joiner}workOther},
            </when>
            <otherwise>
                t.WORK_OTHER = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByProtectEquId != null and ${joiner}fkByProtectEquId.rid != null">
                t.PROTECT_EQU_ID = #{${joiner}fkByProtectEquId.rid},
            </when>
            <otherwise>
                t.PROTECT_EQU_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}planType != null">
                t.PLAN_TYPE = #{${joiner}planType},
            </when>
            <otherwise>
                t.PLAN_TYPE = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_JC_TASK_PSN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_JC_TASK_PSN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_JC_TASK_PSN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TbTjJcTaskPsn">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TB_TJ_JC_TASK_PSN_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TB_TJ_JC_TASK_PSN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            PSN_NAME,
            CARD_TYPE_ID,
            IDC,
            ONGUARD_STATEID,
            POST_ID,
            RMK,
            BHK_CODE,
            WORK_OTHER,
            PROTECT_EQU_ID,
            PLAN_TYPE,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{fkByMainId.rid},
            #{psnName},
            #{fkByCardTypeId.rid},
            #{idc},
            #{fkByOnguardStateid.rid},
            #{fkByPostId.rid},
            #{rmk},
            #{bhkCode},
            #{workOther},
            #{fkByProtectEquId.rid},
            #{planType},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TB_TJ_JC_TASK_PSN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            PSN_NAME,
            CARD_TYPE_ID,
            IDC,
            ONGUARD_STATEID,
            POST_ID,
            RMK,
            BHK_CODE,
            WORK_OTHER,
            PROTECT_EQU_ID,
            PLAN_TYPE,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TB_TJ_JC_TASK_PSN_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByMainId != null and item.fkByMainId.rid != null">
                            #{item.fkByMainId.rid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.psnName != null">
                            #{item.psnName} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByCardTypeId != null and item.fkByCardTypeId.rid != null">
                            #{item.fkByCardTypeId.rid} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.idc != null">
                            #{item.idc} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByOnguardStateid != null and item.fkByOnguardStateid.rid != null">
                            #{item.fkByOnguardStateid.rid} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByPostId != null and item.fkByPostId.rid != null">
                            #{item.fkByPostId.rid} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rmk != null">
                            #{item.rmk} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.bhkCode != null">
                            #{item.bhkCode} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.workOther != null">
                            #{item.workOther} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByProtectEquId != null and item.fkByProtectEquId.rid != null">
                            #{item.fkByProtectEquId.rid} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.planType != null">
                            #{item.planType} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TbTjJcTaskPsn" >
        update TB_TJ_JC_TASK_PSN t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TB_TJ_JC_TASK_PSN t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TbTjJcTaskPsn">
        delete from TB_TJ_JC_TASK_PSN
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <update id="updateBatchByBhkRid">
        UPDATE TB_TJ_JC_TASK_PSN K SET BHK_CODE=NULL
        WHERE K.BHK_CODE IS NOT NULL
          AND EXISTS (
                SELECT 1 FROM TB_TJ_JC_TASK T
                                  INNER JOIN TB_TJ_SRVORG T1 ON T.ORG_ID = T1.REG_ORGID
                                  INNER JOIN TD_TJ_BHK T2 ON T2.BHKORG_ID=T1.RID AND T2.JC_TYPE=2 AND NVL(T2.IF_RHK,0)=0
                WHERE K.BHK_CODE=T2.BHK_CODE AND T.RID=K.MAIN_ID AND T2.RID IN
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            )
    </update>
</mapper>

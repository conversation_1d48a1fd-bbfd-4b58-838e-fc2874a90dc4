<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdCfkfRecoveryRstMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdCfkfRecoveryRst">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="ANALY_YEAR" property="analyYear" />
        <result column="PSN_NO" property="psnNo" />
        <result column="PSN_NAME" property="psnName" />
        <result column="IDC" property="idc" />
        <result column="ZONE_ID" property="fkByZoneId.rid" />
        <result column="SITE_NAME" property="siteName" />
        <result column="FST_RECOVR_DATE" property="fstRecovrDate" />
        <result column="LST_RECOVR_DATE" property="lstRecovrDate" />
        <result column="HAS_INST_REVOCERY" property="hasInstRevocery" />
        <result column="HAS_MED_REVOCERY" property="hasMedRevocery" />
        <result column="HAS_NUTRI_REVOCERY" property="hasNutriRevocery" />
        <result column="HAS_PSY_REVOCERY" property="hasPsyRevocery" />
        <result column="HAS_OTHER_REVOCERY" property="hasOtherRevocery" />
        <result column="REHABI_NUM" property="rehabiNum" />
        <result column="TOTAL_TIME" property="totalTime" />
        <result column="AVG_TIME" property="avgTime" />
        <result column="FST_BLOOD_OXYGEN" property="fstBloodOxygen" />
        <result column="LST_BLOOD_OXYGEN" property="lstBloodOxygen" />
        <result column="FST_ADL" property="fstAdl" />
        <result column="LST_ADL" property="lstAdl" />
        <result column="FST_ANXIETY" property="fstAnxiety" />
        <result column="LST_ANXIETY" property="lstAnxiety" />
        <result column="FST_DEPRESSION" property="fstDepression" />
        <result column="LST_DEPRESSION" property="lstDepression" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.ANALY_YEAR,t.PSN_NO,t.PSN_NAME,t.IDC,t.ZONE_ID,t.SITE_NAME,t.FST_RECOVR_DATE,t.LST_RECOVR_DATE,t.HAS_INST_REVOCERY,t.HAS_MED_REVOCERY,t.HAS_NUTRI_REVOCERY,t.HAS_PSY_REVOCERY,t.HAS_OTHER_REVOCERY,t.REHABI_NUM,t.TOTAL_TIME,t.AVG_TIME,t.FST_BLOOD_OXYGEN,t.LST_BLOOD_OXYGEN,t.FST_ADL,t.LST_ADL,t.FST_ANXIETY,t.LST_ANXIETY,t.FST_DEPRESSION,t.LST_DEPRESSION,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}analyYear != null">
            and ${mAlias}ANALY_YEAR = #{${joiner}analyYear}
        </if>
        <if test="${joiner}psnNo != null">
            and ${mAlias}PSN_NO = #{${joiner}psnNo}
        </if>
        <if test="${joiner}psnName != null">
            and ${mAlias}PSN_NAME = #{${joiner}psnName}
        </if>
        <if test="${joiner}idc != null">
            and ${mAlias}IDC = #{${joiner}idc}
        </if>
        <if test="${joiner}fkByZoneId != null and ${joiner}fkByZoneId.rid != null">
            and ${mAlias}ZONE_ID = #{${joiner}fkByZoneId.rid}
        </if>
        <if test="${joiner}siteName != null">
            and ${mAlias}SITE_NAME = #{${joiner}siteName}
        </if>
        <if test="${joiner}fstRecovrDate != null">
            and ${mAlias}FST_RECOVR_DATE = #{${joiner}fstRecovrDate}
        </if>
        <if test="${joiner}lstRecovrDate != null">
            and ${mAlias}LST_RECOVR_DATE = #{${joiner}lstRecovrDate}
        </if>
        <if test="${joiner}hasInstRevocery != null">
            and ${mAlias}HAS_INST_REVOCERY = #{${joiner}hasInstRevocery}
        </if>
        <if test="${joiner}hasMedRevocery != null">
            and ${mAlias}HAS_MED_REVOCERY = #{${joiner}hasMedRevocery}
        </if>
        <if test="${joiner}hasNutriRevocery != null">
            and ${mAlias}HAS_NUTRI_REVOCERY = #{${joiner}hasNutriRevocery}
        </if>
        <if test="${joiner}hasPsyRevocery != null">
            and ${mAlias}HAS_PSY_REVOCERY = #{${joiner}hasPsyRevocery}
        </if>
        <if test="${joiner}hasOtherRevocery != null">
            and ${mAlias}HAS_OTHER_REVOCERY = #{${joiner}hasOtherRevocery}
        </if>
        <if test="${joiner}rehabiNum != null">
            and ${mAlias}REHABI_NUM = #{${joiner}rehabiNum}
        </if>
        <if test="${joiner}totalTime != null">
            and ${mAlias}TOTAL_TIME = #{${joiner}totalTime}
        </if>
        <if test="${joiner}avgTime != null">
            and ${mAlias}AVG_TIME = #{${joiner}avgTime}
        </if>
        <if test="${joiner}fstBloodOxygen != null">
            and ${mAlias}FST_BLOOD_OXYGEN = #{${joiner}fstBloodOxygen}
        </if>
        <if test="${joiner}lstBloodOxygen != null">
            and ${mAlias}LST_BLOOD_OXYGEN = #{${joiner}lstBloodOxygen}
        </if>
        <if test="${joiner}fstAdl != null">
            and ${mAlias}FST_ADL = #{${joiner}fstAdl}
        </if>
        <if test="${joiner}lstAdl != null">
            and ${mAlias}LST_ADL = #{${joiner}lstAdl}
        </if>
        <if test="${joiner}fstAnxiety != null">
            and ${mAlias}FST_ANXIETY = #{${joiner}fstAnxiety}
        </if>
        <if test="${joiner}lstAnxiety != null">
            and ${mAlias}LST_ANXIETY = #{${joiner}lstAnxiety}
        </if>
        <if test="${joiner}fstDepression != null">
            and ${mAlias}FST_DEPRESSION = #{${joiner}fstDepression}
        </if>
        <if test="${joiner}lstDepression != null">
            and ${mAlias}LST_DEPRESSION = #{${joiner}lstDepression}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}analyYear != null">
                t.ANALY_YEAR = #{${joiner}analyYear},
            </when>
            <otherwise>
                t.ANALY_YEAR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}psnNo != null">
                t.PSN_NO = #{${joiner}psnNo},
            </when>
            <otherwise>
                t.PSN_NO = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}psnName != null">
                t.PSN_NAME = #{${joiner}psnName},
            </when>
            <otherwise>
                t.PSN_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}idc != null">
                t.IDC = #{${joiner}idc},
            </when>
            <otherwise>
                t.IDC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByZoneId != null and ${joiner}fkByZoneId.rid != null">
                t.ZONE_ID = #{${joiner}fkByZoneId.rid},
            </when>
            <otherwise>
                t.ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}siteName != null">
                t.SITE_NAME = #{${joiner}siteName},
            </when>
            <otherwise>
                t.SITE_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fstRecovrDate != null">
                t.FST_RECOVR_DATE = #{${joiner}fstRecovrDate},
            </when>
            <otherwise>
                t.FST_RECOVR_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lstRecovrDate != null">
                t.LST_RECOVR_DATE = #{${joiner}lstRecovrDate},
            </when>
            <otherwise>
                t.LST_RECOVR_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hasInstRevocery != null">
                t.HAS_INST_REVOCERY = #{${joiner}hasInstRevocery},
            </when>
            <otherwise>
                t.HAS_INST_REVOCERY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hasMedRevocery != null">
                t.HAS_MED_REVOCERY = #{${joiner}hasMedRevocery},
            </when>
            <otherwise>
                t.HAS_MED_REVOCERY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hasNutriRevocery != null">
                t.HAS_NUTRI_REVOCERY = #{${joiner}hasNutriRevocery},
            </when>
            <otherwise>
                t.HAS_NUTRI_REVOCERY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hasPsyRevocery != null">
                t.HAS_PSY_REVOCERY = #{${joiner}hasPsyRevocery},
            </when>
            <otherwise>
                t.HAS_PSY_REVOCERY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hasOtherRevocery != null">
                t.HAS_OTHER_REVOCERY = #{${joiner}hasOtherRevocery},
            </when>
            <otherwise>
                t.HAS_OTHER_REVOCERY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rehabiNum != null">
                t.REHABI_NUM = #{${joiner}rehabiNum},
            </when>
            <otherwise>
                t.REHABI_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}totalTime != null">
                t.TOTAL_TIME = #{${joiner}totalTime},
            </when>
            <otherwise>
                t.TOTAL_TIME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}avgTime != null">
                t.AVG_TIME = #{${joiner}avgTime},
            </when>
            <otherwise>
                t.AVG_TIME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fstBloodOxygen != null">
                t.FST_BLOOD_OXYGEN = #{${joiner}fstBloodOxygen},
            </when>
            <otherwise>
                t.FST_BLOOD_OXYGEN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lstBloodOxygen != null">
                t.LST_BLOOD_OXYGEN = #{${joiner}lstBloodOxygen},
            </when>
            <otherwise>
                t.LST_BLOOD_OXYGEN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fstAdl != null">
                t.FST_ADL = #{${joiner}fstAdl},
            </when>
            <otherwise>
                t.FST_ADL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lstAdl != null">
                t.LST_ADL = #{${joiner}lstAdl},
            </when>
            <otherwise>
                t.LST_ADL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fstAnxiety != null">
                t.FST_ANXIETY = #{${joiner}fstAnxiety},
            </when>
            <otherwise>
                t.FST_ANXIETY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lstAnxiety != null">
                t.LST_ANXIETY = #{${joiner}lstAnxiety},
            </when>
            <otherwise>
                t.LST_ANXIETY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fstDepression != null">
                t.FST_DEPRESSION = #{${joiner}fstDepression},
            </when>
            <otherwise>
                t.FST_DEPRESSION = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lstDepression != null">
                t.LST_DEPRESSION = #{${joiner}lstDepression},
            </when>
            <otherwise>
                t.LST_DEPRESSION = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_CFKF_RECOVERY_RST t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_CFKF_RECOVERY_RST t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_CFKF_RECOVERY_RST t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdCfkfRecoveryRst">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_CFKF_RECOVERY_RST_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_CFKF_RECOVERY_RST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            ANALY_YEAR,
            PSN_NO,
            PSN_NAME,
            IDC,
            ZONE_ID,
            SITE_NAME,
            FST_RECOVR_DATE,
            LST_RECOVR_DATE,
            HAS_INST_REVOCERY,
            HAS_MED_REVOCERY,
            HAS_NUTRI_REVOCERY,
            HAS_PSY_REVOCERY,
            HAS_OTHER_REVOCERY,
            REHABI_NUM,
            TOTAL_TIME,
            AVG_TIME,
            FST_BLOOD_OXYGEN,
            LST_BLOOD_OXYGEN,
            FST_ADL,
            LST_ADL,
            FST_ANXIETY,
            LST_ANXIETY,
            FST_DEPRESSION,
            LST_DEPRESSION,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{analyYear},
            #{psnNo},
            #{psnName},
            #{idc},
            #{fkByZoneId.rid},
            #{siteName},
            #{fstRecovrDate},
            #{lstRecovrDate},
            #{hasInstRevocery},
            #{hasMedRevocery},
            #{hasNutriRevocery},
            #{hasPsyRevocery},
            #{hasOtherRevocery},
            #{rehabiNum},
            #{totalTime},
            #{avgTime},
            #{fstBloodOxygen},
            #{lstBloodOxygen},
            #{fstAdl},
            #{lstAdl},
            #{fstAnxiety},
            #{lstAnxiety},
            #{fstDepression},
            #{lstDepression},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_CFKF_RECOVERY_RST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            ANALY_YEAR,
            PSN_NO,
            PSN_NAME,
            IDC,
            ZONE_ID,
            SITE_NAME,
            FST_RECOVR_DATE,
            LST_RECOVR_DATE,
            HAS_INST_REVOCERY,
            HAS_MED_REVOCERY,
            HAS_NUTRI_REVOCERY,
            HAS_PSY_REVOCERY,
            HAS_OTHER_REVOCERY,
            REHABI_NUM,
            TOTAL_TIME,
            AVG_TIME,
            FST_BLOOD_OXYGEN,
            LST_BLOOD_OXYGEN,
            FST_ADL,
            LST_ADL,
            FST_ANXIETY,
            LST_ANXIETY,
            FST_DEPRESSION,
            LST_DEPRESSION,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_CFKF_RECOVERY_RST_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.analyYear != null">
                            #{item.analyYear} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.psnNo != null">
                            #{item.psnNo} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.psnName != null">
                            #{item.psnName} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.idc != null">
                            #{item.idc} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByZoneId != null and item.fkByZoneId.rid != null">
                            #{item.fkByZoneId.rid} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.siteName != null">
                            #{item.siteName} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fstRecovrDate != null">
                            #{item.fstRecovrDate} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.lstRecovrDate != null">
                            #{item.lstRecovrDate} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hasInstRevocery != null">
                            #{item.hasInstRevocery} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hasMedRevocery != null">
                            #{item.hasMedRevocery} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hasNutriRevocery != null">
                            #{item.hasNutriRevocery} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hasPsyRevocery != null">
                            #{item.hasPsyRevocery} AS C16,
                        </when>
                        <otherwise>
                            NULL AS C16,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hasOtherRevocery != null">
                            #{item.hasOtherRevocery} AS C17,
                        </when>
                        <otherwise>
                            NULL AS C17,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rehabiNum != null">
                            #{item.rehabiNum} AS C18,
                        </when>
                        <otherwise>
                            NULL AS C18,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.totalTime != null">
                            #{item.totalTime} AS C19,
                        </when>
                        <otherwise>
                            NULL AS C19,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.avgTime != null">
                            #{item.avgTime} AS C20,
                        </when>
                        <otherwise>
                            NULL AS C20,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fstBloodOxygen != null">
                            #{item.fstBloodOxygen} AS C21,
                        </when>
                        <otherwise>
                            NULL AS C21,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.lstBloodOxygen != null">
                            #{item.lstBloodOxygen} AS C22,
                        </when>
                        <otherwise>
                            NULL AS C22,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fstAdl != null">
                            #{item.fstAdl} AS C23,
                        </when>
                        <otherwise>
                            NULL AS C23,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.lstAdl != null">
                            #{item.lstAdl} AS C24,
                        </when>
                        <otherwise>
                            NULL AS C24,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fstAnxiety != null">
                            #{item.fstAnxiety} AS C25,
                        </when>
                        <otherwise>
                            NULL AS C25,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.lstAnxiety != null">
                            #{item.lstAnxiety} AS C26,
                        </when>
                        <otherwise>
                            NULL AS C26,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fstDepression != null">
                            #{item.fstDepression} AS C27,
                        </when>
                        <otherwise>
                            NULL AS C27,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.lstDepression != null">
                            #{item.lstDepression} AS C28,
                        </when>
                        <otherwise>
                            NULL AS C28,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdCfkfRecoveryRst" >
        update TD_CFKF_RECOVERY_RST t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_CFKF_RECOVERY_RST t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdCfkfRecoveryRst">
        delete from TD_CFKF_RECOVERY_RST
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

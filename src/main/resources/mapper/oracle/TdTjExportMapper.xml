<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdTjExportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdTjExport">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="BUS_TYPE_ID" property="fkByBusTypeId.rid" />
        <result column="EXPORT_CONDITION" property="exportCondition" />
        <result column="EXPORT_CONDITION_SHOW" property="exportConditionShow" />
        <result column="EXPORT_DATE" property="exportDate" />
        <result column="STATE" property="state" />
        <result column="OPER_UNIT_ID" property="fkByOperUnitId.rid" />
        <result column="OPER_PSN_ID" property="fkByOperPsnId.rid" />
        <result column="EXPORT_FILE_NAME" property="exportFileName" />
        <result column="EXPORT_FILE_PATH" property="exportFilePath" />
        <result column="EXPORT_FILE_DATE" property="exportFileDate" />
        <result column="ERROR_MSG" property="errorMsg" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BUS_TYPE_ID,t.EXPORT_CONDITION,t.EXPORT_CONDITION_SHOW,t.EXPORT_DATE,t.STATE,t.OPER_UNIT_ID,t.OPER_PSN_ID,t.EXPORT_FILE_NAME,t.EXPORT_FILE_PATH,t.EXPORT_FILE_DATE,t.ERROR_MSG,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByBusTypeId != null and ${joiner}fkByBusTypeId.rid != null">
            and ${mAlias}BUS_TYPE_ID = #{${joiner}fkByBusTypeId.rid}
        </if>
        <if test="${joiner}exportCondition != null">
            and ${mAlias}EXPORT_CONDITION = #{${joiner}exportCondition}
        </if>
        <if test="${joiner}exportConditionShow != null">
            and ${mAlias}EXPORT_CONDITION_SHOW = #{${joiner}exportConditionShow}
        </if>
        <if test="${joiner}exportDate != null">
            and ${mAlias}EXPORT_DATE = #{${joiner}exportDate}
        </if>
        <if test="${joiner}state != null">
            and ${mAlias}STATE = #{${joiner}state}
        </if>
        <if test="${joiner}fkByOperUnitId != null and ${joiner}fkByOperUnitId.rid != null">
            and ${mAlias}OPER_UNIT_ID = #{${joiner}fkByOperUnitId.rid}
        </if>
        <if test="${joiner}fkByOperPsnId != null and ${joiner}fkByOperPsnId.rid != null">
            and ${mAlias}OPER_PSN_ID = #{${joiner}fkByOperPsnId.rid}
        </if>
        <if test="${joiner}exportFileName != null">
            and ${mAlias}EXPORT_FILE_NAME = #{${joiner}exportFileName}
        </if>
        <if test="${joiner}exportFilePath != null">
            and ${mAlias}EXPORT_FILE_PATH = #{${joiner}exportFilePath}
        </if>
        <if test="${joiner}exportFileDate != null">
            and ${mAlias}EXPORT_FILE_DATE = #{${joiner}exportFileDate}
        </if>
        <if test="${joiner}errorMsg != null">
            and ${mAlias}ERROR_MSG = #{${joiner}errorMsg}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByBusTypeId != null and ${joiner}fkByBusTypeId.rid != null">
                t.BUS_TYPE_ID = #{${joiner}fkByBusTypeId.rid},
            </when>
            <otherwise>
                t.BUS_TYPE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}exportCondition != null">
                t.EXPORT_CONDITION = #{${joiner}exportCondition},
            </when>
            <otherwise>
                t.EXPORT_CONDITION = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}exportConditionShow != null">
                t.EXPORT_CONDITION_SHOW = #{${joiner}exportConditionShow},
            </when>
            <otherwise>
                t.EXPORT_CONDITION_SHOW = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}exportDate != null">
                t.EXPORT_DATE = #{${joiner}exportDate},
            </when>
            <otherwise>
                t.EXPORT_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}state != null">
                t.STATE = #{${joiner}state},
            </when>
            <otherwise>
                t.STATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByOperUnitId != null and ${joiner}fkByOperUnitId.rid != null">
                t.OPER_UNIT_ID = #{${joiner}fkByOperUnitId.rid},
            </when>
            <otherwise>
                t.OPER_UNIT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByOperPsnId != null and ${joiner}fkByOperPsnId.rid != null">
                t.OPER_PSN_ID = #{${joiner}fkByOperPsnId.rid},
            </when>
            <otherwise>
                t.OPER_PSN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}exportFileName != null">
                t.EXPORT_FILE_NAME = #{${joiner}exportFileName},
            </when>
            <otherwise>
                t.EXPORT_FILE_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}exportFilePath != null">
                t.EXPORT_FILE_PATH = #{${joiner}exportFilePath},
            </when>
            <otherwise>
                t.EXPORT_FILE_PATH = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}exportFileDate != null">
                t.EXPORT_FILE_DATE = #{${joiner}exportFileDate},
            </when>
            <otherwise>
                t.EXPORT_FILE_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}errorMsg != null">
                t.ERROR_MSG = #{${joiner}errorMsg},
            </when>
            <otherwise>
                t.ERROR_MSG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}delFileState != null">
                t.DEL_FILE_STATE = #{${joiner}delFileState},
            </when>
            <otherwise>
                t.DEL_FILE_STATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}delFileErrorMsg != null">
                t.DEL_FILE_ERROR_MSG = #{${joiner}delFileErrorMsg},
            </when>
            <otherwise>
                t.DEL_FILE_ERROR_MSG = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_EXPORT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_EXPORT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_EXPORT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdTjExport">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_TJ_EXPORT_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_TJ_EXPORT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            BUS_TYPE_ID,
            EXPORT_CONDITION,
            EXPORT_CONDITION_SHOW,
            EXPORT_DATE,
            STATE,
            OPER_UNIT_ID,
            OPER_PSN_ID,
            EXPORT_FILE_NAME,
            EXPORT_FILE_PATH,
            EXPORT_FILE_DATE,
            ERROR_MSG,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{fkByBusTypeId.rid},
            #{exportCondition},
            #{exportConditionShow},
            #{exportDate},
            #{state},
            #{fkByOperUnitId.rid},
            #{fkByOperPsnId.rid},
            #{exportFileName},
            #{exportFilePath},
            #{exportFileDate},
            #{errorMsg},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_TJ_EXPORT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            BUS_TYPE_ID,
            EXPORT_CONDITION,
            EXPORT_CONDITION_SHOW,
            EXPORT_DATE,
            STATE,
            OPER_UNIT_ID,
            OPER_PSN_ID,
            EXPORT_FILE_NAME,
            EXPORT_FILE_PATH,
            EXPORT_FILE_DATE,
            ERROR_MSG,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_TJ_EXPORT_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByBusTypeId != null and item.fkByBusTypeId.rid != null">
                            #{item.fkByBusTypeId.rid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.exportCondition != null">
                            #{item.exportCondition} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.exportConditionShow != null">
                            #{item.exportConditionShow} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.exportDate != null">
                            #{item.exportDate} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.state != null">
                            #{item.state} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByOperUnitId != null and item.fkByOperUnitId.rid != null">
                            #{item.fkByOperUnitId.rid} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByOperPsnId != null and item.fkByOperPsnId.rid != null">
                            #{item.fkByOperPsnId.rid} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.exportFileName != null">
                            #{item.exportFileName} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.exportFilePath != null">
                            #{item.exportFilePath} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.exportFileDate != null">
                            #{item.exportFileDate} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.errorMsg != null">
                            #{item.errorMsg} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdTjExport" >
        update TD_TJ_EXPORT t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_TJ_EXPORT t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdTjExport">
        delete from TD_TJ_EXPORT
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <!-- 按创建日期排序分页查询 -->
    <select id="pageListOrderByCreateTime" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_EXPORT t
        inner join ts_simple_code t1 on t.bus_type_id=t1.rid
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
            <!-- 仅查询个案导出相关的导出任务 -->
            and t1.extends1 in (1,2,3,4)
        </where>
        ORDER BY t.CREATE_DATE
        ) ZWX
        ) WHERE 1=1
        <if test="first != null and pageSize != null">
            AND RN BETWEEN #{first} AND #{pageSize}
        </if>
    </select>
    <!--查询导出文件时间为时间点前且状态为已下载且删除状态为空或者0,按照导出文件时间倒叙-->
    <select id="queryCleanTdExportList" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
        SELECT
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        FROM TD_TJ_EXPORT t
        WHERE NVL(t.DEL_FILE_STATE,0) = 0
        AND t.STATE = 3
        <!-- 导出文件时间早于 cutoffTime 但不包含 -->
        <if test="cutoffTime != null">
            AND t.EXPORT_FILE_DATE &lt; TO_DATE(#{cutoffTime},'yyyy-MM-dd')
        </if>
        ORDER BY t.EXPORT_FILE_DATE
        ) ZWX
        ) WHERE 1=1
        <if test="dataSize != null">
            AND  RN &lt;= #{dataSize}
        </if>
    </select>


</mapper>

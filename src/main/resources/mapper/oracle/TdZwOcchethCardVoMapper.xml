<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwOcchethCardVoMapper">

<!-- ========================自定义方法====================================== -->
    <select id="findOcchethCard" resultType="com.chis.modules.timer.heth.logic.vo.TdZwOcchethCardVo">
        WITH table1 AS (
            SELECT T.rid,
                ''                                         as securityKey,
                T1.REPORT_ID                               as reportId,
                T1.RID                                     as rcdRid,
                T4.CREDIT_CODE                             as ocode,
                T.PRO_FZ                                   as cname,
                T.PRO_LINKTEL                              as cphone,
                T.CRPT_NAME                                as soname,
                T.RPT_NO                                   as sreportnumber,
                T.CREDIT_CODE                              as socode,
                T6.ZONE_GB                                 as sssxbm,
                T.ADDRESS                                  as saddress,
                T.SAFEPOSITION                             as scname,
                T.SAFEPHONE                                as scphone,
                T7.CODE_NO                                 as ssize,
                TO_CHAR(T.INVEST_START_DATE, 'yyyy-MM-dd') as sfintimes,
                TO_CHAR(T.INVEST_END_DATE, 'yyyy-MM-dd')   as sfintimen,
                TO_CHAR(T.JC_START_DATE, 'yyyy-MM-dd')     as sametimes,
                TO_CHAR(T.JC_END_DATE, 'yyyy-MM-dd')       as sametimen,
                TO_CHAR(T.RPT_DATE, 'yyyy-MM-dd')          as sitrtime,
                T.IF_BADRSN_JC                             as sisys,
                T.IF_STATUS_PJ                             as sisxz,
                T.IF_INST_USE_PJ                           as sissf,
                T.JC_INST_NUM                              as sissfsseq,
                T.JC_NOT_HG_INST_NUM                       as sissfssbq,
                T.NOT_HG_INST_NAME                         as sissfssco,
                T.JC_USE_NUM                               as sissfyfeq,
                T.JC_NOT_HG_USE_NUM                        as sissfyfbq,
                T.NOT_HG_USE_NAME                          as sissfyfco,
                T.IF_STATUS_PJ,
                T.IF_BADRSN_JC,
                T.JC_POST_NUM                              as sysnums,
                T.JC_OVER_NUM                              as sysexnums,
                T.PJ_POST_NUM                              as sxznums,
                T.PJ_OVER_NUM                              as sxzexnums,
                T.IF_BADRSN_JC                              as ifBadrsnJc,
                T.IF_STATUS_PJ                              as ifStatusPj
        FROM TD_ZW_OCCHETH_CARD T
                LEFT JOIN TD_ZYWS_CARD_RCD T1 ON T.RID = T1.BUS_ID AND T1.BUS_TYPE = 12
                LEFT JOIN TD_ZW_OCCHETH_INFO T2 ON T.FILL_UNIT_ID = T2.ORG_ID
                LEFT JOIN TD_ZYWS_CARD_RCD T3 ON T2.RID = T3.BUS_ID AND T3.BUS_TYPE = 11
                LEFT JOIN TS_UNIT T4 ON T4.RID = T.FILL_UNIT_ID
                LEFT JOIN TB_TJ_CRPT T5 ON T5.RID = T.CRPT_ID
                LEFT JOIN TS_ZONE T6 ON T6.RID = T.ZONE_ID
                LEFT JOIN TS_SIMPLE_CODE T7 on T.CRPT_SIZE_ID=T7.RID
            WHERE T.STATE = 1
                AND NVL(T.SOURCE, 0) = 0
                AND NVL(T1.STATE, 0) = 0
                AND T3.STATE = 1
                AND T.DEL_MARK = 0
                <if test="startDate != null and startDate != ''">
                    AND T.CREATE_DATE &gt;= TO_DATE(#{startDate},'yyyy-MM-dd')
                </if>
                AND ROWNUM &lt;= #{dataSize}
        ),
        SIMPLECODETMP2 AS(
            SELECT S.RID, S.CODE_NAME, S.EXTENDS1
            FROM TS_SIMPLE_CODE S
                INNER JOIN TS_CODE_TYPE S1 ON S.CODE_TYPE_ID = S1.RID
            WHERE S1.CODE_TYPE_NAME = '5517'
        )
        SELECT
            kk.*,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_CARD_SERVICE TT
            INNER JOIN TS_SIMPLE_CODE TT1 ON TT.SERVICE_AREA_ID = TT1.RID
            WHERE TT.MAIN_ID=kk.RID AND TT1.EXTENDS1='1') THEN 1 ELSE 0 END AS sisck,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_CARD_SERVICE TT
            INNER JOIN TS_SIMPLE_CODE TT1 ON TT.SERVICE_AREA_ID = TT1.RID
            WHERE TT.MAIN_ID=kk.RID AND TT1.EXTENDS1='2') THEN 1 ELSE 0 END AS sishg,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_CARD_SERVICE TT
            INNER JOIN TS_SIMPLE_CODE TT1 ON TT.SERVICE_AREA_ID = TT1.RID
            WHERE TT.MAIN_ID=kk.RID AND TT1.EXTENDS1='3') THEN 1 ELSE 0 END AS sisyj,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_CARD_SERVICE TT
            INNER JOIN TS_SIMPLE_CODE TT1 ON TT.SERVICE_AREA_ID = TT1.RID
            WHERE TT.MAIN_ID=kk.RID AND TT1.EXTENDS1='4') THEN 1 ELSE 0 END AS sisjx,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_CARD_SERVICE TT
            INNER JOIN TS_SIMPLE_CODE TT1 ON TT.SERVICE_AREA_ID = TT1.RID
            WHERE TT.MAIN_ID=kk.RID AND TT1.EXTENDS1='5') THEN 1 ELSE 0 END AS sishs,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_CARD_SERVICE TT
            INNER JOIN TS_SIMPLE_CODE TT1 ON TT.SERVICE_AREA_ID = TT1.RID
            WHERE TT.MAIN_ID=kk.RID AND TT1.EXTENDS1='6') THEN 1 ELSE 0 END AS sishj,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_JC_BADRSN TT INNER JOIN SIMPLECODETMP2 TT1 ON TT.BADRSN_ID = TT1.RID
            WHERE TT.MAIN_ID = kk.RID AND TT1.EXTENDS1 = '1' and kk.IF_BADRSN_JC = 1) THEN 1 ELSE 0 END AS sysexhx,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_JC_BADRSN TT INNER JOIN SIMPLECODETMP2 TT1 ON TT.BADRSN_ID = TT1.RID
            WHERE TT.MAIN_ID = kk.RID AND TT1.EXTENDS1 = '2' and kk.IF_BADRSN_JC = 1) THEN 1 ELSE 0 END AS sysexfc,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_JC_BADRSN TT INNER JOIN SIMPLECODETMP2 TT1 ON TT.BADRSN_ID = TT1.RID
            WHERE TT.MAIN_ID = kk.RID AND TT1.EXTENDS1 = '3' and kk.IF_BADRSN_JC = 1) THEN 1 ELSE 0 END AS sysexwl,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_JC_BADRSN TT INNER JOIN SIMPLECODETMP2 TT1 ON TT.BADRSN_ID = TT1.RID
            WHERE TT.MAIN_ID = kk.RID AND TT1.EXTENDS1 = '4' and kk.IF_BADRSN_JC = 1) THEN 1 ELSE 0 END AS sysexfs,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_JC_BADRSN TT INNER JOIN SIMPLECODETMP2 TT1 ON TT.BADRSN_ID = TT1.RID
            WHERE TT.MAIN_ID = kk.RID AND TT1.EXTENDS1 = '5' and kk.IF_BADRSN_JC = 1) THEN 1 ELSE 0 END AS sysexsw,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_JC_BADRSN TT INNER JOIN SIMPLECODETMP2 TT1 ON TT.BADRSN_ID = TT1.RID
            WHERE TT.MAIN_ID = kk.RID AND TT1.EXTENDS1 = '6' and kk.IF_BADRSN_JC = 1) THEN 1 ELSE 0 END AS sysexqt,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_PJ_BADRSN TT INNER JOIN SIMPLECODETMP2 TT1 ON TT.BADRSN_ID = TT1.RID
            WHERE TT.MAIN_ID = kk.RID AND TT1.EXTENDS1 = '1' and kk.IF_STATUS_PJ = 1) THEN 1 ELSE 0 END AS sxzexhx,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_PJ_BADRSN TT INNER JOIN SIMPLECODETMP2 TT1 ON TT.BADRSN_ID = TT1.RID
            WHERE TT.MAIN_ID = kk.RID AND TT1.EXTENDS1 = '2' and kk.IF_STATUS_PJ = 1) THEN 1 ELSE 0 END AS sxzexfc,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_PJ_BADRSN TT INNER JOIN SIMPLECODETMP2 TT1 ON TT.BADRSN_ID = TT1.RID
            WHERE TT.MAIN_ID = kk.RID AND TT1.EXTENDS1 = '3' and kk.IF_STATUS_PJ = 1) THEN 1 ELSE 0 END AS sxzexwl,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_PJ_BADRSN TT INNER JOIN SIMPLECODETMP2 TT1 ON TT.BADRSN_ID = TT1.RID
            WHERE TT.MAIN_ID = kk.RID AND TT1.EXTENDS1 = '4' and kk.IF_STATUS_PJ = 1) THEN 1 ELSE 0 END AS sxzexfs,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_PJ_BADRSN TT INNER JOIN SIMPLECODETMP2 TT1 ON TT.BADRSN_ID = TT1.RID
            WHERE TT.MAIN_ID = kk.RID AND TT1.EXTENDS1 = '5' and kk.IF_STATUS_PJ = 1) THEN 1 ELSE 0 END AS sxzexsw,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_PJ_BADRSN TT INNER JOIN SIMPLECODETMP2 TT1 ON TT.BADRSN_ID = TT1.RID
            WHERE TT.MAIN_ID = kk.RID AND TT1.EXTENDS1 = '6' and kk.IF_STATUS_PJ = 1) THEN 1 ELSE 0 END AS sxzexqt
        FROM table1 kk
    </select>

    <!--职业卫生技术服务信息报送卡参与人员子表-->
    <select id="findOcchethCardPsn" resultType="com.chis.modules.timer.heth.logic.vo.TdZwOcchethCardPsnVo">
        with table1 as (
        SELECT *
        FROM (
            SELECT ZWX.*, ROWNUM AS RN
            FROM (
                     select T.RID,
                            T1.rid         as rcdRid,
                            ''             as securityKey,
                            T3.REPORT_ID   as reportId,
                            T4.CREDIT_CODE as ocode,
                            T.PSN_NAME     as name
                     from TD_ZW_OCCHETH_CARD_PSN T
                              left join TD_ZYWS_CARD_RCD T1 on T.RID = T1.BUS_ID and T1.BUS_TYPE = 13
                              left join TD_ZW_OCCHETH_CARD T2 on T.MAIN_ID = T2.RID
                              LEFT JOIN TD_ZYWS_CARD_RCD T3 ON T2.RID = T3.BUS_ID AND T3.BUS_TYPE = 12
                              left join TS_UNIT T4 on T2.FILL_UNIT_ID = T4.RID
                     where T3.STATE = 1 and nvl(T1.STATE,0)=0
                     AND ROWNUM &lt;= #{dataSize}
            ) ZWX
        )
        where 1=1
        )
        select kk.*,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_CARD_ITEM TT
            INNER JOIN TS_SIMPLE_CODE TT1 ON TT.ITEM_ID = TT1.RID
            WHERE TT.MAIN_ID=kk.RID AND TT1.EXTENDS1='1') THEN 1 ELSE 0 END AS job1,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_CARD_ITEM TT
            INNER JOIN TS_SIMPLE_CODE TT1 ON TT.ITEM_ID = TT1.RID
            WHERE TT.MAIN_ID=kk.RID AND TT1.EXTENDS1='3') THEN 1 ELSE 0 END AS job3,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_CARD_ITEM TT
            INNER JOIN TS_SIMPLE_CODE TT1 ON TT.ITEM_ID = TT1.RID
            WHERE TT.MAIN_ID=kk.RID AND TT1.EXTENDS1='4') THEN 1 ELSE 0 END AS job4,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_CARD_ITEM TT
            INNER JOIN TS_SIMPLE_CODE TT1 ON TT.ITEM_ID = TT1.RID
            WHERE TT.MAIN_ID=kk.RID AND (TT1.EXTENDS1='5' or TT1.EXTENDS1='2')) THEN 1 ELSE 0 END AS job5,
            CASE WHEN EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_CARD_ITEM TT
            INNER JOIN TS_SIMPLE_CODE TT1 ON TT.ITEM_ID = TT1.RID
            WHERE TT.MAIN_ID=kk.RID AND TT1.EXTENDS1='6') THEN 1 ELSE 0 END AS job6
        from table1 kk
    </select>

    <!--职业卫生技术服务信息报送卡服务地址子表-->
    <select id="findOcchethCardAddress" resultType="com.chis.modules.timer.heth.logic.vo.TdZwUploadAddressVo">
        SELECT *
        FROM (
            SELECT ZWX.*, ROWNUM AS RN
                FROM (
                         select T.RID,
                                T1.rid         as rcdRid,
                                ''             as securityKey,
                                T3.REPORT_ID   as reportId,
                                T4.CREDIT_CODE as ocode,
                                T5.ZONE_GB     as assxbm
                         from TD_ZW_OCCHETH_CARD_ZONE T
                                  left join TD_ZYWS_CARD_RCD T1 on T.RID = T1.BUS_ID and T1.BUS_TYPE = 14
                                  left join TD_ZW_OCCHETH_CARD T2 on T.MAIN_ID = T2.RID
                                  LEFT JOIN TD_ZYWS_CARD_RCD T3 ON T2.RID = T3.BUS_ID AND T3.BUS_TYPE = 12
                                  left join TS_UNIT T4 on T2.FILL_UNIT_ID = T4.RID
                                  left join TS_ZONE T5 on T.ZONE_ID = T5.RID
                         where T3.STATE = 1 and nvl(T1.STATE,0)=0
                      AND ROWNUM &lt;= #{dataSize}
                ) ZWX
        ) where 1=1
    </select>

    <select id="selectNeedUpdateFileAndSubmitDataForOccHealth" resultType="hashmap">
        SELECT REPORT_ID, CREDIT_CODE, ANNEX_PATH, SIGN_ADDRESS  FROM (
            SELECT R.REPORT_ID, U.CREDIT_CODE, C.ANNEX_PATH,C.SIGN_ADDRESS
            FROM TD_ZW_OCCHETH_CARD C
                    LEFT JOIN TD_ZYWS_CARD_RCD R ON C.RID = R.BUS_ID AND R.BUS_TYPE = #{busTypeMain}
                    LEFT JOIN TS_UNIT U ON C.FILL_UNIT_ID = U.RID
            WHERE C.CREATE_DATE <![CDATA[<=]]> #{lastDate}
                AND NVL(C.DEL_MARK, 0) = 0
                AND C.STATE = 1
                AND R.STATE = 1
                AND NOT EXISTS(SELECT 1
                                FROM TD_ZW_OCCHETH_CARD C1
                                LEFT JOIN TD_ZYWS_CARD_RCD R1 ON C1.RID = R1.BUS_ID AND R1.BUS_TYPE = #{busTypeMain}
                                LEFT JOIN TD_ZW_OCCHETH_CARD_PSN CP ON C1.RID = CP.MAIN_ID
                                LEFT JOIN TD_ZYWS_CARD_RCD R2 ON CP.RID = R2.BUS_ID AND R2.BUS_TYPE = #{busTypePsn}
                                LEFT JOIN TD_ZW_OCCHETH_CARD_ZONE CZ ON C1.RID = CZ.MAIN_ID
                                LEFT JOIN TD_ZYWS_CARD_RCD R3 ON CZ.RID = R3.BUS_ID AND R3.BUS_TYPE = #{busTypeZone}
                                WHERE C1.RID = C.RID
                                    AND (NVL(R1.STATE, 0) <![CDATA[<>]]> 1
                                            OR NVL(R2.STATE, 0) <![CDATA[<>]]> 1
                                            OR (NVL(R3.STATE, 0) <![CDATA[<>]]> 1 AND CZ.RID IS NOT NULL))
                                )
                AND R.REPORT_ID IS NOT NULL
                AND U.CREDIT_CODE IS NOT NULL
                AND C.ANNEX_PATH IS NOT NULL
               AND C.SIGN_ADDRESS IS NOT NULL
            GROUP BY R.REPORT_ID, U.CREDIT_CODE, C.ANNEX_PATH,  C.SIGN_ADDRESS
            ORDER BY R.REPORT_ID
        ) T
        WHERE ROWNUM <![CDATA[<=]]> #{dataSize}
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.OccDisCaseLateTimeMapper">

    <select id="selectBhkInfo"  resultType="com.chis.modules.timer.heth.pojo.OccDisCaseLatePojo">
        with table1 as (select T.RID,T.IDC, T1.RID as crptId,T1.CRPT_NAME, T3.OCC_DISEID,T4.EXTENDS3 as CODE_NO,T1.ZONE_ID,
                               REPLACE(CASE WHEN T5.ZONE_TYPE>2 THEN SUBSTR(T5.FULL_NAME, INSTR(T5.FULL_NAME,'_')+1) ELSE T5.FULL_NAME END,'_','') AS ZONE_NAME,
                               T.RPT_PRINT_DATE
                        from TD_TJ_BHK T
                                 inner join TB_TJ_CRPT T1 on T.CRPT_ID = T1.RID
                                 left join TS_SIMPLE_CODE T2 on T.CARD_TYPE_ID = T2.RID and T2.CODE_NO = '01'
                                 inner join TD_TJ_SUPOCCDISELIST T3 on T.RID=T3.BHK_ID
                                 inner join TS_SIMPLE_CODE T4 on T3.OCC_DISEID = T4.RID
                                 left join TS_ZONE T5 on T1.ZONE_ID=T5.RID
                        where T.IDC is not null
                          and T.IF_TARGETDIS = 1
                          and T.RPT_PRINT_DATE is not null
                          and T5.ZONE_GB like ''||#{zoneCode}||'%'
                        ),
             table2 as (select T.crptId, nvl(T1.BEGIN_DATE, to_date(#{beginDate}, 'yyyy-MM-dd')) as BEGIN_DATE, '1' as WARN_UNIT
                        from table1 T
                                 left join TB_ZW_BEGIN_DATE_RCD T1
                                           on T.crptId = T1.BUS_ID and T1.WARN_TYPE = 3 and T1.BUS_TYPE = 1 and T1.WARN_UNIT = 1
                        group by T.crptId, T1.BEGIN_DATE, T1.WARN_UNIT),
             table3 as (select T.crptId, nvl(T1.BEGIN_DATE, to_date(#{beginDate}, 'yyyy-MM-dd')) as BEGIN_DATE, '2' as WARN_UNIT
                        from table1 T
                                 left join TB_ZW_BEGIN_DATE_RCD T1
                                           on T.crptId = T1.BUS_ID and T1.WARN_TYPE = 3 and T1.BUS_TYPE = 1 and T1.WARN_UNIT = 2
                        group by T.crptId, T1.BEGIN_DATE, T1.WARN_UNIT),
             table4 as (select T.crptId, nvl(T1.BEGIN_DATE, to_date(#{beginDate}, 'yyyy-MM-dd')) as BEGIN_DATE, '3' as WARN_UNIT
                        from table1 T
                                 left join TB_ZW_BEGIN_DATE_RCD T1
                                           on T.crptId = T1.BUS_ID and T1.WARN_TYPE = 3 and T1.BUS_TYPE = 1 and T1.WARN_UNIT = 3
                        group by T.crptId, T1.BEGIN_DATE, T1.WARN_UNIT)

        select *
        from (select T.*, T1.BEGIN_DATE, T1.WARN_UNIT
              from table1 T
                       left join table2 T1 on T.crptId = T1.crptId
              union all

              select T.*, T1.BEGIN_DATE, T1.WARN_UNIT
              from table1 T
                       left join table3 T1 on T.crptId = T1.crptId
              union all

              select T.*, T1.BEGIN_DATE, T1.WARN_UNIT
              from table1 T
                  left join table4 T1 on T.crptId = T1.crptId
              ) TT
        where TT.RPT_PRINT_DATE >= TT.BEGIN_DATE
        group by TT.RID, TT.IDC,TT.ZONE_ID, TT.RPT_PRINT_DATE, TT.OCC_DISEID,TT.CODE_NO,TT.crptId,TT.CRPT_NAME,TT.ZONE_NAME, TT.BEGIN_DATE, TT.WARN_UNIT
        order by TT.crptId, TT.RPT_PRINT_DATE
    </select>


    <select id="selectOccDisCaseByBhkRids" resultType="com.chis.modules.timer.heth.pojo.OccDisCaseLatePojo">
        select T.RID, T.IDC, T.APPLY_DATE, T.APY_ZYB_SMALL_ID as OCC_DISEID
        from TD_ZW_OCCDISCASE T
                 left join TS_SIMPLE_CODE T1 on T.APY_ZYB_SMALL_ID = T1.RID
        where nvl(T.DEL_MARK,0)=0
            and T.APPLY_DATE is not null
            and T.IDC is not null
            and T.APY_ZYB_SMALL_ID is not null
            <if test="idcAndDiseIds!=null and idcAndDiseIds.size >0">
                and
                    <foreach collection="idcAndDiseIds"  index="index" open="(" close=")" item="item" separator="OR">
                        <trim  suffixOverrides="OR">
                          (T.IDC = #{item.idc} and T.APY_ZYB_SMALL_ID =#{item.diseId})
                        </trim>
                    </foreach>
            </if>
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.DisCaseJobMapper">
    <resultMap id="BusDataResultMap" type="com.chis.modules.timer.heth.pojo.bo.warn.DisCaseBusDataBO">
        <result column="BUS_ID" property="id"/>
        <result column="ORG_ID" property="orgId"/>
        <result column="ORG_NAME" property="orgName"/>
        <result column="ZONE_ID" property="zoneId"/>
        <result column="ZONE_NAME" property="zoneName"/>
        <result column="BUS_DATE" property="date"/>
        <result column="DIS_ID" property="disId"/>
    </resultMap>
    <resultMap id="NotInTimeDataResultMap" type="com.chis.modules.timer.heth.pojo.bo.warn.DisCaseNotInTime">
        <result column="BUS_ID" property="id"/>
        <result column="ORG_ID" property="orgId"/>
        <result column="ORG_NAME" property="orgName"/>
        <result column="ZONE_ID" property="zoneId"/>
        <result column="ZONE_NAME" property="zoneName"/>
        <result column="RCV_DATE" property="rcvDate"/>
        <result column="DEAL_DATE" property="dealDate"/>
        <result column="DIS_ID" property="disId"/>
        <result column="WARN_LEVEL" property="warnLevel"/>
        <result column="ID_DISID" property="idAndDisId"/>
    </resultMap>

    <select id="findOccDisCaseDataList" resultMap="BusDataResultMap">
        WITH CRPT_TABLE AS (
            SELECT R.BUS_ID
            FROM TB_ZW_BEGIN_DATE_RCD R
            WHERE R.WARN_TYPE = 2 AND R.BUS_TYPE = 1
            GROUP BY R.BUS_ID
        ),
        CRPT_LEVEL_TABLE AS (
            SELECT M.WARN_LEVEL, C.BUS_ID
            FROM TB_ZW_WARN_MODEL M, CRPT_TABLE C
            WHERE M.WARN_TYPE = 2 AND M.STATE_MARK = 1
            GROUP BY M.WARN_LEVEL, C.BUS_ID
        ),
        MIN_DATE_TABLE AS (
            SELECT BUS_ID, MIN(BEGIN_DATE) AS BEGIN_DATE
            FROM (
                SELECT M.BUS_ID, NVL(R.BEGIN_DATE, #{beginDate}) AS BEGIN_DATE
                FROM CRPT_LEVEL_TABLE M
                    LEFT JOIN TB_ZW_BEGIN_DATE_RCD R ON R.WARN_TYPE = 2 AND R.WARN_UNIT = M.WARN_LEVEL AND M.BUS_ID = R.BUS_ID
                WHERE R.BUS_TYPE IS NULL OR R.BUS_TYPE = 1
                )
            GROUP BY BUS_ID
        )
        SELECT
            OC.RID AS BUS_ID,
            OC.CRPT_ID AS ORG_ID,
            C.CRPT_NAME AS ORG_NAME,
            C.ZONE_ID AS ZONE_ID,
            Z.FULL_NAME AS ZONE_NAME,
            OC.DIAG_DATE AS BUS_DATE
        FROM TD_ZW_OCCDIS_CARD_NEW OC
            INNER JOIN TD_ZW_BGK_LAST_STA S ON S.CART_TYPE = 4 AND OC.RID = S.BUS_ID AND S.STATE = 7
            LEFT JOIN MIN_DATE_TABLE M ON OC.CRPT_ID = M.BUS_ID
            LEFT JOIN TB_TJ_CRPT C ON OC.CRPT_ID = C.RID
            LEFT JOIN TS_ZONE Z ON C.ZONE_ID = Z.RID
        WHERE OC.ZYB_TYPE_ID IS NOT NULL
        <if test="zoneGbMax != null and zoneGbMax != ''">
            AND Z.ZONE_GB LIKE (#{zoneGbMax} || '%') ESCAPE '\'
        </if>
            AND NVL(M.BEGIN_DATE, #{beginDate}) <![CDATA[<=]]> OC.DIAG_DATE
        ORDER BY OC.DIAG_DATE
    </select>

    <select id="findSuspectedOccDisCaseDataList" resultMap="BusDataResultMap">
        SELECT DISTINCT K.RID AS BUS_ID,
                        K.RPT_PRINT_DATE AS BUS_DATE,
                        K1.OCC_DISEID AS DIS_ID,
                        K.CRPT_ID AS ORG_ID,
                        K3.CRPT_NAME AS ORG_NAME,
                        K3.ZONE_ID AS ZONE_ID,
                        K4.FULL_NAME AS ZONE_NAME
        FROM TD_TJ_BHK K
        INNER JOIN TD_TJ_SUPOCCDISELIST K1 ON K1.BHK_ID = K.RID
        LEFT JOIN TB_ZW_BEGIN_DATE_RCD K2 ON K.CRPT_ID = K2.BUS_ID AND K2.WARN_TYPE = 1 AND K2.BUS_TYPE = 1 AND K2.WARN_UNIT = #{warnLevel}
        LEFT JOIN TB_TJ_CRPT K3 ON K3.RID = K.CRPT_ID
        LEFT JOIN TS_ZONE K4 ON K4.RID = K3.ZONE_ID
        WHERE K.IF_TARGETDIS = 1
        <if test="zoneGb != null and zoneGb != ''">
            AND K4.ZONE_GB LIKE (#{zoneGb} || '%') ESCAPE '\'
        </if>
           AND NVL(K2.BEGIN_DATE,#{beginDate}) &lt;= K.RPT_PRINT_DATE
    </select>
    <select id="findNotTimeBhkDatas" resultMap="NotInTimeDataResultMap">
        SELECT
            t.RID as BUS_ID
             ,t5.RID AS ORG_ID
             ,nvl(t4.ORG_NAME,t5.UNITNAME) as ORG_NAME
             ,t3.RID as ZONE_ID
             ,t3.FULL_NAME as ZONE_NAME
             ,t.RPT_PRINT_DATE as RCV_DATE
        FROM  TD_TJ_BHK t
          LEFT JOIN TB_TJ_SRVORG t2 ON t2.RID = t.BHKORG_ID
        LEFT JOIN TS_UNIT t5 ON t5.RID = t2.REG_ORGID
        LEFT JOIN TD_ZW_TJORGINFO t4 ON t4.ORG_ID = t2.REG_ORGID
        LEFT JOIN TS_ZONE t3 ON t3.RID = t5.ZONE_ID
        LEFT JOIN  TB_ZW_BEGIN_DATE_RCD t1 ON t1.BUS_ID = t5.RID AND t1.WARN_TYPE = 4
        AND t1.BUS_TYPE = 2 AND t1.WARN_UNIT = #{warnUnit}
        WHERE t.IF_TARGETDIS=1 AND t5.RID IS NOT NULL
        AND t.RPT_PRINT_DATE >= nvl(t1.BEGIN_DATE,#{beginDate})
          AND  t.RPT_PRINT_DATE  &lt; #{limitDate}
        <if test="zoneCode != null and zoneCode != ''">
            AND t3.ZONE_CODE like concat(#{zoneCode},'%')
        </if>

    </select>
    <select id="findSupoccdiseList" resultMap="NotInTimeDataResultMap">
        SELECT DISTINCT T.BHK_ID as  BUS_ID,T.OCC_DISEID as DIS_ID
        FROM  TD_TJ_SUPOCCDISELIST T
        <where>
            <if test="bhkRids != null and bhkRids.size > 0">
                 T.BHK_ID IN
                <foreach collection="bhkRids" item="item" index="index" separator="," open="(" close=")" >
                    <trim  suffixOverrides=",">
                        #{item}
                    </trim>
                </foreach>
            </if>
        </where>
    </select>
    <select id="findYszybRptList" resultMap="NotInTimeDataResultMap">
        SELECT T.REL_BHK_ID as BUS_ID
             ,T.OCC_DISEID as DIS_ID
             ,T.RPT_DATE as DEAL_DATE
        ,T.REL_BHK_ID ||'_'|| T.OCC_DISEID as ID_DISID
        FROM TD_ZW_YSZYB_RPT T WHERE T.DEL_MARK = 0
        <if test="bhkRids != null and bhkRids.size > 0">
            AND T.REL_BHK_ID IN
            <foreach collection="bhkRids" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>

    </select>

    <select id="findDisCaseNotInTimeDataList" resultMap="NotInTimeDataResultMap" >
        SELECT
            T.RID AS BUS_ID,
            T2.RID AS ORG_ID,
            T2.ORG_NAME AS ORG_NAME,
            T4.RID AS ZONE_ID,
            T4.FULL_NAME AS ZONE_NAME,
            T.TL_DATE AS RCV_DATE,
            T1.RPT_DATE AS DEAL_DATE
        FROM TD_ZW_OCCDISCASE T
        LEFT JOIN TD_ZW_OCCDIS_CARD_NEW T1 ON T1.MAIN_ID = T.RID
        LEFT JOIN TD_ZW_DIAGORGINFO T2 ON T.ACPTORG_ID = T2.ORG_ID
        LEFT JOIN TS_UNIT T3 ON T3.RID = T.ACPTORG_ID
        LEFT JOIN TS_ZONE T4 ON T3.ZONE_ID = T4.RID
        LEFT JOIN TB_ZW_BEGIN_DATE_RCD T5 ON T5.BUS_ID = T2.RID AND T5.WARN_TYPE = 5 AND T5.BUS_TYPE = 3 AND T5.WARN_UNIT = #{warnLevel}
        WHERE
            T.STATE_MARK &gt; 4
        <if test="zoneGb != null and zoneGb != ''">
            AND T4.ZONE_GB LIKE (#{zoneGb} || '%') ESCAPE '\'
        </if>
            AND NVL(T.DEL_MARK, 0)= 0
            AND NVL(T1.RPT_DATE, T.TL_DATE + #{warnDays} + 1)- #{warnDays} &gt; T.TL_DATE
            AND T.TL_DATE &lt; #{limitDate}
            AND NVL(T5.BEGIN_DATE, #{beginDate}) &lt;= T.TL_DATE
    </select>
</mapper>
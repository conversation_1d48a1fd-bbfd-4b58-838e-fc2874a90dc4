<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwcfPatientInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwcfPatientInfo">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="PSN_NAME" property="psnName" />
        <result column="PSN_LINK_WAY" property="psnLinkWay" />
        <result column="IDC" property="idc" />
        <result column="BIRTHDAY" property="birthday" />
        <result column="SEX" property="sex" />
        <result column="CRPT_ZONE_ID" property="fkByCrptZoneId.rid" />
        <result column="CRPT_NAME" property="crptName" />
        <result column="INDUS_TYPE_ID" property="fkByIndusTypeId.rid" />
        <result column="TCH_DUST_YEAR" property="tchDustYear" />
        <result column="TCH_DUST_MONTH" property="tchDustMonth" />
        <result column="PNEUMSIS_TYPE_ID" property="fkByPneumsisTypeId.rid" />
        <result column="OTHER_PNES_NAME" property="otherPnesName" />
        <result column="DIAG_1_DATE" property="diag1Date" />
        <result column="DIAG_2_DATE" property="diag2Date" />
        <result column="DIAG_3_DATE" property="diag3Date" />
        <result column="LIVE_STATE" property="liveState" />
        <result column="DEATH_DATE" property="deathDate" />
        <result column="DIRECT_DEATH_RSN" property="directDeathRsn" />
        <result column="INDIRECT_DEATH_RSN2" property="indirectDeathRsn2" />
        <result column="ORIGIN_DEATH_RSN2" property="originDeathRsn2" />
        <result column="DEATH_RSN_ID" property="fkByDeathRsnId.rid" />
        <result column="RCD_USER_ID" property="fkByRcdUserId.rid" />
        <result column="RCD_UNIT_ID" property="fkByRcdUnitId.rid" />
        <result column="MOD_USER_ID" property="fkByModUserId.rid" />
        <result column="MOD_UNIT_ID" property="fkByModUnitId.rid" />
        <result column="MAIN_CARD_ID" property="mainCardId" />
        <result column="SUB_CARD_ID" property="subCardId" />
        <result column="PATIENT_SOURCE" property="patientSource" />
        <result column="RPT_CARD_NO" property="rptCardNo" />
        <result column="RPT_TYPE_ID" property="fkByRptTypeId.rid" />
        <result column="LINK_PSN_NAME" property="linkPsnName" />
        <result column="LINK_ADDR" property="linkAddr" />
        <result column="LINK_PHONE" property="linkPhone" />
        <result column="CRPT_CREDIT_CODE" property="crptCreditCode" />
        <result column="CRPT_ADDR" property="crptAddr" />
        <result column="ECONOMY_ID" property="fkByEconomyId.rid" />
        <result column="CRPT_SIZE_ID" property="fkByCrptSizeId.rid" />
        <result column="ANALY_WORK_ID" property="fkByAnalyWorkId.rid" />
        <result column="OTHER_WORK_NAME" property="otherWorkName" />
        <result column="BEG_TCH_DUST" property="begTchDust" />
        <result column="DIAG_UNIT_NAME" property="diagUnitName" />
        <result column="DIAG_RESP_PSN" property="diagRespPsn" />
        <result column="FILL_FORM_PSN" property="fillFormPsn" />
        <result column="FILL_LINK" property="fillLink" />
        <result column="FILL_DATE" property="fillDate" />
        <result column="IF_TB" property="ifTb" />
        <result column="TB_DIAG_DATE" property="tbDiagDate" />
        <result column="IF_PUL_INFECTION" property="ifPulInfection" />
        <result column="INFECTION_DIAG_DATE" property="infectionDiagDate" />
        <result column="IF_THE_PNEUM" property="ifThePneum" />
        <result column="PNEUM_DIAG_DATE" property="pneumDiagDate" />
        <result column="IF_PUL_HEART" property="ifPulHeart" />
        <result column="HEART_DIAG_DATE" property="heartDiagDate" />
        <result column="IF_LUNG_CANCER" property="ifLungCancer" />
        <result column="LUNG_DIAG_DATE" property="lungDiagDate" />
        <result column="IF_UNIT_EXISTS" property="ifUnitExists" />
        <result column="IF_WORK_INSURANCE" property="ifWorkInsurance" />
        <result column="IF_MEDICAL_TREAT" property="ifMedicalTreat" />
        <result column="WORK_LEVEL_ID" property="fkByWorkLevelId.rid" />
        <result column="IF_INJURY_TREAT" property="ifInjuryTreat" />
        <result column="IF_DEATH_TREAT" property="ifDeathTreat" />
        <result column="IF_OTHER_TREAT" property="ifOtherTreat" />
        <result column="IF_GET_MEDICAL_HELP" property="ifGetMedicalHelp" />
        <result column="IF_GET_LIVE_HELP" property="ifGetLiveHelp" />
        <result column="RMK" property="rmk" />
        <result column="CRPT_ZONE_COD_GJ" property="crptZoneCodGj" />
        <result column="CRPT_ZONE_NAM_GJ" property="crptZoneNamGj" />
        <result column="RPT_TYPE_COD_GJ" property="rptTypeCodGj" />
        <result column="RPT_TYPE_NAM_GJ" property="rptTypeNamGj" />
        <result column="ECO_COD_GJ" property="ecoCodGj" />
        <result column="ECO_NAM_GJ" property="ecoNamGj" />
        <result column="INDUS_COD_GJ" property="indusCodGj" />
        <result column="INDUS_NAM_GJ" property="indusNamGj" />
        <result column="SIZE_COD_GJ" property="sizeCodGj" />
        <result column="SIZE_NAM_GJ" property="sizeNamGj" />
        <result column="ANALY_WORK_COD_GJ" property="analyWorkCodGj" />
        <result column="ANALY_WORK_NAM_GJ" property="analyWorkNamGj" />
        <result column="OTHER_WORK_NAM_GJ" property="otherWorkNamGj" />
        <result column="PNE_COD_GJ" property="pneCodGj" />
        <result column="PNE_NAM_GJ" property="pneNamGj" />
        <result column="OTHER_PNE_NAM_GJ" property="otherPneNamGj" />
        <result column="DEATH_RSN_COD_GJ" property="deathRsnCodGj" />
        <result column="DEATH_RSN_NAM_GJ" property="deathRsnNamGj" />
        <result column="OTHER_DEATH_NAM_GJ" property="otherDeathNamGj" />
        <result column="LINKMAN" property="linkman" />
        <result column="POSTALCODE" property="postalcode" />
        <result column="IF_CRPT_INDEMNITY" property="ifCrptIndemnity" />
        <result column="IF_URBAN_RURAL_INSURE" property="ifUrbanRuralInsure" />
        <result column="IF_BIG_DIS_INSURE" property="ifBigDisInsure" />
        <result column="MEDICAL_IN_INSURE_RATIO" property="medicalInInsureRatio" />
        <result column="MEDICAL_OUT_INSURE_RATIO" property="medicalOutInsureRatio" />
        <result column="SUBSISTENCE_CASE" property="subsistenceCase" />
        <result column="SUBSISTENCE_AMO" property="subsistenceAmo" />
        <result column="NORMAL_ZONE_ID" property="normalZoneId" />
        <result column="NORMAL_ADDR" property="normalAddr" />
        <result column="FLOWUP_DATE" property="flowupDate" />
        <result column="FLOWUP_PSN" property="flowupPsn" />
        <result column="NOW_ZONE_ID" property="nowZoneId" />
        <result column="NOW_ADDR" property="nowAddr" />
        <result column="SYNC_KF_STATE" property="syncKfState" />
        <result column="SYNC_KF_DATE" property="syncKfDate" />
        <result column="SYNC_KF_ERR_MSG" property="syncKfErrMsg" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.PSN_NAME,t.PSN_LINK_WAY,t.IDC,t.BIRTHDAY,t.SEX,t.CRPT_ZONE_ID,t.CRPT_NAME,t.INDUS_TYPE_ID,t.TCH_DUST_YEAR,t.TCH_DUST_MONTH,t.PNEUMSIS_TYPE_ID,t.OTHER_PNES_NAME,t.DIAG_1_DATE,t.DIAG_2_DATE,t.DIAG_3_DATE,t.LIVE_STATE,t.DEATH_DATE,t.DIRECT_DEATH_RSN,t.INDIRECT_DEATH_RSN2,t.ORIGIN_DEATH_RSN2,t.DEATH_RSN_ID,t.RCD_USER_ID,t.RCD_UNIT_ID,t.MOD_USER_ID,t.MOD_UNIT_ID,t.MAIN_CARD_ID,t.SUB_CARD_ID,t.PATIENT_SOURCE,t.RPT_CARD_NO,t.RPT_TYPE_ID,t.LINK_PSN_NAME,t.LINK_ADDR,t.LINK_PHONE,t.CRPT_CREDIT_CODE,t.CRPT_ADDR,t.ECONOMY_ID,t.CRPT_SIZE_ID,t.ANALY_WORK_ID,t.OTHER_WORK_NAME,t.BEG_TCH_DUST,t.DIAG_UNIT_NAME,t.DIAG_RESP_PSN,t.FILL_FORM_PSN,t.FILL_LINK,t.FILL_DATE,t.IF_TB,t.TB_DIAG_DATE,t.IF_PUL_INFECTION,t.INFECTION_DIAG_DATE,t.IF_THE_PNEUM,t.PNEUM_DIAG_DATE,t.IF_PUL_HEART,t.HEART_DIAG_DATE,t.IF_LUNG_CANCER,t.LUNG_DIAG_DATE,t.IF_UNIT_EXISTS,t.IF_WORK_INSURANCE,t.IF_MEDICAL_TREAT,t.WORK_LEVEL_ID,t.IF_INJURY_TREAT,t.IF_DEATH_TREAT,t.IF_OTHER_TREAT,t.IF_GET_MEDICAL_HELP,t.IF_GET_LIVE_HELP,t.RMK,t.CRPT_ZONE_COD_GJ,t.CRPT_ZONE_NAM_GJ,t.RPT_TYPE_COD_GJ,t.RPT_TYPE_NAM_GJ,t.ECO_COD_GJ,t.ECO_NAM_GJ,t.INDUS_COD_GJ,t.INDUS_NAM_GJ,t.SIZE_COD_GJ,t.SIZE_NAM_GJ,t.ANALY_WORK_COD_GJ,t.ANALY_WORK_NAM_GJ,t.OTHER_WORK_NAM_GJ,t.PNE_COD_GJ,t.PNE_NAM_GJ,t.OTHER_PNE_NAM_GJ,t.DEATH_RSN_COD_GJ,t.DEATH_RSN_NAM_GJ,t.OTHER_DEATH_NAM_GJ,t.LINKMAN,t.POSTALCODE,t.IF_CRPT_INDEMNITY,t.IF_URBAN_RURAL_INSURE,t.IF_BIG_DIS_INSURE,t.MEDICAL_IN_INSURE_RATIO,t.MEDICAL_OUT_INSURE_RATIO,t.SUBSISTENCE_CASE,t.SUBSISTENCE_AMO,t.NORMAL_ZONE_ID,t.NORMAL_ADDR,t.FLOWUP_DATE,t.FLOWUP_PSN,t.NOW_ZONE_ID,t.NOW_ADDR,t.SYNC_KF_STATE,t.SYNC_KF_DATE,t.SYNC_KF_ERR_MSG,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}psnName != null and ${joiner}psnName != ''">
            and ${mAlias}PSN_NAME = #{${joiner}psnName}
        </if>
        <if test="${joiner}psnLinkWay != null and ${joiner}psnLinkWay != ''">
            and ${mAlias}PSN_LINK_WAY = #{${joiner}psnLinkWay}
        </if>
        <if test="${joiner}idc != null and ${joiner}idc != ''">
            and ${mAlias}IDC = #{${joiner}idc}
        </if>
        <if test="${joiner}birthday != null">
            and ${mAlias}BIRTHDAY = #{${joiner}birthday}
        </if>
        <if test="${joiner}sex != null and ${joiner}sex != ''">
            and ${mAlias}SEX = #{${joiner}sex}
        </if>
        <if test="${joiner}fkByCrptZoneId != null and ${joiner}fkByCrptZoneId.rid != null">
            and ${mAlias}CRPT_ZONE_ID = #{${joiner}fkByCrptZoneId.rid}
        </if>
        <if test="${joiner}crptName != null and ${joiner}crptName != ''">
            and ${mAlias}CRPT_NAME = #{${joiner}crptName}
        </if>
        <if test="${joiner}fkByIndusTypeId != null and ${joiner}fkByIndusTypeId.rid != null">
            and ${mAlias}INDUS_TYPE_ID = #{${joiner}fkByIndusTypeId.rid}
        </if>
        <if test="${joiner}tchDustYear != null and ${joiner}tchDustYear != ''">
            and ${mAlias}TCH_DUST_YEAR = #{${joiner}tchDustYear}
        </if>
        <if test="${joiner}tchDustMonth != null and ${joiner}tchDustMonth != ''">
            and ${mAlias}TCH_DUST_MONTH = #{${joiner}tchDustMonth}
        </if>
        <if test="${joiner}fkByPneumsisTypeId != null and ${joiner}fkByPneumsisTypeId.rid != null">
            and ${mAlias}PNEUMSIS_TYPE_ID = #{${joiner}fkByPneumsisTypeId.rid}
        </if>
        <if test="${joiner}otherPnesName != null and ${joiner}otherPnesName != ''">
            and ${mAlias}OTHER_PNES_NAME = #{${joiner}otherPnesName}
        </if>
        <if test="${joiner}diag1Date != null">
            and ${mAlias}DIAG_1_DATE = #{${joiner}diag1Date}
        </if>
        <if test="${joiner}diag2Date != null">
            and ${mAlias}DIAG_2_DATE = #{${joiner}diag2Date}
        </if>
        <if test="${joiner}diag3Date != null">
            and ${mAlias}DIAG_3_DATE = #{${joiner}diag3Date}
        </if>
        <if test="${joiner}liveState != null and ${joiner}liveState != ''">
            and ${mAlias}LIVE_STATE = #{${joiner}liveState}
        </if>
        <if test="${joiner}deathDate != null">
            and ${mAlias}DEATH_DATE = #{${joiner}deathDate}
        </if>
        <if test="${joiner}directDeathRsn != null and ${joiner}directDeathRsn != ''">
            and ${mAlias}DIRECT_DEATH_RSN = #{${joiner}directDeathRsn}
        </if>
        <if test="${joiner}indirectDeathRsn2 != null and ${joiner}indirectDeathRsn2 != ''">
            and ${mAlias}INDIRECT_DEATH_RSN2 = #{${joiner}indirectDeathRsn2}
        </if>
        <if test="${joiner}originDeathRsn2 != null and ${joiner}originDeathRsn2 != ''">
            and ${mAlias}ORIGIN_DEATH_RSN2 = #{${joiner}originDeathRsn2}
        </if>
        <if test="${joiner}fkByDeathRsnId != null and ${joiner}fkByDeathRsnId.rid != null">
            and ${mAlias}DEATH_RSN_ID = #{${joiner}fkByDeathRsnId.rid}
        </if>
        <if test="${joiner}fkByRcdUserId != null and ${joiner}fkByRcdUserId.rid != null">
            and ${mAlias}RCD_USER_ID = #{${joiner}fkByRcdUserId.rid}
        </if>
        <if test="${joiner}fkByRcdUnitId != null and ${joiner}fkByRcdUnitId.rid != null">
            and ${mAlias}RCD_UNIT_ID = #{${joiner}fkByRcdUnitId.rid}
        </if>
        <if test="${joiner}fkByModUserId != null and ${joiner}fkByModUserId.rid != null">
            and ${mAlias}MOD_USER_ID = #{${joiner}fkByModUserId.rid}
        </if>
        <if test="${joiner}fkByModUnitId != null and ${joiner}fkByModUnitId.rid != null">
            and ${mAlias}MOD_UNIT_ID = #{${joiner}fkByModUnitId.rid}
        </if>
        <if test="${joiner}mainCardId != null and ${joiner}mainCardId != ''">
            and ${mAlias}MAIN_CARD_ID = #{${joiner}mainCardId}
        </if>
        <if test="${joiner}subCardId != null and ${joiner}subCardId != ''">
            and ${mAlias}SUB_CARD_ID = #{${joiner}subCardId}
        </if>
        <if test="${joiner}patientSource != null and ${joiner}patientSource != ''">
            and ${mAlias}PATIENT_SOURCE = #{${joiner}patientSource}
        </if>
        <if test="${joiner}rptCardNo != null and ${joiner}rptCardNo != ''">
            and ${mAlias}RPT_CARD_NO = #{${joiner}rptCardNo}
        </if>
        <if test="${joiner}fkByRptTypeId != null and ${joiner}fkByRptTypeId.rid != null">
            and ${mAlias}RPT_TYPE_ID = #{${joiner}fkByRptTypeId.rid}
        </if>
        <if test="${joiner}linkPsnName != null and ${joiner}linkPsnName != ''">
            and ${mAlias}LINK_PSN_NAME = #{${joiner}linkPsnName}
        </if>
        <if test="${joiner}linkAddr != null and ${joiner}linkAddr != ''">
            and ${mAlias}LINK_ADDR = #{${joiner}linkAddr}
        </if>
        <if test="${joiner}linkPhone != null and ${joiner}linkPhone != ''">
            and ${mAlias}LINK_PHONE = #{${joiner}linkPhone}
        </if>
        <if test="${joiner}crptCreditCode != null and ${joiner}crptCreditCode != ''">
            and ${mAlias}CRPT_CREDIT_CODE = #{${joiner}crptCreditCode}
        </if>
        <if test="${joiner}crptAddr != null and ${joiner}crptAddr != ''">
            and ${mAlias}CRPT_ADDR = #{${joiner}crptAddr}
        </if>
        <if test="${joiner}fkByEconomyId != null and ${joiner}fkByEconomyId.rid != null">
            and ${mAlias}ECONOMY_ID = #{${joiner}fkByEconomyId.rid}
        </if>
        <if test="${joiner}fkByCrptSizeId != null and ${joiner}fkByCrptSizeId.rid != null">
            and ${mAlias}CRPT_SIZE_ID = #{${joiner}fkByCrptSizeId.rid}
        </if>
        <if test="${joiner}fkByAnalyWorkId != null and ${joiner}fkByAnalyWorkId.rid != null">
            and ${mAlias}ANALY_WORK_ID = #{${joiner}fkByAnalyWorkId.rid}
        </if>
        <if test="${joiner}otherWorkName != null and ${joiner}otherWorkName != ''">
            and ${mAlias}OTHER_WORK_NAME = #{${joiner}otherWorkName}
        </if>
        <if test="${joiner}begTchDust != null">
            and ${mAlias}BEG_TCH_DUST = #{${joiner}begTchDust}
        </if>
        <if test="${joiner}diagUnitName != null and ${joiner}diagUnitName != ''">
            and ${mAlias}DIAG_UNIT_NAME = #{${joiner}diagUnitName}
        </if>
        <if test="${joiner}diagRespPsn != null and ${joiner}diagRespPsn != ''">
            and ${mAlias}DIAG_RESP_PSN = #{${joiner}diagRespPsn}
        </if>
        <if test="${joiner}fillFormPsn != null and ${joiner}fillFormPsn != ''">
            and ${mAlias}FILL_FORM_PSN = #{${joiner}fillFormPsn}
        </if>
        <if test="${joiner}fillLink != null and ${joiner}fillLink != ''">
            and ${mAlias}FILL_LINK = #{${joiner}fillLink}
        </if>
        <if test="${joiner}fillDate != null">
            and ${mAlias}FILL_DATE = #{${joiner}fillDate}
        </if>
        <if test="${joiner}ifTb != null and ${joiner}ifTb != ''">
            and ${mAlias}IF_TB = #{${joiner}ifTb}
        </if>
        <if test="${joiner}tbDiagDate != null">
            and ${mAlias}TB_DIAG_DATE = #{${joiner}tbDiagDate}
        </if>
        <if test="${joiner}ifPulInfection != null and ${joiner}ifPulInfection != ''">
            and ${mAlias}IF_PUL_INFECTION = #{${joiner}ifPulInfection}
        </if>
        <if test="${joiner}infectionDiagDate != null">
            and ${mAlias}INFECTION_DIAG_DATE = #{${joiner}infectionDiagDate}
        </if>
        <if test="${joiner}ifThePneum != null and ${joiner}ifThePneum != ''">
            and ${mAlias}IF_THE_PNEUM = #{${joiner}ifThePneum}
        </if>
        <if test="${joiner}pneumDiagDate != null">
            and ${mAlias}PNEUM_DIAG_DATE = #{${joiner}pneumDiagDate}
        </if>
        <if test="${joiner}ifPulHeart != null and ${joiner}ifPulHeart != ''">
            and ${mAlias}IF_PUL_HEART = #{${joiner}ifPulHeart}
        </if>
        <if test="${joiner}heartDiagDate != null">
            and ${mAlias}HEART_DIAG_DATE = #{${joiner}heartDiagDate}
        </if>
        <if test="${joiner}ifLungCancer != null and ${joiner}ifLungCancer != ''">
            and ${mAlias}IF_LUNG_CANCER = #{${joiner}ifLungCancer}
        </if>
        <if test="${joiner}lungDiagDate != null">
            and ${mAlias}LUNG_DIAG_DATE = #{${joiner}lungDiagDate}
        </if>
        <if test="${joiner}ifUnitExists != null and ${joiner}ifUnitExists != ''">
            and ${mAlias}IF_UNIT_EXISTS = #{${joiner}ifUnitExists}
        </if>
        <if test="${joiner}ifWorkInsurance != null and ${joiner}ifWorkInsurance != ''">
            and ${mAlias}IF_WORK_INSURANCE = #{${joiner}ifWorkInsurance}
        </if>
        <if test="${joiner}ifMedicalTreat != null and ${joiner}ifMedicalTreat != ''">
            and ${mAlias}IF_MEDICAL_TREAT = #{${joiner}ifMedicalTreat}
        </if>
        <if test="${joiner}fkByWorkLevelId != null and ${joiner}fkByWorkLevelId.rid != null">
            and ${mAlias}WORK_LEVEL_ID = #{${joiner}fkByWorkLevelId.rid}
        </if>
        <if test="${joiner}ifInjuryTreat != null and ${joiner}ifInjuryTreat != ''">
            and ${mAlias}IF_INJURY_TREAT = #{${joiner}ifInjuryTreat}
        </if>
        <if test="${joiner}ifDeathTreat != null and ${joiner}ifDeathTreat != ''">
            and ${mAlias}IF_DEATH_TREAT = #{${joiner}ifDeathTreat}
        </if>
        <if test="${joiner}ifOtherTreat != null and ${joiner}ifOtherTreat != ''">
            and ${mAlias}IF_OTHER_TREAT = #{${joiner}ifOtherTreat}
        </if>
        <if test="${joiner}ifGetMedicalHelp != null and ${joiner}ifGetMedicalHelp != ''">
            and ${mAlias}IF_GET_MEDICAL_HELP = #{${joiner}ifGetMedicalHelp}
        </if>
        <if test="${joiner}ifGetLiveHelp != null and ${joiner}ifGetLiveHelp != ''">
            and ${mAlias}IF_GET_LIVE_HELP = #{${joiner}ifGetLiveHelp}
        </if>
        <if test="${joiner}rmk != null and ${joiner}rmk != ''">
            and ${mAlias}RMK = #{${joiner}rmk}
        </if>
        <if test="${joiner}crptZoneCodGj != null and ${joiner}crptZoneCodGj != ''">
            and ${mAlias}CRPT_ZONE_COD_GJ = #{${joiner}crptZoneCodGj}
        </if>
        <if test="${joiner}crptZoneNamGj != null and ${joiner}crptZoneNamGj != ''">
            and ${mAlias}CRPT_ZONE_NAM_GJ = #{${joiner}crptZoneNamGj}
        </if>
        <if test="${joiner}rptTypeCodGj != null and ${joiner}rptTypeCodGj != ''">
            and ${mAlias}RPT_TYPE_COD_GJ = #{${joiner}rptTypeCodGj}
        </if>
        <if test="${joiner}rptTypeNamGj != null and ${joiner}rptTypeNamGj != ''">
            and ${mAlias}RPT_TYPE_NAM_GJ = #{${joiner}rptTypeNamGj}
        </if>
        <if test="${joiner}ecoCodGj != null and ${joiner}ecoCodGj != ''">
            and ${mAlias}ECO_COD_GJ = #{${joiner}ecoCodGj}
        </if>
        <if test="${joiner}ecoNamGj != null and ${joiner}ecoNamGj != ''">
            and ${mAlias}ECO_NAM_GJ = #{${joiner}ecoNamGj}
        </if>
        <if test="${joiner}indusCodGj != null and ${joiner}indusCodGj != ''">
            and ${mAlias}INDUS_COD_GJ = #{${joiner}indusCodGj}
        </if>
        <if test="${joiner}indusNamGj != null and ${joiner}indusNamGj != ''">
            and ${mAlias}INDUS_NAM_GJ = #{${joiner}indusNamGj}
        </if>
        <if test="${joiner}sizeCodGj != null and ${joiner}sizeCodGj != ''">
            and ${mAlias}SIZE_COD_GJ = #{${joiner}sizeCodGj}
        </if>
        <if test="${joiner}sizeNamGj != null and ${joiner}sizeNamGj != ''">
            and ${mAlias}SIZE_NAM_GJ = #{${joiner}sizeNamGj}
        </if>
        <if test="${joiner}analyWorkCodGj != null and ${joiner}analyWorkCodGj != ''">
            and ${mAlias}ANALY_WORK_COD_GJ = #{${joiner}analyWorkCodGj}
        </if>
        <if test="${joiner}analyWorkNamGj != null and ${joiner}analyWorkNamGj != ''">
            and ${mAlias}ANALY_WORK_NAM_GJ = #{${joiner}analyWorkNamGj}
        </if>
        <if test="${joiner}otherWorkNamGj != null and ${joiner}otherWorkNamGj != ''">
            and ${mAlias}OTHER_WORK_NAM_GJ = #{${joiner}otherWorkNamGj}
        </if>
        <if test="${joiner}pneCodGj != null and ${joiner}pneCodGj != ''">
            and ${mAlias}PNE_COD_GJ = #{${joiner}pneCodGj}
        </if>
        <if test="${joiner}pneNamGj != null and ${joiner}pneNamGj != ''">
            and ${mAlias}PNE_NAM_GJ = #{${joiner}pneNamGj}
        </if>
        <if test="${joiner}otherPneNamGj != null and ${joiner}otherPneNamGj != ''">
            and ${mAlias}OTHER_PNE_NAM_GJ = #{${joiner}otherPneNamGj}
        </if>
        <if test="${joiner}deathRsnCodGj != null and ${joiner}deathRsnCodGj != ''">
            and ${mAlias}DEATH_RSN_COD_GJ = #{${joiner}deathRsnCodGj}
        </if>
        <if test="${joiner}deathRsnNamGj != null and ${joiner}deathRsnNamGj != ''">
            and ${mAlias}DEATH_RSN_NAM_GJ = #{${joiner}deathRsnNamGj}
        </if>
        <if test="${joiner}otherDeathNamGj != null and ${joiner}otherDeathNamGj != ''">
            and ${mAlias}OTHER_DEATH_NAM_GJ = #{${joiner}otherDeathNamGj}
        </if>
        <if test="${joiner}linkman != null and ${joiner}linkman != ''">
            and ${mAlias}LINKMAN = #{${joiner}linkman}
        </if>
        <if test="${joiner}postalcode != null and ${joiner}postalcode != ''">
            and ${mAlias}POSTALCODE = #{${joiner}postalcode}
        </if>
        <if test="${joiner}ifCrptIndemnity != null and ${joiner}ifCrptIndemnity != ''">
            and ${mAlias}IF_CRPT_INDEMNITY = #{${joiner}ifCrptIndemnity}
        </if>
        <if test="${joiner}ifUrbanRuralInsure != null and ${joiner}ifUrbanRuralInsure != ''">
            and ${mAlias}IF_URBAN_RURAL_INSURE = #{${joiner}ifUrbanRuralInsure}
        </if>
        <if test="${joiner}ifBigDisInsure != null and ${joiner}ifBigDisInsure != ''">
            and ${mAlias}IF_BIG_DIS_INSURE = #{${joiner}ifBigDisInsure}
        </if>
        <if test="${joiner}medicalInInsureRatio != null and ${joiner}medicalInInsureRatio != ''">
            and ${mAlias}MEDICAL_IN_INSURE_RATIO = #{${joiner}medicalInInsureRatio}
        </if>
        <if test="${joiner}medicalOutInsureRatio != null and ${joiner}medicalOutInsureRatio != ''">
            and ${mAlias}MEDICAL_OUT_INSURE_RATIO = #{${joiner}medicalOutInsureRatio}
        </if>
        <if test="${joiner}subsistenceCase != null and ${joiner}subsistenceCase != ''">
            and ${mAlias}SUBSISTENCE_CASE = #{${joiner}subsistenceCase}
        </if>
        <if test="${joiner}subsistenceAmo != null and ${joiner}subsistenceAmo != ''">
            and ${mAlias}SUBSISTENCE_AMO = #{${joiner}subsistenceAmo}
        </if>
        <if test="${joiner}normalZoneId != null and ${joiner}normalZoneId != ''">
            and ${mAlias}NORMAL_ZONE_ID = #{${joiner}normalZoneId}
        </if>
        <if test="${joiner}normalAddr != null and ${joiner}normalAddr != ''">
            and ${mAlias}NORMAL_ADDR = #{${joiner}normalAddr}
        </if>
        <if test="${joiner}flowupDate != null">
            and ${mAlias}FLOWUP_DATE = #{${joiner}flowupDate}
        </if>
        <if test="${joiner}flowupPsn != null and ${joiner}flowupPsn != ''">
            and ${mAlias}FLOWUP_PSN = #{${joiner}flowupPsn}
        </if>
        <if test="${joiner}nowZoneId != null and ${joiner}nowZoneId != ''">
            and ${mAlias}NOW_ZONE_ID = #{${joiner}nowZoneId}
        </if>
        <if test="${joiner}nowAddr != null and ${joiner}nowAddr != ''">
            and ${mAlias}NOW_ADDR = #{${joiner}nowAddr}
        </if>
        <if test="${joiner}syncKfState != null and ${joiner}syncKfState != ''">
            and ${mAlias}SYNC_KF_STATE = #{${joiner}syncKfState}
        </if>
        <if test="${joiner}syncKfDate != null">
            and ${mAlias}SYNC_KF_DATE = #{${joiner}syncKfDate}
        </if>
        <if test="${joiner}syncKfErrMsg != null and ${joiner}syncKfErrMsg != ''">
            and ${mAlias}SYNC_KF_ERR_MSG = #{${joiner}syncKfErrMsg}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}psnName != null and ${joiner}psnName != ''">
                t.PSN_NAME = #{${joiner}psnName},
            </when>
            <otherwise>
                t.PSN_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}psnLinkWay != null and ${joiner}psnLinkWay != ''">
                t.PSN_LINK_WAY = #{${joiner}psnLinkWay},
            </when>
            <otherwise>
                t.PSN_LINK_WAY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}idc != null and ${joiner}idc != ''">
                t.IDC = #{${joiner}idc},
            </when>
            <otherwise>
                t.IDC = null,
            </otherwise>
        </choose>
            t.BIRTHDAY = #{${joiner}birthday},
        <choose>
            <when test="${joiner}sex != null and ${joiner}sex != ''">
                t.SEX = #{${joiner}sex},
            </when>
            <otherwise>
                t.SEX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCrptZoneId != null and ${joiner}fkByCrptZoneId.rid != null">
                t.CRPT_ZONE_ID = #{${joiner}fkByCrptZoneId.rid},
            </when>
            <otherwise>
                t.CRPT_ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}crptName != null and ${joiner}crptName != ''">
                t.CRPT_NAME = #{${joiner}crptName},
            </when>
            <otherwise>
                t.CRPT_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByIndusTypeId != null and ${joiner}fkByIndusTypeId.rid != null">
                t.INDUS_TYPE_ID = #{${joiner}fkByIndusTypeId.rid},
            </when>
            <otherwise>
                t.INDUS_TYPE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}tchDustYear != null and ${joiner}tchDustYear != ''">
                t.TCH_DUST_YEAR = #{${joiner}tchDustYear},
            </when>
            <otherwise>
                t.TCH_DUST_YEAR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}tchDustMonth != null and ${joiner}tchDustMonth != ''">
                t.TCH_DUST_MONTH = #{${joiner}tchDustMonth},
            </when>
            <otherwise>
                t.TCH_DUST_MONTH = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByPneumsisTypeId != null and ${joiner}fkByPneumsisTypeId.rid != null">
                t.PNEUMSIS_TYPE_ID = #{${joiner}fkByPneumsisTypeId.rid},
            </when>
            <otherwise>
                t.PNEUMSIS_TYPE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}otherPnesName != null and ${joiner}otherPnesName != ''">
                t.OTHER_PNES_NAME = #{${joiner}otherPnesName},
            </when>
            <otherwise>
                t.OTHER_PNES_NAME = null,
            </otherwise>
        </choose>
            t.DIAG_1_DATE = #{${joiner}diag1Date},
            t.DIAG_2_DATE = #{${joiner}diag2Date},
            t.DIAG_3_DATE = #{${joiner}diag3Date},
        <choose>
            <when test="${joiner}liveState != null and ${joiner}liveState != ''">
                t.LIVE_STATE = #{${joiner}liveState},
            </when>
            <otherwise>
                t.LIVE_STATE = null,
            </otherwise>
        </choose>
            t.DEATH_DATE = #{${joiner}deathDate},
        <choose>
            <when test="${joiner}directDeathRsn != null and ${joiner}directDeathRsn != ''">
                t.DIRECT_DEATH_RSN = #{${joiner}directDeathRsn},
            </when>
            <otherwise>
                t.DIRECT_DEATH_RSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}indirectDeathRsn2 != null and ${joiner}indirectDeathRsn2 != ''">
                t.INDIRECT_DEATH_RSN2 = #{${joiner}indirectDeathRsn2},
            </when>
            <otherwise>
                t.INDIRECT_DEATH_RSN2 = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}originDeathRsn2 != null and ${joiner}originDeathRsn2 != ''">
                t.ORIGIN_DEATH_RSN2 = #{${joiner}originDeathRsn2},
            </when>
            <otherwise>
                t.ORIGIN_DEATH_RSN2 = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByDeathRsnId != null and ${joiner}fkByDeathRsnId.rid != null">
                t.DEATH_RSN_ID = #{${joiner}fkByDeathRsnId.rid},
            </when>
            <otherwise>
                t.DEATH_RSN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByRcdUserId != null and ${joiner}fkByRcdUserId.rid != null">
                t.RCD_USER_ID = #{${joiner}fkByRcdUserId.rid},
            </when>
            <otherwise>
                t.RCD_USER_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByRcdUnitId != null and ${joiner}fkByRcdUnitId.rid != null">
                t.RCD_UNIT_ID = #{${joiner}fkByRcdUnitId.rid},
            </when>
            <otherwise>
                t.RCD_UNIT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByModUserId != null and ${joiner}fkByModUserId.rid != null">
                t.MOD_USER_ID = #{${joiner}fkByModUserId.rid},
            </when>
            <otherwise>
                t.MOD_USER_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByModUnitId != null and ${joiner}fkByModUnitId.rid != null">
                t.MOD_UNIT_ID = #{${joiner}fkByModUnitId.rid},
            </when>
            <otherwise>
                t.MOD_UNIT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}mainCardId != null and ${joiner}mainCardId != ''">
                t.MAIN_CARD_ID = #{${joiner}mainCardId},
            </when>
            <otherwise>
                t.MAIN_CARD_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}subCardId != null and ${joiner}subCardId != ''">
                t.SUB_CARD_ID = #{${joiner}subCardId},
            </when>
            <otherwise>
                t.SUB_CARD_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}patientSource != null and ${joiner}patientSource != ''">
                t.PATIENT_SOURCE = #{${joiner}patientSource},
            </when>
            <otherwise>
                t.PATIENT_SOURCE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rptCardNo != null and ${joiner}rptCardNo != ''">
                t.RPT_CARD_NO = #{${joiner}rptCardNo},
            </when>
            <otherwise>
                t.RPT_CARD_NO = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByRptTypeId != null and ${joiner}fkByRptTypeId.rid != null">
                t.RPT_TYPE_ID = #{${joiner}fkByRptTypeId.rid},
            </when>
            <otherwise>
                t.RPT_TYPE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkPsnName != null and ${joiner}linkPsnName != ''">
                t.LINK_PSN_NAME = #{${joiner}linkPsnName},
            </when>
            <otherwise>
                t.LINK_PSN_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkAddr != null and ${joiner}linkAddr != ''">
                t.LINK_ADDR = #{${joiner}linkAddr},
            </when>
            <otherwise>
                t.LINK_ADDR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkPhone != null and ${joiner}linkPhone != ''">
                t.LINK_PHONE = #{${joiner}linkPhone},
            </when>
            <otherwise>
                t.LINK_PHONE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}crptCreditCode != null and ${joiner}crptCreditCode != ''">
                t.CRPT_CREDIT_CODE = #{${joiner}crptCreditCode},
            </when>
            <otherwise>
                t.CRPT_CREDIT_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}crptAddr != null and ${joiner}crptAddr != ''">
                t.CRPT_ADDR = #{${joiner}crptAddr},
            </when>
            <otherwise>
                t.CRPT_ADDR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByEconomyId != null and ${joiner}fkByEconomyId.rid != null">
                t.ECONOMY_ID = #{${joiner}fkByEconomyId.rid},
            </when>
            <otherwise>
                t.ECONOMY_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCrptSizeId != null and ${joiner}fkByCrptSizeId.rid != null">
                t.CRPT_SIZE_ID = #{${joiner}fkByCrptSizeId.rid},
            </when>
            <otherwise>
                t.CRPT_SIZE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByAnalyWorkId != null and ${joiner}fkByAnalyWorkId.rid != null">
                t.ANALY_WORK_ID = #{${joiner}fkByAnalyWorkId.rid},
            </when>
            <otherwise>
                t.ANALY_WORK_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}otherWorkName != null and ${joiner}otherWorkName != ''">
                t.OTHER_WORK_NAME = #{${joiner}otherWorkName},
            </when>
            <otherwise>
                t.OTHER_WORK_NAME = null,
            </otherwise>
        </choose>
            t.BEG_TCH_DUST = #{${joiner}begTchDust},
        <choose>
            <when test="${joiner}diagUnitName != null and ${joiner}diagUnitName != ''">
                t.DIAG_UNIT_NAME = #{${joiner}diagUnitName},
            </when>
            <otherwise>
                t.DIAG_UNIT_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}diagRespPsn != null and ${joiner}diagRespPsn != ''">
                t.DIAG_RESP_PSN = #{${joiner}diagRespPsn},
            </when>
            <otherwise>
                t.DIAG_RESP_PSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fillFormPsn != null and ${joiner}fillFormPsn != ''">
                t.FILL_FORM_PSN = #{${joiner}fillFormPsn},
            </when>
            <otherwise>
                t.FILL_FORM_PSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fillLink != null and ${joiner}fillLink != ''">
                t.FILL_LINK = #{${joiner}fillLink},
            </when>
            <otherwise>
                t.FILL_LINK = null,
            </otherwise>
        </choose>
            t.FILL_DATE = #{${joiner}fillDate},
        <choose>
            <when test="${joiner}ifTb != null and ${joiner}ifTb != ''">
                t.IF_TB = #{${joiner}ifTb},
            </when>
            <otherwise>
                t.IF_TB = null,
            </otherwise>
        </choose>
            t.TB_DIAG_DATE = #{${joiner}tbDiagDate},
        <choose>
            <when test="${joiner}ifPulInfection != null and ${joiner}ifPulInfection != ''">
                t.IF_PUL_INFECTION = #{${joiner}ifPulInfection},
            </when>
            <otherwise>
                t.IF_PUL_INFECTION = null,
            </otherwise>
        </choose>
            t.INFECTION_DIAG_DATE = #{${joiner}infectionDiagDate},
        <choose>
            <when test="${joiner}ifThePneum != null and ${joiner}ifThePneum != ''">
                t.IF_THE_PNEUM = #{${joiner}ifThePneum},
            </when>
            <otherwise>
                t.IF_THE_PNEUM = null,
            </otherwise>
        </choose>
            t.PNEUM_DIAG_DATE = #{${joiner}pneumDiagDate},
        <choose>
            <when test="${joiner}ifPulHeart != null and ${joiner}ifPulHeart != ''">
                t.IF_PUL_HEART = #{${joiner}ifPulHeart},
            </when>
            <otherwise>
                t.IF_PUL_HEART = null,
            </otherwise>
        </choose>
            t.HEART_DIAG_DATE = #{${joiner}heartDiagDate},
        <choose>
            <when test="${joiner}ifLungCancer != null and ${joiner}ifLungCancer != ''">
                t.IF_LUNG_CANCER = #{${joiner}ifLungCancer},
            </when>
            <otherwise>
                t.IF_LUNG_CANCER = null,
            </otherwise>
        </choose>
            t.LUNG_DIAG_DATE = #{${joiner}lungDiagDate},
        <choose>
            <when test="${joiner}ifUnitExists != null and ${joiner}ifUnitExists != ''">
                t.IF_UNIT_EXISTS = #{${joiner}ifUnitExists},
            </when>
            <otherwise>
                t.IF_UNIT_EXISTS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifWorkInsurance != null and ${joiner}ifWorkInsurance != ''">
                t.IF_WORK_INSURANCE = #{${joiner}ifWorkInsurance},
            </when>
            <otherwise>
                t.IF_WORK_INSURANCE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifMedicalTreat != null and ${joiner}ifMedicalTreat != ''">
                t.IF_MEDICAL_TREAT = #{${joiner}ifMedicalTreat},
            </when>
            <otherwise>
                t.IF_MEDICAL_TREAT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByWorkLevelId != null and ${joiner}fkByWorkLevelId.rid != null">
                t.WORK_LEVEL_ID = #{${joiner}fkByWorkLevelId.rid},
            </when>
            <otherwise>
                t.WORK_LEVEL_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifInjuryTreat != null and ${joiner}ifInjuryTreat != ''">
                t.IF_INJURY_TREAT = #{${joiner}ifInjuryTreat},
            </when>
            <otherwise>
                t.IF_INJURY_TREAT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifDeathTreat != null and ${joiner}ifDeathTreat != ''">
                t.IF_DEATH_TREAT = #{${joiner}ifDeathTreat},
            </when>
            <otherwise>
                t.IF_DEATH_TREAT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifOtherTreat != null and ${joiner}ifOtherTreat != ''">
                t.IF_OTHER_TREAT = #{${joiner}ifOtherTreat},
            </when>
            <otherwise>
                t.IF_OTHER_TREAT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifGetMedicalHelp != null and ${joiner}ifGetMedicalHelp != ''">
                t.IF_GET_MEDICAL_HELP = #{${joiner}ifGetMedicalHelp},
            </when>
            <otherwise>
                t.IF_GET_MEDICAL_HELP = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifGetLiveHelp != null and ${joiner}ifGetLiveHelp != ''">
                t.IF_GET_LIVE_HELP = #{${joiner}ifGetLiveHelp},
            </when>
            <otherwise>
                t.IF_GET_LIVE_HELP = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rmk != null and ${joiner}rmk != ''">
                t.RMK = #{${joiner}rmk},
            </when>
            <otherwise>
                t.RMK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}crptZoneCodGj != null and ${joiner}crptZoneCodGj != ''">
                t.CRPT_ZONE_COD_GJ = #{${joiner}crptZoneCodGj},
            </when>
            <otherwise>
                t.CRPT_ZONE_COD_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}crptZoneNamGj != null and ${joiner}crptZoneNamGj != ''">
                t.CRPT_ZONE_NAM_GJ = #{${joiner}crptZoneNamGj},
            </when>
            <otherwise>
                t.CRPT_ZONE_NAM_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rptTypeCodGj != null and ${joiner}rptTypeCodGj != ''">
                t.RPT_TYPE_COD_GJ = #{${joiner}rptTypeCodGj},
            </when>
            <otherwise>
                t.RPT_TYPE_COD_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rptTypeNamGj != null and ${joiner}rptTypeNamGj != ''">
                t.RPT_TYPE_NAM_GJ = #{${joiner}rptTypeNamGj},
            </when>
            <otherwise>
                t.RPT_TYPE_NAM_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ecoCodGj != null and ${joiner}ecoCodGj != ''">
                t.ECO_COD_GJ = #{${joiner}ecoCodGj},
            </when>
            <otherwise>
                t.ECO_COD_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ecoNamGj != null and ${joiner}ecoNamGj != ''">
                t.ECO_NAM_GJ = #{${joiner}ecoNamGj},
            </when>
            <otherwise>
                t.ECO_NAM_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}indusCodGj != null and ${joiner}indusCodGj != ''">
                t.INDUS_COD_GJ = #{${joiner}indusCodGj},
            </when>
            <otherwise>
                t.INDUS_COD_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}indusNamGj != null and ${joiner}indusNamGj != ''">
                t.INDUS_NAM_GJ = #{${joiner}indusNamGj},
            </when>
            <otherwise>
                t.INDUS_NAM_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}sizeCodGj != null and ${joiner}sizeCodGj != ''">
                t.SIZE_COD_GJ = #{${joiner}sizeCodGj},
            </when>
            <otherwise>
                t.SIZE_COD_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}sizeNamGj != null and ${joiner}sizeNamGj != ''">
                t.SIZE_NAM_GJ = #{${joiner}sizeNamGj},
            </when>
            <otherwise>
                t.SIZE_NAM_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}analyWorkCodGj != null and ${joiner}analyWorkCodGj != ''">
                t.ANALY_WORK_COD_GJ = #{${joiner}analyWorkCodGj},
            </when>
            <otherwise>
                t.ANALY_WORK_COD_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}analyWorkNamGj != null and ${joiner}analyWorkNamGj != ''">
                t.ANALY_WORK_NAM_GJ = #{${joiner}analyWorkNamGj},
            </when>
            <otherwise>
                t.ANALY_WORK_NAM_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}otherWorkNamGj != null and ${joiner}otherWorkNamGj != ''">
                t.OTHER_WORK_NAM_GJ = #{${joiner}otherWorkNamGj},
            </when>
            <otherwise>
                t.OTHER_WORK_NAM_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}pneCodGj != null and ${joiner}pneCodGj != ''">
                t.PNE_COD_GJ = #{${joiner}pneCodGj},
            </when>
            <otherwise>
                t.PNE_COD_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}pneNamGj != null and ${joiner}pneNamGj != ''">
                t.PNE_NAM_GJ = #{${joiner}pneNamGj},
            </when>
            <otherwise>
                t.PNE_NAM_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}otherPneNamGj != null and ${joiner}otherPneNamGj != ''">
                t.OTHER_PNE_NAM_GJ = #{${joiner}otherPneNamGj},
            </when>
            <otherwise>
                t.OTHER_PNE_NAM_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}deathRsnCodGj != null and ${joiner}deathRsnCodGj != ''">
                t.DEATH_RSN_COD_GJ = #{${joiner}deathRsnCodGj},
            </when>
            <otherwise>
                t.DEATH_RSN_COD_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}deathRsnNamGj != null and ${joiner}deathRsnNamGj != ''">
                t.DEATH_RSN_NAM_GJ = #{${joiner}deathRsnNamGj},
            </when>
            <otherwise>
                t.DEATH_RSN_NAM_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}otherDeathNamGj != null and ${joiner}otherDeathNamGj != ''">
                t.OTHER_DEATH_NAM_GJ = #{${joiner}otherDeathNamGj},
            </when>
            <otherwise>
                t.OTHER_DEATH_NAM_GJ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkman != null and ${joiner}linkman != ''">
                t.LINKMAN = #{${joiner}linkman},
            </when>
            <otherwise>
                t.LINKMAN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}postalcode != null and ${joiner}postalcode != ''">
                t.POSTALCODE = #{${joiner}postalcode},
            </when>
            <otherwise>
                t.POSTALCODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifCrptIndemnity != null and ${joiner}ifCrptIndemnity != ''">
                t.IF_CRPT_INDEMNITY = #{${joiner}ifCrptIndemnity},
            </when>
            <otherwise>
                t.IF_CRPT_INDEMNITY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifUrbanRuralInsure != null and ${joiner}ifUrbanRuralInsure != ''">
                t.IF_URBAN_RURAL_INSURE = #{${joiner}ifUrbanRuralInsure},
            </when>
            <otherwise>
                t.IF_URBAN_RURAL_INSURE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifBigDisInsure != null and ${joiner}ifBigDisInsure != ''">
                t.IF_BIG_DIS_INSURE = #{${joiner}ifBigDisInsure},
            </when>
            <otherwise>
                t.IF_BIG_DIS_INSURE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}medicalInInsureRatio != null and ${joiner}medicalInInsureRatio != ''">
                t.MEDICAL_IN_INSURE_RATIO = #{${joiner}medicalInInsureRatio},
            </when>
            <otherwise>
                t.MEDICAL_IN_INSURE_RATIO = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}medicalOutInsureRatio != null and ${joiner}medicalOutInsureRatio != ''">
                t.MEDICAL_OUT_INSURE_RATIO = #{${joiner}medicalOutInsureRatio},
            </when>
            <otherwise>
                t.MEDICAL_OUT_INSURE_RATIO = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}subsistenceCase != null and ${joiner}subsistenceCase != ''">
                t.SUBSISTENCE_CASE = #{${joiner}subsistenceCase},
            </when>
            <otherwise>
                t.SUBSISTENCE_CASE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}subsistenceAmo != null and ${joiner}subsistenceAmo != ''">
                t.SUBSISTENCE_AMO = #{${joiner}subsistenceAmo},
            </when>
            <otherwise>
                t.SUBSISTENCE_AMO = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}normalZoneId != null and ${joiner}normalZoneId != ''">
                t.NORMAL_ZONE_ID = #{${joiner}normalZoneId},
            </when>
            <otherwise>
                t.NORMAL_ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}normalAddr != null and ${joiner}normalAddr != ''">
                t.NORMAL_ADDR = #{${joiner}normalAddr},
            </when>
            <otherwise>
                t.NORMAL_ADDR = null,
            </otherwise>
        </choose>
            t.FLOWUP_DATE = #{${joiner}flowupDate},
        <choose>
            <when test="${joiner}flowupPsn != null and ${joiner}flowupPsn != ''">
                t.FLOWUP_PSN = #{${joiner}flowupPsn},
            </when>
            <otherwise>
                t.FLOWUP_PSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}nowZoneId != null and ${joiner}nowZoneId != ''">
                t.NOW_ZONE_ID = #{${joiner}nowZoneId},
            </when>
            <otherwise>
                t.NOW_ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}nowAddr != null and ${joiner}nowAddr != ''">
                t.NOW_ADDR = #{${joiner}nowAddr},
            </when>
            <otherwise>
                t.NOW_ADDR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}syncKfState != null and ${joiner}syncKfState != ''">
                t.SYNC_KF_STATE = #{${joiner}syncKfState},
            </when>
            <otherwise>
                t.SYNC_KF_STATE = null,
            </otherwise>
        </choose>
            t.SYNC_KF_DATE = #{${joiner}syncKfDate},
        <choose>
            <when test="${joiner}syncKfErrMsg != null and ${joiner}syncKfErrMsg != ''">
                t.SYNC_KF_ERR_MSG = #{${joiner}syncKfErrMsg},
            </when>
            <otherwise>
                t.SYNC_KF_ERR_MSG = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWCF_PATIENT_INFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWCF_PATIENT_INFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWCF_PATIENT_INFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwcfPatientInfo" >
        update TD_ZWCF_PATIENT_INFO t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZWCF_PATIENT_INFO t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZwcfPatientInfo">
        delete from TD_ZWCF_PATIENT_INFO
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->

    <update id="updateSyncKfState0">
        UPDATE TD_ZWCF_PATIENT_INFO
        SET SYNC_KF_STATE   = 0,
            SYNC_KF_DATE    = NULL,
            SYNC_KF_ERR_MSG = NULL
        WHERE NVL(SYNC_KF_STATE, 0) IN (1, 2)
    </update>

    <update id="updateSyncKfState3IdcNull">
        UPDATE TD_ZWCF_PATIENT_INFO
        SET SYNC_KF_STATE   = 3,
            SYNC_KF_DATE    = #{syncKfDate},
            SYNC_KF_ERR_MSG = #{syncKfErrMsg}
        WHERE NVL(SYNC_KF_STATE, 0) = 0
          AND IDC IS NULL
    </update>

    <select id="selectPatientInfoListByState0" resultMap="BaseResultMap">
        SELECT RID, IDC
        FROM TD_ZWCF_PATIENT_INFO
        WHERE NVL(SYNC_KF_STATE, 0) = 0
    </select>

    <delete id="deleteKfInfo">
        BEGIN
            DELETE
            FROM TD_ZWCF_KF_PG
            WHERE MAIN_ID = #{mainId};
            DELETE
            FROM TD_ZWCF_KF_CF_OPER CO
            WHERE EXISTS(SELECT 1 FROM TD_ZWCF_KF_CF C WHERE C.MAIN_ID = #{mainId} AND CO.MAIN_ID = C.RID);
            DELETE
            FROM TD_ZWCF_KF_CF
            WHERE MAIN_ID = #{mainId};
            DELETE
            FROM TD_ZWCF_KF_RCD
            WHERE MAIN_ID = #{mainId}
              AND YEAR = #{year};
        END;
    </delete>

    <update id="updateSyncKfSyncKfByRid" parameterType="TdZwcfPatientInfo">
        UPDATE TD_ZWCF_PATIENT_INFO
        SET SYNC_KF_STATE   = #{patientInfo.syncKfState},
            SYNC_KF_DATE    = #{patientInfo.syncKfDate},
            SYNC_KF_ERR_MSG = #{patientInfo.syncKfErrMsg}
        WHERE RID = #{patientInfo.rid}
    </update>

    <insert id="insertTdZwcfKfPg" parameterType="TdZwcfKfPg">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="tdZwcfKfPg.rid">
            SELECT TD_ZWCF_KF_PG_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_ZWCF_KF_PG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            MAIN_ID,
            PG_TIME,
            PG_PDF_PATH,
            CREATE_DATE,
            CREATE_MANID,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{tdZwcfKfPg.rid},
            #{tdZwcfKfPg.fkByMainId.rid},
            #{tdZwcfKfPg.pgTime},
            #{tdZwcfKfPg.pgPdfPath},
            #{tdZwcfKfPg.createDate},
            #{tdZwcfKfPg.createManid},
        </trim>
    </insert>

    <insert id="insertTdZwcfKfCf" parameterType="TdZwcfKfCf">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="tdZwcfKfCf.rid">
            SELECT TD_ZWCF_KF_CF_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_ZWCF_KF_CF
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            MAIN_ID,
            OPENER_DATE,
            CF_PDF_PATH,
            CREATE_DATE,
            CREATE_MANID,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{tdZwcfKfCf.rid},
            #{tdZwcfKfCf.fkByMainId.rid},
            #{tdZwcfKfCf.openerDate},
            #{tdZwcfKfCf.cfPdfPath},
            #{tdZwcfKfCf.createDate},
            #{tdZwcfKfCf.createManid},
        </trim>
    </insert>

    <insert id="insertTdZwcfKfCfOper" parameterType="TdZwcfKfCfOper">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="tdZwcfKfCfOper.rid">
            SELECT TD_ZWCF_KF_CF_OPER_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_ZWCF_KF_CF_OPER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            MAIN_ID,
            OPER_DATE,
            OPER_PDF_PATH,
            CREATE_DATE,
            CREATE_MANID,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{tdZwcfKfCfOper.rid},
            #{tdZwcfKfCfOper.fkByMainId.rid},
            #{tdZwcfKfCfOper.operDate},
            #{tdZwcfKfCfOper.operPdfPath},
            #{tdZwcfKfCfOper.createDate},
            #{tdZwcfKfCfOper.createManid},
        </trim>
    </insert>

    <insert id="insertTdZwcfKfRcd" parameterType="TdZwcfKfRcd">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="tdZwcfKfRcd.rid">
            SELECT TD_ZWCF_KF_RCD_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_ZWCF_KF_RCD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            MAIN_ID,
            YEAR,
            AREA,
            HOS_NAME,
            FIRST_RECVERY_TIME,
            LAST_RECVERY_TIME,
            HAS_APPARAT_USRECOVERY,
            HAS_RECOVERY,
            RECOVERY_NUM,
            RECOVERY_TIME,
            RECOVERYAVE_TIME,
            FIRST_SPO2,
            THIS_YEARSPO2,
            FIRST_HR,
            THIS_YEAR_HR,
            FIRST_BLOOD,
            THIS_YEAR_BLOOD,
            FIRST_DISTANCE,
            THIS_YEAR_DISTANCE,
            FIRST_MMRC_SCORE,
            THIS_YEAR_MMRC_SCORE,
            FIRST_ACT_SCORE,
            THIS_YEAR_ACT_SCORE,
            FIRST_MIP,
            THIS_YEAR_MIP,
            FIRST_MEP,
            THIS_YEAR_MEP,
            CREATE_DATE,
            CREATE_MANID,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{tdZwcfKfRcd.rid},
            #{tdZwcfKfRcd.fkByMainId.rid},
            #{tdZwcfKfRcd.year},
            #{tdZwcfKfRcd.area},
            #{tdZwcfKfRcd.hosName},
            #{tdZwcfKfRcd.firstRecveryTime},
            #{tdZwcfKfRcd.lastRecveryTime},
            #{tdZwcfKfRcd.hasApparatUsrecovery},
            #{tdZwcfKfRcd.hasRecovery},
            #{tdZwcfKfRcd.recoveryNum},
            #{tdZwcfKfRcd.recoveryTime},
            #{tdZwcfKfRcd.recoveryaveTime},
            #{tdZwcfKfRcd.firstSpo2},
            #{tdZwcfKfRcd.thisYearspo2},
            #{tdZwcfKfRcd.firstHr},
            #{tdZwcfKfRcd.thisYearHr},
            #{tdZwcfKfRcd.firstBlood},
            #{tdZwcfKfRcd.thisYearBlood},
            #{tdZwcfKfRcd.firstDistance},
            #{tdZwcfKfRcd.thisYearDistance},
            #{tdZwcfKfRcd.firstMmrcScore},
            #{tdZwcfKfRcd.thisYearMmrcScore},
            #{tdZwcfKfRcd.firstActScore},
            #{tdZwcfKfRcd.thisYearActScore},
            #{tdZwcfKfRcd.firstMip},
            #{tdZwcfKfRcd.thisYearMip},
            #{tdZwcfKfRcd.firstMep},
            #{tdZwcfKfRcd.thisYearMep},
            #{tdZwcfKfRcd.createDate},
            #{tdZwcfKfRcd.createManid},
        </trim>
    </insert>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwyjBsnAdvCsionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwyjBsnAdvCsion">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="BHK_ID" property="fkByBhkId.rid" />
        <result column="ADV_ID" property="fkByAdvId.rid" />
        <result column="ITEM_RST" property="itemRst" />
        <result column="MSRUNT" property="msrunt" />
        <result column="ITEM_STDVALUE" property="itemStdvalue" />
        <result column="JDGPTN" property="jdgptn" />
        <result column="MAXVAL" property="maxval" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_ID,t.ADV_ID,t.ITEM_RST,t.MSRUNT,t.ITEM_STDVALUE,t.JDGPTN,t.MAXVAL,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByBhkId != null and ${joiner}fkByBhkId.rid != null">
            and ${mAlias}BHK_ID = #{${joiner}fkByBhkId.rid}
        </if>
        <if test="${joiner}fkByAdvId != null and ${joiner}fkByAdvId.rid != null">
            and ${mAlias}ADV_ID = #{${joiner}fkByAdvId.rid}
        </if>
        <if test="${joiner}itemRst != null">
            and ${mAlias}ITEM_RST = #{${joiner}itemRst}
        </if>
        <if test="${joiner}msrunt != null and ${joiner}msrunt != ''">
            and ${mAlias}MSRUNT = #{${joiner}msrunt}
        </if>
        <if test="${joiner}itemStdvalue != null and ${joiner}itemStdvalue != ''">
            and ${mAlias}ITEM_STDVALUE = #{${joiner}itemStdvalue}
        </if>
        <if test="${joiner}jdgptn != null and ${joiner}jdgptn != ''">
            and ${mAlias}JDGPTN = #{${joiner}jdgptn}
        </if>
        <if test="${joiner}maxval != null and ${joiner}maxval != ''">
            and ${mAlias}MAXVAL = #{${joiner}maxval}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByBhkId != null and ${joiner}fkByBhkId.rid != null">
                t.BHK_ID = #{${joiner}fkByBhkId.rid},
            </when>
            <otherwise>
                t.BHK_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByAdvId != null and ${joiner}fkByAdvId.rid != null">
                t.ADV_ID = #{${joiner}fkByAdvId.rid},
            </when>
            <otherwise>
                t.ADV_ID = null,
            </otherwise>
        </choose>
            t.ITEM_RST = #{${joiner}itemRst},
        <choose>
            <when test="${joiner}msrunt != null and ${joiner}msrunt != ''">
                t.MSRUNT = #{${joiner}msrunt},
            </when>
            <otherwise>
                t.MSRUNT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}itemStdvalue != null and ${joiner}itemStdvalue != ''">
                t.ITEM_STDVALUE = #{${joiner}itemStdvalue},
            </when>
            <otherwise>
                t.ITEM_STDVALUE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}jdgptn != null and ${joiner}jdgptn != ''">
                t.JDGPTN = #{${joiner}jdgptn},
            </when>
            <otherwise>
                t.JDGPTN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}maxval != null and ${joiner}maxval != ''">
                t.MAXVAL = #{${joiner}maxval},
            </when>
            <otherwise>
                t.MAXVAL = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_BSN_ADV_CSION t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_BSN_ADV_CSION t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_BSN_ADV_CSION t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwyjBsnAdvCsion" >
        update TD_ZWYJ_BSN_ADV_CSION t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZWYJ_BSN_ADV_CSION t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZwyjBsnAdvCsion">
        delete from TD_ZWYJ_BSN_ADV_CSION
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <delete id="deleteByMainId">
        delete from TD_ZWYJ_BSN_ADV_CSION
        <where>
            BHK_ID = #{mainId}
        </where>
    </delete>



</mapper>

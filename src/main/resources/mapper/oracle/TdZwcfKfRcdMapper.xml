<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwcfKfRcdMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwcfKfRcd">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="YEAR" property="year" />
        <result column="AREA" property="area" />
        <result column="HOS_NAME" property="hosName" />
        <result column="FIRST_RECVERY_TIME" property="firstRecveryTime" />
        <result column="LAST_RECVERY_TIME" property="lastRecveryTime" />
        <result column="HAS_APPARAT_USRECOVERY" property="hasApparatUsrecovery" />
        <result column="HAS_RECOVERY" property="hasRecovery" />
        <result column="RECOVERY_NUM" property="recoveryNum" />
        <result column="RECOVERY_TIME" property="recoveryTime" />
        <result column="RECOVERYAVE_TIME" property="recoveryaveTime" />
        <result column="FIRST_SPO2" property="firstSpo2" />
        <result column="THIS_YEARSPO2" property="thisYearspo2" />
        <result column="FIRST_HR" property="firstHr" />
        <result column="THIS_YEAR_HR" property="thisYearHr" />
        <result column="FIRST_BLOOD" property="firstBlood" />
        <result column="THIS_YEAR_BLOOD" property="thisYearBlood" />
        <result column="FIRST_DISTANCE" property="firstDistance" />
        <result column="THIS_YEAR_DISTANCE" property="thisYearDistance" />
        <result column="FIRST_MMRC_SCORE" property="firstMmrcScore" />
        <result column="THIS_YEAR_MMRC_SCORE" property="thisYearMmrcScore" />
        <result column="FIRST_ACT_SCORE" property="firstActScore" />
        <result column="THIS_YEAR_ACT_SCORE" property="thisYearActScore" />
        <result column="FIRST_MIP" property="firstMip" />
        <result column="THIS_YEAR_MIP" property="thisYearMip" />
        <result column="FIRST_MEP" property="firstMep" />
        <result column="THIS_YEAR_MEP" property="thisYearMep" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MAIN_ID,t.YEAR,t.AREA,t.HOS_NAME,t.FIRST_RECVERY_TIME,t.LAST_RECVERY_TIME,t.HAS_APPARAT_USRECOVERY,t.HAS_RECOVERY,t.RECOVERY_NUM,t.RECOVERY_TIME,t.RECOVERYAVE_TIME,t.FIRST_SPO2,t.THIS_YEARSPO2,t.FIRST_HR,t.THIS_YEAR_HR,t.FIRST_BLOOD,t.THIS_YEAR_BLOOD,t.FIRST_DISTANCE,t.THIS_YEAR_DISTANCE,t.FIRST_MMRC_SCORE,t.THIS_YEAR_MMRC_SCORE,t.FIRST_ACT_SCORE,t.THIS_YEAR_ACT_SCORE,t.FIRST_MIP,t.THIS_YEAR_MIP,t.FIRST_MEP,t.THIS_YEAR_MEP,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}year != null and ${joiner}year != ''">
            and ${mAlias}YEAR = #{${joiner}year}
        </if>
        <if test="${joiner}area != null and ${joiner}area != ''">
            and ${mAlias}AREA = #{${joiner}area}
        </if>
        <if test="${joiner}hosName != null and ${joiner}hosName != ''">
            and ${mAlias}HOS_NAME = #{${joiner}hosName}
        </if>
        <if test="${joiner}firstRecveryTime != null">
            and ${mAlias}FIRST_RECVERY_TIME = #{${joiner}firstRecveryTime}
        </if>
        <if test="${joiner}lastRecveryTime != null">
            and ${mAlias}LAST_RECVERY_TIME = #{${joiner}lastRecveryTime}
        </if>
        <if test="${joiner}hasApparatUsrecovery != null and ${joiner}hasApparatUsrecovery != ''">
            and ${mAlias}HAS_APPARAT_USRECOVERY = #{${joiner}hasApparatUsrecovery}
        </if>
        <if test="${joiner}hasRecovery != null and ${joiner}hasRecovery != ''">
            and ${mAlias}HAS_RECOVERY = #{${joiner}hasRecovery}
        </if>
        <if test="${joiner}recoveryNum != null and ${joiner}recoveryNum != ''">
            and ${mAlias}RECOVERY_NUM = #{${joiner}recoveryNum}
        </if>
        <if test="${joiner}recoveryTime != null and ${joiner}recoveryTime != ''">
            and ${mAlias}RECOVERY_TIME = #{${joiner}recoveryTime}
        </if>
        <if test="${joiner}recoveryaveTime != null and ${joiner}recoveryaveTime != ''">
            and ${mAlias}RECOVERYAVE_TIME = #{${joiner}recoveryaveTime}
        </if>
        <if test="${joiner}firstSpo2 != null and ${joiner}firstSpo2 != ''">
            and ${mAlias}FIRST_SPO2 = #{${joiner}firstSpo2}
        </if>
        <if test="${joiner}thisYearspo2 != null and ${joiner}thisYearspo2 != ''">
            and ${mAlias}THIS_YEARSPO2 = #{${joiner}thisYearspo2}
        </if>
        <if test="${joiner}firstHr != null and ${joiner}firstHr != ''">
            and ${mAlias}FIRST_HR = #{${joiner}firstHr}
        </if>
        <if test="${joiner}thisYearHr != null and ${joiner}thisYearHr != ''">
            and ${mAlias}THIS_YEAR_HR = #{${joiner}thisYearHr}
        </if>
        <if test="${joiner}firstBlood != null and ${joiner}firstBlood != ''">
            and ${mAlias}FIRST_BLOOD = #{${joiner}firstBlood}
        </if>
        <if test="${joiner}thisYearBlood != null and ${joiner}thisYearBlood != ''">
            and ${mAlias}THIS_YEAR_BLOOD = #{${joiner}thisYearBlood}
        </if>
        <if test="${joiner}firstDistance != null and ${joiner}firstDistance != ''">
            and ${mAlias}FIRST_DISTANCE = #{${joiner}firstDistance}
        </if>
        <if test="${joiner}thisYearDistance != null and ${joiner}thisYearDistance != ''">
            and ${mAlias}THIS_YEAR_DISTANCE = #{${joiner}thisYearDistance}
        </if>
        <if test="${joiner}firstMmrcScore != null and ${joiner}firstMmrcScore != ''">
            and ${mAlias}FIRST_MMRC_SCORE = #{${joiner}firstMmrcScore}
        </if>
        <if test="${joiner}thisYearMmrcScore != null and ${joiner}thisYearMmrcScore != ''">
            and ${mAlias}THIS_YEAR_MMRC_SCORE = #{${joiner}thisYearMmrcScore}
        </if>
        <if test="${joiner}firstActScore != null and ${joiner}firstActScore != ''">
            and ${mAlias}FIRST_ACT_SCORE = #{${joiner}firstActScore}
        </if>
        <if test="${joiner}thisYearActScore != null and ${joiner}thisYearActScore != ''">
            and ${mAlias}THIS_YEAR_ACT_SCORE = #{${joiner}thisYearActScore}
        </if>
        <if test="${joiner}firstMip != null and ${joiner}firstMip != ''">
            and ${mAlias}FIRST_MIP = #{${joiner}firstMip}
        </if>
        <if test="${joiner}thisYearMip != null and ${joiner}thisYearMip != ''">
            and ${mAlias}THIS_YEAR_MIP = #{${joiner}thisYearMip}
        </if>
        <if test="${joiner}firstMep != null and ${joiner}firstMep != ''">
            and ${mAlias}FIRST_MEP = #{${joiner}firstMep}
        </if>
        <if test="${joiner}thisYearMep != null and ${joiner}thisYearMep != ''">
            and ${mAlias}THIS_YEAR_MEP = #{${joiner}thisYearMep}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}year != null and ${joiner}year != ''">
                t.YEAR = #{${joiner}year},
            </when>
            <otherwise>
                t.YEAR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}area != null and ${joiner}area != ''">
                t.AREA = #{${joiner}area},
            </when>
            <otherwise>
                t.AREA = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hosName != null and ${joiner}hosName != ''">
                t.HOS_NAME = #{${joiner}hosName},
            </when>
            <otherwise>
                t.HOS_NAME = null,
            </otherwise>
        </choose>
            t.FIRST_RECVERY_TIME = #{${joiner}firstRecveryTime},
            t.LAST_RECVERY_TIME = #{${joiner}lastRecveryTime},
        <choose>
            <when test="${joiner}hasApparatUsrecovery != null and ${joiner}hasApparatUsrecovery != ''">
                t.HAS_APPARAT_USRECOVERY = #{${joiner}hasApparatUsrecovery},
            </when>
            <otherwise>
                t.HAS_APPARAT_USRECOVERY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hasRecovery != null and ${joiner}hasRecovery != ''">
                t.HAS_RECOVERY = #{${joiner}hasRecovery},
            </when>
            <otherwise>
                t.HAS_RECOVERY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}recoveryNum != null and ${joiner}recoveryNum != ''">
                t.RECOVERY_NUM = #{${joiner}recoveryNum},
            </when>
            <otherwise>
                t.RECOVERY_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}recoveryTime != null and ${joiner}recoveryTime != ''">
                t.RECOVERY_TIME = #{${joiner}recoveryTime},
            </when>
            <otherwise>
                t.RECOVERY_TIME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}recoveryaveTime != null and ${joiner}recoveryaveTime != ''">
                t.RECOVERYAVE_TIME = #{${joiner}recoveryaveTime},
            </when>
            <otherwise>
                t.RECOVERYAVE_TIME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}firstSpo2 != null and ${joiner}firstSpo2 != ''">
                t.FIRST_SPO2 = #{${joiner}firstSpo2},
            </when>
            <otherwise>
                t.FIRST_SPO2 = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}thisYearspo2 != null and ${joiner}thisYearspo2 != ''">
                t.THIS_YEARSPO2 = #{${joiner}thisYearspo2},
            </when>
            <otherwise>
                t.THIS_YEARSPO2 = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}firstHr != null and ${joiner}firstHr != ''">
                t.FIRST_HR = #{${joiner}firstHr},
            </when>
            <otherwise>
                t.FIRST_HR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}thisYearHr != null and ${joiner}thisYearHr != ''">
                t.THIS_YEAR_HR = #{${joiner}thisYearHr},
            </when>
            <otherwise>
                t.THIS_YEAR_HR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}firstBlood != null and ${joiner}firstBlood != ''">
                t.FIRST_BLOOD = #{${joiner}firstBlood},
            </when>
            <otherwise>
                t.FIRST_BLOOD = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}thisYearBlood != null and ${joiner}thisYearBlood != ''">
                t.THIS_YEAR_BLOOD = #{${joiner}thisYearBlood},
            </when>
            <otherwise>
                t.THIS_YEAR_BLOOD = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}firstDistance != null and ${joiner}firstDistance != ''">
                t.FIRST_DISTANCE = #{${joiner}firstDistance},
            </when>
            <otherwise>
                t.FIRST_DISTANCE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}thisYearDistance != null and ${joiner}thisYearDistance != ''">
                t.THIS_YEAR_DISTANCE = #{${joiner}thisYearDistance},
            </when>
            <otherwise>
                t.THIS_YEAR_DISTANCE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}firstMmrcScore != null and ${joiner}firstMmrcScore != ''">
                t.FIRST_MMRC_SCORE = #{${joiner}firstMmrcScore},
            </when>
            <otherwise>
                t.FIRST_MMRC_SCORE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}thisYearMmrcScore != null and ${joiner}thisYearMmrcScore != ''">
                t.THIS_YEAR_MMRC_SCORE = #{${joiner}thisYearMmrcScore},
            </when>
            <otherwise>
                t.THIS_YEAR_MMRC_SCORE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}firstActScore != null and ${joiner}firstActScore != ''">
                t.FIRST_ACT_SCORE = #{${joiner}firstActScore},
            </when>
            <otherwise>
                t.FIRST_ACT_SCORE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}thisYearActScore != null and ${joiner}thisYearActScore != ''">
                t.THIS_YEAR_ACT_SCORE = #{${joiner}thisYearActScore},
            </when>
            <otherwise>
                t.THIS_YEAR_ACT_SCORE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}firstMip != null and ${joiner}firstMip != ''">
                t.FIRST_MIP = #{${joiner}firstMip},
            </when>
            <otherwise>
                t.FIRST_MIP = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}thisYearMip != null and ${joiner}thisYearMip != ''">
                t.THIS_YEAR_MIP = #{${joiner}thisYearMip},
            </when>
            <otherwise>
                t.THIS_YEAR_MIP = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}firstMep != null and ${joiner}firstMep != ''">
                t.FIRST_MEP = #{${joiner}firstMep},
            </when>
            <otherwise>
                t.FIRST_MEP = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}thisYearMep != null and ${joiner}thisYearMep != ''">
                t.THIS_YEAR_MEP = #{${joiner}thisYearMep},
            </when>
            <otherwise>
                t.THIS_YEAR_MEP = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWCF_KF_RCD t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWCF_KF_RCD t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWCF_KF_RCD t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwcfKfRcd" >
        update TD_ZWCF_KF_RCD t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZWCF_KF_RCD t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZwcfKfRcd">
        delete from TD_ZWCF_KF_RCD
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

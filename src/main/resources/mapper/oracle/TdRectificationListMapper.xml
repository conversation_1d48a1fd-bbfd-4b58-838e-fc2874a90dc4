<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdRectificationListMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdRectificationList">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="UUID" property="uuid" />
        <result column="UNIT_NAME" property="unitName" />
        <result column="ZONE_ID" property="fkByZoneId.rid" />
        <result column="CREDIT_CODE" property="creditCode" />
        <result column="INDUSTRY_ID" property="fkByIndustryId.rid" />
        <result column="ECONOMIC_ID" property="fkByEconomicId.rid" />
        <result column="ENTERPRISE_SCALE_ID" property="fkByEnterpriseScaleId.rid" />
        <result column="LINK_MAN" property="linkMan" />
        <result column="LINK_PHONE" property="linkPhone" />
        <result column="ADDRESS" property="address" />
        <result column="ENROL_ADDRESS" property="enrolAddress" />
        <result column="IF_ABOVE_10_STAFF" property="ifAbove10Staff" />
        <result column="IF_MAIN_FACTOR_OVER" property="ifMainFactorOver" />
        <result column="IF_WORK_PLACE_DETECTION" property="ifWorkPlaceDetection" />
        <result column="IF_OTHER_FACTOR_OVER" property="ifOtherFactorOver" />
        <result column="DATA_SOURCE" property="dataSource" />
        <result column="REGULATION_RSN" property="regulationRsn" />
        <result column="CONFIRM_DATE" property="confirmDate" />
        <result column="REPORT_DATE" property="reportDate" />
        <result column="COMPLETE_DATE" property="completeDate" />
        <result column="IN_PROGRESS" property="inProgress" />
        <result column="PRE_TOTAL_HG" property="preTotalHg" />
        <result column="RESULT_ABNORMAL" property="resultAbnormal" />
        <result column="RECTIFICATION_RESULT" property="rectificationResult" />
        <result column="REPORT_STATE" property="reportState" />
        <result column="CHECK_UNIT" property="checkUnit" />
        <result column="CHECK_PSN" property="checkPsn" />
        <result column="CHECK_DATE" property="checkDate" />
        <result column="CHECK_ADV" property="checkAdv" />
        <result column="SERVICE_ORG_NAME" property="serviceOrgName" />
        <result column="UPDATE_DATE" property="updateDate" />
        <result column="CRPT_ID" property="fkByCrptId.rid" />
        <result column="SOURCE_CRPT_ID" property="fkBySourceCrptId.rid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.UUID,t.UNIT_NAME,t.ZONE_ID,t.CREDIT_CODE,t.INDUSTRY_ID,t.ECONOMIC_ID,t.ENTERPRISE_SCALE_ID,t.LINK_MAN,t.LINK_PHONE,t.ADDRESS,t.ENROL_ADDRESS,t.IF_ABOVE_10_STAFF,t.IF_MAIN_FACTOR_OVER,t.IF_WORK_PLACE_DETECTION,t.IF_OTHER_FACTOR_OVER,t.DATA_SOURCE,t.REGULATION_RSN,t.CONFIRM_DATE,t.REPORT_DATE,t.COMPLETE_DATE,t.IN_PROGRESS,t.PRE_TOTAL_HG,t.RESULT_ABNORMAL,t.RECTIFICATION_RESULT,t.REPORT_STATE,t.CHECK_UNIT,t.CHECK_PSN,t.CHECK_DATE,t.CHECK_ADV,t.SERVICE_ORG_NAME,t.UPDATE_DATE,t.CRPT_ID,t.SOURCE_CRPT_ID,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}uuid != null">
            and ${mAlias}UUID = #{${joiner}uuid}
        </if>
        <if test="${joiner}unitName != null">
            and ${mAlias}UNIT_NAME = #{${joiner}unitName}
        </if>
        <if test="${joiner}fkByZoneId != null and ${joiner}fkByZoneId.rid != null">
            and ${mAlias}ZONE_ID = #{${joiner}fkByZoneId.rid}
        </if>
        <if test="${joiner}creditCode != null">
            and ${mAlias}CREDIT_CODE = #{${joiner}creditCode}
        </if>
        <if test="${joiner}fkByIndustryId != null and ${joiner}fkByIndustryId.rid != null">
            and ${mAlias}INDUSTRY_ID = #{${joiner}fkByIndustryId.rid}
        </if>
        <if test="${joiner}fkByEconomicId != null and ${joiner}fkByEconomicId.rid != null">
            and ${mAlias}ECONOMIC_ID = #{${joiner}fkByEconomicId.rid}
        </if>
        <if test="${joiner}fkByEnterpriseScaleId != null and ${joiner}fkByEnterpriseScaleId.rid != null">
            and ${mAlias}ENTERPRISE_SCALE_ID = #{${joiner}fkByEnterpriseScaleId.rid}
        </if>
        <if test="${joiner}linkMan != null">
            and ${mAlias}LINK_MAN = #{${joiner}linkMan}
        </if>
        <if test="${joiner}linkPhone != null">
            and ${mAlias}LINK_PHONE = #{${joiner}linkPhone}
        </if>
        <if test="${joiner}address != null">
            and ${mAlias}ADDRESS = #{${joiner}address}
        </if>
        <if test="${joiner}enrolAddress != null">
            and ${mAlias}ENROL_ADDRESS = #{${joiner}enrolAddress}
        </if>
        <if test="${joiner}ifAbove10Staff != null">
            and ${mAlias}IF_ABOVE_10_STAFF = #{${joiner}ifAbove10Staff}
        </if>
        <if test="${joiner}ifMainFactorOver != null">
            and ${mAlias}IF_MAIN_FACTOR_OVER = #{${joiner}ifMainFactorOver}
        </if>
        <if test="${joiner}ifWorkPlaceDetection != null">
            and ${mAlias}IF_WORK_PLACE_DETECTION = #{${joiner}ifWorkPlaceDetection}
        </if>
        <if test="${joiner}ifOtherFactorOver != null">
            and ${mAlias}IF_OTHER_FACTOR_OVER = #{${joiner}ifOtherFactorOver}
        </if>
        <if test="${joiner}dataSource != null">
            and ${mAlias}DATA_SOURCE = #{${joiner}dataSource}
        </if>
        <if test="${joiner}regulationRsn != null">
            and ${mAlias}REGULATION_RSN = #{${joiner}regulationRsn}
        </if>
        <if test="${joiner}confirmDate != null">
            and ${mAlias}CONFIRM_DATE = #{${joiner}confirmDate}
        </if>
        <if test="${joiner}reportDate != null">
            and ${mAlias}REPORT_DATE = #{${joiner}reportDate}
        </if>
        <if test="${joiner}completeDate != null">
            and ${mAlias}COMPLETE_DATE = #{${joiner}completeDate}
        </if>
        <if test="${joiner}inProgress != null">
            and ${mAlias}IN_PROGRESS = #{${joiner}inProgress}
        </if>
        <if test="${joiner}preTotalHg != null">
            and ${mAlias}PRE_TOTAL_HG = #{${joiner}preTotalHg}
        </if>
        <if test="${joiner}resultAbnormal != null">
            and ${mAlias}RESULT_ABNORMAL = #{${joiner}resultAbnormal}
        </if>
        <if test="${joiner}rectificationResult != null">
            and ${mAlias}RECTIFICATION_RESULT = #{${joiner}rectificationResult}
        </if>
        <if test="${joiner}reportState != null">
            and ${mAlias}REPORT_STATE = #{${joiner}reportState}
        </if>
        <if test="${joiner}checkUnit != null">
            and ${mAlias}CHECK_UNIT = #{${joiner}checkUnit}
        </if>
        <if test="${joiner}checkPsn != null">
            and ${mAlias}CHECK_PSN = #{${joiner}checkPsn}
        </if>
        <if test="${joiner}checkDate != null">
            and ${mAlias}CHECK_DATE = #{${joiner}checkDate}
        </if>
        <if test="${joiner}checkAdv != null">
            and ${mAlias}CHECK_ADV = #{${joiner}checkAdv}
        </if>
        <if test="${joiner}serviceOrgName != null">
            and ${mAlias}SERVICE_ORG_NAME = #{${joiner}serviceOrgName}
        </if>
        <if test="${joiner}updateDate != null">
            and ${mAlias}UPDATE_DATE = #{${joiner}updateDate}
        </if>
        <if test="${joiner}fkByCrptId != null and ${joiner}fkByCrptId.rid != null">
            and ${mAlias}CRPT_ID = #{${joiner}fkByCrptId.rid}
        </if>
        <if test="${joiner}fkBySourceCrptId != null and ${joiner}fkBySourceCrptId.rid != null">
            and ${mAlias}SOURCE_CRPT_ID = #{${joiner}fkBySourceCrptId.rid}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}uuid != null">
                t.UUID = #{${joiner}uuid},
            </when>
            <otherwise>
                t.UUID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}unitName != null">
                t.UNIT_NAME = #{${joiner}unitName},
            </when>
            <otherwise>
                t.UNIT_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByZoneId != null and ${joiner}fkByZoneId.rid != null">
                t.ZONE_ID = #{${joiner}fkByZoneId.rid},
            </when>
            <otherwise>
                t.ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}creditCode != null">
                t.CREDIT_CODE = #{${joiner}creditCode},
            </when>
            <otherwise>
                t.CREDIT_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByIndustryId != null and ${joiner}fkByIndustryId.rid != null">
                t.INDUSTRY_ID = #{${joiner}fkByIndustryId.rid},
            </when>
            <otherwise>
                t.INDUSTRY_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByEconomicId != null and ${joiner}fkByEconomicId.rid != null">
                t.ECONOMIC_ID = #{${joiner}fkByEconomicId.rid},
            </when>
            <otherwise>
                t.ECONOMIC_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByEnterpriseScaleId != null and ${joiner}fkByEnterpriseScaleId.rid != null">
                t.ENTERPRISE_SCALE_ID = #{${joiner}fkByEnterpriseScaleId.rid},
            </when>
            <otherwise>
                t.ENTERPRISE_SCALE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkMan != null">
                t.LINK_MAN = #{${joiner}linkMan},
            </when>
            <otherwise>
                t.LINK_MAN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkPhone != null">
                t.LINK_PHONE = #{${joiner}linkPhone},
            </when>
            <otherwise>
                t.LINK_PHONE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}address != null">
                t.ADDRESS = #{${joiner}address},
            </when>
            <otherwise>
                t.ADDRESS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}enrolAddress != null">
                t.ENROL_ADDRESS = #{${joiner}enrolAddress},
            </when>
            <otherwise>
                t.ENROL_ADDRESS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifAbove10Staff != null">
                t.IF_ABOVE_10_STAFF = #{${joiner}ifAbove10Staff},
            </when>
            <otherwise>
                t.IF_ABOVE_10_STAFF = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifMainFactorOver != null">
                t.IF_MAIN_FACTOR_OVER = #{${joiner}ifMainFactorOver},
            </when>
            <otherwise>
                t.IF_MAIN_FACTOR_OVER = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifWorkPlaceDetection != null">
                t.IF_WORK_PLACE_DETECTION = #{${joiner}ifWorkPlaceDetection},
            </when>
            <otherwise>
                t.IF_WORK_PLACE_DETECTION = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifOtherFactorOver != null">
                t.IF_OTHER_FACTOR_OVER = #{${joiner}ifOtherFactorOver},
            </when>
            <otherwise>
                t.IF_OTHER_FACTOR_OVER = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dataSource != null">
                t.DATA_SOURCE = #{${joiner}dataSource},
            </when>
            <otherwise>
                t.DATA_SOURCE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}regulationRsn != null">
                t.REGULATION_RSN = #{${joiner}regulationRsn},
            </when>
            <otherwise>
                t.REGULATION_RSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}confirmDate != null">
                t.CONFIRM_DATE = #{${joiner}confirmDate},
            </when>
            <otherwise>
                t.CONFIRM_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}reportDate != null">
                t.REPORT_DATE = #{${joiner}reportDate},
            </when>
            <otherwise>
                t.REPORT_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}completeDate != null">
                t.COMPLETE_DATE = #{${joiner}completeDate},
            </when>
            <otherwise>
                t.COMPLETE_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}inProgress != null">
                t.IN_PROGRESS = #{${joiner}inProgress},
            </when>
            <otherwise>
                t.IN_PROGRESS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}preTotalHg != null">
                t.PRE_TOTAL_HG = #{${joiner}preTotalHg},
            </when>
            <otherwise>
                t.PRE_TOTAL_HG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}resultAbnormal != null">
                t.RESULT_ABNORMAL = #{${joiner}resultAbnormal},
            </when>
            <otherwise>
                t.RESULT_ABNORMAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rectificationResult != null">
                t.RECTIFICATION_RESULT = #{${joiner}rectificationResult},
            </when>
            <otherwise>
                t.RECTIFICATION_RESULT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}reportState != null">
                t.REPORT_STATE = #{${joiner}reportState},
            </when>
            <otherwise>
                t.REPORT_STATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}checkUnit != null">
                t.CHECK_UNIT = #{${joiner}checkUnit},
            </when>
            <otherwise>
                t.CHECK_UNIT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}checkPsn != null">
                t.CHECK_PSN = #{${joiner}checkPsn},
            </when>
            <otherwise>
                t.CHECK_PSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}checkDate != null">
                t.CHECK_DATE = #{${joiner}checkDate},
            </when>
            <otherwise>
                t.CHECK_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}checkAdv != null">
                t.CHECK_ADV = #{${joiner}checkAdv},
            </when>
            <otherwise>
                t.CHECK_ADV = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}serviceOrgName != null">
                t.SERVICE_ORG_NAME = #{${joiner}serviceOrgName},
            </when>
            <otherwise>
                t.SERVICE_ORG_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}updateDate != null">
                t.UPDATE_DATE = #{${joiner}updateDate},
            </when>
            <otherwise>
                t.UPDATE_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCrptId != null and ${joiner}fkByCrptId.rid != null">
                t.CRPT_ID = #{${joiner}fkByCrptId.rid},
            </when>
            <otherwise>
                t.CRPT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkBySourceCrptId != null and ${joiner}fkBySourceCrptId.rid != null">
                t.SOURCE_CRPT_ID = #{${joiner}fkBySourceCrptId.rid},
            </when>
            <otherwise>
                t.SOURCE_CRPT_ID = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_RECTIFICATION_LIST t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_RECTIFICATION_LIST t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_RECTIFICATION_LIST t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdRectificationList">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_RECTIFICATION_LIST_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_RECTIFICATION_LIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            UUID,
            UNIT_NAME,
            ZONE_ID,
            CREDIT_CODE,
            INDUSTRY_ID,
            ECONOMIC_ID,
            ENTERPRISE_SCALE_ID,
            LINK_MAN,
            LINK_PHONE,
            ADDRESS,
            ENROL_ADDRESS,
            IF_ABOVE_10_STAFF,
            IF_MAIN_FACTOR_OVER,
            IF_WORK_PLACE_DETECTION,
            IF_OTHER_FACTOR_OVER,
            DATA_SOURCE,
            REGULATION_RSN,
            CONFIRM_DATE,
            REPORT_DATE,
            COMPLETE_DATE,
            IN_PROGRESS,
            PRE_TOTAL_HG,
            RESULT_ABNORMAL,
            RECTIFICATION_RESULT,
            REPORT_STATE,
            CHECK_UNIT,
            CHECK_PSN,
            CHECK_DATE,
            CHECK_ADV,
            SERVICE_ORG_NAME,
            UPDATE_DATE,
            CRPT_ID,
            SOURCE_CRPT_ID,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{uuid},
            #{unitName},
            #{fkByZoneId.rid},
            #{creditCode},
            #{fkByIndustryId.rid},
            #{fkByEconomicId.rid},
            #{fkByEnterpriseScaleId.rid},
            #{linkMan},
            #{linkPhone},
            #{address},
            #{enrolAddress},
            #{ifAbove10Staff},
            #{ifMainFactorOver},
            #{ifWorkPlaceDetection},
            #{ifOtherFactorOver},
            #{dataSource},
            #{regulationRsn},
            #{confirmDate},
            #{reportDate},
            #{completeDate},
            #{inProgress},
            #{preTotalHg},
            #{resultAbnormal},
            #{rectificationResult},
            #{reportState},
            #{checkUnit},
            #{checkPsn},
            #{checkDate},
            #{checkAdv},
            #{serviceOrgName},
            #{updateDate},
            #{fkByCrptId.rid},
            #{fkBySourceCrptId.rid},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_RECTIFICATION_LIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            UUID,
            UNIT_NAME,
            ZONE_ID,
            CREDIT_CODE,
            INDUSTRY_ID,
            ECONOMIC_ID,
            ENTERPRISE_SCALE_ID,
            LINK_MAN,
            LINK_PHONE,
            ADDRESS,
            ENROL_ADDRESS,
            IF_ABOVE_10_STAFF,
            IF_MAIN_FACTOR_OVER,
            IF_WORK_PLACE_DETECTION,
            IF_OTHER_FACTOR_OVER,
            DATA_SOURCE,
            REGULATION_RSN,
            CONFIRM_DATE,
            REPORT_DATE,
            COMPLETE_DATE,
            IN_PROGRESS,
            PRE_TOTAL_HG,
            RESULT_ABNORMAL,
            RECTIFICATION_RESULT,
            REPORT_STATE,
            CHECK_UNIT,
            CHECK_PSN,
            CHECK_DATE,
            CHECK_ADV,
            SERVICE_ORG_NAME,
            UPDATE_DATE,
            CRPT_ID,
            SOURCE_CRPT_ID,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_RECTIFICATION_LIST_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.uuid != null">
                            #{item.uuid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.unitName != null">
                            #{item.unitName} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByZoneId != null and item.fkByZoneId.rid != null">
                            #{item.fkByZoneId.rid} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.creditCode != null">
                            #{item.creditCode} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByIndustryId != null and item.fkByIndustryId.rid != null">
                            #{item.fkByIndustryId.rid} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByEconomicId != null and item.fkByEconomicId.rid != null">
                            #{item.fkByEconomicId.rid} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByEnterpriseScaleId != null and item.fkByEnterpriseScaleId.rid != null">
                            #{item.fkByEnterpriseScaleId.rid} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.linkMan != null">
                            #{item.linkMan} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.linkPhone != null">
                            #{item.linkPhone} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.address != null">
                            #{item.address} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.enrolAddress != null">
                            #{item.enrolAddress} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifAbove10Staff != null">
                            #{item.ifAbove10Staff} AS C16,
                        </when>
                        <otherwise>
                            NULL AS C16,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifMainFactorOver != null">
                            #{item.ifMainFactorOver} AS C17,
                        </when>
                        <otherwise>
                            NULL AS C17,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifWorkPlaceDetection != null">
                            #{item.ifWorkPlaceDetection} AS C18,
                        </when>
                        <otherwise>
                            NULL AS C18,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifOtherFactorOver != null">
                            #{item.ifOtherFactorOver} AS C19,
                        </when>
                        <otherwise>
                            NULL AS C19,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.dataSource != null">
                            #{item.dataSource} AS C20,
                        </when>
                        <otherwise>
                            NULL AS C20,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.regulationRsn != null">
                            #{item.regulationRsn} AS C21,
                        </when>
                        <otherwise>
                            NULL AS C21,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.confirmDate != null">
                            #{item.confirmDate} AS C22,
                        </when>
                        <otherwise>
                            NULL AS C22,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.reportDate != null">
                            #{item.reportDate} AS C23,
                        </when>
                        <otherwise>
                            NULL AS C23,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.completeDate != null">
                            #{item.completeDate} AS C24,
                        </when>
                        <otherwise>
                            NULL AS C24,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.inProgress != null">
                            #{item.inProgress} AS C25,
                        </when>
                        <otherwise>
                            NULL AS C25,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.preTotalHg != null">
                            #{item.preTotalHg} AS C26,
                        </when>
                        <otherwise>
                            NULL AS C26,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.resultAbnormal != null">
                            #{item.resultAbnormal} AS C27,
                        </when>
                        <otherwise>
                            NULL AS C27,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rectificationResult != null">
                            #{item.rectificationResult} AS C28,
                        </when>
                        <otherwise>
                            NULL AS C28,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.reportState != null">
                            #{item.reportState} AS C29,
                        </when>
                        <otherwise>
                            NULL AS C29,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.checkUnit != null">
                            #{item.checkUnit} AS C30,
                        </when>
                        <otherwise>
                            NULL AS C30,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.checkPsn != null">
                            #{item.checkPsn} AS C31,
                        </when>
                        <otherwise>
                            NULL AS C31,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.checkDate != null">
                            #{item.checkDate} AS C32,
                        </when>
                        <otherwise>
                            NULL AS C32,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.checkAdv != null">
                            #{item.checkAdv} AS C33,
                        </when>
                        <otherwise>
                            NULL AS C33,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.serviceOrgName != null">
                            #{item.serviceOrgName} AS C34,
                        </when>
                        <otherwise>
                            NULL AS C34,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.updateDate != null">
                            #{item.updateDate} AS C35,
                        </when>
                        <otherwise>
                            NULL AS C35,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByCrptId != null and item.fkByCrptId.rid != null">
                            #{item.fkByCrptId.rid} AS C36,
                        </when>
                        <otherwise>
                            NULL AS C36,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkBySourceCrptId != null and item.fkBySourceCrptId.rid != null">
                            #{item.fkBySourceCrptId.rid} AS C37,
                        </when>
                        <otherwise>
                            NULL AS C37,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdRectificationList" >
        update TD_RECTIFICATION_LIST t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_RECTIFICATION_LIST t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 注意 joiner 需要加入 -->
    <delete id="removeByEntity" parameterType="TdRectificationList">
        delete from TD_RECTIFICATION_LIST
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
<select id="findByUuidList" resultMap="BaseResultMap" parameterType="java.util.List">
    select
    <trim suffixOverrides=",">
        <include refid="BaseColumnList"/>
    </trim>
    from  TD_RECTIFICATION_LIST t
    where 1=1
    <if test="list != null and list.size > 0">
        AND t.UUID IN
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")" >
            <trim  suffixOverrides=",">
                #{item}
            </trim>
        </foreach>
    </if>
</select>



</mapper>

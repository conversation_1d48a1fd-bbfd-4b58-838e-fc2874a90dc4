<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZyUnitharmfactorcheckMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZyUnitharmfactorcheck">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="IFAT" property="ifat" />
        <result column="TEST_UNIT_NAMES" property="testUnitNames" />
        <result column="TEST_REPORT_NOS" property="testReportNos" />
        <result column="IFAT_DUST" property="ifatDust" />
        <result column="IFAT_DUST_ALL_CHECKNUM" property="ifatDustAllChecknum" />
        <result column="IFAT_DUST_ALL_EXCESSNUM" property="ifatDustAllExcessnum" />
        <result column="IFAT_DUST_CHECKNUM" property="ifatDustChecknum" />
        <result column="IFAT_DUST_EXCESSNUM" property="ifatDustExcessnum" />
        <result column="IFAT_DUST_COAL_CHECKNUM" property="ifatDustCoalChecknum" />
        <result column="IFAT_DUST_COAL_EXCESSNUM" property="ifatDustCoalExcessnum" />
        <result column="IFAT_DUST_ASBESTOS_CHECKNUM" property="ifatDustAsbestosChecknum" />
        <result column="IFAT_DUST_ASBESTOS_EXCESSNUM" property="ifatDustAsbestosExcessnum" />
        <result column="IFAT_CHEMISTRY" property="ifatChemistry" />
        <result column="IFAT_CHEMISTRY_ALL_CHECKNUM" property="ifatChemistryAllChecknum" />
        <result column="IFAT_CHEMISTRY_ALL_EXCESSNUM" property="ifatChemistryAllExcessnum" />
        <result column="IFAT_CHEMISTRY_CHECKNUM" property="ifatChemistryChecknum" />
        <result column="IFAT_CHEMISTRY_EXCESSNUM" property="ifatChemistryExcessnum" />
        <result column="IFAT_CHEMISTRY_BENZENE_CHECKNU" property="ifatChemistryBenzeneChecknu" />
        <result column="IFAT_CHEMISTRY_BENZENE_EXCESSN" property="ifatChemistryBenzeneExcessn" />
        <result column="IFAT_PHYSICS" property="ifatPhysics" />
        <result column="IFAT_PHYSICS_ALL_CHECKNUM" property="ifatPhysicsAllChecknum" />
        <result column="IFAT_PHYSICS_ALL_EXCESSNUM" property="ifatPhysicsAllExcessnum" />
        <result column="IFAT_PHYSICS_CHECKNUM" property="ifatPhysicsChecknum" />
        <result column="IFAT_PHYSICS_EXCESSNUM" property="ifatPhysicsExcessnum" />
        <result column="IFAT_RADIOACTIVITY" property="ifatRadioactivity" />
        <result column="IFAT_RADIOACTIVITY_CHECKNUM" property="ifatRadioactivityChecknum" />
        <result column="IFAT_RADIOACTIVITY_EXCESSNUM" property="ifatRadioactivityExcessnum" />
        <result column="IFAT_BIOLOGYOTHER" property="ifatBiologyother" />
        <result column="IFAT_BIOLOGYOTHER_CHECKNUM" property="ifatBiologyotherChecknum" />
        <result column="IFAT_BIOLOGYOTHER_EXCESSNUM" property="ifatBiologyotherExcessnum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MAIN_ID,t.IFAT,t.TEST_UNIT_NAMES,t.TEST_REPORT_NOS,t.IFAT_DUST,t.IFAT_DUST_ALL_CHECKNUM,t.IFAT_DUST_ALL_EXCESSNUM,t.IFAT_DUST_CHECKNUM,t.IFAT_DUST_EXCESSNUM,t.IFAT_DUST_COAL_CHECKNUM,t.IFAT_DUST_COAL_EXCESSNUM,t.IFAT_DUST_ASBESTOS_CHECKNUM,t.IFAT_DUST_ASBESTOS_EXCESSNUM,t.IFAT_CHEMISTRY,t.IFAT_CHEMISTRY_ALL_CHECKNUM,t.IFAT_CHEMISTRY_ALL_EXCESSNUM,t.IFAT_CHEMISTRY_CHECKNUM,t.IFAT_CHEMISTRY_EXCESSNUM,t.IFAT_CHEMISTRY_BENZENE_CHECKNU,t.IFAT_CHEMISTRY_BENZENE_EXCESSN,t.IFAT_PHYSICS,t.IFAT_PHYSICS_ALL_CHECKNUM,t.IFAT_PHYSICS_ALL_EXCESSNUM,t.IFAT_PHYSICS_CHECKNUM,t.IFAT_PHYSICS_EXCESSNUM,t.IFAT_RADIOACTIVITY,t.IFAT_RADIOACTIVITY_CHECKNUM,t.IFAT_RADIOACTIVITY_EXCESSNUM,t.IFAT_BIOLOGYOTHER,t.IFAT_BIOLOGYOTHER_CHECKNUM,t.IFAT_BIOLOGYOTHER_EXCESSNUM,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}ifat != null">
            and ${mAlias}IFAT = #{${joiner}ifat}
        </if>
        <if test="${joiner}testUnitNames != null">
            and ${mAlias}TEST_UNIT_NAMES = #{${joiner}testUnitNames}
        </if>
        <if test="${joiner}testReportNos != null">
            and ${mAlias}TEST_REPORT_NOS = #{${joiner}testReportNos}
        </if>
        <if test="${joiner}ifatDust != null">
            and ${mAlias}IFAT_DUST = #{${joiner}ifatDust}
        </if>
        <if test="${joiner}ifatDustAllChecknum != null">
            and ${mAlias}IFAT_DUST_ALL_CHECKNUM = #{${joiner}ifatDustAllChecknum}
        </if>
        <if test="${joiner}ifatDustAllExcessnum != null">
            and ${mAlias}IFAT_DUST_ALL_EXCESSNUM = #{${joiner}ifatDustAllExcessnum}
        </if>
        <if test="${joiner}ifatDustChecknum != null">
            and ${mAlias}IFAT_DUST_CHECKNUM = #{${joiner}ifatDustChecknum}
        </if>
        <if test="${joiner}ifatDustExcessnum != null">
            and ${mAlias}IFAT_DUST_EXCESSNUM = #{${joiner}ifatDustExcessnum}
        </if>
        <if test="${joiner}ifatDustCoalChecknum != null">
            and ${mAlias}IFAT_DUST_COAL_CHECKNUM = #{${joiner}ifatDustCoalChecknum}
        </if>
        <if test="${joiner}ifatDustCoalExcessnum != null">
            and ${mAlias}IFAT_DUST_COAL_EXCESSNUM = #{${joiner}ifatDustCoalExcessnum}
        </if>
        <if test="${joiner}ifatDustAsbestosChecknum != null">
            and ${mAlias}IFAT_DUST_ASBESTOS_CHECKNUM = #{${joiner}ifatDustAsbestosChecknum}
        </if>
        <if test="${joiner}ifatDustAsbestosExcessnum != null">
            and ${mAlias}IFAT_DUST_ASBESTOS_EXCESSNUM = #{${joiner}ifatDustAsbestosExcessnum}
        </if>
        <if test="${joiner}ifatChemistry != null">
            and ${mAlias}IFAT_CHEMISTRY = #{${joiner}ifatChemistry}
        </if>
        <if test="${joiner}ifatChemistryAllChecknum != null">
            and ${mAlias}IFAT_CHEMISTRY_ALL_CHECKNUM = #{${joiner}ifatChemistryAllChecknum}
        </if>
        <if test="${joiner}ifatChemistryAllExcessnum != null">
            and ${mAlias}IFAT_CHEMISTRY_ALL_EXCESSNUM = #{${joiner}ifatChemistryAllExcessnum}
        </if>
        <if test="${joiner}ifatChemistryChecknum != null">
            and ${mAlias}IFAT_CHEMISTRY_CHECKNUM = #{${joiner}ifatChemistryChecknum}
        </if>
        <if test="${joiner}ifatChemistryExcessnum != null">
            and ${mAlias}IFAT_CHEMISTRY_EXCESSNUM = #{${joiner}ifatChemistryExcessnum}
        </if>
        <if test="${joiner}ifatChemistryBenzeneChecknu != null">
            and ${mAlias}IFAT_CHEMISTRY_BENZENE_CHECKNU = #{${joiner}ifatChemistryBenzeneChecknu}
        </if>
        <if test="${joiner}ifatChemistryBenzeneExcessn != null">
            and ${mAlias}IFAT_CHEMISTRY_BENZENE_EXCESSN = #{${joiner}ifatChemistryBenzeneExcessn}
        </if>
        <if test="${joiner}ifatPhysics != null">
            and ${mAlias}IFAT_PHYSICS = #{${joiner}ifatPhysics}
        </if>
        <if test="${joiner}ifatPhysicsAllChecknum != null">
            and ${mAlias}IFAT_PHYSICS_ALL_CHECKNUM = #{${joiner}ifatPhysicsAllChecknum}
        </if>
        <if test="${joiner}ifatPhysicsAllExcessnum != null">
            and ${mAlias}IFAT_PHYSICS_ALL_EXCESSNUM = #{${joiner}ifatPhysicsAllExcessnum}
        </if>
        <if test="${joiner}ifatPhysicsChecknum != null">
            and ${mAlias}IFAT_PHYSICS_CHECKNUM = #{${joiner}ifatPhysicsChecknum}
        </if>
        <if test="${joiner}ifatPhysicsExcessnum != null">
            and ${mAlias}IFAT_PHYSICS_EXCESSNUM = #{${joiner}ifatPhysicsExcessnum}
        </if>
        <if test="${joiner}ifatRadioactivity != null">
            and ${mAlias}IFAT_RADIOACTIVITY = #{${joiner}ifatRadioactivity}
        </if>
        <if test="${joiner}ifatRadioactivityChecknum != null">
            and ${mAlias}IFAT_RADIOACTIVITY_CHECKNUM = #{${joiner}ifatRadioactivityChecknum}
        </if>
        <if test="${joiner}ifatRadioactivityExcessnum != null">
            and ${mAlias}IFAT_RADIOACTIVITY_EXCESSNUM = #{${joiner}ifatRadioactivityExcessnum}
        </if>
        <if test="${joiner}ifatBiologyother != null">
            and ${mAlias}IFAT_BIOLOGYOTHER = #{${joiner}ifatBiologyother}
        </if>
        <if test="${joiner}ifatBiologyotherChecknum != null">
            and ${mAlias}IFAT_BIOLOGYOTHER_CHECKNUM = #{${joiner}ifatBiologyotherChecknum}
        </if>
        <if test="${joiner}ifatBiologyotherExcessnum != null">
            and ${mAlias}IFAT_BIOLOGYOTHER_EXCESSNUM = #{${joiner}ifatBiologyotherExcessnum}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifat != null">
                t.IFAT = #{${joiner}ifat},
            </when>
            <otherwise>
                t.IFAT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}testUnitNames != null">
                t.TEST_UNIT_NAMES = #{${joiner}testUnitNames},
            </when>
            <otherwise>
                t.TEST_UNIT_NAMES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}testReportNos != null">
                t.TEST_REPORT_NOS = #{${joiner}testReportNos},
            </when>
            <otherwise>
                t.TEST_REPORT_NOS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatDust != null">
                t.IFAT_DUST = #{${joiner}ifatDust},
            </when>
            <otherwise>
                t.IFAT_DUST = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatDustAllChecknum != null">
                t.IFAT_DUST_ALL_CHECKNUM = #{${joiner}ifatDustAllChecknum},
            </when>
            <otherwise>
                t.IFAT_DUST_ALL_CHECKNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatDustAllExcessnum != null">
                t.IFAT_DUST_ALL_EXCESSNUM = #{${joiner}ifatDustAllExcessnum},
            </when>
            <otherwise>
                t.IFAT_DUST_ALL_EXCESSNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatDustChecknum != null">
                t.IFAT_DUST_CHECKNUM = #{${joiner}ifatDustChecknum},
            </when>
            <otherwise>
                t.IFAT_DUST_CHECKNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatDustExcessnum != null">
                t.IFAT_DUST_EXCESSNUM = #{${joiner}ifatDustExcessnum},
            </when>
            <otherwise>
                t.IFAT_DUST_EXCESSNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatDustCoalChecknum != null">
                t.IFAT_DUST_COAL_CHECKNUM = #{${joiner}ifatDustCoalChecknum},
            </when>
            <otherwise>
                t.IFAT_DUST_COAL_CHECKNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatDustCoalExcessnum != null">
                t.IFAT_DUST_COAL_EXCESSNUM = #{${joiner}ifatDustCoalExcessnum},
            </when>
            <otherwise>
                t.IFAT_DUST_COAL_EXCESSNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatDustAsbestosChecknum != null">
                t.IFAT_DUST_ASBESTOS_CHECKNUM = #{${joiner}ifatDustAsbestosChecknum},
            </when>
            <otherwise>
                t.IFAT_DUST_ASBESTOS_CHECKNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatDustAsbestosExcessnum != null">
                t.IFAT_DUST_ASBESTOS_EXCESSNUM = #{${joiner}ifatDustAsbestosExcessnum},
            </when>
            <otherwise>
                t.IFAT_DUST_ASBESTOS_EXCESSNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatChemistry != null">
                t.IFAT_CHEMISTRY = #{${joiner}ifatChemistry},
            </when>
            <otherwise>
                t.IFAT_CHEMISTRY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatChemistryAllChecknum != null">
                t.IFAT_CHEMISTRY_ALL_CHECKNUM = #{${joiner}ifatChemistryAllChecknum},
            </when>
            <otherwise>
                t.IFAT_CHEMISTRY_ALL_CHECKNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatChemistryAllExcessnum != null">
                t.IFAT_CHEMISTRY_ALL_EXCESSNUM = #{${joiner}ifatChemistryAllExcessnum},
            </when>
            <otherwise>
                t.IFAT_CHEMISTRY_ALL_EXCESSNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatChemistryChecknum != null">
                t.IFAT_CHEMISTRY_CHECKNUM = #{${joiner}ifatChemistryChecknum},
            </when>
            <otherwise>
                t.IFAT_CHEMISTRY_CHECKNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatChemistryExcessnum != null">
                t.IFAT_CHEMISTRY_EXCESSNUM = #{${joiner}ifatChemistryExcessnum},
            </when>
            <otherwise>
                t.IFAT_CHEMISTRY_EXCESSNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatChemistryBenzeneChecknu != null">
                t.IFAT_CHEMISTRY_BENZENE_CHECKNU = #{${joiner}ifatChemistryBenzeneChecknu},
            </when>
            <otherwise>
                t.IFAT_CHEMISTRY_BENZENE_CHECKNU = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatChemistryBenzeneExcessn != null">
                t.IFAT_CHEMISTRY_BENZENE_EXCESSN = #{${joiner}ifatChemistryBenzeneExcessn},
            </when>
            <otherwise>
                t.IFAT_CHEMISTRY_BENZENE_EXCESSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatPhysics != null">
                t.IFAT_PHYSICS = #{${joiner}ifatPhysics},
            </when>
            <otherwise>
                t.IFAT_PHYSICS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatPhysicsAllChecknum != null">
                t.IFAT_PHYSICS_ALL_CHECKNUM = #{${joiner}ifatPhysicsAllChecknum},
            </when>
            <otherwise>
                t.IFAT_PHYSICS_ALL_CHECKNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatPhysicsAllExcessnum != null">
                t.IFAT_PHYSICS_ALL_EXCESSNUM = #{${joiner}ifatPhysicsAllExcessnum},
            </when>
            <otherwise>
                t.IFAT_PHYSICS_ALL_EXCESSNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatPhysicsChecknum != null">
                t.IFAT_PHYSICS_CHECKNUM = #{${joiner}ifatPhysicsChecknum},
            </when>
            <otherwise>
                t.IFAT_PHYSICS_CHECKNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatPhysicsExcessnum != null">
                t.IFAT_PHYSICS_EXCESSNUM = #{${joiner}ifatPhysicsExcessnum},
            </when>
            <otherwise>
                t.IFAT_PHYSICS_EXCESSNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatRadioactivity != null">
                t.IFAT_RADIOACTIVITY = #{${joiner}ifatRadioactivity},
            </when>
            <otherwise>
                t.IFAT_RADIOACTIVITY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatRadioactivityChecknum != null">
                t.IFAT_RADIOACTIVITY_CHECKNUM = #{${joiner}ifatRadioactivityChecknum},
            </when>
            <otherwise>
                t.IFAT_RADIOACTIVITY_CHECKNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatRadioactivityExcessnum != null">
                t.IFAT_RADIOACTIVITY_EXCESSNUM = #{${joiner}ifatRadioactivityExcessnum},
            </when>
            <otherwise>
                t.IFAT_RADIOACTIVITY_EXCESSNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatBiologyother != null">
                t.IFAT_BIOLOGYOTHER = #{${joiner}ifatBiologyother},
            </when>
            <otherwise>
                t.IFAT_BIOLOGYOTHER = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatBiologyotherChecknum != null">
                t.IFAT_BIOLOGYOTHER_CHECKNUM = #{${joiner}ifatBiologyotherChecknum},
            </when>
            <otherwise>
                t.IFAT_BIOLOGYOTHER_CHECKNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifatBiologyotherExcessnum != null">
                t.IFAT_BIOLOGYOTHER_EXCESSNUM = #{${joiner}ifatBiologyotherExcessnum},
            </when>
            <otherwise>
                t.IFAT_BIOLOGYOTHER_EXCESSNUM = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITHARMFACTORCHECK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITHARMFACTORCHECK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITHARMFACTORCHECK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdZyUnitharmfactorcheck">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_ZY_UNITHARMFACTORCHECK_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_ZY_UNITHARMFACTORCHECK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            IFAT,
            TEST_UNIT_NAMES,
            TEST_REPORT_NOS,
            IFAT_DUST,
            IFAT_DUST_ALL_CHECKNUM,
            IFAT_DUST_ALL_EXCESSNUM,
            IFAT_DUST_CHECKNUM,
            IFAT_DUST_EXCESSNUM,
            IFAT_DUST_COAL_CHECKNUM,
            IFAT_DUST_COAL_EXCESSNUM,
            IFAT_DUST_ASBESTOS_CHECKNUM,
            IFAT_DUST_ASBESTOS_EXCESSNUM,
            IFAT_CHEMISTRY,
            IFAT_CHEMISTRY_ALL_CHECKNUM,
            IFAT_CHEMISTRY_ALL_EXCESSNUM,
            IFAT_CHEMISTRY_CHECKNUM,
            IFAT_CHEMISTRY_EXCESSNUM,
            IFAT_CHEMISTRY_BENZENE_CHECKNU,
            IFAT_CHEMISTRY_BENZENE_EXCESSN,
            IFAT_PHYSICS,
            IFAT_PHYSICS_ALL_CHECKNUM,
            IFAT_PHYSICS_ALL_EXCESSNUM,
            IFAT_PHYSICS_CHECKNUM,
            IFAT_PHYSICS_EXCESSNUM,
            IFAT_RADIOACTIVITY,
            IFAT_RADIOACTIVITY_CHECKNUM,
            IFAT_RADIOACTIVITY_EXCESSNUM,
            IFAT_BIOLOGYOTHER,
            IFAT_BIOLOGYOTHER_CHECKNUM,
            IFAT_BIOLOGYOTHER_EXCESSNUM,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{fkByMainId.rid},
            #{ifat},
            #{testUnitNames},
            #{testReportNos},
            #{ifatDust},
            #{ifatDustAllChecknum},
            #{ifatDustAllExcessnum},
            #{ifatDustChecknum},
            #{ifatDustExcessnum},
            #{ifatDustCoalChecknum},
            #{ifatDustCoalExcessnum},
            #{ifatDustAsbestosChecknum},
            #{ifatDustAsbestosExcessnum},
            #{ifatChemistry},
            #{ifatChemistryAllChecknum},
            #{ifatChemistryAllExcessnum},
            #{ifatChemistryChecknum},
            #{ifatChemistryExcessnum},
            #{ifatChemistryBenzeneChecknu},
            #{ifatChemistryBenzeneExcessn},
            #{ifatPhysics},
            #{ifatPhysicsAllChecknum},
            #{ifatPhysicsAllExcessnum},
            #{ifatPhysicsChecknum},
            #{ifatPhysicsExcessnum},
            #{ifatRadioactivity},
            #{ifatRadioactivityChecknum},
            #{ifatRadioactivityExcessnum},
            #{ifatBiologyother},
            #{ifatBiologyotherChecknum},
            #{ifatBiologyotherExcessnum},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_ZY_UNITHARMFACTORCHECK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            IFAT,
            TEST_UNIT_NAMES,
            TEST_REPORT_NOS,
            IFAT_DUST,
            IFAT_DUST_ALL_CHECKNUM,
            IFAT_DUST_ALL_EXCESSNUM,
            IFAT_DUST_CHECKNUM,
            IFAT_DUST_EXCESSNUM,
            IFAT_DUST_COAL_CHECKNUM,
            IFAT_DUST_COAL_EXCESSNUM,
            IFAT_DUST_ASBESTOS_CHECKNUM,
            IFAT_DUST_ASBESTOS_EXCESSNUM,
            IFAT_CHEMISTRY,
            IFAT_CHEMISTRY_ALL_CHECKNUM,
            IFAT_CHEMISTRY_ALL_EXCESSNUM,
            IFAT_CHEMISTRY_CHECKNUM,
            IFAT_CHEMISTRY_EXCESSNUM,
            IFAT_CHEMISTRY_BENZENE_CHECKNU,
            IFAT_CHEMISTRY_BENZENE_EXCESSN,
            IFAT_PHYSICS,
            IFAT_PHYSICS_ALL_CHECKNUM,
            IFAT_PHYSICS_ALL_EXCESSNUM,
            IFAT_PHYSICS_CHECKNUM,
            IFAT_PHYSICS_EXCESSNUM,
            IFAT_RADIOACTIVITY,
            IFAT_RADIOACTIVITY_CHECKNUM,
            IFAT_RADIOACTIVITY_EXCESSNUM,
            IFAT_BIOLOGYOTHER,
            IFAT_BIOLOGYOTHER_CHECKNUM,
            IFAT_BIOLOGYOTHER_EXCESSNUM,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_ZY_UNITHARMFACTORCHECK_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByMainId != null and item.fkByMainId.rid != null">
                            #{item.fkByMainId.rid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifat != null">
                            #{item.ifat} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.testUnitNames != null">
                            #{item.testUnitNames} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.testReportNos != null">
                            #{item.testReportNos} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatDust != null">
                            #{item.ifatDust} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatDustAllChecknum != null">
                            #{item.ifatDustAllChecknum} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatDustAllExcessnum != null">
                            #{item.ifatDustAllExcessnum} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatDustChecknum != null">
                            #{item.ifatDustChecknum} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatDustExcessnum != null">
                            #{item.ifatDustExcessnum} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatDustCoalChecknum != null">
                            #{item.ifatDustCoalChecknum} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatDustCoalExcessnum != null">
                            #{item.ifatDustCoalExcessnum} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatDustAsbestosChecknum != null">
                            #{item.ifatDustAsbestosChecknum} AS C16,
                        </when>
                        <otherwise>
                            NULL AS C16,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatDustAsbestosExcessnum != null">
                            #{item.ifatDustAsbestosExcessnum} AS C17,
                        </when>
                        <otherwise>
                            NULL AS C17,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatChemistry != null">
                            #{item.ifatChemistry} AS C18,
                        </when>
                        <otherwise>
                            NULL AS C18,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatChemistryAllChecknum != null">
                            #{item.ifatChemistryAllChecknum} AS C19,
                        </when>
                        <otherwise>
                            NULL AS C19,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatChemistryAllExcessnum != null">
                            #{item.ifatChemistryAllExcessnum} AS C20,
                        </when>
                        <otherwise>
                            NULL AS C20,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatChemistryChecknum != null">
                            #{item.ifatChemistryChecknum} AS C21,
                        </when>
                        <otherwise>
                            NULL AS C21,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatChemistryExcessnum != null">
                            #{item.ifatChemistryExcessnum} AS C22,
                        </when>
                        <otherwise>
                            NULL AS C22,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatChemistryBenzeneChecknu != null">
                            #{item.ifatChemistryBenzeneChecknu} AS C23,
                        </when>
                        <otherwise>
                            NULL AS C23,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatChemistryBenzeneExcessn != null">
                            #{item.ifatChemistryBenzeneExcessn} AS C24,
                        </when>
                        <otherwise>
                            NULL AS C24,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatPhysics != null">
                            #{item.ifatPhysics} AS C25,
                        </when>
                        <otherwise>
                            NULL AS C25,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatPhysicsAllChecknum != null">
                            #{item.ifatPhysicsAllChecknum} AS C26,
                        </when>
                        <otherwise>
                            NULL AS C26,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatPhysicsAllExcessnum != null">
                            #{item.ifatPhysicsAllExcessnum} AS C27,
                        </when>
                        <otherwise>
                            NULL AS C27,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatPhysicsChecknum != null">
                            #{item.ifatPhysicsChecknum} AS C28,
                        </when>
                        <otherwise>
                            NULL AS C28,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatPhysicsExcessnum != null">
                            #{item.ifatPhysicsExcessnum} AS C29,
                        </when>
                        <otherwise>
                            NULL AS C29,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatRadioactivity != null">
                            #{item.ifatRadioactivity} AS C30,
                        </when>
                        <otherwise>
                            NULL AS C30,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatRadioactivityChecknum != null">
                            #{item.ifatRadioactivityChecknum} AS C31,
                        </when>
                        <otherwise>
                            NULL AS C31,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatRadioactivityExcessnum != null">
                            #{item.ifatRadioactivityExcessnum} AS C32,
                        </when>
                        <otherwise>
                            NULL AS C32,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatBiologyother != null">
                            #{item.ifatBiologyother} AS C33,
                        </when>
                        <otherwise>
                            NULL AS C33,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatBiologyotherChecknum != null">
                            #{item.ifatBiologyotherChecknum} AS C34,
                        </when>
                        <otherwise>
                            NULL AS C34,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifatBiologyotherExcessnum != null">
                            #{item.ifatBiologyotherExcessnum} AS C35,
                        </when>
                        <otherwise>
                            NULL AS C35,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZyUnitharmfactorcheck" >
        update TD_ZY_UNITHARMFACTORCHECK t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_ZY_UNITHARMFACTORCHECK t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZyUnitharmfactorcheck">
        delete from TD_ZY_UNITHARMFACTORCHECK
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

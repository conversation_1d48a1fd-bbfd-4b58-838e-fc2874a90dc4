<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZyUnitfactorcrowdMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZyUnitfactorcrowd">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="IFHF_DUST" property="ifhfDust" />
        <result column="HF_DUST_PEOPLES" property="hfDustPeoples" />
        <result column="HF_DUST_SILICON_PEOPLES" property="hfDustSiliconPeoples" />
        <result column="HF_DUST_COAL_PEOPLES" property="hfDustCoalPeoples" />
        <result column="HF_DUST_ASBESTOS_NUM" property="hfDustAsbestosNum" />
        <result column="IFHF_CHEMISTRY" property="ifhfChemistry" />
        <result column="HF_CHEMISTRY_PEOPLES" property="hfChemistryPeoples" />
        <result column="HF_CHEMISTRY_LEAD_PEOPLES" property="hfChemistryLeadPeoples" />
        <result column="HF_CHEMISTRY_BENZENE_PEOPLES" property="hfChemistryBenzenePeoples" />
        <result column="IFHF_PHYSICS" property="ifhfPhysics" />
        <result column="HF_PHYSICS_PEOPLES" property="hfPhysicsPeoples" />
        <result column="HF_PHYSICS_NOISE_PEOPLES" property="hfPhysicsNoisePeoples" />
        <result column="IFHF_RADIOACTIVITY" property="ifhfRadioactivity" />
        <result column="HF_RADIOACTIVITY_PEOPLES" property="hfRadioactivityPeoples" />
        <result column="IFHF_BIOLOGY" property="ifhfBiology" />
        <result column="HF_BIOLOGY_PEOPLES" property="hfBiologyPeoples" />
        <result column="IFHF_OTHER" property="ifhfOther" />
        <result column="HF_OTHER_PEOPLES" property="hfOtherPeoples" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MAIN_ID,t.IFHF_DUST,t.HF_DUST_PEOPLES,t.HF_DUST_SILICON_PEOPLES,t.HF_DUST_COAL_PEOPLES,t.HF_DUST_ASBESTOS_NUM,t.IFHF_CHEMISTRY,t.HF_CHEMISTRY_PEOPLES,t.HF_CHEMISTRY_LEAD_PEOPLES,t.HF_CHEMISTRY_BENZENE_PEOPLES,t.IFHF_PHYSICS,t.HF_PHYSICS_PEOPLES,t.HF_PHYSICS_NOISE_PEOPLES,t.IFHF_RADIOACTIVITY,t.HF_RADIOACTIVITY_PEOPLES,t.IFHF_BIOLOGY,t.HF_BIOLOGY_PEOPLES,t.IFHF_OTHER,t.HF_OTHER_PEOPLES,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}ifhfDust != null">
            and ${mAlias}IFHF_DUST = #{${joiner}ifhfDust}
        </if>
        <if test="${joiner}hfDustPeoples != null">
            and ${mAlias}HF_DUST_PEOPLES = #{${joiner}hfDustPeoples}
        </if>
        <if test="${joiner}hfDustSiliconPeoples != null">
            and ${mAlias}HF_DUST_SILICON_PEOPLES = #{${joiner}hfDustSiliconPeoples}
        </if>
        <if test="${joiner}hfDustCoalPeoples != null">
            and ${mAlias}HF_DUST_COAL_PEOPLES = #{${joiner}hfDustCoalPeoples}
        </if>
        <if test="${joiner}hfDustAsbestosNum != null">
            and ${mAlias}HF_DUST_ASBESTOS_NUM = #{${joiner}hfDustAsbestosNum}
        </if>
        <if test="${joiner}ifhfChemistry != null">
            and ${mAlias}IFHF_CHEMISTRY = #{${joiner}ifhfChemistry}
        </if>
        <if test="${joiner}hfChemistryPeoples != null">
            and ${mAlias}HF_CHEMISTRY_PEOPLES = #{${joiner}hfChemistryPeoples}
        </if>
        <if test="${joiner}hfChemistryLeadPeoples != null">
            and ${mAlias}HF_CHEMISTRY_LEAD_PEOPLES = #{${joiner}hfChemistryLeadPeoples}
        </if>
        <if test="${joiner}hfChemistryBenzenePeoples != null">
            and ${mAlias}HF_CHEMISTRY_BENZENE_PEOPLES = #{${joiner}hfChemistryBenzenePeoples}
        </if>
        <if test="${joiner}ifhfPhysics != null">
            and ${mAlias}IFHF_PHYSICS = #{${joiner}ifhfPhysics}
        </if>
        <if test="${joiner}hfPhysicsPeoples != null">
            and ${mAlias}HF_PHYSICS_PEOPLES = #{${joiner}hfPhysicsPeoples}
        </if>
        <if test="${joiner}hfPhysicsNoisePeoples != null">
            and ${mAlias}HF_PHYSICS_NOISE_PEOPLES = #{${joiner}hfPhysicsNoisePeoples}
        </if>
        <if test="${joiner}ifhfRadioactivity != null">
            and ${mAlias}IFHF_RADIOACTIVITY = #{${joiner}ifhfRadioactivity}
        </if>
        <if test="${joiner}hfRadioactivityPeoples != null">
            and ${mAlias}HF_RADIOACTIVITY_PEOPLES = #{${joiner}hfRadioactivityPeoples}
        </if>
        <if test="${joiner}ifhfBiology != null">
            and ${mAlias}IFHF_BIOLOGY = #{${joiner}ifhfBiology}
        </if>
        <if test="${joiner}hfBiologyPeoples != null">
            and ${mAlias}HF_BIOLOGY_PEOPLES = #{${joiner}hfBiologyPeoples}
        </if>
        <if test="${joiner}ifhfOther != null">
            and ${mAlias}IFHF_OTHER = #{${joiner}ifhfOther}
        </if>
        <if test="${joiner}hfOtherPeoples != null">
            and ${mAlias}HF_OTHER_PEOPLES = #{${joiner}hfOtherPeoples}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifhfDust != null">
                t.IFHF_DUST = #{${joiner}ifhfDust},
            </when>
            <otherwise>
                t.IFHF_DUST = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hfDustPeoples != null">
                t.HF_DUST_PEOPLES = #{${joiner}hfDustPeoples},
            </when>
            <otherwise>
                t.HF_DUST_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hfDustSiliconPeoples != null">
                t.HF_DUST_SILICON_PEOPLES = #{${joiner}hfDustSiliconPeoples},
            </when>
            <otherwise>
                t.HF_DUST_SILICON_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hfDustCoalPeoples != null">
                t.HF_DUST_COAL_PEOPLES = #{${joiner}hfDustCoalPeoples},
            </when>
            <otherwise>
                t.HF_DUST_COAL_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hfDustAsbestosNum != null">
                t.HF_DUST_ASBESTOS_NUM = #{${joiner}hfDustAsbestosNum},
            </when>
            <otherwise>
                t.HF_DUST_ASBESTOS_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifhfChemistry != null">
                t.IFHF_CHEMISTRY = #{${joiner}ifhfChemistry},
            </when>
            <otherwise>
                t.IFHF_CHEMISTRY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hfChemistryPeoples != null">
                t.HF_CHEMISTRY_PEOPLES = #{${joiner}hfChemistryPeoples},
            </when>
            <otherwise>
                t.HF_CHEMISTRY_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hfChemistryLeadPeoples != null">
                t.HF_CHEMISTRY_LEAD_PEOPLES = #{${joiner}hfChemistryLeadPeoples},
            </when>
            <otherwise>
                t.HF_CHEMISTRY_LEAD_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hfChemistryBenzenePeoples != null">
                t.HF_CHEMISTRY_BENZENE_PEOPLES = #{${joiner}hfChemistryBenzenePeoples},
            </when>
            <otherwise>
                t.HF_CHEMISTRY_BENZENE_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifhfPhysics != null">
                t.IFHF_PHYSICS = #{${joiner}ifhfPhysics},
            </when>
            <otherwise>
                t.IFHF_PHYSICS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hfPhysicsPeoples != null">
                t.HF_PHYSICS_PEOPLES = #{${joiner}hfPhysicsPeoples},
            </when>
            <otherwise>
                t.HF_PHYSICS_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hfPhysicsNoisePeoples != null">
                t.HF_PHYSICS_NOISE_PEOPLES = #{${joiner}hfPhysicsNoisePeoples},
            </when>
            <otherwise>
                t.HF_PHYSICS_NOISE_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifhfRadioactivity != null">
                t.IFHF_RADIOACTIVITY = #{${joiner}ifhfRadioactivity},
            </when>
            <otherwise>
                t.IFHF_RADIOACTIVITY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hfRadioactivityPeoples != null">
                t.HF_RADIOACTIVITY_PEOPLES = #{${joiner}hfRadioactivityPeoples},
            </when>
            <otherwise>
                t.HF_RADIOACTIVITY_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifhfBiology != null">
                t.IFHF_BIOLOGY = #{${joiner}ifhfBiology},
            </when>
            <otherwise>
                t.IFHF_BIOLOGY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hfBiologyPeoples != null">
                t.HF_BIOLOGY_PEOPLES = #{${joiner}hfBiologyPeoples},
            </when>
            <otherwise>
                t.HF_BIOLOGY_PEOPLES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifhfOther != null">
                t.IFHF_OTHER = #{${joiner}ifhfOther},
            </when>
            <otherwise>
                t.IFHF_OTHER = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hfOtherPeoples != null">
                t.HF_OTHER_PEOPLES = #{${joiner}hfOtherPeoples},
            </when>
            <otherwise>
                t.HF_OTHER_PEOPLES = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITFACTORCROWD t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITFACTORCROWD t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITFACTORCROWD t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdZyUnitfactorcrowd">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_ZY_UNITFACTORCROWD_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_ZY_UNITFACTORCROWD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            IFHF_DUST,
            HF_DUST_PEOPLES,
            HF_DUST_SILICON_PEOPLES,
            HF_DUST_COAL_PEOPLES,
            HF_DUST_ASBESTOS_NUM,
            IFHF_CHEMISTRY,
            HF_CHEMISTRY_PEOPLES,
            HF_CHEMISTRY_LEAD_PEOPLES,
            HF_CHEMISTRY_BENZENE_PEOPLES,
            IFHF_PHYSICS,
            HF_PHYSICS_PEOPLES,
            HF_PHYSICS_NOISE_PEOPLES,
            IFHF_RADIOACTIVITY,
            HF_RADIOACTIVITY_PEOPLES,
            IFHF_BIOLOGY,
            HF_BIOLOGY_PEOPLES,
            IFHF_OTHER,
            HF_OTHER_PEOPLES,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{fkByMainId.rid},
            #{ifhfDust},
            #{hfDustPeoples},
            #{hfDustSiliconPeoples},
            #{hfDustCoalPeoples},
            #{hfDustAsbestosNum},
            #{ifhfChemistry},
            #{hfChemistryPeoples},
            #{hfChemistryLeadPeoples},
            #{hfChemistryBenzenePeoples},
            #{ifhfPhysics},
            #{hfPhysicsPeoples},
            #{hfPhysicsNoisePeoples},
            #{ifhfRadioactivity},
            #{hfRadioactivityPeoples},
            #{ifhfBiology},
            #{hfBiologyPeoples},
            #{ifhfOther},
            #{hfOtherPeoples},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_ZY_UNITFACTORCROWD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            IFHF_DUST,
            HF_DUST_PEOPLES,
            HF_DUST_SILICON_PEOPLES,
            HF_DUST_COAL_PEOPLES,
            HF_DUST_ASBESTOS_NUM,
            IFHF_CHEMISTRY,
            HF_CHEMISTRY_PEOPLES,
            HF_CHEMISTRY_LEAD_PEOPLES,
            HF_CHEMISTRY_BENZENE_PEOPLES,
            IFHF_PHYSICS,
            HF_PHYSICS_PEOPLES,
            HF_PHYSICS_NOISE_PEOPLES,
            IFHF_RADIOACTIVITY,
            HF_RADIOACTIVITY_PEOPLES,
            IFHF_BIOLOGY,
            HF_BIOLOGY_PEOPLES,
            IFHF_OTHER,
            HF_OTHER_PEOPLES,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_ZY_UNITFACTORCROWD_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByMainId != null and item.fkByMainId.rid != null">
                            #{item.fkByMainId.rid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifhfDust != null">
                            #{item.ifhfDust} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hfDustPeoples != null">
                            #{item.hfDustPeoples} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hfDustSiliconPeoples != null">
                            #{item.hfDustSiliconPeoples} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hfDustCoalPeoples != null">
                            #{item.hfDustCoalPeoples} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hfDustAsbestosNum != null">
                            #{item.hfDustAsbestosNum} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifhfChemistry != null">
                            #{item.ifhfChemistry} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hfChemistryPeoples != null">
                            #{item.hfChemistryPeoples} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hfChemistryLeadPeoples != null">
                            #{item.hfChemistryLeadPeoples} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hfChemistryBenzenePeoples != null">
                            #{item.hfChemistryBenzenePeoples} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifhfPhysics != null">
                            #{item.ifhfPhysics} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hfPhysicsPeoples != null">
                            #{item.hfPhysicsPeoples} AS C16,
                        </when>
                        <otherwise>
                            NULL AS C16,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hfPhysicsNoisePeoples != null">
                            #{item.hfPhysicsNoisePeoples} AS C17,
                        </when>
                        <otherwise>
                            NULL AS C17,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifhfRadioactivity != null">
                            #{item.ifhfRadioactivity} AS C18,
                        </when>
                        <otherwise>
                            NULL AS C18,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hfRadioactivityPeoples != null">
                            #{item.hfRadioactivityPeoples} AS C19,
                        </when>
                        <otherwise>
                            NULL AS C19,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifhfBiology != null">
                            #{item.ifhfBiology} AS C20,
                        </when>
                        <otherwise>
                            NULL AS C20,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hfBiologyPeoples != null">
                            #{item.hfBiologyPeoples} AS C21,
                        </when>
                        <otherwise>
                            NULL AS C21,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifhfOther != null">
                            #{item.ifhfOther} AS C22,
                        </when>
                        <otherwise>
                            NULL AS C22,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hfOtherPeoples != null">
                            #{item.hfOtherPeoples} AS C23,
                        </when>
                        <otherwise>
                            NULL AS C23,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZyUnitfactorcrowd" >
        update TD_ZY_UNITFACTORCROWD t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_ZY_UNITFACTORCROWD t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZyUnitfactorcrowd">
        delete from TD_ZY_UNITFACTORCROWD
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.CrptMergeMapper">

    <select id="findTdTjCrptTaskVoListBySize" parameterType="java.lang.Integer"
            resultType="com.chis.modules.timer.heth.logic.vo.TdTjCrptTaskVo">
        WITH TMP_CRPT_TASK AS(SELECT * FROM TD_TJ_CRPT_TASK WHERE STATE = 0 ORDER BY RID)
        SELECT
            t.RID             AS  "rid",
            t.NEWEST_CRPT_ID  AS  "newestCrptId",
            t.CRPT_NAME       AS  "crptName",
            t.ZONE_ID         AS  "zoneId",
            t.CREDIT_CODE     AS  "creditCode",
            t.ADDRESS         AS  "address",
            t.ENROL_ADDRESS   AS  "enrolAddress",
            t.INDUS_TYPE_ID   AS  "indusTypeId",
            t.ECONOMY_ID      AS  "economyId",
            t.CRPT_SIZE_ID    AS  "crptSizeId",
            t.LINK_MAN        AS  "linkMan",
            t.LINK_PHONE      AS  "linkPhone",
            t.OPER_PSN_ID     AS  "opePsnId"
        FROM TMP_CRPT_TASK t
        WHERE ROWNUM &lt;= #{dataSize}
    </select>

    <select id="findTdTjCrptTaskSubVoListByMainIdList" parameterType="java.util.List"
            resultType="com.chis.modules.timer.heth.logic.vo.TdTjCrptTaskSubVo">
        SELECT
            t.MAIN_ID         AS "mainId",
            t.STOP_CRPT_ID    AS "stopCrptId"
        FROM TD_TJ_CRPT_TASK_SUB t
        WHERE 1=1
        <if test="mainRidList != null and mainRidList.size > 0">
            AND t.MAIN_ID IN
            <foreach collection="mainRidList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>

    <update id="updateCrptTask" >
        UPDATE TD_TJ_CRPT_TASK t
        <set>
            t.STATE = #{state},t.MODIFY_DATE = SYSDATE,t.MODIFY_MANID = t.CREATE_MANID,
            <choose>
                <when test="errMsg != null and errMsg != ''">
                    t.ERROR_MSG = #{errMsg},
                </when>
                <otherwise>
                    t.ERROR_MSG = null,
                </otherwise>
            </choose>
        </set>
        WHERE t.rid = #{rid}
    </update>

    <update id="updateDynamicBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update ${item.tableName}
            <set>
                <foreach collection="item.columnPOList" item="subItm" index="index" open="" close="" separator=",">
                    <choose>
                        <when test="subItm.valColumn != null and subItm.valColumn != ''">
                            ${subItm.columnName} = ${subItm.valColumn}
                        </when>
                        <otherwise>
                            ${subItm.columnName} = #{subItm.columnVal}
                        </otherwise>
                    </choose>
                </foreach>
            </set>
            where
            <foreach collection="item.paramList" item="paramItm" index="index" open="" close="" separator=" AND ">
                <choose>
                    <when test="paramItm.columnVal != null and paramItm.columnVal != ''">
                        ${paramItm.columnName} = #{paramItm.columnVal}
                    </when>
                    <otherwise>
                        ${paramItm.columnName} is null
                    </otherwise>
                </choose>
            </foreach>
        </foreach>
    </update>

    <select id="checkTableIfExist" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM USER_TABLES WHERE TABLE_NAME = #{tableName}
    </select>

    <select id="findSubCrptNameByFatherIdList" resultType="java.util.HashMap"  >
        SELECT
               t.RID AS "rid",
               t.CRPT_NAME AS "crptName"
        FROM TB_TJ_CRPT t WHERE 1=1
        <if test="mainRidList != null and mainRidList.size > 0">
            AND t.UPPER_UNIT_ID IN
            <foreach collection="mainRidList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>

    <select id="findRidListByCrptNameAndInstitutionCode" resultType="java.lang.Integer">
        SELECT t.RID FROM TB_TJ_CRPT t WHERE t.INSTITUTION_CODE = #{institutionCode} AND t.CRPT_NAME = #{crptName}
    </select>

    <select id="findMainCrptRidListByInstitutionCode" resultType="java.lang.Integer" >
        SELECT t.RID FROM TB_TJ_CRPT t WHERE t.INSTITUTION_CODE = #{institutionCode} AND t.IF_SUB_ORG = 0
    </select>

    <select id="checkIfExistBgkLastSta" parameterType="java.lang.Integer" resultType="java.lang.Integer" >
        SELECT COUNT(1) FROM TD_ZW_BGK_LAST_STA t WHERE t.CART_TYPE=10 AND t.BUS_ID=#{rid}
    </select>

    <insert id="insertBgkLastSta" >
        insert into TD_ZW_BGK_LAST_STA
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CART_TYPE,
            BUS_ID,
            CREATE_DATE,
            CREATE_MANID,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            TD_ZW_BGK_LAST_STA_SEQ.Nextval,
               10,
            #{busId},
            SYSDATE,
            #{createManid},
        </trim>
    </insert>

    <update id="updateStopCrptInfo" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TB_TJ_CRPT
            set INSTITUTION_CODE = INSTITUTION_CODE||'-'||TO_CHAR(SYSDATE,'YYYYMMDDHH24MISS')||'XD',DEL_MARK=1,UPDATE_STOP=1,STOP_RSN='修订后停用',UPDATE_STATE=2,INTER_PRC_TAG=0
            where
            rid=#{item}
        </foreach>
    </update>

    <select id="findDistinctCrptBadRsnVoList" parameterType="java.util.List"
            resultType="com.chis.modules.timer.heth.logic.vo.CrptBadRsnVo">
        SELECT DISTINCT
             t.SOURCE_ID AS "sourceId",
             t.BADRSN_ID AS "badRsnId"
        FROM TB_TJ_CRPT_BADRSN t
        WHERE 1=1
        <if test="crptRidList != null and crptRidList.size > 0">
            AND t.CRPT_ID IN
            <foreach collection="crptRidList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>

    <insert id="insertCrptBadRsnBatch" parameterType="java.util.List">
        insert into TB_TJ_CRPT_BADRSN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            CRPT_ID,
            SOURCE_ID,
            BADRSN_ID,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TB_TJ_CRPT_BADRSN_SEQ.Nextval, A.* FROM ("
                 close=") A">
            SELECT
            SYSDATE AS C1,
            #{createManid} AS C2,
            #{crptId} AS C3,
            #{item.sourceId} AS C4,
            #{item.badRsnId} AS C5
            FROM DUAL
        </foreach>
    </insert>
</mapper>
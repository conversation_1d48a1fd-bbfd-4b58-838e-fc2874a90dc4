<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwyjBsnCsionItemsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwyjBsnCsionItems">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="ITEM_ID" property="fkByItemId.rid" />
        <result column="SEX" property="sex" />
        <result column="JDGPTN" property="jdgptn" />
        <result column="MSRUNT_ID" property="fkByMsruntId.rid" />
        <result column="GE" property="ge" />
        <result column="GT" property="gt" />
        <result column="LE" property="le" />
        <result column="LT" property="lt" />
        <result column="HG_FLAG" property="hgFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MAIN_ID,t.ITEM_ID,t.SEX,t.JDGPTN,t.MSRUNT_ID,t.GE,t.GT,t.LE,t.LT,t.HG_FLAG,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}fkByItemId != null and ${joiner}fkByItemId.rid != null">
            and ${mAlias}ITEM_ID = #{${joiner}fkByItemId.rid}
        </if>
        <if test="${joiner}sex != null and ${joiner}sex != ''">
            and ${mAlias}SEX = #{${joiner}sex}
        </if>
        <if test="${joiner}jdgptn != null and ${joiner}jdgptn != ''">
            and ${mAlias}JDGPTN = #{${joiner}jdgptn}
        </if>
        <if test="${joiner}fkByMsruntId != null and ${joiner}fkByMsruntId.rid != null">
            and ${mAlias}MSRUNT_ID = #{${joiner}fkByMsruntId.rid}
        </if>
        <if test="${joiner}hgFlag != null and ${joiner}hgFlag != ''">
            and ${mAlias}HG_FLAG = #{${joiner}hgFlag}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByItemId != null and ${joiner}fkByItemId.rid != null">
                t.ITEM_ID = #{${joiner}fkByItemId.rid},
            </when>
            <otherwise>
                t.ITEM_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}sex != null and ${joiner}sex != ''">
                t.SEX = #{${joiner}sex},
            </when>
            <otherwise>
                t.SEX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}jdgptn != null and ${joiner}jdgptn != ''">
                t.JDGPTN = #{${joiner}jdgptn},
            </when>
            <otherwise>
                t.JDGPTN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMsruntId != null and ${joiner}fkByMsruntId.rid != null">
                t.MSRUNT_ID = #{${joiner}fkByMsruntId.rid},
            </when>
            <otherwise>
                t.MSRUNT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ge != null and ${joiner}ge != ''">
                t.GE = #{${joiner}ge},
            </when>
            <otherwise>
                t.GE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}gt != null and ${joiner}gt != ''">
                t.GT = #{${joiner}gt},
            </when>
            <otherwise>
                t.GT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}le != null and ${joiner}le != ''">
                t.LE = #{${joiner}le},
            </when>
            <otherwise>
                t.LE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lt != null and ${joiner}lt != ''">
                t.LT = #{${joiner}lt},
            </when>
            <otherwise>
                t.LT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hgFlag != null and ${joiner}hgFlag != ''">
                t.HG_FLAG = #{${joiner}hgFlag},
            </when>
            <otherwise>
                t.HG_FLAG = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_BSN_CSION_ITEMS t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_BSN_CSION_ITEMS t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultType="TdZwyjBsnCsionItems">
        select
        <trim suffixOverrides=",">
            t.RID AS "rid",
            t.MAIN_ID AS "fkByMainId.rid",
            t.ITEM_ID AS "fkByItemId.rid",
            t.SEX AS "sex",
            t.JDGPTN AS "jdgptn",
            t.MSRUNT_ID AS "fkByMsruntId.rid",
            t.GE AS "ge",
            t.GT AS "gt",
            t.LE AS "le",
            t.LT AS "lt",
            t.HG_FLAG AS "hgFlag",
        </trim>
        from  TD_ZWYJ_BSN_CSION_ITEMS t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwyjBsnCsionItems" >
        update TD_ZWYJ_BSN_CSION_ITEMS t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZWYJ_BSN_CSION_ITEMS t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZwyjBsnCsionItems">
        delete from TD_ZWYJ_BSN_CSION_ITEMS
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

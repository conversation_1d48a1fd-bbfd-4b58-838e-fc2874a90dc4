<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdPreviousDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdPreviousDetail">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="FACTOR_ID" property="fkByFactorId.rid" />
        <result column="DETECTION_STATION" property="detectionStation" />
        <result column="OVERWEIGHT_STATION" property="overweightStation" />
        <result column="DETECTION_POINT" property="detectionPoint" />
        <result column="OVERWEIGHT_POINT" property="overweightPoint" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MAIN_ID,t.FACTOR_ID,t.DETECTION_STATION,t.OVERWEIGHT_STATION,t.DETECTION_POINT,t.OVERWEIGHT_POINT,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}fkByFactorId != null and ${joiner}fkByFactorId.rid != null">
            and ${mAlias}FACTOR_ID = #{${joiner}fkByFactorId.rid}
        </if>
        <if test="${joiner}detectionStation != null">
            and ${mAlias}DETECTION_STATION = #{${joiner}detectionStation}
        </if>
        <if test="${joiner}overweightStation != null">
            and ${mAlias}OVERWEIGHT_STATION = #{${joiner}overweightStation}
        </if>
        <if test="${joiner}detectionPoint != null">
            and ${mAlias}DETECTION_POINT = #{${joiner}detectionPoint}
        </if>
        <if test="${joiner}overweightPoint != null">
            and ${mAlias}OVERWEIGHT_POINT = #{${joiner}overweightPoint}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByFactorId != null and ${joiner}fkByFactorId.rid != null">
                t.FACTOR_ID = #{${joiner}fkByFactorId.rid},
            </when>
            <otherwise>
                t.FACTOR_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}detectionStation != null">
                t.DETECTION_STATION = #{${joiner}detectionStation},
            </when>
            <otherwise>
                t.DETECTION_STATION = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}overweightStation != null">
                t.OVERWEIGHT_STATION = #{${joiner}overweightStation},
            </when>
            <otherwise>
                t.OVERWEIGHT_STATION = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}detectionPoint != null">
                t.DETECTION_POINT = #{${joiner}detectionPoint},
            </when>
            <otherwise>
                t.DETECTION_POINT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}overweightPoint != null">
                t.OVERWEIGHT_POINT = #{${joiner}overweightPoint},
            </when>
            <otherwise>
                t.OVERWEIGHT_POINT = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_PREVIOUS_DETAIL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_PREVIOUS_DETAIL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_PREVIOUS_DETAIL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdPreviousDetail">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_PREVIOUS_DETAIL_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_PREVIOUS_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            FACTOR_ID,
            DETECTION_STATION,
            OVERWEIGHT_STATION,
            DETECTION_POINT,
            OVERWEIGHT_POINT,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{fkByMainId.rid},
            #{fkByFactorId.rid},
            #{detectionStation},
            #{overweightStation},
            #{detectionPoint},
            #{overweightPoint},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_PREVIOUS_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            FACTOR_ID,
            DETECTION_STATION,
            OVERWEIGHT_STATION,
            DETECTION_POINT,
            OVERWEIGHT_POINT,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_PREVIOUS_DETAIL_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByMainId != null and item.fkByMainId.rid != null">
                            #{item.fkByMainId.rid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByFactorId != null and item.fkByFactorId.rid != null">
                            #{item.fkByFactorId.rid} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.detectionStation != null">
                            #{item.detectionStation} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.overweightStation != null">
                            #{item.overweightStation} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.detectionPoint != null">
                            #{item.detectionPoint} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.overweightPoint != null">
                            #{item.overweightPoint} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdPreviousDetail" >
        update TD_PREVIOUS_DETAIL t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_PREVIOUS_DETAIL t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 注意 joiner 需要加入 -->
    <delete id="removeByEntity" parameterType="TdPreviousDetail">
        delete from TD_PREVIOUS_DETAIL
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

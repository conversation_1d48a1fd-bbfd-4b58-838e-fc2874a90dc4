<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwBadrsnStdMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwBadrsnStd">
        <result column="RID" property="rid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="BADRSN_ID" property="fkByBadrsnId.rid" />
        <result column="STOP_TAG" property="stopTag" />
        <result column="ITEM_DESC" property="itemDesc" />
        <result column="DETER_WAY" property="deterWay" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.MODIFY_DATE,t.MODIFY_MANID,t.CREATE_DATE,t.CREATE_MANID,
        t.BADRSN_ID,t.STOP_TAG,t.ITEM_DESC,t.DETER_WAY,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}fkByBadrsnId != null and ${joiner}fkByBadrsnId.rid != null">
            and ${mAlias}BADRSN_ID = #{${joiner}fkByBadrsnId.rid}
        </if>
        <if test="${joiner}stopTag != null and ${joiner}stopTag != ''">
            and ${mAlias}STOP_TAG = #{${joiner}stopTag}
        </if>
        <if test="${joiner}itemDesc != null and ${joiner}itemDesc != ''">
            and ${mAlias}ITEM_DESC = #{${joiner}itemDesc}
        </if>
        <if test="${joiner}deterWay != null and ${joiner}deterWay != ''">
            and ${mAlias}DETER_WAY = #{${joiner}deterWay}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByBadrsnId != null and ${joiner}fkByBadrsnId.rid != null">
                t.BADRSN_ID = #{${joiner}fkByBadrsnId.rid},
            </when>
            <otherwise>
                t.BADRSN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}stopTag != null and ${joiner}stopTag != ''">
                t.STOP_TAG = #{${joiner}stopTag},
            </when>
            <otherwise>
                t.STOP_TAG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}itemDesc != null and ${joiner}itemDesc != ''">
                t.ITEM_DESC = #{${joiner}itemDesc},
            </when>
            <otherwise>
                t.ITEM_DESC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}deterWay != null and ${joiner}deterWay != ''">
                t.DETER_WAY = #{${joiner}deterWay},
            </when>
            <otherwise>
                t.DETER_WAY = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZW_BADRSN_STD t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZW_BADRSN_STD t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZW_BADRSN_STD t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwBadrsnStd" >
        update TD_ZW_BADRSN_STD t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZW_BADRSN_STD t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZwBadrsnStd">
        delete from TD_ZW_BADRSN_STD
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

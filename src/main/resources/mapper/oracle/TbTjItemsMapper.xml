<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TbTjItemsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TbTjItems">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="NUM" property="num" />
        <result column="ITEM_CODE" property="itemCode" />
        <result column="ITEM_NAME" property="itemName" />
        <result column="PYXNAM" property="pyxnam" />
        <result column="MSRUNT" property="msrunt" />
        <result column="ITEM_SORTID" property="fkByItemSortid.rid" />
        <result column="JDGPTN" property="jdgptn" />
        <result column="MINVAL" property="minval" />
        <result column="MAXVAL" property="maxval" />
        <result column="ITEM_STDVALUE" property="itemStdvalue" />
        <result column="DFLT" property="dflt" />
        <result column="STOP_TAG" property="stopTag" />
        <result column="SEX" property="sex" />
        <result column="ITEM_TAG" property="itemTag" />
        <result column="MSRUNT_ID" property="msruntId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.NUM,t.ITEM_CODE,t.ITEM_NAME,t.PYXNAM,t.MSRUNT,t.ITEM_SORTID,t.JDGPTN,t.MINVAL,t.MAXVAL,t.ITEM_STDVALUE,t.DFLT,t.STOP_TAG,t.SEX,t.ITEM_TAG,t.MSRUNT_ID,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}num != null and ${joiner}num != ''">
            and ${mAlias}NUM = #{${joiner}num}
        </if>
        <if test="${joiner}itemCode != null and ${joiner}itemCode != ''">
            and ${mAlias}ITEM_CODE = #{${joiner}itemCode}
        </if>
        <if test="${joiner}itemName != null and ${joiner}itemName != ''">
            and ${mAlias}ITEM_NAME = #{${joiner}itemName}
        </if>
        <if test="${joiner}pyxnam != null and ${joiner}pyxnam != ''">
            and ${mAlias}PYXNAM = #{${joiner}pyxnam}
        </if>
        <if test="${joiner}msrunt != null and ${joiner}msrunt != ''">
            and ${mAlias}MSRUNT = #{${joiner}msrunt}
        </if>
        <if test="${joiner}fkByItemSortid != null and ${joiner}fkByItemSortid.rid != null">
            and ${mAlias}ITEM_SORTID = #{${joiner}fkByItemSortid.rid}
        </if>
        <if test="${joiner}jdgptn != null and ${joiner}jdgptn != ''">
            and ${mAlias}JDGPTN = #{${joiner}jdgptn}
        </if>
        <if test="${joiner}minval != null and ${joiner}minval != ''">
            and ${mAlias}MINVAL = #{${joiner}minval}
        </if>
        <if test="${joiner}maxval != null and ${joiner}maxval != ''">
            and ${mAlias}MAXVAL = #{${joiner}maxval}
        </if>
        <if test="${joiner}itemStdvalue != null and ${joiner}itemStdvalue != ''">
            and ${mAlias}ITEM_STDVALUE = #{${joiner}itemStdvalue}
        </if>
        <if test="${joiner}dflt != null and ${joiner}dflt != ''">
            and ${mAlias}DFLT = #{${joiner}dflt}
        </if>
        <if test="${joiner}stopTag != null and ${joiner}stopTag != ''">
            and ${mAlias}STOP_TAG = #{${joiner}stopTag}
        </if>
        <if test="${joiner}sex != null and ${joiner}sex != ''">
            and ${mAlias}SEX = #{${joiner}sex}
        </if>
        <if test="${joiner}itemTag != null and ${joiner}itemTag != ''">
            and ${mAlias}ITEM_TAG = #{${joiner}itemTag}
        </if>
        <if test="${joiner}msruntId != null and ${joiner}msruntId != ''">
            and ${mAlias}MSRUNT_ID = #{${joiner}msruntId}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}num != null and ${joiner}num != ''">
                t.NUM = #{${joiner}num},
            </when>
            <otherwise>
                t.NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}itemCode != null and ${joiner}itemCode != ''">
                t.ITEM_CODE = #{${joiner}itemCode},
            </when>
            <otherwise>
                t.ITEM_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}itemName != null and ${joiner}itemName != ''">
                t.ITEM_NAME = #{${joiner}itemName},
            </when>
            <otherwise>
                t.ITEM_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}pyxnam != null and ${joiner}pyxnam != ''">
                t.PYXNAM = #{${joiner}pyxnam},
            </when>
            <otherwise>
                t.PYXNAM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}msrunt != null and ${joiner}msrunt != ''">
                t.MSRUNT = #{${joiner}msrunt},
            </when>
            <otherwise>
                t.MSRUNT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByItemSortid != null and ${joiner}fkByItemSortid.rid != null">
                t.ITEM_SORTID = #{${joiner}fkByItemSortid.rid},
            </when>
            <otherwise>
                t.ITEM_SORTID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}jdgptn != null and ${joiner}jdgptn != ''">
                t.JDGPTN = #{${joiner}jdgptn},
            </when>
            <otherwise>
                t.JDGPTN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}minval != null and ${joiner}minval != ''">
                t.MINVAL = #{${joiner}minval},
            </when>
            <otherwise>
                t.MINVAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}maxval != null and ${joiner}maxval != ''">
                t.MAXVAL = #{${joiner}maxval},
            </when>
            <otherwise>
                t.MAXVAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}itemStdvalue != null and ${joiner}itemStdvalue != ''">
                t.ITEM_STDVALUE = #{${joiner}itemStdvalue},
            </when>
            <otherwise>
                t.ITEM_STDVALUE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dflt != null and ${joiner}dflt != ''">
                t.DFLT = #{${joiner}dflt},
            </when>
            <otherwise>
                t.DFLT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}stopTag != null and ${joiner}stopTag != ''">
                t.STOP_TAG = #{${joiner}stopTag},
            </when>
            <otherwise>
                t.STOP_TAG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}sex != null and ${joiner}sex != ''">
                t.SEX = #{${joiner}sex},
            </when>
            <otherwise>
                t.SEX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}itemTag != null and ${joiner}itemTag != ''">
                t.ITEM_TAG = #{${joiner}itemTag},
            </when>
            <otherwise>
                t.ITEM_TAG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}msruntId != null and ${joiner}msruntId != ''">
                t.MSRUNT_ID = #{${joiner}msruntId},
            </when>
            <otherwise>
                t.MSRUNT_ID = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_ITEMS t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_ITEMS t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_ITEMS t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TbTjItems" >
        update TB_TJ_ITEMS t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TB_TJ_ITEMS t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TbTjItems">
        delete from TB_TJ_ITEMS
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <select id="findTjItemListByState" resultMap="BaseResultMap">
        SELECT
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        FROM TB_TJ_ITEMS T
        <where>
            AND T.STOP_TAG = #{state}
        </where>
    </select>



</mapper>

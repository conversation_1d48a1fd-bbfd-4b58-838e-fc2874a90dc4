<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwyjBsnBadrsnsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwyjBsnBadrsns">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="BHK_ID" property="fkByBhkId.rid" />
        <result column="BADRSN_ID" property="fkByBadrsnId.rid" />
        <result column="EXAM_CONCLUSION_ID" property="fkByExamConclusionId.rid" />
        <result column="QTJB_NAME" property="qtjbName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,
        t.BHK_ID,t.BADRSN_ID,t.EXAM_CONCLUSION_ID,t.QTJB_NAME,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}fkByBhkId != null and ${joiner}fkByBhkId.rid != null">
            and ${mAlias}BHK_ID = #{${joiner}fkByBhkId.rid}
        </if>
        <if test="${joiner}fkByBadrsnId != null and ${joiner}fkByBadrsnId.rid != null">
            and ${mAlias}BADRSN_ID = #{${joiner}fkByBadrsnId.rid}
        </if>
        <if test="${joiner}fkByExamConclusionId != null and ${joiner}fkByExamConclusionId.rid != null">
            and ${mAlias}EXAM_CONCLUSION_ID = #{${joiner}fkByExamConclusionId.rid}
        </if>
        <if test="${joiner}qtjbName != null and ${joiner}qtjbName != ''">
            and ${mAlias}QTJB_NAME = #{${joiner}qtjbName}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByBhkId != null and ${joiner}fkByBhkId.rid != null">
                t.BHK_ID = #{${joiner}fkByBhkId.rid},
            </when>
            <otherwise>
                t.BHK_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByBadrsnId != null and ${joiner}fkByBadrsnId.rid != null">
                t.BADRSN_ID = #{${joiner}fkByBadrsnId.rid},
            </when>
            <otherwise>
                t.BADRSN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByExamConclusionId != null and ${joiner}fkByExamConclusionId.rid != null">
                t.EXAM_CONCLUSION_ID = #{${joiner}fkByExamConclusionId.rid},
            </when>
            <otherwise>
                t.EXAM_CONCLUSION_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}qtjbName != null and ${joiner}qtjbName != ''">
                t.QTJB_NAME = #{${joiner}qtjbName},
            </when>
            <otherwise>
                t.QTJB_NAME = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_BSN_BADRSNS t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_BSN_BADRSNS t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_BSN_BADRSNS t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwyjBsnBadrsns" >
        update TD_ZWYJ_BSN_BADRSNS t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZWYJ_BSN_BADRSNS t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZwyjBsnBadrsns">
        delete from TD_ZWYJ_BSN_BADRSNS
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <delete id="deleteByMainId">
        delete from TD_ZWYJ_BSN_BADRSNS
        <where>
            BHK_ID = #{mainId}
        </where>
    </delete>



</mapper>

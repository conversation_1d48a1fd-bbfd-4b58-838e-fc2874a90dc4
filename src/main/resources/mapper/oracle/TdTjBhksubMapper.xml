<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdTjBhksubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdTjBhksub">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="BHK_ID" property="bhkId" />
        <result column="ITEM_ID" property="fkByItemId.rid" />
        <result column="MSRUNT" property="msrunt" />
        <result column="ITEM_STDVALUE" property="itemStdvalue" />
        <result column="ITEM_RST" property="itemRst" />
        <result column="RGLTAG" property="rgltag" />
        <result column="RST_DESC" property="rstDesc" />
        <result column="IF_LACK" property="ifLack" />
        <result column="CHKDAT" property="chkdat" />
        <result column="CHKDOCT" property="chkdoct" />
        <result column="JDGPTN" property="jdgptn" />
        <result column="MINVAL" property="minval" />
        <result column="MAXVAL" property="maxval" />
        <result column="DIAG_REST" property="diagRest" />
        <result column="MSRUNT_ID" property="msruntId" />
        <result column="RST_FLAG" property="rstFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,
        t.BHK_ID,t.ITEM_ID,t.MSRUNT,t.ITEM_STDVALUE,t.ITEM_RST,t.RGLTAG,t.RST_DESC,t.IF_LACK,t.CHKDAT,t.CHKDOCT,t.JDGPTN,t.MINVAL,t.MAXVAL,t.DIAG_REST,t.MSRUNT_ID,t.RST_FLAG,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为"t." -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}bhkId != null and ${joiner}bhkId != ''">
            and ${mAlias}BHK_ID = #{${joiner}bhkId}
        </if>
        <if test="${joiner}fkByItemId != null and ${joiner}fkByItemId.rid != null">
            and ${mAlias}ITEM_ID = #{${joiner}fkByItemId.rid}
        </if>
        <if test="${joiner}msrunt != null and ${joiner}msrunt != ''">
            and ${mAlias}MSRUNT = #{${joiner}msrunt}
        </if>
        <if test="${joiner}itemStdvalue != null and ${joiner}itemStdvalue != ''">
            and ${mAlias}ITEM_STDVALUE = #{${joiner}itemStdvalue}
        </if>
        <if test="${joiner}itemRst != null">
            and ${mAlias}ITEM_RST = #{${joiner}itemRst}
        </if>
        <if test="${joiner}rgltag != null and ${joiner}rgltag != ''">
            and ${mAlias}RGLTAG = #{${joiner}rgltag}
        </if>
        <if test="${joiner}rstDesc != null and ${joiner}rstDesc != ''">
            and ${mAlias}RST_DESC = #{${joiner}rstDesc}
        </if>
        <if test="${joiner}ifLack != null and ${joiner}ifLack != ''">
            and ${mAlias}IF_LACK = #{${joiner}ifLack}
        </if>
        <if test="${joiner}chkdat != null">
            and ${mAlias}CHKDAT = #{${joiner}chkdat}
        </if>
        <if test="${joiner}chkdoct != null and ${joiner}chkdoct != ''">
            and ${mAlias}CHKDOCT = #{${joiner}chkdoct}
        </if>
        <if test="${joiner}jdgptn != null and ${joiner}jdgptn != ''">
            and ${mAlias}JDGPTN = #{${joiner}jdgptn}
        </if>
        <if test="${joiner}minval != null and ${joiner}minval != ''">
            and ${mAlias}MINVAL = #{${joiner}minval}
        </if>
        <if test="${joiner}maxval != null and ${joiner}maxval != ''">
            and ${mAlias}MAXVAL = #{${joiner}maxval}
        </if>
        <if test="${joiner}diagRest != null">
            and ${mAlias}DIAG_REST = #{${joiner}diagRest}
        </if>
        <if test="${joiner}msruntId != null and ${joiner}msruntId != ''">
            and ${mAlias}MSRUNT_ID = #{${joiner}msruntId}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为"item." -->
    <sql id="BaseUpdateFullFieldList">
        t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}bhkId != null and ${joiner}bhkId != ''">
                t.BHK_ID = #{${joiner}bhkId},
            </when>
            <otherwise>
                t.BHK_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByItemId != null and ${joiner}fkByItemId.rid != null">
                t.ITEM_ID = #{${joiner}fkByItemId.rid},
            </when>
            <otherwise>
                t.ITEM_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}msrunt != null and ${joiner}msrunt != ''">
                t.MSRUNT = #{${joiner}msrunt},
            </when>
            <otherwise>
                t.MSRUNT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}itemStdvalue != null and ${joiner}itemStdvalue != ''">
                t.ITEM_STDVALUE = #{${joiner}itemStdvalue},
            </when>
            <otherwise>
                t.ITEM_STDVALUE = null,
            </otherwise>
        </choose>
        t.ITEM_RST = #{${joiner}itemRst},
        <choose>
            <when test="${joiner}rgltag != null and ${joiner}rgltag != ''">
                t.RGLTAG = #{${joiner}rgltag},
            </when>
            <otherwise>
                t.RGLTAG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rstDesc != null and ${joiner}rstDesc != ''">
                t.RST_DESC = #{${joiner}rstDesc},
            </when>
            <otherwise>
                t.RST_DESC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifLack != null and ${joiner}ifLack != ''">
                t.IF_LACK = #{${joiner}ifLack},
            </when>
            <otherwise>
                t.IF_LACK = null,
            </otherwise>
        </choose>
        t.CHKDAT = #{${joiner}chkdat},
        <choose>
            <when test="${joiner}chkdoct != null and ${joiner}chkdoct != ''">
                t.CHKDOCT = #{${joiner}chkdoct},
            </when>
            <otherwise>
                t.CHKDOCT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}jdgptn != null and ${joiner}jdgptn != ''">
                t.JDGPTN = #{${joiner}jdgptn},
            </when>
            <otherwise>
                t.JDGPTN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}minval != null and ${joiner}minval != ''">
                t.MINVAL = #{${joiner}minval},
            </when>
            <otherwise>
                t.MINVAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}maxval != null and ${joiner}maxval != ''">
                t.MAXVAL = #{${joiner}maxval},
            </when>
            <otherwise>
                t.MAXVAL = null,
            </otherwise>
        </choose>
        t.DIAG_REST = #{${joiner}diagRest},
        <choose>
            <when test="${joiner}msruntId != null and ${joiner}msruntId != ''">
                t.MSRUNT_ID = #{${joiner}msruntId},
            </when>
            <otherwise>
                t.MSRUNT_ID = null,
            </otherwise>
        </choose>
    </sql>


    <!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_BHKSUB t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX
        ) WHERE 1=1
        <if test="first != null and pageSize != null">
            AND RN BETWEEN #{first} AND #{pageSize}
        </if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_BHKSUB t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_BHKSUB t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdTjBhksub" >
        update TD_TJ_BHKSUB t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_TJ_BHKSUB t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdTjBhksub">
        delete from TD_TJ_BHKSUB
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

    <!-- ========================自定义方法====================================== -->

    <!-- 依据体检主表rid集合批量查询 未缺项 定量项目 且危急值项目维护中存在数据的体检子表集合-->
    <select id="selectDangerValSubBhkList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_BHKSUB t
        INNER JOIN TD_ZWYJ_DANGER_ITEMS T1 ON t.ITEM_ID = T1.ITEM_ID AND t.MSRUNT_ID = T1.MSRUNT_ID
        WHERE t.IF_LACK = 0
        AND t.JDGPTN = 2
        AND T1.IF_REVEAL = 1
        <if test="null != bhkIdList and bhkIdList.size > 0">
            AND t.BHK_ID IN
            <foreach collection="bhkIdList" item="item" index="index" separator="," open="(" close=")">
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
        ORDER BY t.RID
    </select>

    <select id="selectSubBhkListByBhkRidList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_BHKSUB t
        WHERE t.BHK_ID IN
        <foreach collection="bhkIdList" item="item" index="index" separator="," open="(" close=")">
            <trim  suffixOverrides=",">
                #{item}
            </trim>
        </foreach>
    </select>

    <select id="selectSubBhkListByItemTagAndBhkRidList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_BHKSUB t
        inner join TB_TJ_ITEMS t1 on t.ITEM_ID=t1.rid
        WHERE t.BHK_ID IN
        <foreach collection="bhkIdList" item="item" index="index" separator="," open="(" close=")">
            <trim  suffixOverrides=",">
                #{item}
            </trim>
        </foreach>
        <if test="itemTag != null">
            AND t1.ITEM_TAG=#{itemTag}
        </if>
    </select>

    <select id="selectChestRstList" resultType="com.chis.modules.timer.heth.logic.ChestRstPO">
        SELECT RID AS bhkId, RST_FLAG AS rstFlags
        FROM (SELECT ZWX.*, ROWNUM AS RN
        FROM (SELECT
        B.RID, LISTAGG(BS.RST_FLAG, '@') WITHIN GROUP (ORDER BY BS.RID) AS RST_FLAG
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        INNER JOIN TS_SIMPLE_CODE SC ON I.ITEM_TAG_ID = SC.RID
        WHERE SC.EXTENDS3 = '30'
        AND B.CHEST_RESULT IS NULL
        AND NVL(BS.IF_LACK, 0) = 0
        <if test="bhkStartDate != null and bhkStartDate != ''">
            AND B.BHK_DATE &gt;= TO_DATE(#{bhkStartDate},'yyyy-MM-dd')
        </if>
        <if test="bhkEndDate != null and bhkEndDate != ''">
            AND B.BHK_DATE &lt;= TO_DATE(#{bhkEndDate}||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test="rptPrintStartDate != null and rptPrintStartDate != ''">
            AND B.RPT_PRINT_DATE &gt;= TO_DATE(#{rptPrintStartDate},'yyyy-MM-dd')
        </if>
        <if test="rptPrintEndDate != null and rptPrintEndDate != ''">
            AND B.RPT_PRINT_DATE &lt;= TO_DATE(#{rptPrintEndDate}||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
        </if>
        GROUP BY B.RID) ZWX
        WHERE RST_FLAG IS NOT NULL)
        WHERE RN BETWEEN 0 AND #{size}
    </select>

    <select id="selectHearingRstList" resultType="com.chis.modules.timer.heth.logic.HearingRstPO">
        WITH ID_TABLE AS (
        SELECT RID
        FROM (
        SELECT ZWX.RID, ROWNUM AS RN
        FROM (
        SELECT B.RID
        FROM TD_TJ_BHKSUB BS
        INNER JOIN TD_TJ_BHK B ON B.RID = BS.BHK_ID
        WHERE B.HEARING_RST_ID IS NULL 
        <choose>
            <when test="useRgltagMode">
                AND BS.RGLTAG IS NOT NULL
            </when>
            <otherwise>
                AND BS.ITEM_RST IS NOT NULL
            </otherwise>
        </choose>
        AND NVL(BS.IF_LACK, 0) = 0
        AND BS.ITEM_ID IN
        <foreach collection="itemCodeList" item="itemCode" open="(" separator="," close=")">
            #{itemCode}
        </foreach>
        <if test="bhkStartDate != null and bhkStartDate != ''">
            AND B.BHK_DATE &gt;= TO_DATE(#{bhkStartDate},'yyyy-MM-dd')
        </if>
        <if test="bhkEndDate != null and bhkEndDate != ''">
            AND B.BHK_DATE &lt;= TO_DATE(#{bhkEndDate}||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test="rptPrintStartDate != null and rptPrintStartDate != ''">
            AND B.RPT_PRINT_DATE &gt;= TO_DATE(#{rptPrintStartDate},'yyyy-MM-dd')
        </if>
        <if test="rptPrintEndDate != null and rptPrintEndDate != ''">
            AND B.RPT_PRINT_DATE &lt;= TO_DATE(#{rptPrintEndDate}||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
        </if>
        GROUP BY B.RID
        ) ZWX
        )
        WHERE RN BETWEEN 0 AND #{size}
        )
        SELECT BS.BHK_ID AS bhkId, I.ITEM_CODE AS itemCode, BS.ITEM_RST AS itemRst,BS.RGLTAG AS rgltag
        FROM TD_TJ_BHKSUB BS
        INNER JOIN ID_TABLE ID ON BS.BHK_ID = ID.RID
        INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID
        WHERE I.RID IN
        <foreach collection="itemCodeList" item="itemCode" open="(" separator="," close=")">
            #{itemCode}
        </foreach>
        <choose>
            <when test="useRgltagMode">
                AND BS.RGLTAG IS NOT NULL
            </when>
            <otherwise>
                AND BS.ITEM_RST IS NOT NULL
            </otherwise>
        </choose>
    </select>

    <select id="findItemRidListByCodeList" resultType="java.lang.Integer" parameterType="java.util.List">
        SELECT T.RID
        FROM TB_TJ_ITEMS T
        WHERE T.ITEM_CODE IN
        <foreach collection="itemCodeList" item="item" index="index" separator="," open="(" close=")" >
            <trim  suffixOverrides=",">
                #{item}
            </trim>
        </foreach>
    </select>

</mapper>

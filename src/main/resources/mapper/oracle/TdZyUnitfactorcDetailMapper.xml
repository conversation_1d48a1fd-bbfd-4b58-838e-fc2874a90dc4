<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZyUnitfactorcDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZyUnitfactorcDetail">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="HAZARDS_SORT" property="hazardsSort" />
        <result column="HAZARDS_ID" property="fkByHazardsId.rid" />
        <result column="HAZARDS_NAME" property="hazardsName" />
        <result column="SUPERVISION_REQUIREMENT" property="supervisionRequirement" />
        <result column="CONTACT_NUMBER" property="contactNumber" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MAIN_ID,t.HAZARDS_SORT,t.HAZARDS_ID,t.HAZARDS_NAME,t.SUPERVISION_REQUIREMENT,t.CONTACT_NUMBER,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}hazardsSort != null">
            and ${mAlias}HAZARDS_SORT = #{${joiner}hazardsSort}
        </if>
        <if test="${joiner}fkByHazardsId != null and ${joiner}fkByHazardsId.rid != null">
            and ${mAlias}HAZARDS_ID = #{${joiner}fkByHazardsId.rid}
        </if>
        <if test="${joiner}hazardsName != null">
            and ${mAlias}HAZARDS_NAME = #{${joiner}hazardsName}
        </if>
        <if test="${joiner}supervisionRequirement != null">
            and ${mAlias}SUPERVISION_REQUIREMENT = #{${joiner}supervisionRequirement}
        </if>
        <if test="${joiner}contactNumber != null">
            and ${mAlias}CONTACT_NUMBER = #{${joiner}contactNumber}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hazardsSort != null">
                t.HAZARDS_SORT = #{${joiner}hazardsSort},
            </when>
            <otherwise>
                t.HAZARDS_SORT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByHazardsId != null and ${joiner}fkByHazardsId.rid != null">
                t.HAZARDS_ID = #{${joiner}fkByHazardsId.rid},
            </when>
            <otherwise>
                t.HAZARDS_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}hazardsName != null">
                t.HAZARDS_NAME = #{${joiner}hazardsName},
            </when>
            <otherwise>
                t.HAZARDS_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}supervisionRequirement != null">
                t.SUPERVISION_REQUIREMENT = #{${joiner}supervisionRequirement},
            </when>
            <otherwise>
                t.SUPERVISION_REQUIREMENT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}contactNumber != null">
                t.CONTACT_NUMBER = #{${joiner}contactNumber},
            </when>
            <otherwise>
                t.CONTACT_NUMBER = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITFACTORC_DETAIL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITFACTORC_DETAIL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITFACTORC_DETAIL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdZyUnitfactorcDetail">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_ZY_UNITFACTORC_DETAIL_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_ZY_UNITFACTORC_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            HAZARDS_SORT,
            HAZARDS_ID,
            HAZARDS_NAME,
            SUPERVISION_REQUIREMENT,
            CONTACT_NUMBER,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{fkByMainId.rid},
            #{hazardsSort},
            #{fkByHazardsId.rid},
            #{hazardsName},
            #{supervisionRequirement},
            #{contactNumber},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_ZY_UNITFACTORC_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MAIN_ID,
            HAZARDS_SORT,
            HAZARDS_ID,
            HAZARDS_NAME,
            SUPERVISION_REQUIREMENT,
            CONTACT_NUMBER,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_ZY_UNITFACTORC_DETAIL_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByMainId != null and item.fkByMainId.rid != null">
                            #{item.fkByMainId.rid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hazardsSort != null">
                            #{item.hazardsSort} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByHazardsId != null and item.fkByHazardsId.rid != null">
                            #{item.fkByHazardsId.rid} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.hazardsName != null">
                            #{item.hazardsName} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.supervisionRequirement != null">
                            #{item.supervisionRequirement} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.contactNumber != null">
                            #{item.contactNumber} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZyUnitfactorcDetail" >
        update TD_ZY_UNITFACTORC_DETAIL t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_ZY_UNITFACTORC_DETAIL t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZyUnitfactorcDetail">
        delete from TD_ZY_UNITFACTORC_DETAIL
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

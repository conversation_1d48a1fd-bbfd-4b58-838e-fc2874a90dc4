<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZdzybSpecialAnalyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZdzybSpecialAnaly">
        <result column="RID" property="rid" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="ANALY_TYPE" property="analyType" />
        <result column="BADRSN_ID" property="fkByBadrsnId.rid" />
        <result column="ANALY_NAME" property="analyName" />
        <result column="ITEM_STDVALUE" property="itemStdvalue" />
        <result column="XH" property="xh" />
        <result column="RMK" property="rmk" />
        <result column="IF_OCC_CARCINOGENS" property="ifOccCarcinogens" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_MANID,t.CREATE_DATE,t.MODIFY_DATE,t.MODIFY_MANID,
        t.ANALY_TYPE,t.BADRSN_ID,t.ANALY_NAME,t.ITEM_STDVALUE,t.XH,t.RMK,t.IF_OCC_CARCINOGENS,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}analyType != null and ${joiner}analyType != ''">
            and ${mAlias}ANALY_TYPE = #{${joiner}analyType}
        </if>
        <if test="${joiner}fkByBadrsnId != null and ${joiner}fkByBadrsnId.rid != null">
            and ${mAlias}BADRSN_ID = #{${joiner}fkByBadrsnId.rid}
        </if>
        <if test="${joiner}analyName != null and ${joiner}analyName != ''">
            and ${mAlias}ANALY_NAME = #{${joiner}analyName}
        </if>
        <if test="${joiner}itemStdvalue != null and ${joiner}itemStdvalue != ''">
            and ${mAlias}ITEM_STDVALUE = #{${joiner}itemStdvalue}
        </if>
        <if test="${joiner}xh != null and ${joiner}xh != ''">
            and ${mAlias}XH = #{${joiner}xh}
        </if>
        <if test="${joiner}rmk != null and ${joiner}rmk != ''">
            and ${mAlias}RMK = #{${joiner}rmk}
        </if>
        <if test="${joiner}ifOccCarcinogens != null and ${joiner}ifOccCarcinogens != ''">
            and ${mAlias}IF_OCC_CARCINOGENS = #{${joiner}ifOccCarcinogens}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.CREATE_DATE = #{${joiner}createDate},
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}analyType != null and ${joiner}analyType != ''">
                t.ANALY_TYPE = #{${joiner}analyType},
            </when>
            <otherwise>
                t.ANALY_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByBadrsnId != null and ${joiner}fkByBadrsnId.rid != null">
                t.BADRSN_ID = #{${joiner}fkByBadrsnId.rid},
            </when>
            <otherwise>
                t.BADRSN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}analyName != null and ${joiner}analyName != ''">
                t.ANALY_NAME = #{${joiner}analyName},
            </when>
            <otherwise>
                t.ANALY_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}itemStdvalue != null and ${joiner}itemStdvalue != ''">
                t.ITEM_STDVALUE = #{${joiner}itemStdvalue},
            </when>
            <otherwise>
                t.ITEM_STDVALUE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}xh != null and ${joiner}xh != ''">
                t.XH = #{${joiner}xh},
            </when>
            <otherwise>
                t.XH = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rmk != null and ${joiner}rmk != ''">
                t.RMK = #{${joiner}rmk},
            </when>
            <otherwise>
                t.RMK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifOccCarcinogens != null and ${joiner}ifOccCarcinogens != ''">
                t.IF_OCC_CARCINOGENS = #{${joiner}ifOccCarcinogens},
            </when>
            <otherwise>
                t.IF_OCC_CARCINOGENS = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZDZYB_SPECIAL_ANALY t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZDZYB_SPECIAL_ANALY t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZDZYB_SPECIAL_ANALY t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZdzybSpecialAnaly" >
        update TD_ZDZYB_SPECIAL_ANALY t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZDZYB_SPECIAL_ANALY t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZdzybSpecialAnaly">
        delete from TD_ZDZYB_SPECIAL_ANALY
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

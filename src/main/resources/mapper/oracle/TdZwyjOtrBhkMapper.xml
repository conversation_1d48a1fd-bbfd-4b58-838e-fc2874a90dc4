<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwyjOtrBhkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwyjOtrBhk">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="BHK_CODE" property="bhkCode" />
        <result column="BHKORG_ID" property="fkByBhkorgId.rid" />
        <result column="CRPT_ZONE_ID" property="fkByCrptZoneId.rid" />
        <result column="CRPT_ID" property="fkByCrptId.rid" />
        <result column="PERSON_NAME" property="personName" />
        <result column="CARD_TYPE_ID" property="fkByCardTypeId.rid" />
        <result column="IDC" property="idc" />
        <result column="BHK_DATE" property="bhkDate" />
        <result column="BADRSN" property="badrsn" />
        <result column="OTR_TYPE" property="otrType" />
        <result column="HAPPEN_DATE" property="happenDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ZONE_ID,t.CRPT_ID,t.PERSON_NAME,t.CARD_TYPE_ID,t.IDC,t.BHK_DATE,t.BADRSN,t.OTR_TYPE,t.HAPPEN_DATE,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}bhkCode != null and ${joiner}bhkCode != ''">
            and ${mAlias}BHK_CODE = #{${joiner}bhkCode}
        </if>
        <if test="${joiner}fkByBhkorgId != null and ${joiner}fkByBhkorgId.rid != null">
            and ${mAlias}BHKORG_ID = #{${joiner}fkByBhkorgId.rid}
        </if>
        <if test="${joiner}fkByCrptZoneId != null and ${joiner}fkByCrptZoneId.rid != null">
            and ${mAlias}CRPT_ZONE_ID = #{${joiner}fkByCrptZoneId.rid}
        </if>
        <if test="${joiner}fkByCrptId != null and ${joiner}fkByCrptId.rid != null">
            and ${mAlias}CRPT_ID = #{${joiner}fkByCrptId.rid}
        </if>
        <if test="${joiner}personName != null and ${joiner}personName != ''">
            and ${mAlias}PERSON_NAME = #{${joiner}personName}
        </if>
        <if test="${joiner}fkByCardTypeId != null and ${joiner}fkByCardTypeId.rid != null">
            and ${mAlias}CARD_TYPE_ID = #{${joiner}fkByCardTypeId.rid}
        </if>
        <if test="${joiner}idc != null and ${joiner}idc != ''">
            and ${mAlias}IDC = #{${joiner}idc}
        </if>
        <if test="${joiner}bhkDate != null">
            and ${mAlias}BHK_DATE = #{${joiner}bhkDate}
        </if>
        <if test="${joiner}badrsn != null and ${joiner}badrsn != ''">
            and ${mAlias}BADRSN = #{${joiner}badrsn}
        </if>
        <if test="${joiner}otrType != null and ${joiner}otrType != ''">
            and ${mAlias}OTR_TYPE = #{${joiner}otrType}
        </if>
        <if test="${joiner}happenDate != null">
            and ${mAlias}HAPPEN_DATE = #{${joiner}happenDate}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}bhkCode != null and ${joiner}bhkCode != ''">
                t.BHK_CODE = #{${joiner}bhkCode},
            </when>
            <otherwise>
                t.BHK_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByBhkorgId != null and ${joiner}fkByBhkorgId.rid != null">
                t.BHKORG_ID = #{${joiner}fkByBhkorgId.rid},
            </when>
            <otherwise>
                t.BHKORG_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCrptZoneId != null and ${joiner}fkByCrptZoneId.rid != null">
                t.CRPT_ZONE_ID = #{${joiner}fkByCrptZoneId.rid},
            </when>
            <otherwise>
                t.CRPT_ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCrptId != null and ${joiner}fkByCrptId.rid != null">
                t.CRPT_ID = #{${joiner}fkByCrptId.rid},
            </when>
            <otherwise>
                t.CRPT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}personName != null and ${joiner}personName != ''">
                t.PERSON_NAME = #{${joiner}personName},
            </when>
            <otherwise>
                t.PERSON_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCardTypeId != null and ${joiner}fkByCardTypeId.rid != null">
                t.CARD_TYPE_ID = #{${joiner}fkByCardTypeId.rid},
            </when>
            <otherwise>
                t.CARD_TYPE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}idc != null and ${joiner}idc != ''">
                t.IDC = #{${joiner}idc},
            </when>
            <otherwise>
                t.IDC = null,
            </otherwise>
        </choose>
            t.BHK_DATE = #{${joiner}bhkDate},
        <choose>
            <when test="${joiner}badrsn != null and ${joiner}badrsn != ''">
                t.BADRSN = #{${joiner}badrsn},
            </when>
            <otherwise>
                t.BADRSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}otrType != null and ${joiner}otrType != ''">
                t.OTR_TYPE = #{${joiner}otrType},
            </when>
            <otherwise>
                t.OTR_TYPE = null,
            </otherwise>
        </choose>
            t.HAPPEN_DATE = #{${joiner}happenDate},
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_OTR_BHK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_OTR_BHK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_OTR_BHK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwyjOtrBhk" >
        update TD_ZWYJ_OTR_BHK t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZWYJ_OTR_BHK t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZwyjOtrBhk">
        delete from TD_ZWYJ_OTR_BHK
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->

    <!-- 查询不在超范围服务预警统计日志中的超范围服务体检记录列表 数据返回条数有限制 若未传递数据长度 则默认一百条 -->
    <select id="findTdZwyjOtrBhkExcludeSmyRCD" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_OTR_BHK t
        LEFT JOIN TD_ZWYJ_OTR_SMY_RCD t1 ON t.RID = t1.BHK_ID
        WHERE t1.RID IS NULL
        <if test="dataSize != null">
            AND ROWNUM &lt;=  #{dataSize}
        </if>
        <if test="dataSize == null">
            AND ROWNUM &lt;=  1000
        </if>
        ORDER BY t.RID
    </select>


</mapper>

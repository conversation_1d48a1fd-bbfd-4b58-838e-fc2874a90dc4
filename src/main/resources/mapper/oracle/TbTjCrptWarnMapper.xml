<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TbTjCrptWarnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TbTjCrptWarn">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="CRPT_ID" property="fkByCrptId.rid" />
        <result column="WARN_ID" property="fkByWarnId.rid" />
        <result column="WARN_EXT1" property="fkByWarnId.extends1" />
        <result column="WARN_DATE" property="warnDate" />
        <result column="RESULT_ID" property="fkByResultId.rid" />
        <result column="RESULT_EXT1" property="fkByResultId.extends1" />
        <result column="DEAL_DATE" property="dealDate" />
        <result column="DEAL_PSN_ID" property="fkByDealPsnId.rid" />
        <result column="RMK" property="rmk" />
        <result column="STATE" property="state" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.CRPT_ID,t.WARN_ID,t.WARN_DATE,t.RESULT_ID,t.DEAL_DATE,t.DEAL_PSN_ID,t.RMK,t.STATE,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByCrptId != null and ${joiner}fkByCrptId.rid != null">
            and ${mAlias}CRPT_ID = #{${joiner}fkByCrptId.rid}
        </if>
        <if test="${joiner}fkByWarnId != null and ${joiner}fkByWarnId.rid != null">
            and ${mAlias}WARN_ID = #{${joiner}fkByWarnId.rid}
        </if>
        <if test="${joiner}warnDate != null">
            and ${mAlias}WARN_DATE = #{${joiner}warnDate}
        </if>
        <if test="${joiner}fkByResultId != null and ${joiner}fkByResultId.rid != null">
            and ${mAlias}RESULT_ID = #{${joiner}fkByResultId.rid}
        </if>
        <if test="${joiner}dealDate != null">
            and ${mAlias}DEAL_DATE = #{${joiner}dealDate}
        </if>
        <if test="${joiner}fkByDealPsnId != null and ${joiner}fkByDealPsnId.rid != null">
            and ${mAlias}DEAL_PSN_ID = #{${joiner}fkByDealPsnId.rid}
        </if>
        <if test="${joiner}rmk != null">
            and ${mAlias}RMK = #{${joiner}rmk}
        </if>
        <if test="${joiner}state != null">
            and ${mAlias}STATE = #{${joiner}state}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCrptId != null and ${joiner}fkByCrptId.rid != null">
                t.CRPT_ID = #{${joiner}fkByCrptId.rid},
            </when>
            <otherwise>
                t.CRPT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByWarnId != null and ${joiner}fkByWarnId.rid != null">
                t.WARN_ID = #{${joiner}fkByWarnId.rid},
            </when>
            <otherwise>
                t.WARN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}warnDate != null">
                t.WARN_DATE = #{${joiner}warnDate},
            </when>
            <otherwise>
                t.WARN_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByResultId != null and ${joiner}fkByResultId.rid != null">
                t.RESULT_ID = #{${joiner}fkByResultId.rid},
            </when>
            <otherwise>
                t.RESULT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dealDate != null">
                t.DEAL_DATE = #{${joiner}dealDate},
            </when>
            <otherwise>
                t.DEAL_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByDealPsnId != null and ${joiner}fkByDealPsnId.rid != null">
                t.DEAL_PSN_ID = #{${joiner}fkByDealPsnId.rid},
            </when>
            <otherwise>
                t.DEAL_PSN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rmk != null">
                t.RMK = #{${joiner}rmk},
            </when>
            <otherwise>
                t.RMK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}state != null">
                t.STATE = #{${joiner}state},
            </when>
            <otherwise>
                t.STATE = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_CRPT_WARN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_CRPT_WARN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_CRPT_WARN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TbTjCrptWarn">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TB_TJ_CRPT_WARN_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TB_TJ_CRPT_WARN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            CRPT_ID,
            WARN_ID,
            WARN_DATE,
            RESULT_ID,
            DEAL_DATE,
            DEAL_PSN_ID,
            RMK,
            STATE,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{fkByCrptId.rid},
            #{fkByWarnId.rid},
            #{warnDate},
            #{fkByResultId.rid},
            #{dealDate},
            #{fkByDealPsnId.rid},
            #{rmk},
            #{state},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TB_TJ_CRPT_WARN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            CRPT_ID,
            WARN_ID,
            WARN_DATE,
            RESULT_ID,
            DEAL_DATE,
            DEAL_PSN_ID,
            RMK,
            STATE,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TB_TJ_CRPT_WARN_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByCrptId != null and item.fkByCrptId.rid != null">
                            #{item.fkByCrptId.rid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByWarnId != null and item.fkByWarnId.rid != null">
                            #{item.fkByWarnId.rid} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.warnDate != null">
                            #{item.warnDate} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByResultId != null and item.fkByResultId.rid != null">
                            #{item.fkByResultId.rid} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.dealDate != null">
                            #{item.dealDate} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByDealPsnId != null and item.fkByDealPsnId.rid != null">
                            #{item.fkByDealPsnId.rid} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rmk != null">
                            #{item.rmk} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.state != null">
                            #{item.state} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TbTjCrptWarn" >
        update TB_TJ_CRPT_WARN t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TB_TJ_CRPT_WARN t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 注意 joiner 需要加入 -->
    <delete id="removeByEntity" parameterType="TbTjCrptWarn">
        delete from TB_TJ_CRPT_WARN
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <select id="selectLastestWarnByCrptIds" resultMap="BaseResultMap">
        WITH TMP_CRPT_WARN_TOOL6 AS (
            SELECT K.CRPT_ID ,K.WARN_ID,MAX(K.WARN_DATE) AS WARN_DATE
            FROM TB_TJ_CRPT_WARN K
            WHERE 1=1
            <if test="crptRidList != null and crptRidList.size > 0">
                AND K.CRPT_ID IN
                <foreach collection="crptRidList" item="item" index="index" separator="," open="(" close=")" >
                    <trim  suffixOverrides=",">
                        #{item}
                    </trim>
                </foreach>
            </if>
            GROUP BY K.CRPT_ID ,K.WARN_ID
        )
        SELECT
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
            t2.EXTENDS1 AS WARN_EXT1,
            t3.EXTENDS1 AS RESULT_EXT1,
        </trim>
        FROM TMP_CRPT_WARN_TOOL6 t1
        INNER JOIN TB_TJ_CRPT_WARN t ON t.CRPT_ID = t1.CRPT_ID AND t.WARN_ID = t1.WARN_ID AND t.WARN_DATE = t1.WARN_DATE
        LEFT JOIN TS_SIMPLE_CODE t2 ON t.WARN_ID = t2.RID
        LEFT JOIN TS_SIMPLE_CODE t3 ON t.RESULT_ID = t3.RID
        ORDER BY t.RID DESC
    </select>



</mapper>

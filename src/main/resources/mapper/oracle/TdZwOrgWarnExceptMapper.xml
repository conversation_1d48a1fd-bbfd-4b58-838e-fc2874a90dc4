<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwOrgWarnExceptMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chis.modules.timer.heth.entity.TdZwOrgWarnExcept">
        <result column="RID" property="rid"/>
        <result column="CREATE_DATE" property="createDate"/>
        <result column="CREATE_MANID" property="createManid"/>
        <result column="MODIFY_DATE" property="modifyDate"/>
        <result column="MODIFY_MANID" property="modifyManid"/>
        <result column="MAIN_ID" property="fkByMainId.rid"/>
        <result column="EXCEPT_ID" property="fkByExceptId.rid"/>
        <result column="FILING_DATE" property="filingDate"/>
        <result column="FILING_YEAR" property="filingYear"/>
        <result column="NEXT_DATE" property="nextDate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MAIN_ID,t.EXCEPT_ID,t.FILING_DATE,t.FILING_YEAR,t.NEXT_DATE,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}fkByExceptId != null and ${joiner}fkByExceptId.rid != null">
            and ${mAlias}EXCEPT_ID = #{${joiner}fkByExceptId.rid}
        </if>
        <if test="${joiner}filingDate != null">
            and ${mAlias}FILING_DATE = #{${joiner}filingDate}
        </if>
        <if test="${joiner}filingYear != null and ${joiner}filingYear != ''">
            and ${mAlias}FILING_YEAR = #{${joiner}filingYear}
        </if>
        <if test="${joiner}nextDate != null">
            and ${mAlias}NEXT_DATE = #{${joiner}nextDate}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
        t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByExceptId != null and ${joiner}fkByExceptId.rid != null">
                t.EXCEPT_ID = #{${joiner}fkByExceptId.rid},
            </when>
            <otherwise>
                t.EXCEPT_ID = null,
            </otherwise>
        </choose>
        t.FILING_DATE = #{${joiner}filingDate},
        <choose>
            <when test="${joiner}filingYear != null and ${joiner}filingYear != ''">
                t.FILING_YEAR = #{${joiner}filingYear},
            </when>
            <otherwise>
                t.FILING_YEAR = null,
            </otherwise>
        </choose>
        t.NEXT_DATE = #{${joiner}nextDate},
    </sql>


    <!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from TD_ZW_ORG_WARN_EXCEPT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX
        ) WHERE 1=1
        <if test="first != null and pageSize != null">
            AND RN BETWEEN #{first} AND #{pageSize}
        </if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from TD_ZW_ORG_WARN_EXCEPT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from TD_ZW_ORG_WARN_EXCEPT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwOrgWarnExcept">
        update TD_ZW_ORG_WARN_EXCEPT t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZW_ORG_WARN_EXCEPT t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="com.chis.modules.timer.heth.entity.TdZwOrgWarnExcept">
        delete from TD_ZW_ORG_WARN_EXCEPT
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

    <!-- ========================自定义方法====================================== -->
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZyUnitbasicinfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZyUnitbasicinfo">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="UUID" property="uuid" />
        <result column="RECORD_UUID" property="recordUuid" />
        <result column="DECLARE_YEAR" property="declareYear" />
        <result column="DECLARE_TYPE" property="declareType" />
        <result column="DECLARE_STATUS" property="declareStatus" />
        <result column="DECLARE_DATE" property="declareDate" />
        <result column="APPROVE_DATE" property="approveDate" />
        <result column="REASON_ID" property="fkByReasonId.rid" />
        <result column="REMARK" property="remark" />
        <result column="ZONE_ID" property="fkByZoneId.rid" />
        <result column="UNIT_NAME" property="unitName" />
        <result column="CREDIT_CODE" property="creditCode" />
        <result column="REG_ADDR" property="regAddr" />
        <result column="WORK_ADDR" property="workAddr" />
        <result column="ENTERPRISE_SCALE_ID" property="fkByEnterpriseScaleId.rid" />
        <result column="INDUSTRY_CATE_ID" property="fkByIndustryCateId.rid" />
        <result column="ECONOMIC_ID" property="fkByEconomicId.rid" />
        <result column="FILL_MAN" property="fillMan" />
        <result column="FILL_PHONE" property="fillPhone" />
        <result column="LEGAL_PERSON" property="legalPerson" />
        <result column="LEGAL_PERSON_PHONE" property="legalPersonPhone" />
        <result column="LINK_MANAGER" property="linkManager" />
        <result column="LINK_PHONE" property="linkPhone" />
        <result column="EMP_NUM" property="empNum" />
        <result column="EXTERNAL_NUM" property="externalNum" />
        <result column="VICTIMS_NUM" property="victimsNum" />
        <result column="OCCUPATIONAL_DISEASES_NUM" property="occupationalDiseasesNum" />
        <result column="IF_BRANCH" property="ifBranch" />
        <result column="PARENT_UNIT_UUID" property="parentUnitUuid" />
        <result column="PARENT_UNIT_NAME" property="parentUnitName" />
        <result column="PARENT_UNIT_CREDIT" property="parentUnitCredit" />
        <result column="PARENT_ZONE_ID" property="fkByParentZoneId.rid" />
        <result column="CRPT_ID" property="crptId" />
        <result column="IF_WAR_PRODUCT" property="ifWarProduct" />
        <result column="OPERATION_STATUS" property="operationStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.UUID,t.RECORD_UUID,t.DECLARE_YEAR,t.DECLARE_TYPE,t.DECLARE_STATUS,t.DECLARE_DATE,t.APPROVE_DATE,t.REASON_ID,t.REMARK,t.ZONE_ID,t.UNIT_NAME,t.CREDIT_CODE,t.REG_ADDR,t.WORK_ADDR,t.ENTERPRISE_SCALE_ID,t.INDUSTRY_CATE_ID,t.ECONOMIC_ID,t.FILL_MAN,t.FILL_PHONE,t.LEGAL_PERSON,t.LEGAL_PERSON_PHONE,t.LINK_MANAGER,t.LINK_PHONE,t.EMP_NUM,t.EXTERNAL_NUM,t.VICTIMS_NUM,t.OCCUPATIONAL_DISEASES_NUM,t.IF_BRANCH,t.PARENT_UNIT_UUID,t.IF_WAR_PRODUCT,t.OPERATION_STATUS,
        t.PARENT_UNIT_NAME,t.PARENT_UNIT_CREDIT,t.PARENT_ZONE_ID,t.CRPT_ID,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}uuid != null">
            and ${mAlias}UUID = #{${joiner}uuid}
        </if>
        <if test="${joiner}recordUuid != null">
            and ${mAlias}RECORD_UUID = #{${joiner}recordUuid}
        </if>
        <if test="${joiner}declareYear != null">
            and ${mAlias}DECLARE_YEAR = #{${joiner}declareYear}
        </if>
        <if test="${joiner}declareType != null">
            and ${mAlias}DECLARE_TYPE = #{${joiner}declareType}
        </if>
        <if test="${joiner}declareStatus != null">
            and ${mAlias}DECLARE_STATUS = #{${joiner}declareStatus}
        </if>
        <if test="${joiner}declareDate != null">
            and ${mAlias}DECLARE_DATE = #{${joiner}declareDate}
        </if>
        <if test="${joiner}approveDate != null">
            and ${mAlias}APPROVE_DATE = #{${joiner}approveDate}
        </if>
        <if test="${joiner}fkByReasonId != null and ${joiner}fkByReasonId.rid != null">
            and ${mAlias}REASON_ID = #{${joiner}fkByReasonId.rid}
        </if>
        <if test="${joiner}remark != null">
            and ${mAlias}REMARK = #{${joiner}remark}
        </if>
        <if test="${joiner}fkByZoneId != null and ${joiner}fkByZoneId.rid != null">
            and ${mAlias}ZONE_ID = #{${joiner}fkByZoneId.rid}
        </if>
        <if test="${joiner}unitName != null">
            and ${mAlias}UNIT_NAME = #{${joiner}unitName}
        </if>
        <if test="${joiner}creditCode != null">
            and ${mAlias}CREDIT_CODE = #{${joiner}creditCode}
        </if>
        <if test="${joiner}regAddr != null">
            and ${mAlias}REG_ADDR = #{${joiner}regAddr}
        </if>
        <if test="${joiner}workAddr != null">
            and ${mAlias}WORK_ADDR = #{${joiner}workAddr}
        </if>
        <if test="${joiner}fkByEnterpriseScaleId != null and ${joiner}fkByEnterpriseScaleId.rid != null">
            and ${mAlias}ENTERPRISE_SCALE_ID = #{${joiner}fkByEnterpriseScaleId.rid}
        </if>
        <if test="${joiner}fkByIndustryCateId != null and ${joiner}fkByIndustryCateId.rid != null">
            and ${mAlias}INDUSTRY_CATE_ID = #{${joiner}fkByIndustryCateId.rid}
        </if>
        <if test="${joiner}fkByEconomicId != null and ${joiner}fkByEconomicId.rid != null">
            and ${mAlias}ECONOMIC_ID = #{${joiner}fkByEconomicId.rid}
        </if>
        <if test="${joiner}fillMan != null">
            and ${mAlias}FILL_MAN = #{${joiner}fillMan}
        </if>
        <if test="${joiner}fillPhone != null">
            and ${mAlias}FILL_PHONE = #{${joiner}fillPhone}
        </if>
        <if test="${joiner}legalPerson != null">
            and ${mAlias}LEGAL_PERSON = #{${joiner}legalPerson}
        </if>
        <if test="${joiner}legalPersonPhone != null">
            and ${mAlias}LEGAL_PERSON_PHONE = #{${joiner}legalPersonPhone}
        </if>
        <if test="${joiner}linkManager != null">
            and ${mAlias}LINK_MANAGER = #{${joiner}linkManager}
        </if>
        <if test="${joiner}linkPhone != null">
            and ${mAlias}LINK_PHONE = #{${joiner}linkPhone}
        </if>
        <if test="${joiner}empNum != null">
            and ${mAlias}EMP_NUM = #{${joiner}empNum}
        </if>
        <if test="${joiner}externalNum != null">
            and ${mAlias}EXTERNAL_NUM = #{${joiner}externalNum}
        </if>
        <if test="${joiner}victimsNum != null">
            and ${mAlias}VICTIMS_NUM = #{${joiner}victimsNum}
        </if>
        <if test="${joiner}occupationalDiseasesNum != null">
            and ${mAlias}OCCUPATIONAL_DISEASES_NUM = #{${joiner}occupationalDiseasesNum}
        </if>
        <if test="${joiner}ifBranch != null">
            and ${mAlias}IF_BRANCH = #{${joiner}ifBranch}
        </if>
        <if test="${joiner}parentUnitUuid != null">
            and ${mAlias}PARENT_UNIT_UUID = #{${joiner}parentUnitUuid}
        </if>
        <if test="${joiner}ifWarProduct != null">
            and ${mAlias}IF_WAR_PRODUCT = #{${joiner}ifWarProduct}
        </if>
        <if test="${joiner}operationStatus != null">
            and ${mAlias}OPERATION_STATUS = #{${joiner}operationStatus}
        </if>
        <if test="${joiner}parentUnitName != null">
            and ${mAlias}PARENT_UNIT_NAME = #{${joiner}parentUnitName}
        </if>
        <if test="${joiner}parentUnitCredit != null">
            and ${mAlias}PARENT_UNIT_CREDIT = #{${joiner}parentUnitCredit}
        </if>
        <if test="${joiner}fkByParentZoneId != null and ${joiner}fkByParentZoneId.rid != null">
            and ${mAlias}PARENT_ZONE_ID = #{${joiner}fkByParentZoneId.rid}
        </if>
        <if test="${joiner}crptId != null">
            and ${mAlias}CRPT_ID = #{${joiner}crptId}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}uuid != null">
                t.UUID = #{${joiner}uuid},
            </when>
            <otherwise>
                t.UUID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}recordUuid != null">
                t.RECORD_UUID = #{${joiner}recordUuid},
            </when>
            <otherwise>
                t.RECORD_UUID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}declareYear != null">
                t.DECLARE_YEAR = #{${joiner}declareYear},
            </when>
            <otherwise>
                t.DECLARE_YEAR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}declareType != null">
                t.DECLARE_TYPE = #{${joiner}declareType},
            </when>
            <otherwise>
                t.DECLARE_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}declareStatus != null">
                t.DECLARE_STATUS = #{${joiner}declareStatus},
            </when>
            <otherwise>
                t.DECLARE_STATUS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}declareDate != null">
                t.DECLARE_DATE = #{${joiner}declareDate},
            </when>
            <otherwise>
                t.DECLARE_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}approveDate != null">
                t.APPROVE_DATE = #{${joiner}approveDate},
            </when>
            <otherwise>
                t.APPROVE_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByReasonId != null and ${joiner}fkByReasonId.rid != null">
                t.REASON_ID = #{${joiner}fkByReasonId.rid},
            </when>
            <otherwise>
                t.REASON_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}remark != null">
                t.REMARK = #{${joiner}remark},
            </when>
            <otherwise>
                t.REMARK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByZoneId != null and ${joiner}fkByZoneId.rid != null">
                t.ZONE_ID = #{${joiner}fkByZoneId.rid},
            </when>
            <otherwise>
                t.ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}unitName != null">
                t.UNIT_NAME = #{${joiner}unitName},
            </when>
            <otherwise>
                t.UNIT_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}creditCode != null">
                t.CREDIT_CODE = #{${joiner}creditCode},
            </when>
            <otherwise>
                t.CREDIT_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}regAddr != null">
                t.REG_ADDR = #{${joiner}regAddr},
            </when>
            <otherwise>
                t.REG_ADDR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}workAddr != null">
                t.WORK_ADDR = #{${joiner}workAddr},
            </when>
            <otherwise>
                t.WORK_ADDR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByEnterpriseScaleId != null and ${joiner}fkByEnterpriseScaleId.rid != null">
                t.ENTERPRISE_SCALE_ID = #{${joiner}fkByEnterpriseScaleId.rid},
            </when>
            <otherwise>
                t.ENTERPRISE_SCALE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByIndustryCateId != null and ${joiner}fkByIndustryCateId.rid != null">
                t.INDUSTRY_CATE_ID = #{${joiner}fkByIndustryCateId.rid},
            </when>
            <otherwise>
                t.INDUSTRY_CATE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByEconomicId != null and ${joiner}fkByEconomicId.rid != null">
                t.ECONOMIC_ID = #{${joiner}fkByEconomicId.rid},
            </when>
            <otherwise>
                t.ECONOMIC_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fillMan != null">
                t.FILL_MAN = #{${joiner}fillMan},
            </when>
            <otherwise>
                t.FILL_MAN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fillPhone != null">
                t.FILL_PHONE = #{${joiner}fillPhone},
            </when>
            <otherwise>
                t.FILL_PHONE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}legalPerson != null">
                t.LEGAL_PERSON = #{${joiner}legalPerson},
            </when>
            <otherwise>
                t.LEGAL_PERSON = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}legalPersonPhone != null">
                t.LEGAL_PERSON_PHONE = #{${joiner}legalPersonPhone},
            </when>
            <otherwise>
                t.LEGAL_PERSON_PHONE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkManager != null">
                t.LINK_MANAGER = #{${joiner}linkManager},
            </when>
            <otherwise>
                t.LINK_MANAGER = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkPhone != null">
                t.LINK_PHONE = #{${joiner}linkPhone},
            </when>
            <otherwise>
                t.LINK_PHONE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}empNum != null">
                t.EMP_NUM = #{${joiner}empNum},
            </when>
            <otherwise>
                t.EMP_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}externalNum != null">
                t.EXTERNAL_NUM = #{${joiner}externalNum},
            </when>
            <otherwise>
                t.EXTERNAL_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}victimsNum != null">
                t.VICTIMS_NUM = #{${joiner}victimsNum},
            </when>
            <otherwise>
                t.VICTIMS_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}occupationalDiseasesNum != null">
                t.OCCUPATIONAL_DISEASES_NUM = #{${joiner}occupationalDiseasesNum},
            </when>
            <otherwise>
                t.OCCUPATIONAL_DISEASES_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifBranch != null">
                t.IF_BRANCH = #{${joiner}ifBranch},
            </when>
            <otherwise>
                t.IF_BRANCH = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}parentUnitUuid != null">
                t.PARENT_UNIT_UUID = #{${joiner}parentUnitUuid},
            </when>
            <otherwise>
                t.PARENT_UNIT_UUID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifWarProduct != null">
                t.IF_WAR_PRODUCT = #{${joiner}ifWarProduct},
            </when>
            <otherwise>
                t.IF_WAR_PRODUCT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}operationStatus != null">
                t.OPERATION_STATUS = #{${joiner}operationStatus},
            </when>
            <otherwise>
                t.OPERATION_STATUS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}parentUnitName != null">
                t.PARENT_UNIT_NAME = #{${joiner}parentUnitName},
            </when>
            <otherwise>
                t.PARENT_UNIT_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}parentUnitCredit != null">
                t.PARENT_UNIT_CREDIT = #{${joiner}parentUnitCredit},
            </when>
            <otherwise>
                t.PARENT_UNIT_CREDIT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByParentZoneId != null and ${joiner}fkByParentZoneId.rid != null">
                t.PARENT_ZONE_ID = #{${joiner}fkByParentZoneId.rid},
            </when>
            <otherwise>
                t.PARENT_ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}crptId != null">
                t.CRPT_ID = #{${joiner}crptId},
            </when>
            <otherwise>
                t.CRPT_ID = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITBASICINFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITBASICINFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZY_UNITBASICINFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdZyUnitbasicinfo">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_ZY_UNITBASICINFO_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_ZY_UNITBASICINFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            UUID,
            RECORD_UUID,
            DECLARE_YEAR,
            DECLARE_TYPE,
            DECLARE_STATUS,
            DECLARE_DATE,
            APPROVE_DATE,
            REASON_ID,
            REMARK,
            ZONE_ID,
            UNIT_NAME,
            CREDIT_CODE,
            REG_ADDR,
            WORK_ADDR,
            ENTERPRISE_SCALE_ID,
            INDUSTRY_CATE_ID,
            ECONOMIC_ID,
            FILL_MAN,
            FILL_PHONE,
            LEGAL_PERSON,
            LEGAL_PERSON_PHONE,
            LINK_MANAGER,
            LINK_PHONE,
            EMP_NUM,
            EXTERNAL_NUM,
            VICTIMS_NUM,
            OCCUPATIONAL_DISEASES_NUM,
            IF_BRANCH,
            PARENT_UNIT_UUID,
            IF_WAR_PRODUCT,
            OPERATION_STATUS,
            PARENT_UNIT_NAME,
            PARENT_UNIT_CREDIT,
            PARENT_ZONE_ID,
            CRPT_ID,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{uuid},
            #{recordUuid},
            #{declareYear},
            #{declareType},
            #{declareStatus},
            #{declareDate},
            #{approveDate},
            #{fkByReasonId.rid},
            #{remark},
            #{fkByZoneId.rid},
            #{unitName},
            #{creditCode},
            #{regAddr},
            #{workAddr},
            #{fkByEnterpriseScaleId.rid},
            #{fkByIndustryCateId.rid},
            #{fkByEconomicId.rid},
            #{fillMan},
            #{fillPhone},
            #{legalPerson},
            #{legalPersonPhone},
            #{linkManager},
            #{linkPhone},
            #{empNum},
            #{externalNum},
            #{victimsNum},
            #{occupationalDiseasesNum},
            #{ifBranch},
            #{parentUnitUuid},
            #{ifWarProduct},
            #{operationStatus},
            #{parentUnitName},
            #{parentUnitCredit},
            #{fkByParentZoneId.rid},
            #{crptId},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_ZY_UNITBASICINFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            UUID,
            RECORD_UUID,
            DECLARE_YEAR,
            DECLARE_TYPE,
            DECLARE_STATUS,
            DECLARE_DATE,
            APPROVE_DATE,
            REASON_ID,
            REMARK,
            ZONE_ID,
            UNIT_NAME,
            CREDIT_CODE,
            REG_ADDR,
            WORK_ADDR,
            ENTERPRISE_SCALE_ID,
            INDUSTRY_CATE_ID,
            ECONOMIC_ID,
            FILL_MAN,
            FILL_PHONE,
            LEGAL_PERSON,
            LEGAL_PERSON_PHONE,
            LINK_MANAGER,
            LINK_PHONE,
            EMP_NUM,
            EXTERNAL_NUM,
            VICTIMS_NUM,
            OCCUPATIONAL_DISEASES_NUM,
            IF_BRANCH,
            PARENT_UNIT_UUID,
            IF_WAR_PRODUCT,
            OPERATION_STATUS,
            PARENT_UNIT_NAME,
            PARENT_UNIT_CREDIT,
            PARENT_ZONE_ID,
            CRPT_ID,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_ZY_UNITBASICINFO_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.uuid != null">
                            #{item.uuid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.recordUuid != null">
                            #{item.recordUuid} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.declareYear != null">
                            #{item.declareYear} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.declareType != null">
                            #{item.declareType} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.declareStatus != null">
                            #{item.declareStatus} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.declareDate != null">
                            #{item.declareDate} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.approveDate != null">
                            #{item.approveDate} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByReasonId != null and item.fkByReasonId.rid != null">
                            #{item.fkByReasonId.rid} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.remark != null">
                            #{item.remark} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByZoneId != null and item.fkByZoneId.rid != null">
                            #{item.fkByZoneId.rid} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.unitName != null">
                            #{item.unitName} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.creditCode != null">
                            #{item.creditCode} AS C16,
                        </when>
                        <otherwise>
                            NULL AS C16,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.regAddr != null">
                            #{item.regAddr} AS C17,
                        </when>
                        <otherwise>
                            NULL AS C17,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.workAddr != null">
                            #{item.workAddr} AS C18,
                        </when>
                        <otherwise>
                            NULL AS C18,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByEnterpriseScaleId != null and item.fkByEnterpriseScaleId.rid != null">
                            #{item.fkByEnterpriseScaleId.rid} AS C19,
                        </when>
                        <otherwise>
                            NULL AS C19,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByIndustryCateId != null and item.fkByIndustryCateId.rid != null">
                            #{item.fkByIndustryCateId.rid} AS C20,
                        </when>
                        <otherwise>
                            NULL AS C20,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByEconomicId != null and item.fkByEconomicId.rid != null">
                            #{item.fkByEconomicId.rid} AS C21,
                        </when>
                        <otherwise>
                            NULL AS C21,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fillMan != null">
                            #{item.fillMan} AS C22,
                        </when>
                        <otherwise>
                            NULL AS C22,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fillPhone != null">
                            #{item.fillPhone} AS C23,
                        </when>
                        <otherwise>
                            NULL AS C23,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.legalPerson != null">
                            #{item.legalPerson} AS C24,
                        </when>
                        <otherwise>
                            NULL AS C24,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.legalPersonPhone != null">
                            #{item.legalPersonPhone} AS C25,
                        </when>
                        <otherwise>
                            NULL AS C25,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.linkManager != null">
                            #{item.linkManager} AS C26,
                        </when>
                        <otherwise>
                            NULL AS C26,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.linkPhone != null">
                            #{item.linkPhone} AS C27,
                        </when>
                        <otherwise>
                            NULL AS C27,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.empNum != null">
                            #{item.empNum} AS C28,
                        </when>
                        <otherwise>
                            NULL AS C28,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.externalNum != null">
                            #{item.externalNum} AS C29,
                        </when>
                        <otherwise>
                            NULL AS C29,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.victimsNum != null">
                            #{item.victimsNum} AS C30,
                        </when>
                        <otherwise>
                            NULL AS C30,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.occupationalDiseasesNum != null">
                            #{item.occupationalDiseasesNum} AS C31,
                        </when>
                        <otherwise>
                            NULL AS C31,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifBranch != null">
                            #{item.ifBranch} AS C32,
                        </when>
                        <otherwise>
                            NULL AS C32,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.parentUnitUuid != null">
                            #{item.parentUnitUuid} AS C33,
                        </when>
                        <otherwise>
                            NULL AS C33,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifWarProduct != null">
                            #{item.ifWarProduct} AS C34,
                        </when>
                        <otherwise>
                            NULL AS C34,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.operationStatus != null">
                            #{item.operationStatus} AS C35,
                        </when>
                        <otherwise>
                            NULL AS C35,
                        </otherwise>
                    </choose>
                t.PARENT_UNIT_NAME,t.PARENT_UNIT_CREDIT,t.PARENT_ZONE_ID,t.CRPT_ID,
                <choose>
                    <when test="item.parentUnitName != null">
                        #{item.parentUnitName} AS C36,
                    </when>
                    <otherwise>
                        NULL AS C36,
                    </otherwise>
                </choose>
                <choose>
                    <when test="item.parentUnitCredit != null">
                        #{item.parentUnitCredit} AS C37,
                    </when>
                    <otherwise>
                        NULL AS C37,
                    </otherwise>
                </choose>
                <choose>
                    <when test="item.fkByParentZoneId != null and item.fkByParentZoneId.rid != null">
                        #{item.fkByParentZoneId.rid} AS C38,
                    </when>
                    <otherwise>
                        NULL AS C38,
                    </otherwise>
                </choose>
                <choose>
                    <when test="item.crptId != null">
                        #{item.crptId} AS C39,
                    </when>
                    <otherwise>
                        NULL AS C39,
                    </otherwise>
                </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZyUnitbasicinfo" >
        update TD_ZY_UNITBASICINFO t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_ZY_UNITBASICINFO t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZyUnitbasicinfo">
        delete from TD_ZY_UNITBASICINFO
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwTjorginfoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwTjorginfo">
        <result column="RID" property="rid"/>
        <result column="CREATE_DATE" property="createDate"/>
        <result column="CREATE_MANID" property="createManid"/>
        <result column="ORG_ID" property="fkByOrgId.rid"/>
        <result column="ORG_NAME" property="orgName"/>
        <result column="ORG_ADDR" property="orgAddr"/>
        <result column="ORG_FZ" property="orgFz"/>
        <result column="ORG_FZZW" property="orgFzzw"/>
        <result column="LINK_MAN" property="linkMan"/>
        <result column="LINK_MB" property="linkMb"/>
        <result column="LINK_TEL" property="linkTel"/>
        <result column="FAX" property="fax"/>
        <result column="ZIPCODE" property="zipcode"/>
        <result column="EMAIL" property="email"/>
        <result column="CERT_NO" property="certNo"/>
        <result column="FIRST_GETDAY" property="firstGetday"/>
        <result column="STATE" property="state"/>
        <result column="CANCEL_STATE" property="cancelState"/>
        <result column="CANCEL_DATE" property="cancelDate"/>
        <result column="VALID_DATE" property="validDate"/>
        <result column="CREDIT_CODE" property="creditCode"/>
        <result column="OUT_WORK_POWER" property="outWorkPower"/>
    </resultMap>

    <resultMap id="OrgWarnInfoMap" type="com.chis.modules.timer.heth.pojo.bo.warn.CheckOrgWarnBO">
        <result column="RID" property="rid"/>
        <result column="IF_FILING_FIRST" property="ifFilingFirst"/>
        <result column="FILING_YEAR" property="filingYear"/>
        <result column="FIRST_GETDAY" property="firstGetday"/>
        <result column="WARN_ID" property="warnId"/>
        <result column="WARN_DATE" property="warnDate"/>
        <collection property="itemList" ofType="int">
            <result column="ITEM_ID"/>
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,
        t.ORG_ID,t.ORG_NAME,t.ORG_ADDR,t.ORG_FZ,t.ORG_FZZW,t.LINK_MAN,t.LINK_MB,t.LINK_TEL,t.FAX,t.ZIPCODE,t.EMAIL,t.CERT_NO,t.FIRST_GETDAY,t.STATE,t.CANCEL_STATE,t.CANCEL_DATE,t.VALID_DATE,t.CREDIT_CODE,t.OUT_WORK_POWER,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}fkByOrgId != null and ${joiner}fkByOrgId.rid != null">
            and ${mAlias}ORG_ID = #{${joiner}fkByOrgId.rid}
        </if>
        <if test="${joiner}orgName != null and ${joiner}orgName != ''">
            and ${mAlias}ORG_NAME = #{${joiner}orgName}
        </if>
        <if test="${joiner}orgAddr != null and ${joiner}orgAddr != ''">
            and ${mAlias}ORG_ADDR = #{${joiner}orgAddr}
        </if>
        <if test="${joiner}orgFz != null and ${joiner}orgFz != ''">
            and ${mAlias}ORG_FZ = #{${joiner}orgFz}
        </if>
        <if test="${joiner}orgFzzw != null and ${joiner}orgFzzw != ''">
            and ${mAlias}ORG_FZZW = #{${joiner}orgFzzw}
        </if>
        <if test="${joiner}linkMan != null and ${joiner}linkMan != ''">
            and ${mAlias}LINK_MAN = #{${joiner}linkMan}
        </if>
        <if test="${joiner}linkMb != null and ${joiner}linkMb != ''">
            and ${mAlias}LINK_MB = #{${joiner}linkMb}
        </if>
        <if test="${joiner}linkTel != null and ${joiner}linkTel != ''">
            and ${mAlias}LINK_TEL = #{${joiner}linkTel}
        </if>
        <if test="${joiner}fax != null and ${joiner}fax != ''">
            and ${mAlias}FAX = #{${joiner}fax}
        </if>
        <if test="${joiner}zipcode != null and ${joiner}zipcode != ''">
            and ${mAlias}ZIPCODE = #{${joiner}zipcode}
        </if>
        <if test="${joiner}email != null and ${joiner}email != ''">
            and ${mAlias}EMAIL = #{${joiner}email}
        </if>
        <if test="${joiner}certNo != null and ${joiner}certNo != ''">
            and ${mAlias}CERT_NO = #{${joiner}certNo}
        </if>
        <if test="${joiner}firstGetday != null">
            and ${mAlias}FIRST_GETDAY = #{${joiner}firstGetday}
        </if>
        <if test="${joiner}state != null and ${joiner}state != ''">
            and ${mAlias}STATE = #{${joiner}state}
        </if>
        <if test="${joiner}cancelState != null and ${joiner}cancelState != ''">
            and ${mAlias}CANCEL_STATE = #{${joiner}cancelState}
        </if>
        <if test="${joiner}cancelDate != null">
            and ${mAlias}CANCEL_DATE = #{${joiner}cancelDate}
        </if>
        <if test="${joiner}validDate != null">
            and ${mAlias}VALID_DATE = #{${joiner}validDate}
        </if>
        <if test="${joiner}creditCode != null and ${joiner}creditCode != ''">
            and ${mAlias}CREDIT_CODE = #{${joiner}creditCode}
        </if>
        <if test="${joiner}outWorkPower != null and ${joiner}outWorkPower != ''">
            and ${mAlias}OUT_WORK_POWER = #{${joiner}outWorkPower}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByOrgId != null and ${joiner}fkByOrgId.rid != null">
                t.ORG_ID = #{${joiner}fkByOrgId.rid},
            </when>
            <otherwise>
                t.ORG_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}orgName != null and ${joiner}orgName != ''">
                t.ORG_NAME = #{${joiner}orgName},
            </when>
            <otherwise>
                t.ORG_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}orgAddr != null and ${joiner}orgAddr != ''">
                t.ORG_ADDR = #{${joiner}orgAddr},
            </when>
            <otherwise>
                t.ORG_ADDR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}orgFz != null and ${joiner}orgFz != ''">
                t.ORG_FZ = #{${joiner}orgFz},
            </when>
            <otherwise>
                t.ORG_FZ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}orgFzzw != null and ${joiner}orgFzzw != ''">
                t.ORG_FZZW = #{${joiner}orgFzzw},
            </when>
            <otherwise>
                t.ORG_FZZW = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkMan != null and ${joiner}linkMan != ''">
                t.LINK_MAN = #{${joiner}linkMan},
            </when>
            <otherwise>
                t.LINK_MAN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkMb != null and ${joiner}linkMb != ''">
                t.LINK_MB = #{${joiner}linkMb},
            </when>
            <otherwise>
                t.LINK_MB = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkTel != null and ${joiner}linkTel != ''">
                t.LINK_TEL = #{${joiner}linkTel},
            </when>
            <otherwise>
                t.LINK_TEL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fax != null and ${joiner}fax != ''">
                t.FAX = #{${joiner}fax},
            </when>
            <otherwise>
                t.FAX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}zipcode != null and ${joiner}zipcode != ''">
                t.ZIPCODE = #{${joiner}zipcode},
            </when>
            <otherwise>
                t.ZIPCODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}email != null and ${joiner}email != ''">
                t.EMAIL = #{${joiner}email},
            </when>
            <otherwise>
                t.EMAIL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}certNo != null and ${joiner}certNo != ''">
                t.CERT_NO = #{${joiner}certNo},
            </when>
            <otherwise>
                t.CERT_NO = null,
            </otherwise>
        </choose>
        t.FIRST_GETDAY = #{${joiner}firstGetday},
        <choose>
            <when test="${joiner}state != null and ${joiner}state != ''">
                t.STATE = #{${joiner}state},
            </when>
            <otherwise>
                t.STATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}cancelState != null and ${joiner}cancelState != ''">
                t.CANCEL_STATE = #{${joiner}cancelState},
            </when>
            <otherwise>
                t.CANCEL_STATE = null,
            </otherwise>
        </choose>
        t.CANCEL_DATE = #{${joiner}cancelDate},
        t.VALID_DATE = #{${joiner}validDate},
        <choose>
            <when test="${joiner}creditCode != null and ${joiner}creditCode != ''">
                t.CREDIT_CODE = #{${joiner}creditCode},
            </when>
            <otherwise>
                t.CREDIT_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}outWorkPower != null and ${joiner}outWorkPower != ''">
                t.OUT_WORK_POWER = #{${joiner}outWorkPower},
            </when>
            <otherwise>
                t.OUT_WORK_POWER = null,
            </otherwise>
        </choose>
    </sql>


    <!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from TD_ZW_TJORGINFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX
        ) WHERE 1=1
        <if test="first != null and pageSize != null">
            AND RN BETWEEN #{first} AND #{pageSize}
        </if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from TD_ZW_TJORGINFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from TD_ZW_TJORGINFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwTjorginfo">
        update TD_ZW_TJORGINFO t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZW_TJORGINFO t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZwTjorginfo">
        delete from TD_ZW_TJORGINFO
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

    <!-- ========================自定义方法====================================== -->
    <select id="selectAllOrgInfoList" resultMap="BaseResultMap">
        SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        ,t2.rid srvId
        from TD_ZW_TJORGINFO t
        LEFT JOIN ts_unit T1 ON T1.RID = T.ORG_ID
        LEFT JOIN TB_TJ_SRVORG t2 on t2.REG_ORGID = t1.rid
        where t.STATE =1
        ) ZWX
    </select>

    <select id="selectOrgWarnInfoList" resultMap="OrgWarnInfoMap">
        SELECT T.RID, T.IF_FILING_FIRST, T.FILING_YEAR, T.FIRST_GETDAY, SC.RID AS ITEM_ID, W.RID AS WARN_ID, W.WARN_DATE
        FROM TD_ZW_TJORGINFO T
                 LEFT JOIN TD_ZW_ORG_WARN_MAIN W ON T.RID = W.BUS_ID AND W.BUS_TYPE = 0
                 LEFT JOIN TD_ZW_TJORGGITEMS I ON T.RID = I.ORG_ID
                 LEFT JOIN TS_SIMPLE_CODE SC ON I.ITEM_CODE = SC.CODE_NO
                 RIGHT JOIN TS_CODE_TYPE CT ON SC.CODE_TYPE_ID = CT.RID AND CT.CODE_TYPE_NAME = '5018'
        WHERE T.STATE = 1
          AND (
            W.RID IS NULL
                OR T.LAST_SMT_DATE > W.WARN_DATE
                OR (
                T.FIRST_GETDAY &lt; #{date}
                    AND EXISTS(SELECT 1
                               FROM TD_ZW_ORG_WARN_EXCEPT E
                               WHERE E.MAIN_ID = W.RID AND E.EXCEPT_ID = #{newSimpleCodeId})
                )
            )
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdTjBhkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdTjBhk">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="BHK_CODE" property="bhkCode" />
        <result column="BHKORG_ID" property="fkByBhkorgId.rid" />
        <result column="BHKORG_NAME" property="fkByBhkorgId.unitName" />
        <result column="CRPT_ID" property="fkByCrptId.rid" />
        <result column="PERSON_ID" property="fkByPersonId.rid" />
        <result column="PERSON_NAME" property="personName" />
        <result column="SEX" property="sex" />
        <result column="IDC" property="idc" />
        <result column="BRTH" property="brth" />
        <result column="AGE" property="age" />
        <result column="ISXMRD" property="isxmrd" />
        <result column="LNKTEL" property="lnktel" />
        <result column="DPT" property="dpt" />
        <result column="WRKNUM" property="wrknum" />
        <result column="WRKLNT" property="wrklnt" />
        <result column="WRKLNTMONTH" property="wrklntmonth" />
        <result column="TCHBADRSNTIM" property="tchbadrsntim" />
        <result column="TCHBADRSNMONTH" property="tchbadrsnmonth" />
        <result column="WORK_NAME" property="workName" />
        <result column="BHK_TYPE" property="bhkType" />
        <result column="ONGUARD_STATEID" property="fkByOnguardStateid.rid" />
        <result column="BHK_DATE" property="bhkDate" />
        <result column="BHKRST" property="bhkrst" />
        <result column="MHKADV" property="mhkadv" />
        <result column="OCP_BHKRSTDES" property="ocpBhkrstdes" />
        <result column="MHKDCTNO" property="mhkdctno" />
        <result column="MHKDCT" property="mhkdct" />
        <result column="JDGDAT" property="jdgdat" />
        <result column="BADRSN" property="badrsn" />
        <result column="IF_LACKITM" property="ifLackitm" />
        <result column="IF_TARGETDIS" property="ifTargetdis" />
        <result column="IF_WRKTABU" property="ifWrktabu" />
        <result column="IF_INTEITM_LACK" property="ifInteitmLack" />
        <result column="IF_RHK" property="ifRhk" />
        <result column="PROCESS_LACK" property="processLack" />
        <result column="LACK_MSG" property="lackMsg" />
        <result column="PSN_TYPE" property="psnType" />
        <result column="LAST_BHK_CODE" property="lastBhkCode" />
        <result column="LAST_FST_BHK_CODE" property="lastFstBhkCode" />
        <result column="CRPT_NAME" property="crptName" />
        <result column="UUID" property="uuid" />
        <result column="RPT_PRINT_DATE" property="rptPrintDate" />
        <result column="IF_ITEM_SET_STD" property="ifItemSetStd" />
        <result column="SET_STD_MSG" property="setStdMsg" />
        <result column="IF_REPORT_INTIME" property="ifReportIntime" />
        <result column="IF_INTO_ZDZYB_ANALY" property="ifIntoZdzybAnaly" />
        <result column="CHECK_STATE" property="checkState" />
        <result column="BACK_RSN" property="backRsn" />
        <result column="NOT_CHECK_STATE" property="notCheckState" />
        <result column="NOT_CHECK_RSN" property="notCheckRsn" />
        <result column="WORK_TYPE_ID" property="fkByWorkTypeId.rid" />
        <result column="WORK_OTHER" property="workOther" />
        <result column="CARD_TYPE_ID" property="fkByCardTypeId.rid" />
        <result column="HARM_START_DATE" property="harmStartDate" />
        <result column="DEL_RSN" property="delRsn" />
        <result column="JC_TYPE" property="jcType" />
        <result column="OTHER_BADRSN" property="otherBadrsn" />
        <result column="IF_INTO_CHECK" property="ifIntoCheck" />
        <result column="IF_INDUS_TYPE_NOSTD" property="ifIndusTypeNostd" />
        <result column="IF_CRPT_SIZE_NOSTD" property="ifCrptSizeNostd" />
        <result column="IF_ABNOMAL" property="ifAbnomal" />
        <result column="STATE" property="state" />
        <result column="COUNTY_SMT_DATE" property="countySmtDate" />
        <result column="COUNTY_RST" property="countyRst" />
        <result column="COUNTY_AUDIT_ADV" property="countyAuditAdv" />
        <result column="COUNTY_CHK_ORGID" property="countyChkOrgid" />
        <result column="CITY_SMT_DATE" property="citySmtDate" />
        <result column="CITY_RST" property="cityRst" />
        <result column="CITY_AUDIT_ADV" property="cityAuditAdv" />
        <result column="CIYT_CHK_ORGID" property="ciytChkOrgid" />
        <result column="PRO_SMT_DATE" property="proSmtDate" />
        <result column="CITY_RST2" property="cityRst2" />
        <result column="PRO_AUDIT_ADV" property="proAuditAdv" />
        <result column="PRO_CHK_ORGID" property="proChkOrgid" />
        <result column="DEAL_COMPLETE_DATE" property="dealCompleteDate" />
        <result column="IF_WRK_AGE_NOSTD" property="ifWrkAgeNostd" />
        <result column="ENTRUST_CRPT_ID" property="entrustId.rid" />
        <result column="ifCityDirect" property="ifCityDirect" />
        <result column="ifProvDirect" property="ifProvDirect" />
        <result column="ZONE_GB" property="zoneGb" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.BHK_CODE,t.BHKORG_ID,t.CRPT_ID,t.PERSON_ID,t.PERSON_NAME,t.SEX,
        t.IDC,t.BRTH,t.AGE,t.ISXMRD,t.LNKTEL,t.DPT,t.WRKNUM,t.WRKLNT,t.WRKLNTMONTH,
        t.TCHBADRSNTIM,t.TCHBADRSNMONTH,t.WORK_NAME,t.BHK_TYPE,t.ONGUARD_STATEID,t.BHK_DATE,
        t.BHKRST,t.MHKADV,t.OCP_BHKRSTDES,t.MHKDCTNO,t.MHKDCT,t.JDGDAT,t.BADRSN,t.IF_LACKITM,
        t.IF_TARGETDIS,t.IF_WRKTABU,t.IF_INTEITM_LACK,t.IF_RHK,t.PROCESS_LACK,t.LACK_MSG,t.PSN_TYPE,
        t.LAST_BHK_CODE,t.LAST_FST_BHK_CODE,t.CRPT_NAME,t.UUID,t.RPT_PRINT_DATE,t.IF_ITEM_SET_STD,
        t.SET_STD_MSG,t.IF_REPORT_INTIME,t.IF_INTO_ZDZYB_ANALY,t.CHECK_STATE,t.BACK_RSN,t.NOT_CHECK_STATE,
        t.NOT_CHECK_RSN,t.WORK_TYPE_ID,t.WORK_OTHER,t.CARD_TYPE_ID,t.HARM_START_DATE,t.DEL_RSN,t.JC_TYPE,
        t.OTHER_BADRSN,t.IF_INTO_CHECK,t.IF_INDUS_TYPE_NOSTD,t.IF_CRPT_SIZE_NOSTD,t.IF_ABNOMAL,t.STATE,
        t.COUNTY_SMT_DATE,t.COUNTY_RST,t.COUNTY_AUDIT_ADV,t.COUNTY_CHK_ORGID,t.CITY_SMT_DATE,t.CITY_RST,
        t.CITY_AUDIT_ADV,t.CIYT_CHK_ORGID,t.PRO_SMT_DATE,t.CITY_RST2,t.PRO_AUDIT_ADV,t.PRO_CHK_ORGID,
        t.DEAL_COMPLETE_DATE,t.IF_WRK_AGE_NOSTD,t.ENTRUST_CRPT_ID,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}bhkCode != null and ${joiner}bhkCode != ''">
            and ${mAlias}BHK_CODE = #{${joiner}bhkCode}
        </if>
        <if test="${joiner}fkByBhkorgId != null and ${joiner}fkByBhkorgId.rid != null">
            and ${mAlias}BHKORG_ID = #{${joiner}fkByBhkorgId.rid}
        </if>
        <if test="${joiner}fkByCrptId != null and ${joiner}fkByCrptId.rid != null">
            and ${mAlias}CRPT_ID = #{${joiner}fkByCrptId.rid}
        </if>
        <if test="${joiner}fkByPersonId != null and ${joiner}fkByPersonId.rid != null">
            and ${mAlias}PERSON_ID = #{${joiner}fkByPersonId.rid}
        </if>
        <if test="${joiner}personName != null and ${joiner}personName != ''">
            and ${mAlias}PERSON_NAME = #{${joiner}personName}
        </if>
        <if test="${joiner}sex != null and ${joiner}sex != ''">
            and ${mAlias}SEX = #{${joiner}sex}
        </if>
        <if test="${joiner}idc != null and ${joiner}idc != ''">
            and ${mAlias}IDC = #{${joiner}idc}
        </if>
        <if test="${joiner}brth != null">
            and ${mAlias}BRTH = #{${joiner}brth}
        </if>
        <if test="${joiner}age != null and ${joiner}age != ''">
            and ${mAlias}AGE = #{${joiner}age}
        </if>
        <if test="${joiner}isxmrd != null and ${joiner}isxmrd != ''">
            and ${mAlias}ISXMRD = #{${joiner}isxmrd}
        </if>
        <if test="${joiner}lnktel != null and ${joiner}lnktel != ''">
            and ${mAlias}LNKTEL = #{${joiner}lnktel}
        </if>
        <if test="${joiner}dpt != null and ${joiner}dpt != ''">
            and ${mAlias}DPT = #{${joiner}dpt}
        </if>
        <if test="${joiner}wrknum != null and ${joiner}wrknum != ''">
            and ${mAlias}WRKNUM = #{${joiner}wrknum}
        </if>
        <if test="${joiner}wrklnt != null and ${joiner}wrklnt != ''">
            and ${mAlias}WRKLNT = #{${joiner}wrklnt}
        </if>
        <if test="${joiner}wrklntmonth != null and ${joiner}wrklntmonth != ''">
            and ${mAlias}WRKLNTMONTH = #{${joiner}wrklntmonth}
        </if>
        <if test="${joiner}tchbadrsntim != null and ${joiner}tchbadrsntim != ''">
            and ${mAlias}TCHBADRSNTIM = #{${joiner}tchbadrsntim}
        </if>
        <if test="${joiner}tchbadrsnmonth != null and ${joiner}tchbadrsnmonth != ''">
            and ${mAlias}TCHBADRSNMONTH = #{${joiner}tchbadrsnmonth}
        </if>
        <if test="${joiner}workName != null and ${joiner}workName != ''">
            and ${mAlias}WORK_NAME = #{${joiner}workName}
        </if>
        <if test="${joiner}bhkType != null and ${joiner}bhkType != ''">
            and ${mAlias}BHK_TYPE = #{${joiner}bhkType}
        </if>
        <if test="${joiner}fkByOnguardStateid != null and ${joiner}fkByOnguardStateid.rid != null">
            and ${mAlias}ONGUARD_STATEID = #{${joiner}fkByOnguardStateid.rid}
        </if>
        <if test="${joiner}bhkDate != null">
            and ${mAlias}BHK_DATE = #{${joiner}bhkDate}
        </if>
        <if test="${joiner}bhkrst != null">
            and ${mAlias}BHKRST = #{${joiner}bhkrst}
        </if>
        <if test="${joiner}mhkadv != null">
            and ${mAlias}MHKADV = #{${joiner}mhkadv}
        </if>
        <if test="${joiner}ocpBhkrstdes != null and ${joiner}ocpBhkrstdes != ''">
            and ${mAlias}OCP_BHKRSTDES = #{${joiner}ocpBhkrstdes}
        </if>
        <if test="${joiner}mhkdctno != null and ${joiner}mhkdctno != ''">
            and ${mAlias}MHKDCTNO = #{${joiner}mhkdctno}
        </if>
        <if test="${joiner}mhkdct != null and ${joiner}mhkdct != ''">
            and ${mAlias}MHKDCT = #{${joiner}mhkdct}
        </if>
        <if test="${joiner}jdgdat != null">
            and ${mAlias}JDGDAT = #{${joiner}jdgdat}
        </if>
        <if test="${joiner}badrsn != null and ${joiner}badrsn != ''">
            and ${mAlias}BADRSN = #{${joiner}badrsn}
        </if>
        <if test="${joiner}ifLackitm != null and ${joiner}ifLackitm != ''">
            and ${mAlias}IF_LACKITM = #{${joiner}ifLackitm}
        </if>
        <if test="${joiner}ifTargetdis != null and ${joiner}ifTargetdis != ''">
            and ${mAlias}IF_TARGETDIS = #{${joiner}ifTargetdis}
        </if>
        <if test="${joiner}ifWrktabu != null and ${joiner}ifWrktabu != ''">
            and ${mAlias}IF_WRKTABU = #{${joiner}ifWrktabu}
        </if>
        <if test="${joiner}ifInteitmLack != null and ${joiner}ifInteitmLack != ''">
            and ${mAlias}IF_INTEITM_LACK = #{${joiner}ifInteitmLack}
        </if>
        <if test="${joiner}ifRhk != null and ${joiner}ifRhk != ''">
            and ${mAlias}IF_RHK = #{${joiner}ifRhk}
        </if>
        <if test="${joiner}processLack != null and ${joiner}processLack != ''">
            and ${mAlias}PROCESS_LACK = #{${joiner}processLack}
        </if>
        <if test="${joiner}lackMsg != null and ${joiner}lackMsg != ''">
            and ${mAlias}LACK_MSG = #{${joiner}lackMsg}
        </if>
        <if test="${joiner}psnType != null and ${joiner}psnType != ''">
            and ${mAlias}PSN_TYPE = #{${joiner}psnType}
        </if>
        <if test="${joiner}lastBhkCode != null and ${joiner}lastBhkCode != ''">
            and ${mAlias}LAST_BHK_CODE = #{${joiner}lastBhkCode}
        </if>
        <if test="${joiner}lastFstBhkCode != null and ${joiner}lastFstBhkCode != ''">
            and ${mAlias}LAST_FST_BHK_CODE = #{${joiner}lastFstBhkCode}
        </if>
        <if test="${joiner}crptName != null and ${joiner}crptName != ''">
            and ${mAlias}CRPT_NAME = #{${joiner}crptName}
        </if>
        <if test="${joiner}uuid != null and ${joiner}uuid != ''">
            and ${mAlias}UUID = #{${joiner}uuid}
        </if>
        <if test="${joiner}rptPrintDate != null">
            and ${mAlias}RPT_PRINT_DATE = #{${joiner}rptPrintDate}
        </if>
        <if test="${joiner}ifItemSetStd != null and ${joiner}ifItemSetStd != ''">
            and ${mAlias}IF_ITEM_SET_STD = #{${joiner}ifItemSetStd}
        </if>
        <if test="${joiner}setStdMsg != null and ${joiner}setStdMsg != ''">
            and ${mAlias}SET_STD_MSG = #{${joiner}setStdMsg}
        </if>
        <if test="${joiner}ifReportIntime != null and ${joiner}ifReportIntime != ''">
            and ${mAlias}IF_REPORT_INTIME = #{${joiner}ifReportIntime}
        </if>
        <if test="${joiner}ifIntoZdzybAnaly != null and ${joiner}ifIntoZdzybAnaly != ''">
            and ${mAlias}IF_INTO_ZDZYB_ANALY = #{${joiner}ifIntoZdzybAnaly}
        </if>
        <if test="${joiner}checkState != null and ${joiner}checkState != ''">
            and ${mAlias}CHECK_STATE = #{${joiner}checkState}
        </if>
        <if test="${joiner}backRsn != null and ${joiner}backRsn != ''">
            and ${mAlias}BACK_RSN = #{${joiner}backRsn}
        </if>
        <if test="${joiner}notCheckState != null and ${joiner}notCheckState != ''">
            and ${mAlias}NOT_CHECK_STATE = #{${joiner}notCheckState}
        </if>
        <if test="${joiner}notCheckRsn != null and ${joiner}notCheckRsn != ''">
            and ${mAlias}NOT_CHECK_RSN = #{${joiner}notCheckRsn}
        </if>
        <if test="${joiner}fkByWorkTypeId != null and ${joiner}fkByWorkTypeId.rid != null">
            and ${mAlias}WORK_TYPE_ID = #{${joiner}fkByWorkTypeId.rid}
        </if>
        <if test="${joiner}workOther != null and ${joiner}workOther != ''">
            and ${mAlias}WORK_OTHER = #{${joiner}workOther}
        </if>
        <if test="${joiner}fkByCardTypeId != null and ${joiner}fkByCardTypeId.rid != null">
            and ${mAlias}CARD_TYPE_ID = #{${joiner}fkByCardTypeId.rid}
        </if>
        <if test="${joiner}harmStartDate != null">
            and ${mAlias}HARM_START_DATE = #{${joiner}harmStartDate}
        </if>
        <if test="${joiner}delRsn != null and ${joiner}delRsn != ''">
            and ${mAlias}DEL_RSN = #{${joiner}delRsn}
        </if>
        <if test="${joiner}jcType != null and ${joiner}jcType != ''">
            and ${mAlias}JC_TYPE = #{${joiner}jcType}
        </if>
        <if test="${joiner}otherBadrsn != null and ${joiner}otherBadrsn != ''">
            and ${mAlias}OTHER_BADRSN = #{${joiner}otherBadrsn}
        </if>
        <if test="${joiner}ifIntoCheck != null and ${joiner}ifIntoCheck != ''">
            and ${mAlias}IF_INTO_CHECK = #{${joiner}ifIntoCheck}
        </if>
        <if test="${joiner}ifIndusTypeNostd != null and ${joiner}ifIndusTypeNostd != ''">
            and ${mAlias}IF_INDUS_TYPE_NOSTD = #{${joiner}ifIndusTypeNostd}
        </if>
        <if test="${joiner}ifCrptSizeNostd != null and ${joiner}ifCrptSizeNostd != ''">
            and ${mAlias}IF_CRPT_SIZE_NOSTD = #{${joiner}ifCrptSizeNostd}
        </if>
        <if test="${joiner}ifAbnomal != null and ${joiner}ifAbnomal != ''">
            and ${mAlias}IF_ABNOMAL = #{${joiner}ifAbnomal}
        </if>
        <if test="${joiner}state != null and ${joiner}state != ''">
            and ${mAlias}STATE = #{${joiner}state}
        </if>
        <if test="${joiner}countySmtDate != null">
            and ${mAlias}COUNTY_SMT_DATE = #{${joiner}countySmtDate}
        </if>
        <if test="${joiner}countyRst != null and ${joiner}countyRst != ''">
            and ${mAlias}COUNTY_RST = #{${joiner}countyRst}
        </if>
        <if test="${joiner}countyAuditAdv != null and ${joiner}countyAuditAdv != ''">
            and ${mAlias}COUNTY_AUDIT_ADV = #{${joiner}countyAuditAdv}
        </if>
        <if test="${joiner}countyChkOrgid != null and ${joiner}countyChkOrgid != ''">
            and ${mAlias}COUNTY_CHK_ORGID = #{${joiner}countyChkOrgid}
        </if>
        <if test="${joiner}citySmtDate != null">
            and ${mAlias}CITY_SMT_DATE = #{${joiner}citySmtDate}
        </if>
        <if test="${joiner}cityRst != null and ${joiner}cityRst != ''">
            and ${mAlias}CITY_RST = #{${joiner}cityRst}
        </if>
        <if test="${joiner}cityAuditAdv != null and ${joiner}cityAuditAdv != ''">
            and ${mAlias}CITY_AUDIT_ADV = #{${joiner}cityAuditAdv}
        </if>
        <if test="${joiner}ciytChkOrgid != null and ${joiner}ciytChkOrgid != ''">
            and ${mAlias}CIYT_CHK_ORGID = #{${joiner}ciytChkOrgid}
        </if>
        <if test="${joiner}proSmtDate != null">
            and ${mAlias}PRO_SMT_DATE = #{${joiner}proSmtDate}
        </if>
        <if test="${joiner}cityRst2 != null and ${joiner}cityRst2 != ''">
            and ${mAlias}CITY_RST2 = #{${joiner}cityRst2}
        </if>
        <if test="${joiner}proAuditAdv != null and ${joiner}proAuditAdv != ''">
            and ${mAlias}PRO_AUDIT_ADV = #{${joiner}proAuditAdv}
        </if>
        <if test="${joiner}proChkOrgid != null and ${joiner}proChkOrgid != ''">
            and ${mAlias}PRO_CHK_ORGID = #{${joiner}proChkOrgid}
        </if>
        <if test="${joiner}dealCompleteDate != null">
            and ${mAlias}DEAL_COMPLETE_DATE = #{${joiner}dealCompleteDate}
        </if>
        <if test="${joiner}ifWrkAgeNostd != null">
            and ${mAlias}IF_WRK_AGE_NOSTD = #{${joiner}ifWrkAgeNostd}
        </if>
        <if test="${joiner}entrustId != null and ${joiner}entrustId.rid != null">
            and ${mAlias}ENTRUST_CRPT_ID = #{${joiner}entrustId.rid}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
        t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}bhkCode != null and ${joiner}bhkCode != ''">
                t.BHK_CODE = #{${joiner}bhkCode},
            </when>
            <otherwise>
                t.BHK_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByBhkorgId != null and ${joiner}fkByBhkorgId.rid != null">
                t.BHKORG_ID = #{${joiner}fkByBhkorgId.rid},
            </when>
            <otherwise>
                t.BHKORG_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCrptId != null and ${joiner}fkByCrptId.rid != null">
                t.CRPT_ID = #{${joiner}fkByCrptId.rid},
            </when>
            <otherwise>
                t.CRPT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByPersonId != null and ${joiner}fkByPersonId.rid != null">
                t.PERSON_ID = #{${joiner}fkByPersonId.rid},
            </when>
            <otherwise>
                t.PERSON_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}personName != null and ${joiner}personName != ''">
                t.PERSON_NAME = #{${joiner}personName},
            </when>
            <otherwise>
                t.PERSON_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}sex != null and ${joiner}sex != ''">
                t.SEX = #{${joiner}sex},
            </when>
            <otherwise>
                t.SEX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}idc != null and ${joiner}idc != ''">
                t.IDC = #{${joiner}idc},
            </when>
            <otherwise>
                t.IDC = null,
            </otherwise>
        </choose>
        t.BRTH = #{${joiner}brth},
        <choose>
            <when test="${joiner}age != null and ${joiner}age != ''">
                t.AGE = #{${joiner}age},
            </when>
            <otherwise>
                t.AGE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}isxmrd != null and ${joiner}isxmrd != ''">
                t.ISXMRD = #{${joiner}isxmrd},
            </when>
            <otherwise>
                t.ISXMRD = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lnktel != null and ${joiner}lnktel != ''">
                t.LNKTEL = #{${joiner}lnktel},
            </when>
            <otherwise>
                t.LNKTEL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dpt != null and ${joiner}dpt != ''">
                t.DPT = #{${joiner}dpt},
            </when>
            <otherwise>
                t.DPT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}wrknum != null and ${joiner}wrknum != ''">
                t.WRKNUM = #{${joiner}wrknum},
            </when>
            <otherwise>
                t.WRKNUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}wrklnt != null and ${joiner}wrklnt != ''">
                t.WRKLNT = #{${joiner}wrklnt},
            </when>
            <otherwise>
                t.WRKLNT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}wrklntmonth != null and ${joiner}wrklntmonth != ''">
                t.WRKLNTMONTH = #{${joiner}wrklntmonth},
            </when>
            <otherwise>
                t.WRKLNTMONTH = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}tchbadrsntim != null and ${joiner}tchbadrsntim != ''">
                t.TCHBADRSNTIM = #{${joiner}tchbadrsntim},
            </when>
            <otherwise>
                t.TCHBADRSNTIM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}tchbadrsnmonth != null and ${joiner}tchbadrsnmonth != ''">
                t.TCHBADRSNMONTH = #{${joiner}tchbadrsnmonth},
            </when>
            <otherwise>
                t.TCHBADRSNMONTH = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}workName != null and ${joiner}workName != ''">
                t.WORK_NAME = #{${joiner}workName},
            </when>
            <otherwise>
                t.WORK_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}bhkType != null and ${joiner}bhkType != ''">
                t.BHK_TYPE = #{${joiner}bhkType},
            </when>
            <otherwise>
                t.BHK_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByOnguardStateid != null and ${joiner}fkByOnguardStateid.rid != null">
                t.ONGUARD_STATEID = #{${joiner}fkByOnguardStateid.rid},
            </when>
            <otherwise>
                t.ONGUARD_STATEID = null,
            </otherwise>
        </choose>
        t.BHK_DATE = #{${joiner}bhkDate},
        t.BHKRST = #{${joiner}bhkrst},
        t.MHKADV = #{${joiner}mhkadv},
        <choose>
            <when test="${joiner}ocpBhkrstdes != null and ${joiner}ocpBhkrstdes != ''">
                t.OCP_BHKRSTDES = #{${joiner}ocpBhkrstdes},
            </when>
            <otherwise>
                t.OCP_BHKRSTDES = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}mhkdctno != null and ${joiner}mhkdctno != ''">
                t.MHKDCTNO = #{${joiner}mhkdctno},
            </when>
            <otherwise>
                t.MHKDCTNO = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}mhkdct != null and ${joiner}mhkdct != ''">
                t.MHKDCT = #{${joiner}mhkdct},
            </when>
            <otherwise>
                t.MHKDCT = null,
            </otherwise>
        </choose>
        t.JDGDAT = #{${joiner}jdgdat},
        <choose>
            <when test="${joiner}badrsn != null and ${joiner}badrsn != ''">
                t.BADRSN = #{${joiner}badrsn},
            </when>
            <otherwise>
                t.BADRSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifLackitm != null and ${joiner}ifLackitm != ''">
                t.IF_LACKITM = #{${joiner}ifLackitm},
            </when>
            <otherwise>
                t.IF_LACKITM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifTargetdis != null and ${joiner}ifTargetdis != ''">
                t.IF_TARGETDIS = #{${joiner}ifTargetdis},
            </when>
            <otherwise>
                t.IF_TARGETDIS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifWrktabu != null and ${joiner}ifWrktabu != ''">
                t.IF_WRKTABU = #{${joiner}ifWrktabu},
            </when>
            <otherwise>
                t.IF_WRKTABU = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifInteitmLack != null and ${joiner}ifInteitmLack != ''">
                t.IF_INTEITM_LACK = #{${joiner}ifInteitmLack},
            </when>
            <otherwise>
                t.IF_INTEITM_LACK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifRhk != null and ${joiner}ifRhk != ''">
                t.IF_RHK = #{${joiner}ifRhk},
            </when>
            <otherwise>
                t.IF_RHK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}processLack != null and ${joiner}processLack != ''">
                t.PROCESS_LACK = #{${joiner}processLack},
            </when>
            <otherwise>
                t.PROCESS_LACK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lackMsg != null and ${joiner}lackMsg != ''">
                t.LACK_MSG = #{${joiner}lackMsg},
            </when>
            <otherwise>
                t.LACK_MSG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}psnType != null and ${joiner}psnType != ''">
                t.PSN_TYPE = #{${joiner}psnType},
            </when>
            <otherwise>
                t.PSN_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lastBhkCode != null and ${joiner}lastBhkCode != ''">
                t.LAST_BHK_CODE = #{${joiner}lastBhkCode},
            </when>
            <otherwise>
                t.LAST_BHK_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lastFstBhkCode != null and ${joiner}lastFstBhkCode != ''">
                t.LAST_FST_BHK_CODE = #{${joiner}lastFstBhkCode},
            </when>
            <otherwise>
                t.LAST_FST_BHK_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}crptName != null and ${joiner}crptName != ''">
                t.CRPT_NAME = #{${joiner}crptName},
            </when>
            <otherwise>
                t.CRPT_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}uuid != null and ${joiner}uuid != ''">
                t.UUID = #{${joiner}uuid},
            </when>
            <otherwise>
                t.UUID = null,
            </otherwise>
        </choose>
        t.RPT_PRINT_DATE = #{${joiner}rptPrintDate},
        <choose>
            <when test="${joiner}ifItemSetStd != null and ${joiner}ifItemSetStd != ''">
                t.IF_ITEM_SET_STD = #{${joiner}ifItemSetStd},
            </when>
            <otherwise>
                t.IF_ITEM_SET_STD = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}setStdMsg != null and ${joiner}setStdMsg != ''">
                t.SET_STD_MSG = #{${joiner}setStdMsg},
            </when>
            <otherwise>
                t.SET_STD_MSG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifReportIntime != null and ${joiner}ifReportIntime != ''">
                t.IF_REPORT_INTIME = #{${joiner}ifReportIntime},
            </when>
            <otherwise>
                t.IF_REPORT_INTIME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifIntoZdzybAnaly != null and ${joiner}ifIntoZdzybAnaly != ''">
                t.IF_INTO_ZDZYB_ANALY = #{${joiner}ifIntoZdzybAnaly},
            </when>
            <otherwise>
                t.IF_INTO_ZDZYB_ANALY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}checkState != null and ${joiner}checkState != ''">
                t.CHECK_STATE = #{${joiner}checkState},
            </when>
            <otherwise>
                t.CHECK_STATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}backRsn != null and ${joiner}backRsn != ''">
                t.BACK_RSN = #{${joiner}backRsn},
            </when>
            <otherwise>
                t.BACK_RSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}notCheckState != null and ${joiner}notCheckState != ''">
                t.NOT_CHECK_STATE = #{${joiner}notCheckState},
            </when>
            <otherwise>
                t.NOT_CHECK_STATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}notCheckRsn != null and ${joiner}notCheckRsn != ''">
                t.NOT_CHECK_RSN = #{${joiner}notCheckRsn},
            </when>
            <otherwise>
                t.NOT_CHECK_RSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByWorkTypeId != null and ${joiner}fkByWorkTypeId.rid != null">
                t.WORK_TYPE_ID = #{${joiner}fkByWorkTypeId.rid},
            </when>
            <otherwise>
                t.WORK_TYPE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}workOther != null and ${joiner}workOther != ''">
                t.WORK_OTHER = #{${joiner}workOther},
            </when>
            <otherwise>
                t.WORK_OTHER = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCardTypeId != null and ${joiner}fkByCardTypeId.rid != null">
                t.CARD_TYPE_ID = #{${joiner}fkByCardTypeId.rid},
            </when>
            <otherwise>
                t.CARD_TYPE_ID = null,
            </otherwise>
        </choose>
        t.HARM_START_DATE = #{${joiner}harmStartDate},
        <choose>
            <when test="${joiner}delRsn != null and ${joiner}delRsn != ''">
                t.DEL_RSN = #{${joiner}delRsn},
            </when>
            <otherwise>
                t.DEL_RSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}jcType != null and ${joiner}jcType != ''">
                t.JC_TYPE = #{${joiner}jcType},
            </when>
            <otherwise>
                t.JC_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}otherBadrsn != null and ${joiner}otherBadrsn != ''">
                t.OTHER_BADRSN = #{${joiner}otherBadrsn},
            </when>
            <otherwise>
                t.OTHER_BADRSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifIntoCheck != null and ${joiner}ifIntoCheck != ''">
                t.IF_INTO_CHECK = #{${joiner}ifIntoCheck},
            </when>
            <otherwise>
                t.IF_INTO_CHECK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifIndusTypeNostd != null and ${joiner}ifIndusTypeNostd != ''">
                t.IF_INDUS_TYPE_NOSTD = #{${joiner}ifIndusTypeNostd},
            </when>
            <otherwise>
                t.IF_INDUS_TYPE_NOSTD = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifCrptSizeNostd != null and ${joiner}ifCrptSizeNostd != ''">
                t.IF_CRPT_SIZE_NOSTD = #{${joiner}ifCrptSizeNostd},
            </when>
            <otherwise>
                t.IF_CRPT_SIZE_NOSTD = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifAbnomal != null and ${joiner}ifAbnomal != ''">
                t.IF_ABNOMAL = #{${joiner}ifAbnomal},
            </when>
            <otherwise>
                t.IF_ABNOMAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}state != null and ${joiner}state != ''">
                t.STATE = #{${joiner}state},
            </when>
            <otherwise>
                t.STATE = null,
            </otherwise>
        </choose>
        t.COUNTY_SMT_DATE = #{${joiner}countySmtDate},
        <choose>
            <when test="${joiner}countyRst != null and ${joiner}countyRst != ''">
                t.COUNTY_RST = #{${joiner}countyRst},
            </when>
            <otherwise>
                t.COUNTY_RST = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}countyAuditAdv != null and ${joiner}countyAuditAdv != ''">
                t.COUNTY_AUDIT_ADV = #{${joiner}countyAuditAdv},
            </when>
            <otherwise>
                t.COUNTY_AUDIT_ADV = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}countyChkOrgid != null and ${joiner}countyChkOrgid != ''">
                t.COUNTY_CHK_ORGID = #{${joiner}countyChkOrgid},
            </when>
            <otherwise>
                t.COUNTY_CHK_ORGID = null,
            </otherwise>
        </choose>
        t.CITY_SMT_DATE = #{${joiner}citySmtDate},
        <choose>
            <when test="${joiner}cityRst != null and ${joiner}cityRst != ''">
                t.CITY_RST = #{${joiner}cityRst},
            </when>
            <otherwise>
                t.CITY_RST = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}cityAuditAdv != null and ${joiner}cityAuditAdv != ''">
                t.CITY_AUDIT_ADV = #{${joiner}cityAuditAdv},
            </when>
            <otherwise>
                t.CITY_AUDIT_ADV = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ciytChkOrgid != null and ${joiner}ciytChkOrgid != ''">
                t.CIYT_CHK_ORGID = #{${joiner}ciytChkOrgid},
            </when>
            <otherwise>
                t.CIYT_CHK_ORGID = null,
            </otherwise>
        </choose>
        t.PRO_SMT_DATE = #{${joiner}proSmtDate},
        <choose>
            <when test="${joiner}cityRst2 != null and ${joiner}cityRst2 != ''">
                t.CITY_RST2 = #{${joiner}cityRst2},
            </when>
            <otherwise>
                t.CITY_RST2 = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}proAuditAdv != null and ${joiner}proAuditAdv != ''">
                t.PRO_AUDIT_ADV = #{${joiner}proAuditAdv},
            </when>
            <otherwise>
                t.PRO_AUDIT_ADV = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}proChkOrgid != null and ${joiner}proChkOrgid != ''">
                t.PRO_CHK_ORGID = #{${joiner}proChkOrgid},
            </when>
            <otherwise>
                t.PRO_CHK_ORGID = null,
            </otherwise>
        </choose>
        t.DEAL_COMPLETE_DATE = #{${joiner}dealCompleteDate},
        <choose>
            <when test="${joiner}ifWrkAgeNostd != null">
                t.IF_WRK_AGE_NOSTD = #{${joiner}ifWrkAgeNostd},
            </when>
            <otherwise>
                t.IF_WRK_AGE_NOSTD = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}entrustId != null and ${joiner}entrustId.rid != null">
                t.ENTRUST_CRPT_ID = #{${joiner}entrustId.rid},
            </when>
            <otherwise>
                t.ENTRUST_CRPT_ID = null,
            </otherwise>
        </choose>
    </sql>


    <!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_BHK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX
        ) WHERE 1=1
        <if test="first != null and pageSize != null">
            AND RN BETWEEN #{first} AND #{pageSize}
        </if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_BHK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_BHK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdTjBhk" >
        update TD_TJ_BHK t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_TJ_BHK t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdTjBhk">
        delete from TD_TJ_BHK
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <select id="selectBhks" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
            T2.IF_INTO_ZDZYB_ANALY AS frstIfIntoZdzybAnaly,
            T2.CHECK_STATE AS frstCheckState,
            T1.CRPT_NAME as entrustCrptName,
        </trim>
        from  TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.ENTRUST_CRPT_ID
        LEFT JOIN TD_TJ_BHK T2 ON T2.BHK_CODE = T.LAST_FST_BHK_CODE and t2.BHKORG_ID = t.BHKORG_ID
        <where>
            t.BHK_TYPE in (3,4)
            AND T1.INTER_PRC_TAG = 1
            AND NOT EXISTS (SELECT 1 FROM TD_ZWYJ_BSN_RCD T2 WHERE T2.BHK_ID = T.RID)
            <if test="startDate != null">
                AND T.RPT_PRINT_DATE &gt;= TO_DATE(#{startDate},'yyyy-MM-dd')
            </if>
        </where>
        ) ZWX
        ) WHERE 1=1
        <if test="dataSize != null">
            AND RN BETWEEN 0 AND #{dataSize}
        </if>
    </select>

    <select id="selectOutRangeBhks" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        ,t3.rid crptZoneId
        ,t3.zone_gb crptZoneCode
        from  TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.ENTRUST_CRPT_ID
        left join TD_ZWYJ_OTR_RCD t2 on t2.BHK_CODE = t.BHK_CODE and t.BHKORG_ID = t2.BHKORG_ID
        left join ts_zone t3 on t1.ZONE_ID = t3.rid
        <where>
            and t.BHK_TYPE in (3,4)
            AND T1.INTER_PRC_TAG = 1
            <if test="startDate != null and startDate != ''">
                AND T.RPT_PRINT_DATE &gt;= TO_DATE(#{startDate},'yyyy-MM-dd')
            </if>
            and t2.rid is null
        </where>
        order by t.rid desc
        ) ZWX
        ) WHERE 1=1

        <if test="dataSize != null">
            AND RN BETWEEN 0 AND #{dataSize}
        </if>
    </select>

    <!-- 查询体检主表TD_TJ_BHK 条件是体检类型BHK_TYPE为3，4 并且对应的企业TB_TJ_CRPT中新规范数据标记INTER_PRC_TAG为1 并且 在危急值处理日志TD_ZWYJ_DANGER_LOG中没有对应记录 数据返回条数有限制 若未传递数据长度 则默认一千条 -->
    <select id="selectDangerValToolBhks" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
            ,T1.RID as ENTRUST_CRPT_ID
        from TD_TJ_BHK t
        LEFT JOIN TB_TJ_CRPT T1 ON t.ENTRUST_CRPT_ID = T1.RID
        LEFT JOIN TD_ZWYJ_DANGER_LOG T2 ON t.RID = T2.BHK_ID
        WHERE T1.INTER_PRC_TAG = 1
           AND t.BHK_TYPE IN (3,4)
           AND T2.RID IS NULL
        <if test="startDate != null">
            AND t.RPT_PRINT_DATE &gt;= TO_DATE(#{startDate},'yyyy-MM-dd')
        </if>
        <if test="dataSize != null">
            AND ROWNUM &lt;=  #{dataSize}
        </if>
        <if test="dataSize == null">
            AND ROWNUM &lt;=  1000
        </if>
        ORDER BY t.RID
    </select>

    <select id="selectAuditCalculationBhks" resultType="TdTjBhk">
        SELECT
        <trim suffixOverrides=",">
            T.RID AS "rid",
            T.IF_RHK AS "ifRhk",
            T.JC_TYPE AS "jcType",
            T.PROCESS_LACK AS "processLack",
            T.IF_INTEITM_LACK AS "ifInteitmLack",
            T1.RID AS "fkByCrptId.rid",
            T1.CRPT_SIZE_ID AS "fkByCrptId.fkByCrptSizeId.rid",
            T1.INDUS_TYPE_ID AS "fkByCrptId.fkByIndusTypeId.rid",
            T2.RID AS "fkByCrptId.fkByZoneId.rid",
            T.ONGUARD_STATEID AS "fkByOnguardStateid.rid",
            T.WRKLNT AS "wrklnt",
            T.WRKLNTMONTH AS "wrklntmonth",
            T.TCHBADRSNTIM AS "tchbadrsntim",
            T.TCHBADRSNMONTH AS "tchbadrsnmonth",
            T.BRTH AS "brth",
            T.BHK_DATE AS "bhkDate",
            T3.RID AS "entrustId.rid",
            T3.CRPT_SIZE_ID AS "entrustId.fkByCrptSizeId.rid",
            T3.INDUS_TYPE_ID AS "entrustId.fkByIndusTypeId.rid",
            T3.INSTITUTION_CODE AS "entrustId.institutionCode",
            T3.ZONE_ID AS "entrustId.fkByZoneId.rid",
            T5.CODE_NAME AS "crptSizeCodeName",
            T6.CODE_NAME AS "indusTypeCodeName",
            T4.RID AS "fkByBhkorgId.rid",
            T4.UNIT_CODE AS "fkByBhkorgId.unitCode",
        </trim>
         FROM TD_TJ_BHK T
         INNER JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID
         INNER JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID
         INNER JOIN TB_TJ_CRPT T3 ON T3.RID = T.ENTRUST_CRPT_ID
         INNER JOIN TB_TJ_SRVORG T4 ON T.BHKORG_ID=T4.RID
         LEFT JOIN TS_SIMPLE_CODE T5 ON T5.RID=T3.CRPT_SIZE_ID
         LEFT JOIN TS_SIMPLE_CODE T6 ON T6.RID=T3.INDUS_TYPE_ID
         WHERE 1=1
         AND (T.IF_RHK = 1 OR (T.IF_RHK = 0 AND T.PROCESS_LACK = 1))
        <if test="ifNotCheckFsBadRsn != null and 1 == ifNotCheckFsBadRsn">
            AND T.IF_INTO_CHECK IS NULL
        </if>
        <if test="ifNotCheckFsBadRsn == null or 1 != ifNotCheckFsBadRsn">
            AND (T.IF_INTO_CHECK IS NULL OR T.IF_INTO_CHECK = 0)
        </if>
        <if test="startDate != null">
            AND T.BHK_DATE &gt;= TO_DATE(#{startDate},'yyyy-MM-dd')
        </if>
        <if test="dataSize != null and dataSize &lt; 1000">
            AND ROWNUM &lt;=  #{dataSize}
        </if>
        <if test="dataSize == null or dataSize &gt;= 1000">
            AND ROWNUM &lt;=  900
        </if>
    </select>

    <!-- 查询条件 已经测试searchPersonName不存在sql注入 -->
    <sql id="initSearchConditionSql">
        FROM TD_TJ_BHK T1
        INNER JOIN TD_TJ_PERSON T2 ON T2.RID = T1.PERSON_ID
        INNER JOIN TB_TJ_CRPT crpt ON crpt.RID = T1.CRPT_ID
        INNER JOIN TB_TJ_CRPT crpt1 ON crpt1.RID = T1.ENTRUST_CRPT_ID
        INNER JOIN TS_ZONE T6 ON T6.RID = crpt.ZONE_ID
        INNER JOIN TS_ZONE T7 ON T7.RID = crpt1.ZONE_ID
        WHERE 1=1 AND crpt.INTER_PRC_TAG = 1
        <if test="${joiner}searchPersonName != null and ${joiner}searchPersonName != '' ">
            AND T2.PERSON_NAME LIKE '%'||#{${joiner}searchPersonName}||'%' escape '\'
        </if>
        <if test="${joiner}searchPsnType != null ">
            AND T2.CARD_TYPE_ID = #{${joiner}searchPsnType}
        </if>
        <if test='${joiner}searchIDC != null and ${joiner}searchIDC != "" and ${joiner}searchPsnType != 3 '>
            AND T2.IDC = #{${joiner}searchIDC}
        </if>
        <if test='${joiner}zkBhkCode != null and ${joiner}zkBhkCode != "" '>
            AND T1.ZK_BHK_CODE = #{${joiner}zkBhkCode}
        </if>
        <if test='${joiner}searchUnitId!=null and ${joiner}searchUnitId.size()>0'>
            AND T1.BHKORG_ID in
            <foreach collection="${joiner}searchUnitId" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test='${joiner}selectAgeAnalyDetails!=null and ${joiner}selectAgeAnalyDetails.size()>0'>
            AND
            <foreach collection="${joiner}selectAgeAnalyDetails" item="item" index="index" open="(" close=")"
                     separator="or">
                (
                <if test="item.geNum == null and item.gtNum == null and null == item.leNum and null==item.ltNum ">
                    T1.AGE is null
                </if>
                <if test="item.geNum!=null">
                    T1.AGE  <![CDATA[ >= ]]> #{item.geNum}
                </if>
                <if test="item.geNum!=null and item.gtNum!=null">
                    AND
                </if>
                <if test="item.gtNum!=null">
                    T1.AGE <![CDATA[ > ]]> #{item.gtNum}
                </if>
                <if test="item.leNum!=null and (null!=item.geNum or null!=item.gtNum)">
                    AND
                </if>
                <if test="item.leNum!=null">
                    T1.AGE  <![CDATA[ <= ]]> #{item.leNum}
                </if>
                <if test="item.ltNum!=null and (null!=item.geNum or null!=item.gtNum or null!=item.leNum)">
                    AND
                </if>
                <if test="item.ltNum!=null">
                    T1.AGE <![CDATA[ < ]]> #{item.ltNum}
                </if>
                )
            </foreach>
        </if>
        <if test='${joiner}selectWorkAnalyDetails!=null and ${joiner}selectWorkAnalyDetails.size()>0'>
            AND
            <foreach collection="${joiner}selectWorkAnalyDetails" item="item" index="index" open="(" close=")"
                     separator="or">
                (
                <if test="item.leNum == null and item.ltNum == null and null == item.geNum and null==item.gtNum ">
                    T1.TCHBADRSNTIM is null
                </if>
                <if test="item.geNum!=null">
                    T1.TCHBADRSNTIM  <![CDATA[ >= ]]> #{item.geNum}
                </if>
                <if test="item.geNum!=null and item.gtNum!=null">
                    AND
                </if>
                <if test="item.gtNum!=null">
                    T1.TCHBADRSNTIM <![CDATA[ > ]]> #{item.gtNum}
                </if>
                <if test="item.leNum!=null and (null!=item.geNum or null!=item.gtNum)">
                    AND
                </if>
                <if test="item.leNum!=null">
                    T1.TCHBADRSNTIM  <![CDATA[ <= ]]> #{item.leNum}
                </if>
                <if test="item.ltNum!=null and (null!=item.geNum or null!=item.gtNum or null!=item.leNum)">
                    AND
                </if>
                <if test="item.ltNum!=null">
                    T1.TCHBADRSNTIM <![CDATA[ < ]]> #{item.ltNum}
                </if>
                )
            </foreach>
        </if>
        <if test='${joiner}searchEntrustCrptZoneGb != null and ${joiner}searchEntrustCrptZoneGb != ""'>
            AND T7.ZONE_GB LIKE #{${joiner}searchEntrustCrptZoneGb}||'%'
        </if>
        <if test='${joiner}searchEntrustCrptName != null and ${joiner}searchEntrustCrptName != ""'>
            AND crpt1.CRPT_NAME LIKE '%'||#{${joiner}searchEntrustCrptName}||'%' escape '\'
        </if>
        <if test='${joiner}searchZoneCode != null and ${joiner}searchZoneCode != ""'>
            AND T6.ZONE_GB LIKE #{${joiner}searchZoneCode}||'%'
        </if>
        <if test='${joiner}searchCrptName != null and ${joiner}searchCrptName != ""'>
            AND crpt.CRPT_NAME LIKE '%'||#{${joiner}searchCrptName}||'%' escape '\'
        </if>
        <if test='${joiner}searchBhkType!=null and ${joiner}searchBhkType.size()>0'>
            <if test='${joiner}searchBhkTypeHas3!=null and ${joiner}searchBhkTypeHas3 == true '>
                AND T1.BHK_TYPE IN (3, 4)
                AND (T1.IF_ONLY_FS IS NULL OR T1.IF_ONLY_FS = 0)
            </if>
            <if test='${joiner}searchBhkTypeHas3!=null and ${joiner}searchBhkTypeHas3 == false '>
                AND T1.BHK_TYPE IN
                <foreach collection="${joiner}searchBhkType" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test='${joiner}searchBhkType==null or ${joiner}searchBhkType.size()==0'>
            AND T1.BHK_TYPE IN (3, 4)
        </if>
        <if test="${joiner}searchStartTime != null and ${joiner}searchStartTime != ''">
            AND T1.BHK_DATE <![CDATA[ >= ]]> TO_DATE(#{${joiner}searchStartTime},'yyyy-MM-dd')
        </if>
        <if test="${joiner}searchEndTime != null and ${joiner}searchEndTime != ''">
            AND T1.BHK_DATE <![CDATA[ <= ]]> TO_DATE(#{${joiner}searchEndTime}||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test="${joiner}selectOnGuardIds != null and ${joiner}selectOnGuardIds.size()>0">
            AND T1.ONGUARD_STATEID IN
            <foreach collection="${joiner}selectOnGuardIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="${joiner}selectBadRsnIds != null and ${joiner}selectBadRsnIds.size()>0 and ${joiner}searchSelBhkrstIds != null and ${joiner}searchSelBhkrstIds.size()>0">
            AND EXISTS ( SELECT 1 FROM TD_TJ_BADRSNS BADRSN WHERE BADRSN.BHK_ID=T1.RID AND
            BADRSN.BADRSN_ID IN
            <foreach collection="${joiner}selectBadRsnIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND BADRSN.EXAM_CONCLUSION_ID IN
            <foreach collection="${joiner}searchSelBhkrstIds" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="${joiner}selectBadRsnIds != null and ${joiner}selectBadRsnIds.size()>0 and (${joiner}searchSelBhkrstIds == null or ${joiner}searchSelBhkrstIds.size()==0)">
            AND EXISTS ( SELECT 1 FROM TD_TJ_BADRSNS BADRSN WHERE BADRSN.BHK_ID=T1.RID AND
            BADRSN.BADRSN_ID IN
            <foreach collection="${joiner}selectBadRsnIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="${joiner}searchSelBhkrstIds != null and ${joiner}searchSelBhkrstIds.size()>0 and (${joiner}selectBadRsnIds == null or ${joiner}selectBadRsnIds.size()==0)">
            AND EXISTS ( SELECT 1 FROM TD_TJ_BADRSNS BS WHERE BS.BHK_ID=T1.RID AND
            BS.EXAM_CONCLUSION_ID IN
            <foreach collection="${joiner}searchSelBhkrstIds" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="${joiner}searchSelMhkrstIds != null and ${joiner}searchSelMhkrstIds.size()>0">
            AND EXISTS ( SELECT 1 FROM TD_TJ_MHKRST MR WHERE MR.BHK_ID=T1.RID AND
            MR.BHKRST_ID IN
            <foreach collection="${joiner}searchSelMhkrstIds" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="${joiner}searchItemIds != null and ${joiner}searchItemIds != ''">
            AND EXISTS ( SELECT 1 FROM TD_TJ_BHKSUB TSUB WHERE TSUB.BHK_ID=T1.RID
            AND
            <foreach collection='${joiner}searchItemIds.split(",")' item="item" index="index" open="(" close=")"
                     separator="OR">
                (TSUB.ITEM_ID =
                <trim suffixOverrides="@@0">
                    ${item}
                </trim>
                <if test='item.contains("@@0")'>
                    and TSUB.RGLTAG = 0
                </if>
                )
            </foreach>
            )
        </if>
        <if test="${joiner}searchJcType != null and ${joiner}searchJcType.size()>0">
            AND T1.JC_TYPE IN
            <foreach collection="${joiner}searchJcType" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="${joiner}startRptPrintDate != null and ${joiner}startRptPrintDate != ''">
            AND T1.RPT_PRINT_DATE <![CDATA[ >= ]]> TO_DATE(#{${joiner}startRptPrintDate},'yyyy-MM-dd')
        </if>
        <if test="${joiner}endRptPrintDate != null and ${joiner}endRptPrintDate != ''">
            AND T1.RPT_PRINT_DATE <![CDATA[ <= ]]> TO_DATE(#{${joiner}endRptPrintDate}||' 23:59:59','yyyy-MM-dd
            HH24:mi:ss')
        </if>
        <if test="${joiner}startCreateDate != null and ${joiner}startCreateDate != ''">
            AND T1.CREATE_DATE <![CDATA[ >= ]]> TO_DATE(#{${joiner}startCreateDate},'yyyy-MM-dd')
        </if>
        <if test="${joiner}endCreateDate != null and ${joiner}endCreateDate != ''">
            AND T1.CREATE_DATE <![CDATA[ <= ]]> TO_DATE(#{${joiner}endCreateDate}||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
        </if>
    </sql>

    <!--个案查询-->
    <select id="findTjPersonSearch" resultType="java.lang.Integer">
        SELECT * FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
        <choose>
        <when test="!ifAdmin and searchBhkNum != null and searchUnitId!= null">
            SELECT T.RID FROM (
            SELECT T1.RID,T1.PERSON_ID,T2.PERSON_NAME
            <include refid="initSearchConditionSql">
                <property name="joiner" value="property."/>
            </include>
             ) T
            INNER JOIN (SELECT T1.PERSON_ID,
            COUNT(T1.RID) IDCS
            FROM TD_TJ_BHK T1
            INNER JOIN TB_TJ_CRPT crpt ON crpt.RID = T1.CRPT_ID
            WHERE T1.BHK_TYPE IN (3, 4)
            AND crpt.INTER_PRC_TAG = 1
            AND T1.BHKORG_ID = #{searchUnitId}
            GROUP BY T1.PERSON_ID) A ON A.PERSON_ID = T.PERSON_ID
            WHERE 1 = 1
            AND A.IDCS >= #{searchBhkNum}
            ORDER BY T.PERSON_NAME,T.PERSON_ID
        </when>
        <otherwise>
            SELECT  T1.RID
            <include refid="initSearchConditionSql">
                <property name="joiner" value="property."/>
            </include>
            <if test="searchBhkNum!=null">
                AND T2.ARCH_NUM >= #{searchBhkNum}
            </if>
            ORDER BY T2.PERSON_NAME,T1.PERSON_ID
        </otherwise>
        </choose>
        ) ZWX
        ) WHERE 1=1
        <if test="startRow != null and endRow != null">
            AND RN BETWEEN #{startRow} AND #{endRow}
        </if>
    </select>

    <!--个案查询数量-->
    <select id="findTjPersonSearchCounts" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM (
        <choose>
            <when test="!ifAdmin and searchBhkNum != null and searchUnitId!= null">
                SELECT T.RID FROM (
                SELECT T1.RID,T1.PERSON_ID,T2.PERSON_NAME
                <include refid="initSearchConditionSql">
                    <property name="joiner" value="property."/>
                </include>
                ) T
                INNER JOIN (SELECT T1.PERSON_ID,
                COUNT(T1.RID) IDCS
                FROM TD_TJ_BHK T1
                INNER JOIN TB_TJ_CRPT crpt ON crpt.RID = T1.CRPT_ID
                WHERE T1.BHK_TYPE IN (3, 4)
                AND crpt.INTER_PRC_TAG = 1
                AND T1.BHKORG_ID = #{searchUnitId}
                GROUP BY T1.PERSON_ID) A ON A.PERSON_ID = T.PERSON_ID
                WHERE 1 = 1
                AND A.IDCS >= #{searchBhkNum}
            </when>
            <otherwise>
                SELECT  T1.RID
                <include refid="initSearchConditionSql">
                    <property name="joiner" value="property."/>
                </include>
                <if test="searchBhkNum!=null">
                    AND T2.ARCH_NUM >= #{searchBhkNum}
                </if>
            </otherwise>
        </choose>
        )
    </select>

    <!-- 通过初检rid集合 获取初检对应的最新的复检rid -->
    <select id="findLastestRhkRidByFstRid" parameterType="java.util.List" resultType="java.util.Map">
        SELECT K.FATHERID,K.RID FROM (
        SELECT T1.RID AS FATHERID,
        T.RID,
        ROW_NUMBER() OVER (PARTITION BY T.LAST_FST_BHK_CODE,T.BHKORG_ID ORDER BY T.BHK_DATE DESC ) AS RN
        FROM TD_TJ_BHK T
        INNER JOIN TD_TJ_BHK T1 ON T.BHKORG_ID = T1.BHKORG_ID AND T.LAST_FST_BHK_CODE = T1.BHK_CODE
        WHERE T1.IF_RHK = 0
        AND T.IF_RHK = 1
        <if test="mainRidList != null and mainRidList.size()>0">
            AND T1.RID IN
            <foreach collection="mainRidList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ) K WHERE K.RN = 1
    </select>

    <update id="updateChestRstBatch" parameterType="java.util.List">
        UPDATE TD_TJ_BHK SET CHEST_RESULT = #{rst} WHERE RID IN
        <foreach collection="ridList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateHearingRstBatch" parameterType="java.util.List">
        UPDATE TD_TJ_BHK SET HEARING_RST_ID = #{rst} WHERE RID IN
        <foreach collection="ridList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="queryCheckByCondition" parameterType="com.chis.modules.timer.heth.pojo.BhkCheckCondition"
            resultMap="BaseResultMap" >
        SELECT *
            FROM (SELECT *
                FROM (select T.RID,
                T6.if_city_direct as ifCityDirect,
                T6.if_prov_direct as ifProvDirect,
                T5.CRPT_NAME,
                T.PERSON_NAME,
                T4.ZONE_GB,
                <if test="checkLevel!=null and checkLevel!='' and 2==checkLevel ">
                    <if test="zoneType!=null and zoneType!='' and zoneType &gt;= 3 ">
                        CASE
                        WHEN T.STATE <![CDATA[ < ]]> 4 THEN NULL
                        WHEN T6.if_prov_direct = 1 THEN T.DEAL_COMPLETE_DATE
                        ELSE T.COUNTY_SMT_DATE END AS revDate
                    </if>
                    <if test="zoneType!=null and zoneType!='' and zoneType &lt; 3 ">
                        T.DEAL_COMPLETE_DATE AS revDate
                    </if>
                </if>
                <if test="checkLevel!=null and checkLevel!='' and 3==checkLevel ">
                    <if test="zoneType!=null and zoneType!='' and zoneType == 2 ">
                        CASE
                        WHEN T.STATE <![CDATA[ < ]]> 4 THEN NULL
                        WHEN T6.if_city_direct = 1 THEN T.COUNTY_SMT_DATE
                        ELSE T.CITY_SMT_DATE END AS revDate
                    </if>
                    <if test="zoneType!=null and zoneType!='' and zoneType == 3 ">
                        CASE WHEN T6.if_city_direct = 1 THEN T.DEAL_COMPLETE_DATE
                        WHEN T.STATE <![CDATA[ < ]]> 2 THEN NULL
                        ELSE T.COUNTY_SMT_DATE END AS revDate
                    </if>
                    <if test="zoneType!=null and zoneType!='' and zoneType != 2 and zoneType != 3 ">
                        T.DEAL_COMPLETE_DATE AS revDate
                    </if>
                </if>
                FROM TD_TJ_BHK T
                LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID
                LEFT JOIN TB_TJ_CRPT T5 ON T5.RID = T.ENTRUST_CRPT_ID
                LEFT JOIN TS_SIMPLE_CODE T2 ON T2.RID = T.ONGUARD_STATEID
                LEFT JOIN TB_TJ_SRVORG T3 ON T3.RID = T.BHKORG_ID
                LEFT JOIN TS_ZONE T4 ON T4.RID = T1.ZONE_ID
                LEFT JOIN TS_ZONE T6 ON T6.RID = T5.ZONE_ID
                WHERE 1 = 1 AND T.IF_INTO_CHECK = 1
                    <!--检查机构-->
                    <if test="searchUnitId!=null and searchUnitId!=''">
                        AND T.BHKORG_ID IN (${searchUnitId})
                    </if>
                    <!--用工单位地区-->
                    <if test="searchZoneCodeEmp!=null and searchZoneCodeEmp!=''">
                        AND T6.ZONE_GB LIKE #{searchZoneCodeEmp}||'%'
                    </if>
                    <!--用工单位企业名称-->
                    <if test="searchCrptNameEmp!=null and searchCrptNameEmp!=''">
                        AND T5.CRPT_NAME LIKE #{searchCrptNameEmp}||'%' escape '\'
                    </if>
                    <!--用工单位社会信用代码-->
                    <if test="searchCreditCodeEmp!=null and searchCreditCodeEmp!=''">
                        AND T5.INSTITUTION_CODE = #{searchCreditCodeEmp}
                    </if>
                    <!--用人单位地区-->
                    <if test="searchZoneCode!=null and searchZoneCode!=''">
                        AND T4.ZONE_GB LIKE #{searchZoneCode}||'%'
                    </if>
                    <!--用人单位企业名称-->
                    <if test="searchCrptName!=null and searchCrptName!=''">
                        AND T1.CRPT_NAME LIKE '%'|| #{searchCrptName} ||'%' escape '\'
                    </if>
                    <!--用人单位社会信用代码-->
                    <if test="searchCreditCode!=null and searchCreditCode!=''">
                        AND T1.INSTITUTION_CODE = #{searchCreditCode}
                    </if>
                    <!--人员姓名-->
                    <if test="searchPersonName!=null and searchPersonName!=''">
                        AND T.PERSON_NAME LIKE '%'|| #{searchPersonName} ||'%' escape '\'
                    </if>
                    <!--人员身份证-->
                    <if test="searchIdc!=null and searchIdc!=''">
                        AND T.IDC = #{searchIdc}
                    </if>
                    <!--体检日期-->
                    <if test="searchBhkBdate != null">
                        AND T.BHK_DATE >= #{searchBhkBdate}
                    </if>
                    <if test="searchBhkEdate != null">
                        AND T.BHK_DATE <![CDATA[<=]]> #{searchBhkEdate}
                    </if>
                    <!--在岗状态-->
                    <if test="selectOnGuardIds != null and selectOnGuardIds!=''">
                        AND T.ONGUARD_STATEID in (${selectOnGuardIds})
                    </if>
                    <!--危害因素-->
                    <if test="(selectBadRsnIds != null and selectBadRsnIds!='') or (searchSelBhkrstIds != null and searchSelBhkrstIds!='')">
                        AND EXISTS(SELECT 1 FROM TD_TJ_BADRSNS TT WHERE TT.BHK_ID = T.RID
                        <if test="selectBadRsnIds != null and selectBadRsnIds!=''">
                            AND TT.BADRSN_ID IN (${selectBadRsnIds})
                        </if>
                        <if test="searchSelBhkrstIds != null and searchSelBhkrstIds!=''">
                            AND TT.EXAM_CONCLUSION_ID IN (${searchSelBhkrstIds})
                        </if>
                        )
                    </if>
                    <!--监测类别-->
                    <if test="jcTypes != null and jcTypes!=''">
                        AND T.JC_TYPE in (${jcTypes})
                    </if>
                    <!--是否复检-->
                    <if test="ifRhks != null and ifRhks!=''">
                        AND T.IF_RHK in (${ifRhks})
                    </if>
                    <!--是否结论异常-->
                    <if test="ifAbnormals != null and ifAbnormals!=''">
                        AND T.IF_ABNOMAL in (${ifAbnormals})
                    </if>
                    <!--体检类型-->
                    <if test="searchBhkType != null and searchBhkType!=''">
                        AND T.BHK_TYPE IN (${searchBhkType})
                    </if>
                    <!--报告日期-->
                    <if test="startCreateDate != null">
                        AND T.CREATE_DATE >= #{startCreateDate}
                    </if>
                    <if test="endCreateDate != null">
                        AND T.CREATE_DATE <![CDATA[<=]]> #{endCreateDate}
                    </if>
                    <!--报告出具日期-->
                    <if test="startRptPrintDate != null">
                        AND T.RPT_PRINT_DATE >= #{startRptPrintDate}
                    </if>
                    <if test="endRptPrintDate != null">
                        AND T.RPT_PRINT_DATE <![CDATA[<=]]> #{endRptPrintDate}
                    </if>
                    <!--状态-->
                    <if test="statueStr != null and statueStr!=''">
                        AND (${statueStr})
                    </if>
                    <!--异常情况 吉林特有-->
                    <if test="searchAbnormals != null and searchAbnormals!=''">
                        AND (
                        EXISTS(
                        SELECT 1 FROM TD_TJ_BHK_ABNOMAL abnomal WHERE abnomal.BHK_ID = T.RID AND abnomal.ABNOMAL_INFO
                        LIKE '%'|| #{searchAbnormals} ||'%' )
                         OR T.LACK_MSG LIKE '%'|| #{searchAbnormals} ||'%' )
                    </if>
                    <!--是否迟报 云南特有-->
                    <if test="ifLate != null and ifLate!='' and ifLate==0  and maxDaysLateByCaseReview!=null">
                        AND TRUNC(T.CREATE_DATE) - TRUNC(T.RPT_PRINT_DATE) <![CDATA[<=]]> #{maxDaysLateByCaseReview}
                    </if>
                    <if test="ifLate != null and ifLate!='' and ifLate==1  and maxDaysLateByCaseReview!=null">
                        AND TRUNC(T.CREATE_DATE) - TRUNC(T.RPT_PRINT_DATE) > #{maxDaysLateByCaseReview}
                    </if>
                    <!--广东特性字段 质控编号-->
                    <if test="searchZkBhkCode != null and searchZkBhkCode!=''">
                        AND T.ZK_BHK_CODE = #{searchZkBhkCode}
                    </if>
            ) A
            where 1 = 1
                <if test="searchRcvBdate != null">
                    AND A.revDate >= #{searchRcvBdate}
                </if>
                <if test="searchRcvEdate != null">
                    AND A.revDate <![CDATA[<=]]> #{searchRcvEdate}
                </if>
        ) AA
        ORDER BY AA.revDate, AA.ZONE_GB, AA.CRPT_NAME, AA.PERSON_NAME

    </select>

    <update id="updateBhkBytype" parameterType="com.chis.modules.timer.heth.pojo.BhkCheckPojo">
        UPDATE TD_TJ_BHK SET STATE=#{pojo.state},
                             MODIFY_DATE=sysdate,
                             MODIFY_MANID=#{psnId},
                             <if test="pojo.index==0">
                                 COUNTY_CHECK_WAY=#{pojo.countyCheckWay},
                                 COUNTY_RST=#{pojo.countyRst},
                                 COUNTY_AUDIT_ADV=#{pojo.countyAuditAdv},
                                 COUNTY_CHK_ORGID=#{pojo.countyChkOrgid},
                                 COUNTY_SMT_DATE=TO_DATE(#{pojo.countySmtDate}, 'YYYY-MM-DD HH24:MI:SS')
                             </if>
                             <if test="pojo.index==1">
                                 COUNTY_CHECK_WAY=#{pojo.countyCheckWay},
                                 COUNTY_RST=#{pojo.countyRst},
                                 COUNTY_AUDIT_ADV=#{pojo.countyAuditAdv},
                                 COUNTY_CHK_ORGID=#{pojo.countyChkOrgid},
                                 COUNTY_SMT_DATE=TO_DATE(#{pojo.countySmtDate}, 'YYYY-MM-DD HH24:MI:SS')
                             </if>
                             <if test="pojo.index==2">
                                 CITY_RST=#{pojo.cityRst},
                                 CITY_AUDIT_ADV=#{pojo.cityAuditAdv},
                                 CIYT_CHK_ORGID=#{pojo.cityChkOrgid},
                                 CITY_SMT_DATE=TO_DATE(#{pojo.citySmtDate}, 'YYYY-MM-DD HH24:MI:SS'),
                                 CITY_CHECK_WAY=#{pojo.cityCheckWay}
                             </if>
                             <if test="pojo.index==3">
                                 CITY_RST2=#{pojo.cityRst2},
                                 PRO_AUDIT_ADV=#{pojo.proAuditAdv},
                                 PRO_CHK_ORGID=#{pojo.proChkOrgid},
                                 PRO_SMT_DATE=TO_DATE(#{pojo.proSmtDate}, 'YYYY-MM-DD HH24:MI:SS'),
                                 PRO_CHK_PSNID=#{pojo.proChkPsnid},
                                 PRO_CHECK_WAY=#{pojo.proCheckWay}
                             </if>
                         WHERE RID IN
                        <foreach collection="list" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
    </update>


    <!-- 根据条件批量查询体检记录 -->
    <select id="selectBhksByConditions" resultMap="BaseResultMap">
        SELECT 
           T.*,
           T6.if_city_direct as ifCityDirect,
           T6.if_prov_direct as ifProvDirect,
           T6.ZONE_GB,
           T4.UNIT_NAME as BHKORG_NAME
        FROM 
            TD_TJ_BHK T
            INNER JOIN TB_TJ_SRVORG T4 ON T.BHKORG_ID=T4.RID
            LEFT JOIN TB_TJ_CRPT T5 ON T5.RID = T.ENTRUST_CRPT_ID
            LEFT JOIN TS_ZONE T6 ON T6.RID = T5.ZONE_ID
        WHERE 
            (T.BHK_CODE, T4.UNIT_NAME) IN
            <foreach collection="list" item="condition" open="(" separator="," close=")">
                (#{condition.checkNo}, #{condition.checkOrg})
            </foreach>
    </select>

    <update id="updateBhkBackBytype" parameterType="com.chis.modules.timer.heth.pojo.json.BhkBatchUpdateDTO">
        UPDATE TD_TJ_BHK
        SET STATE=#{dto.pojo.state},
        MODIFY_DATE=sysdate,
        MODIFY_MANID=#{dto.psnId},
        <choose>
            <when test="dto.pojo.index == 0 or dto.pojo.index == 1">
                COUNTY_CHECK_WAY=#{dto.pojo.countyCheckWay},
                COUNTY_RST=#{dto.pojo.countyRst},
                COUNTY_AUDIT_ADV=CASE RID
                <foreach collection="dto.ridToAdvMap" item="adv" index="rid">
                    WHEN #{rid} THEN #{adv}
                </foreach>
                ELSE null END,
                COUNTY_CHK_ORGID=#{dto.pojo.countyChkOrgid},
                COUNTY_SMT_DATE=TO_DATE(#{dto.pojo.countySmtDate}, 'YYYY-MM-DD HH24:MI:SS')
            </when>
            <when test="dto.pojo.index == 2">
                CITY_RST=#{dto.pojo.cityRst},
                CITY_AUDIT_ADV=CASE RID
                <foreach collection="dto.ridToAdvMap" item="adv" index="rid">
                    WHEN #{rid} THEN #{adv}
                </foreach>
                ELSE null END,
                CIYT_CHK_ORGID=#{dto.pojo.cityChkOrgid},
                CITY_SMT_DATE=TO_DATE(#{dto.pojo.citySmtDate}, 'YYYY-MM-DD HH24:MI:SS'),
                CITY_CHECK_WAY=#{dto.pojo.cityCheckWay}
            </when>
            <when test="dto.pojo.index == 3">
                CITY_RST2=#{dto.pojo.cityRst2},
                PRO_AUDIT_ADV=CASE RID
                <foreach collection="dto.ridToAdvMap" item="adv" index="rid">
                    WHEN #{rid} THEN #{adv}
                </foreach>
                ELSE null END,
                PRO_CHK_ORGID=#{dto.pojo.proChkOrgid},
                PRO_SMT_DATE=TO_DATE(#{dto.pojo.proSmtDate}, 'YYYY-MM-DD HH24:MI:SS'),
                PRO_CHK_PSNID=#{dto.pojo.proChkPsnid},
                PRO_CHECK_WAY=#{dto.pojo.proCheckWay}
            </when>
        </choose>
        WHERE RID IN
        <foreach collection="dto.ridList" item="rid" open="(" separator="," close=")">
            #{rid}
        </foreach>

    </update>


</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.CheckOrgWarnMapper">

    <select id="findInstInfoWarnBOByOrgId" resultType="com.chis.modules.timer.heth.pojo.bo.warn.InstInfoWarnBO">
        SELECT T.INST_ID AS rid ,T.ORG_ID AS orgRid ,T2.INST_KIND_DETAL_ID as instKindId,T2.LAST_ACPT_DATE AS lastAcptDate,T2.LAST_ACPT_CIRCLE_DOT as lastAcptCircleDot
        FROM TD_ZW_TJINST T
        LEFT JOIN  TD_ZW_TJORGINFO T1 ON T.ORG_ID =T1.RID
        LEFT JOIN TD_ZW_INSTINFO T2 ON T.INST_ID = T2.RID
        LEFT JOIN TS_SIMPLE_CODE T3 ON T2.INST_STATE_ID =T3.RID
        WHERE T3.EXTENDS1 = 1
        AND T.ORG_ID IN
        <foreach collection="orgIdList" item="item" index="index" separator="," open="(" close=")" >
            <trim  suffixOverrides=",">
                #{item}
            </trim>
        </foreach>
    </select>
</mapper>

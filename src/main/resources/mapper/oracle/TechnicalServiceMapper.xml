<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TechnicalServiceMapper">

    <select id="getOcchethInfoVo" resultType="com.chis.modules.timer.heth.logic.vo.OcchethInfoDataVo">
        SELECT  DISTINCT T.RID,T.ORG_NAME as oname,T.ORG_FZ as lname,T.CERT_NO as bno,T.VALID_DATE as validDate,
                         T3.CREDIT_CODE as ocode,T4.ZONE_GB as zoneGb,T.ORG_ADDR as raddress,
                         T.LAB_ADDR as laddress,T.LINK_MAN as cname,T.LINK_MB as cphone,
                         T4.REAL_ZONE_TYPE as realZoneType,T2.RID as cardId,T.FIRST_GETDAY as issuedDate
        FROM TD_ZW_OCCHETH_INFO T
                 LEFT JOIN TD_ZW_OCCHETH_CARD T1 ON T.ORG_ID  = T1.FILL_UNIT_ID
                 LEFT JOIN TD_ZYWS_CARD_RCD T2 ON T.RID = T2.BUS_ID AND T2.BUS_TYPE = #{type}
                 LEFT JOIN TS_UNIT T3 ON T.ORG_ID = T3.RID
                 LEFT JOIN TS_ZONE T4 ON T4.RID = T3.ZONE_ID
        WHERE T.STATE = 1 AND T1.DEL_MARK = 0 AND (T2.STATE = 0 OR T2.STATE IS NULL)
        <if test="dataSize != null">
            AND ROWNUM &lt;=  #{dataSize}
        </if>
        <if test="dataSize == null">
            AND ROWNUM &lt;=  1000
        </if>
    </select>
    <select id="getOcchethItems" resultType="com.chis.modules.timer.heth.logic.vo.ItemsVo">
        SELECT T.ORG_ID as orgId, T.ITEM_CODE as itemCode,tsc.EXTENDS1 as ext
        FROM  TD_ZW_OCCHETH_ITEMS T
                  LEFT JOIN TS_SIMPLE_CODE tsc ON T.ITEM_CODE = tsc.CODE_NO
                  LEFT JOIN TS_CODE_TYPE tct ON tsc.CODE_TYPE_ID = tct.RID
        WHERE tct.CODE_TYPE_NAME = '5320'
    AND T.ORG_ID IN
        <foreach collection="ridList" item="item" index="index" separator="," open="(" close=")" >
            <trim  suffixOverrides=",">
                #{item}
            </trim>
        </foreach>
    </select>
    <select id="getSrvorgInfoVo" resultType="com.chis.modules.timer.heth.logic.vo.SrvorgInfoDataVo">
        SELECT  DISTINCT T.RID,T.ORG_NAME as oname,T.ORG_FZ as lname,T.CERT_NO as bno,T.VALID_DATE as validDate,
        T3.CREDIT_CODE as ocode,T4.ZONE_GB as zoneGb,T.ORG_ADDR as raddress,
        T.FIRST_GETDAY as firstGetday,T.LINK_MAN as cname,T.LINK_MB as cphone,
        T4.REAL_ZONE_TYPE as realZoneType,T2.RID as cardId
        FROM TD_ZW_SRVORGINFO T
        LEFT JOIN TD_ZW_SRVORG_CARD T1 ON T.ORG_ID  = T1.FILL_UNIT_ID
        LEFT JOIN TD_ZYWS_CARD_RCD T2 ON T.RID = T2.BUS_ID  AND T2.BUS_TYPE = #{type}
        LEFT JOIN TS_UNIT T3 ON T.ORG_ID = T3.RID
        LEFT JOIN TS_ZONE T4 ON T4.RID = T3.ZONE_ID
        WHERE T.STATE = 1 AND T1.DEL_MARK = 0 AND (T2.STATE = 0 OR T2.STATE IS NULL)
        <if test="dataSize != null">
            AND ROWNUM &lt;=  #{dataSize}
        </if>
        <if test="dataSize == null">
            AND ROWNUM &lt;=  1000
        </if>
    </select>
    <select id="getSrvorgItems" resultType="com.chis.modules.timer.heth.logic.vo.ItemsVo">
        SELECT T.ORG_ID as orgId, T.ITEM_CODE as itemCode,tsc.EXTENDS2 as ext
        FROM  TD_ZW_SRVORGITEMS T
        LEFT JOIN TS_SIMPLE_CODE tsc ON T.ITEM_CODE = tsc.CODE_NO
        LEFT JOIN TS_CODE_TYPE tct ON tsc.CODE_TYPE_ID = tct.RID
        WHERE tct.CODE_TYPE_NAME = '5019'
        AND T.ORG_ID IN
        <foreach collection="ridList" item="item" index="index" separator="," open="(" close=")" >
            <trim  suffixOverrides=",">
                #{item}
            </trim>
        </foreach>
    </select>
    <select id="getPsnNums" resultType="com.chis.modules.timer.heth.logic.vo.PsnNumVo">
        SELECT T.ORG_ID as orgId,count(T.EMP_ID) as count FROM  TD_ZW_SRVORGPSNS T
        WHERE T.ON_DUTY = 1
        AND T.ORG_ID IN
        <foreach collection="ridList" item="item" index="index" separator="," open="(" close=")" >
            <trim  suffixOverrides=",">
                #{item}
            </trim>
        </foreach>
        GROUP BY T.ORG_ID
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwyjOtrCrptListMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwyjOtrCrptList">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="CRPT_ID" property="fkByCrptId.rid" />
        <result column="CRPT_ZONE_ID" property="fkByCrptZoneId.rid" />
        <result column="CRPT_NAME" property="crptName" />
        <result column="CREDIT_CODE" property="creditCode" />
        <result column="BEGIN_BHK_DATE" property="beginBhkDate" />
        <result column="END_BHK_DATE" property="endBhkDate" />
        <result column="OTR_PSNS" property="otrPsns" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MAIN_ID,t.CRPT_ID,t.CRPT_ZONE_ID,t.CRPT_NAME,t.CREDIT_CODE,t.BEGIN_BHK_DATE,t.END_BHK_DATE,t.OTR_PSNS,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}fkByCrptId != null and ${joiner}fkByCrptId.rid != null">
            and ${mAlias}CRPT_ID = #{${joiner}fkByCrptId.rid}
        </if>
        <if test="${joiner}fkByCrptZoneId != null and ${joiner}fkByCrptZoneId.rid != null">
            and ${mAlias}CRPT_ZONE_ID = #{${joiner}fkByCrptZoneId.rid}
        </if>
        <if test="${joiner}crptName != null and ${joiner}crptName != ''">
            and ${mAlias}CRPT_NAME = #{${joiner}crptName}
        </if>
        <if test="${joiner}creditCode != null and ${joiner}creditCode != ''">
            and ${mAlias}CREDIT_CODE = #{${joiner}creditCode}
        </if>
        <if test="${joiner}beginBhkDate != null">
            and ${mAlias}BEGIN_BHK_DATE = #{${joiner}beginBhkDate}
        </if>
        <if test="${joiner}endBhkDate != null">
            and ${mAlias}END_BHK_DATE = #{${joiner}endBhkDate}
        </if>
        <if test="${joiner}otrPsns != null and ${joiner}otrPsns != ''">
            and ${mAlias}OTR_PSNS = #{${joiner}otrPsns}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCrptId != null and ${joiner}fkByCrptId.rid != null">
                t.CRPT_ID = #{${joiner}fkByCrptId.rid},
            </when>
            <otherwise>
                t.CRPT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCrptZoneId != null and ${joiner}fkByCrptZoneId.rid != null">
                t.CRPT_ZONE_ID = #{${joiner}fkByCrptZoneId.rid},
            </when>
            <otherwise>
                t.CRPT_ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}crptName != null and ${joiner}crptName != ''">
                t.CRPT_NAME = #{${joiner}crptName},
            </when>
            <otherwise>
                t.CRPT_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}creditCode != null and ${joiner}creditCode != ''">
                t.CREDIT_CODE = #{${joiner}creditCode},
            </when>
            <otherwise>
                t.CREDIT_CODE = null,
            </otherwise>
        </choose>
            t.BEGIN_BHK_DATE = #{${joiner}beginBhkDate},
            t.END_BHK_DATE = #{${joiner}endBhkDate},
        <choose>
            <when test="${joiner}otrPsns != null and ${joiner}otrPsns != ''">
                t.OTR_PSNS = #{${joiner}otrPsns},
            </when>
            <otherwise>
                t.OTR_PSNS = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_OTR_CRPT_LIST t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_OTR_CRPT_LIST t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZWYJ_OTR_CRPT_LIST t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwyjOtrCrptList" >
        update TD_ZWYJ_OTR_CRPT_LIST t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_ZWYJ_OTR_CRPT_LIST t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdZwyjOtrCrptList">
        delete from TD_ZWYJ_OTR_CRPT_LIST
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

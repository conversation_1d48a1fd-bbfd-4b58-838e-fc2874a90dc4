<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.BhkCalulationFileExportMapper">
    <resultMap id="bhkSubMap" type="com.chis.modules.timer.heth.logic.vo.BhkSubExportVo">
        <result column="BHK_ID" property="bhkRid"/>
        <result column="ITEM_ID" property="itemRid"/>
        <result column="ITEM_NAME" property="itemName"/>
        <result column="ITEM_RST" property="itemRst"/>
        <result column="MSRUNT" property="msrunt"/>
        <result column="RGLTAG" property="rglTagVal"/>
        <result column="RST_FLAG" property="rstFlagVal"/>
        <result column="ITEM_TAG" property="itemTag"/>
    </resultMap>
    <resultMap id="emhistoryMap" type="com.chis.modules.timer.heth.logic.vo.EmhistoryExportVo">
        <result column="BHK_ID" property="bhkRId"/>
        <result column="STASTP_DATE" property="stastpDate"/>
        <result column="UNIT_NAME" property="unitName"/>
        <result column="DEPARTMENT" property="department"/>
        <result column="DEFEND_STEP" property="defendStep"/>
        <result column="PRFWRKLOD" property="prfwrklod"/>
        <result column="PRFSHNVLU" property="prfshnvlu"/>
        <result column="PRFEXCSHN" property="prfexcshn"/>
        <result column="PRFRAYSRT2" property="prfraysrt2"/>
        <result column="FSSZL" property="fsszl"/>
        <result column="HIS_TYPE" property="hisType"/>
    </resultMap>
    <resultMap id="anamnesisMap" type="com.chis.modules.timer.heth.logic.vo.AnamnesisExportVo">
        <result column="BHK_ID" property="bhkRid"/>
        <result column="HSTNAM" property="hstnam"/>
        <result column="HSTDAT" property="hstdat"/>
        <result column="HSTUNT" property="hstunt"/>
        <result column="HSTCRUPRC" property="hstcruprc"/>
        <result column="HSTLPS" property="hstlps"/>
    </resultMap>
    <resultMap id="exmAndSymMap" type="com.chis.modules.timer.heth.logic.vo.ExmsAndSymptomExportVo">
        <result column="RID" property="bhkRid"/>
        <result column="JZS" property="jzs"/>
        <result column="GRS" property="grs"/>
        <result column="OTH" property="other"/>
        <result column="CODE_NAME" property="sym"/>
        <result column="OTHSYM" property="othsym"/>
        <result column="SMKSTA" property="smksta"/>
        <result column="SMKYERQTY" property="smkyerqty"/>
        <result column="SMKMTHQTY" property="smkmthqty"/>
        <result column="SMKDAYBLE" property="smkdayble"/>
        <result column="WINSTA" property="winsta"/>
        <result column="WINDAYMLX" property="windaymlx"/>
        <result column="WINYERQTY" property="winyerqty"/>
        <result column="MRYDAT" property="mrydat"/>
        <result column="CPLRDTCND" property="cplrdtcnd"/>
        <result column="CPLPRFHTHCND" property="cplprfhthcnd"/>
    </resultMap>
    <resultMap id="abnormalBadrsnMap" type="com.chis.modules.timer.heth.logic.vo.AbnormalBadrsnExportVo">
        <result column="BHK_ID" property="bhkRid"/>
        <result column="NOSTD_FLAG" property="nostdFlag"/>
        <result column="BADRSNRID" property="badRsnRid"/>
        <result column="BADRSNNAME" property="badRsnName"/>
        <result column="ITEMNAME" property="itemName"/>
        <result column="ITMDESC" property="itmDesc"/>
        <result column="DETER_WAY" property="deterWay"/>
    </resultMap>
    <resultMap id="baseInfoMap" type="com.chis.modules.timer.heth.logic.vo.BaseInfoExportVo">
        <result column="RID" property="bhkRid"/>
        <result column="PERSON_NAME" property="personName"/>
        <result column="ZK_BHK_CODE" property="zkBhkCode"/>
        <result column="BHK_CODE" property="bhkCode"/>
        <result column="SEX" property="sex"/>
        <result column="AGE" property="age"/>
        <result column="CARD_TYPE_ID" property="cardTypeId"/>
        <result column="IDC" property="idc"/>
        <result column="LNKTEL" property="linkTel"/>
        <result column="CRPT_NAME" property="crptName"/>
        <result column="LINKMAN2" property="linkMan2"/>
        <result column="LINKPHONE2" property="linkPhone2"/>
        <result column="FULL_NAME" property="fullName"/>
        <result column="ADDRESS" property="address"/>
        <result column="INSTITUTION_CODE" property="institutionCode"/>
        <result column="CRPT_SIZE_ID" property="crptSizeId"/>
        <result column="ECONOMY_ID" property="economyId"/>
        <result column="INDUS_TYPE_ID" property="indusTypeId"/>
        <result column="JC_TYPE" property="jcType"/>
        <result column="ONGUARD_STATEID" property="onguardStateId"/>
        <result column="DPT" property="dpt"/>
        <result column="WORK_NAME" property="workName"/>
        <result column="WRKLNT" property="wrklnt"/>
        <result column="WRKLNTMONTH" property="wrklntMonth"/>
        <result column="TCHBADRSNTIM" property="tchBadRsntim"/>
        <result column="TCHBADRSNMONTH" property="tchBadRsnMonth"/>
        <result column="BADNAME" property="badName"/>
        <result column="BHKRST_ID" property="bhkRstId"/>
        <result column="UNIT_NAME" property="unitName"/>
        <result column="BHK_DATE" property="bhkDate"/>
        <result column="RPT_PRINT_DATE" property="rptPrintDate"/>
        <result column="CREATE_DATE" property="fillDate"/>
        <result column="DEAL_COMPLETE_DATE" property="dealCompleteDate"/>
        <result column="CHKSTATE" property="chkState"/>
        <result column="IF_RHK" property="ifRhk"/>
        <result column="IF_ABNOMAL" property="ifAbnomal"/>
        <result column="IF_CRPT_SIZE_NOSTD" property="ifCrptSizeNostd"/>
        <result column="IF_INDUS_TYPE_NOSTD" property="ifIndusTypeNostd"/>
        <result column="IF_INTEITM_LACK" property="ifInteitmLack"/>
        <result column="LACK_MSG" property="lackMsg"/>
        <result column="COUNTY_SMT_DATE" property="countySmtDate"/>
        <result column="CITY_SMT_DATE" property="citySmtDate"/>
        <result column="IF_CITY_DIRECT" property="ifCityDirect"/>
        <result column="IF_PROV_DIRECT" property="ifProvDirect"/>
        <result column="MHKADV" property="mhkAdv"/>
        <result column="CONTRAINDICATION" property="contraindication"/>
        <result column="SUOCCDISEASE" property="suOccDisease"/>
        <result column="IF_WRK_AGE_NOSTD" property="ifWrkAgeNostd"/>
        <result column="COUNTY_AUDIT_ADV" property="countyAuditAdv"/>
        <result column="CITY_AUDIT_ADV" property="cityAuditAdv"/>
        <result column="PRO_SMT_DATE" property="proSmtDate"/>
        <result column="PRO_AUDIT_ADV" property="proAuditAdv"/>
        <result column="EMPCRPTNAME" property="empCrptName"/>
        <result column="EMPLINKMAN" property="empLinkMan"/>
        <result column="EMPLINKPHONE" property="empLinkPhone"/>
        <result column="EMPZONE" property="empZone"/>
        <result column="EMPADDRESS" property="empAddress"/>
        <result column="EMPINSTCODE" property="empInstItutionCode"/>
        <result column="EMPCRPTSIZE" property="empCrptSizeId"/>
        <result column="EMPECONOMY" property="empEconomyId"/>
        <result column="EMPINDUSTYPE" property="empIndusTypeId"/>
        <result column="TCHBADNAME" property="touchBadName"/>
        <result column="ACTIVEBADNAME" property="activeBadName"/>
        <result column="ERR_MSG" property="errMsg"/>
        <result column="PROTECTEQUNAME" property="protectEquName"/>
        <result column="BHK_TYPE" property="bhkType"/>
        <result column="BADRSNRSTNAME" property="badRsnRstName"/>
        <result column="WORKNO" property="workNo"/>
    </resultMap>
    <select id="findBaseInfoExportVoListByRidList" resultMap="baseInfoMap" parameterType="java.util.List">
        SELECT T.RID,
               T.PERSON_NAME,
               T.ZK_BHK_CODE,
               T.BHK_CODE,
               T.SEX,
               T.AGE,
               T.CARD_TYPE_ID,
               T.IDC,
               T.LNKTEL,
               T2.CRPT_NAME,
               M.LINKMAN2,
               M.LINKPHONE2,
               T3.FULL_NAME,
               T2.ADDRESS,
               T2.INSTITUTION_CODE,
               T2.CRPT_SIZE_ID,
               T2.ECONOMY_ID,
               T2.INDUS_TYPE_ID,
               T.JC_TYPE,
               T.ONGUARD_STATEID,
               T.DPT,
               T.WORK_NAME,
               T.WRKLNT,
               T.WRKLNTMONTH,
               T.TCHBADRSNTIM,
               T.TCHBADRSNMONTH,
               A.BADNAME,
               T10.BHKRST_ID,
               T9.UNIT_NAME,
               T.BHK_DATE,
               T.RPT_PRINT_DATE,
               T.CREATE_DATE,
               T.DEAL_COMPLETE_DATE,
               T.STATE AS CHKSTATE,
               T.IF_RHK,
               T.IF_ABNOMAL,
               T.IF_CRPT_SIZE_NOSTD,
               T.IF_INDUS_TYPE_NOSTD,
               T.IF_INTEITM_LACK,
               T.LACK_MSG,
               T.COUNTY_SMT_DATE,
               T.CITY_SMT_DATE,
               G2.IF_CITY_DIRECT,
               G2.IF_PROV_DIRECT,
               T.MHKADV,
               B.CONTRAINDICATION,
               C.SUOCCDISEASE,
               T.IF_WRK_AGE_NOSTD,
               T.COUNTY_AUDIT_ADV,
               T.CITY_AUDIT_ADV,
               T.PRO_SMT_DATE,
               T.PRO_AUDIT_ADV,
               G.CRPT_NAME AS EMPCRPTNAME,
               G1.LINKMAN2 AS EMPLINKMAN,
               G1.LINKPHONE2 AS EMPLINKPHONE,
               G2.FULL_NAME AS EMPZONE,
               G.ADDRESS AS EMPADDRESS,
               G.INSTITUTION_CODE AS EMPINSTCODE,
               G.CRPT_SIZE_ID AS EMPCRPTSIZE,
               G.ECONOMY_ID AS EMPECONOMY,
               G.INDUS_TYPE_ID AS EMPINDUSTYPE,
               K.TCHBADNAME,
               A1.ACTIVEBADNAME,
               T.ERR_MSG
              ,PE.CODE_NAME  AS PROTECTEQUNAME
              ,T.BHK_TYPE AS BHK_TYPE
            ,A2.BADRSNRSTNAME AS BADRSNRSTNAME
            , SC.CODE_NO AS WORKNO
          FROM TD_TJ_BHK T
         INNER JOIN TD_TJ_PERSON T1 ON T.PERSON_ID = T1.RID
         INNER JOIN TB_TJ_CRPT T2 ON T.CRPT_ID = T2.RID
         LEFT JOIN TB_TJ_CRPT G ON T.ENTRUST_CRPT_ID = G.RID
         LEFT JOIN TS_ZONE T3 ON T2.ZONE_ID = T3.RID
         LEFT JOIN TS_ZONE G2 ON G.ZONE_ID = G2.RID
         LEFT JOIN TB_TJ_SRVORG T9 ON T.BHKORG_ID = T9.RID
         LEFT JOIN TB_TJ_CRPT_INDEPEND M ON M.CRPT_ID = T.CRPT_ID AND M.UNIT_ID = T9.REG_ORGID AND M.BUS_TYPE = 1
         LEFT JOIN TB_TJ_CRPT_INDEPEND G1 ON G1.CRPT_ID = T.ENTRUST_CRPT_ID AND G1.UNIT_ID = T9.REG_ORGID AND G1.BUS_TYPE = 1
         LEFT JOIN TD_TJ_MHKRST T10 ON T10.BHK_ID = T.RID
         LEFT JOIN (SELECT WM_CONCAT(H2.CODE_NAME) AS BADNAME ,H.RID FROM TD_TJ_BHK H LEFT JOIN TD_TJ_BADRSNS H1 ON H.RID = H1.BHK_ID LEFT JOIN TS_SIMPLE_CODE H2 ON H2.RID = H1.BADRSN_ID GROUP BY H.RID) A ON A.RID =T.RID
         LEFT JOIN (SELECT WM_CONCAT(H2.CODE_NAME) AS ACTIVEBADNAME ,H.RID FROM TD_TJ_BHK H LEFT JOIN TD_TJ_BADRSNS H1 ON H.RID = H1.BHK_ID LEFT JOIN TS_SIMPLE_CODE H2 ON H2.RID = H1.BADRSN_ID where H1.IF_ZD_JC=1 GROUP BY H.RID) A1 ON A1.RID =T.RID
         LEFT JOIN (SELECT LISTAGG(H2.CODE_NAME || '_' || H3.CODE_NAME, '，') WITHIN GROUP (ORDER BY H2.NUM,H2.CODE_NO) AS BADRSNRSTNAME, H.RID
                FROM TD_TJ_BHK H LEFT JOIN TD_TJ_BADRSNS H1 ON H.RID = H1.BHK_ID
                LEFT JOIN TS_SIMPLE_CODE H2 ON H2.RID = H1.BADRSN_ID
                LEFT JOIN TS_SIMPLE_CODE H3 ON H3.RID = H1.EXAM_CONCLUSION_ID
                GROUP BY H.RID ) A2 ON A2.RID =T.RID
         LEFT JOIN (SELECT LISTAGG(TSC.CODE_NAME, '#@，@#') WITHIN GROUP (ORDER BY TSC.NUM) AS CONTRAINDICATION, T.RID FROM TD_TJ_BHK T LEFT JOIN TD_TJ_CONTRAINDLIST TTC ON T.RID = TTC.BHK_ID LEFT JOIN TS_SIMPLE_CODE TSC ON TSC.RID = TTC.CONTRAIND_ID GROUP BY T.RID) B ON B.RID = T.RID
         LEFT JOIN (SELECT LISTAGG(TSC.CODE_NAME, '#@，@#') WITHIN GROUP (ORDER BY TSC.NUM) AS SUOCCDISEASE, T.RID FROM TD_TJ_BHK T LEFT JOIN TD_TJ_SUPOCCDISELIST TTS ON T.RID = TTS.BHK_ID LEFT JOIN TS_SIMPLE_CODE TSC ON TSC.RID = TTS.OCC_DISEID GROUP BY T.RID) C ON C.RID = T.RID
         LEFT JOIN (SELECT WM_CONCAT(Q2.CODE_NAME) AS TCHBADNAME ,Q.RID FROM TD_TJ_BHK Q LEFT JOIN TD_TJ_TCH_BADRSNS Q1 ON Q.RID = Q1.BHK_ID LEFT JOIN TS_SIMPLE_CODE Q2 ON Q2.RID = Q1.BADRSN_ID GROUP BY Q.RID) K ON K.RID =T.RID
         LEFT JOIN TS_SIMPLE_CODE PE  ON PE.RID = T.PROTECT_EQU_ID
         LEFT JOIN TS_SIMPLE_CODE SC  ON SC.RID = T.WORK_TYPE_ID
        WHERE 1=1
        <if test="bhkRidList != null and bhkRidList.size > 0">
            AND T.RID IN
            <foreach collection="bhkRidList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>

    <select id="findAbnormalBadrsnExportVoListByBhkRidList" resultMap="abnormalBadrsnMap" parameterType="java.util.List">
        SELECT T.BHK_ID,
                T.NOSTD_FLAG,
                T3.RID AS BADRSNRID,
                T3.CODE_NAME AS BADRSNNAME,
                T4.ITEM_NAME AS ITEMNAME,
                T2.ITEM_DESC AS ITMDESC,
                T2.DETER_WAY
        FROM TD_TJ_BHK_ITEM_STD T
        INNER JOIN TD_ZW_BADRSN_ITEM T1 ON T.ITEM_STD_ID = T1.RID
        INNER JOIN TD_ZW_BADRSN_STD T2 ON T1.MAIN_ID = T2.RID
        INNER JOIN TS_SIMPLE_CODE T3 ON T3.RID = T2.BADRSN_ID
        INNER JOIN TB_TJ_ITEMS T4 ON T1.ITEM_ID = T4.RID
         WHERE 1=1
        <if test="bhkRidList != null and bhkRidList.size > 0">
            AND T.BHK_ID IN
            <foreach collection="bhkRidList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>

    <select id="findExmsAndSymExportVoListByBhkRidList" resultMap="exmAndSymMap" parameterType="java.util.List">
        SELECT T.RID, T1.JZS, T1.GRS, T1.OTH, T3.CODE_NAME, T2.OTHSYM,
            T4.CODE_NAME as SMKSTA,
            T1.SMKYERQTY,
            T1.SMKMTHQTY,
            T1.SMKDAYBLE
            ,T1.WINSTA,T1.WINDAYMLX,T1.WINYERQTY
            ,T1.MRYDAT,T1.CPLRDTCND,T1.CPLPRFHTHCND
          FROM TD_TJ_BHK T
          LEFT JOIN TD_TJ_EXMSDATA T1 ON T1.BHK_ID = T.RID
          LEFT JOIN TD_TJ_SYMPTOM T2 ON T2.BHK_ID = T.RID
          LEFT JOIN TS_SIMPLE_CODE T3 ON T2.SYM_ID = T3.RID
          LEFT JOIN TS_SIMPLE_CODE T4 ON T1.SMKSTA_ID = T4.RID
         WHERE 1 = 1
        <if test="bhkRidList != null and bhkRidList.size > 0">
            AND T.RID IN
            <foreach collection="bhkRidList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>

    <select id="findAnamnesisExportVoListByBhkRidList" resultMap="anamnesisMap" parameterType="java.util.List">
        SELECT T.BHK_ID, T.HSTNAM, T.HSTDAT, T.HSTUNT, T.HSTCRUPRC, T.HSTLPS
          FROM TD_TJ_ANAMNESIS T
         WHERE 1 = 1
        <if test="bhkRidList != null and bhkRidList.size > 0">
            AND T.BHK_ID IN
            <foreach collection="bhkRidList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
         ORDER BY T.HSTDAT, T.RID
    </select>

    <select id="findEmhistoryExportVoListByBhkRidList" resultMap="emhistoryMap" parameterType="java.util.List">
        SELECT T.BHK_ID,
               T.STASTP_DATE,
               T.UNIT_NAME,
               T.DEPARTMENT,
               T.DEFEND_STEP,
               T.PRFWRKLOD,
               T.PRFSHNVLU,
               T.PRFEXCSHN,
               T.PRFRAYSRT2,
               T.FSSZL,
               T.HIS_TYPE
          FROM TD_TJ_EMHISTORY T
         WHERE 1 = 1
        <if test="bhkRidList != null and bhkRidList.size > 0">
            AND T.BHK_ID IN
            <foreach collection="bhkRidList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
         ORDER BY T.CHKDAT,T.RID
    </select>

    <select id="findSubExportVoListByBhkRidList" resultMap="bhkSubMap" parameterType="java.util.List">
        SELECT T.BHK_ID,
               T.ITEM_ID,
               T2.ITEM_NAME,
               T.ITEM_RST,
               T.MSRUNT,
               T.RGLTAG,
               T.RST_FLAG,
               T2.ITEM_TAG
          FROM TD_TJ_BHKSUB T
          LEFT JOIN TB_TJ_ITEMS T2 ON T.ITEM_ID = T2.RID
         WHERE 1=1
        <if test="bhkRidList != null and bhkRidList.size > 0">
            AND T.BHK_ID IN
            <foreach collection="bhkRidList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>

    <!-- 个案审核查询主表rid -->
    <!-- if when 条件中的大于用gt 大于等于用gt= 小于用lt 小于等于用lt= -->
    <select id="findBhkRidListByQueryPO" resultType="java.lang.Integer"
            parameterType="com.chis.modules.timer.heth.logic.BhkAuditQueryPO">
        <if test="searchZoneCode != null and searchZoneCode != ''">
            <!-- searchZoneCode为空的时候bind会报错 所以这边bind放if里 -->
            <bind name="queryZoneCode" value="searchZoneCode+'%'"/>
        </if>
        <if test="searchCrptName != null and searchCrptName != ''">
            <bind name="queryCrptName" value="'%'+searchCrptName+'%'"/>
        </if>
        <if test="searchPersonName != null and searchPersonName != ''">
            <bind name="queryPersonName" value="'%'+searchPersonName+'%'"/>
        </if>
        <if test="searchZoneCodeEmp != null and searchZoneCodeEmp != ''">
            <bind name="queryZoneCodeEmp" value="searchZoneCodeEmp+'%'"/>
        </if>
        <if test="searchCrptNameEmp != null and searchCrptNameEmp != ''">
            <bind name="queryCrptNameEmp" value="'%'+searchCrptNameEmp+'%'"/>
        </if>
        <if test="searchAbnormals != null and searchAbnormals != ''">
            <bind name="queryAbnormals" value="'%'+searchAbnormals+'%'"/>
        </if>
        SELECT RID FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
            SELECT A.RID FROM (
            SELECT T.RID,T5.CRPT_NAME, T.PERSON_NAME,T4.ZONE_GB,
            <if test="null != checkLevel and 2 == checkLevel">
                <choose>
                    <when test="null != zoneType and zoneType lte 3">
                        CASE WHEN T.STATE <![CDATA[ < ]]> 4 THEN NULL WHEN T6.IF_PROV_DIRECT = 1 THEN T.DEAL_COMPLETE_DATE ELSE T.COUNTY_SMT_DATE END AS revDate
                    </when>
                    <otherwise>
                        T.DEAL_COMPLETE_DATE AS revDate
                    </otherwise>
                </choose>
            </if>
            <if test="null != checkLevel and 3 == checkLevel">
                <choose>
                    <when test="null != zoneType and 2 == zoneType">
                        CASE WHEN T.STATE <![CDATA[ < ]]> 4 THEN NULL WHEN T6.IF_CITY_DIRECT = 1 THEN T.COUNTY_SMT_DATE ELSE T.CITY_SMT_DATE END AS revDate
                    </when>
                    <when test="null != zoneType and 3 == zoneType">
                        CASE WHEN T6.IF_CITY_DIRECT = 1 THEN T.DEAL_COMPLETE_DATE WHEN T.STATE <![CDATA[ < ]]> 2 THEN NULL ELSE T.COUNTY_SMT_DATE END AS revDate
                    </when>
                    <otherwise>
                        T.DEAL_COMPLETE_DATE AS revDate
                    </otherwise>
                </choose>
            </if>
            <if test="null == checkLevel or (2 != checkLevel and 3 != checkLevel)">
                '' AS revDate
            </if>
            ,''
            FROM TD_TJ_BHK T
            LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID
            LEFT JOIN TB_TJ_CRPT T5 ON T5.RID = T.ENTRUST_CRPT_ID
            LEFT JOIN TS_SIMPLE_CODE T2 ON T2.RID = T.ONGUARD_STATEID
            LEFT JOIN TB_TJ_SRVORG T3 ON T3.RID = T.BHKORG_ID
            LEFT JOIN TS_ZONE T4 ON T4.RID = T1.ZONE_ID
            LEFT JOIN TS_ZONE T6 ON T6.RID = T5.ZONE_ID
            WHERE 1=1 AND T.IF_INTO_CHECK =1
            <if test="searchUnitIdList != null and searchUnitIdList.size > 0">
                AND T.BHKORG_ID IN
                <foreach collection="searchUnitIdList" item="item" index="index" separator="," open="(" close=")" >
                    <trim  suffixOverrides=",">
                        #{item}
                    </trim>
                </foreach>
            </if>
            <if test="searchZoneCodeEmp != null and searchZoneCodeEmp != ''">
                AND T6.ZONE_GB LIKE #{queryZoneCodeEmp} escape  '\'
            </if>
            <if test="searchCrptNameEmp != null and searchCrptNameEmp != ''">
                AND T5.CRPT_NAME LIKE #{queryCrptNameEmp} escape  '\'
            </if>
            <if test="searchCreditCodeEmp != null and searchCreditCodeEmp != ''">
                AND T5.INSTITUTION_CODE = #{searchCreditCodeEmp}
            </if>
            <if test="searchZoneCode != null and searchZoneCode != ''">
                AND T4.ZONE_GB LIKE #{queryZoneCode} escape  '\'
            </if>
            <if test="searchCrptName != null and searchCrptName != ''">
                AND T1.CRPT_NAME LIKE #{queryCrptName} escape  '\'
            </if>
            <if test="searchCreditCode != null and searchCreditCode != ''">
                AND T1.INSTITUTION_CODE = #{searchCreditCode}
            </if>
            <if test="searchPersonName != null and searchPersonName != ''">
                AND T.PERSON_NAME LIKE #{queryPersonName} escape  '\'
            </if>
            <if test="searchIdc != null and searchIdc != ''">
                AND T.IDC = #{searchIdc}
            </if>
            <if test="zkBhkCode != null and zkBhkCode != '' ">
                AND T.ZK_BHK_CODE = #{zkBhkCode}
            </if>
            <if test="searchBhkBdate != null and searchBhkBdate != ''">
                AND T.BHK_DATE &gt;= TO_DATE(#{searchBhkBdate},'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="searchBhkEdate != null and searchBhkEdate != ''">
                AND T.BHK_DATE &lt;= TO_DATE(#{searchBhkEdate},'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="selectOnGuardIds != null and selectOnGuardIds.size > 0">
                AND T.ONGUARD_STATEID IN
                <foreach collection="selectOnGuardIds" item="item" index="index" separator="," open="(" close=")" >
                    <trim  suffixOverrides=",">
                        #{item}
                    </trim>
                </foreach>
            </if>
            <if test="(selectBadRsnIds != null and selectBadRsnIds.size > 0) or (searchSelBhkrstIds != null and searchSelBhkrstIds.size > 0)">
                AND EXISTS(SELECT 1 FROM TD_TJ_BADRSNS TT WHERE TT.BHK_ID = T.RID
                <if test="selectBadRsnIds != null and selectBadRsnIds.size > 0">
                    AND TT.BADRSN_ID IN
                    <foreach collection="selectBadRsnIds" item="item" index="index" separator="," open="(" close=")">
                        <trim suffixOverrides=",">
                            #{item}
                        </trim>
                    </foreach>

                </if>
                <if test="searchSelBhkrstIds != null and searchSelBhkrstIds.size > 0">
                    AND TT.EXAM_CONCLUSION_ID IN
                    <foreach collection="searchSelBhkrstIds" item="item" index="index" separator="," open="(" close=")">
                        <trim suffixOverrides=",">
                            #{item}
                        </trim>
                    </foreach>
                </if>
                )
            </if>
            <if test="jcTypeList != null and jcTypeList.size > 0">
                AND T.JC_TYPE IN
                <foreach collection="jcTypeList" item="item" index="index" separator="," open="(" close=")" >
                    <trim  suffixOverrides=",">
                        #{item}
                    </trim>
                </foreach>
            </if>
            <if test="null != ifRhk or 0 == ifRhk">
                AND T.IF_RHK = #{ifRhk}
            </if>
            <if test="null != ifAbnormal or 0 == ifAbnormal">
                AND T.IF_ABNOMAL = #{ifAbnormal}
            </if>
            <if test="searchAbnormals != null and searchAbnormals != ''">
                AND ( EXISTS( SELECT 1 FROM TD_TJ_BHK_ABNOMAL abnomal WHERE abnomal.BHK_ID = T.RID
                AND abnomal.ABNOMAL_INFO LIKE #{queryAbnormals} escape '\'
                )
                OR T.LACK_MSG LIKE #{queryAbnormals} escape '\'
                )
            </if>
            <if test="stateList != null and stateList.size > 0">
                AND (
                T.STATE IN
                <foreach collection="searchStateList" item="item" index="index" separator="," open="(" close=")">
                    <trim suffixOverrides=",">
                        #{item}
                    </trim>
                </foreach>
                <if test="null != ifConZero and 1 == ifConZero">
                    OR (T.STATE = 0 AND NVL(T6.IF_CITY_DIRECT, 0) = 0)
                </if>
                <if test="null != ifConOne and 1 == ifConOne">
                    OR (T.STATE = 1 AND NVL(T6.IF_CITY_DIRECT, 0) = 0)
                </if>
                <if test="null != ifConTwo and 1 == ifConTwo">
                    OR (T.STATE = 0 AND NVL(T6.IF_CITY_DIRECT, 0) = 1)
                </if>
                <if test="null != ifConThree and 1 == ifConThree">
                    OR (T.STATE = 1 AND NVL(T6.IF_CITY_DIRECT, 0) = 1)
                </if>
                )
            </if>
            <if test="bhkTypeList != null and bhkTypeList.size > 0">
                AND T.BHK_TYPE IN
                <foreach collection="bhkTypeList" item="item" index="index" separator="," open="(" close=")" >
                    <trim  suffixOverrides=",">
                        #{item}
                    </trim>
                </foreach>
            </if>
            <if test="startCreateDate != null and startCreateDate != ''">
                AND T.CREATE_DATE &gt;= TO_DATE(#{startCreateDate},'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="endCreateDate != null and endCreateDate != ''">
                AND T.CREATE_DATE &lt;= TO_DATE(#{endCreateDate},'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="startRptPrintDate != null and startRptPrintDate != ''">
                AND T.RPT_PRINT_DATE &gt;= TO_DATE(#{startRptPrintDate},'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="endRptPrintDate != null and endRptPrintDate != ''">
                AND T.RPT_PRINT_DATE &lt;= TO_DATE(#{endRptPrintDate},'YYYY-MM-DD HH24:MI:SS')
            </if>
            ) A WHERE 1 = 1
            <if test="searchRcvBdate != null and searchRcvBdate != ''">
                AND revDate &gt;= TO_DATE(#{searchRcvBdate},'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="searchRcvEdate != null and searchRcvEdate != ''">
                AND revDate &lt;= TO_DATE(#{searchRcvEdate},'YYYY-MM-DD HH24:MI:SS')
            </if>
            ORDER BY A.revDate,A.ZONE_GB, A.CRPT_NAME, A.PERSON_NAME
        ) ZWX
        ) WHERE 1=1
        <if test="startRow != null and endRow != null">
            AND RN BETWEEN #{startRow} AND #{endRow}
        </if>
    </select>

    <!-- 个案审核查询满足条件的主表rid个数 -->
    <select id="findBhkRidCountByQueryPO" resultType="java.lang.Integer"
            parameterType="com.chis.modules.timer.heth.logic.BhkAuditQueryPO">
        <if test="searchZoneCode != null and searchZoneCode != ''">
            <!-- searchZoneCode为空的时候bind会报错 所以这边bind放if里 -->
            <bind name="queryZoneCode" value="searchZoneCode+'%'"/>
        </if>
        <if test="searchCrptName != null and searchCrptName != ''">
            <bind name="queryCrptName" value="'%'+searchCrptName+'%'"/>
        </if>
        <if test="searchPersonName != null and searchPersonName != ''">
            <bind name="queryPersonName" value="'%'+searchPersonName+'%'"/>
        </if>
        <if test="searchZoneCodeEmp != null and searchZoneCodeEmp != ''">
            <bind name="queryZoneCodeEmp" value="searchZoneCodeEmp+'%'"/>
        </if>
        <if test="searchCrptNameEmp != null and searchCrptNameEmp != ''">
            <bind name="queryCrptNameEmp" value="'%'+searchCrptNameEmp+'%'"/>
        </if>
        <if test="searchAbnormals != null and searchAbnormals != ''">
            <bind name="queryAbnormals" value="'%'+searchAbnormals+'%'"/>
        </if>
        SELECT COUNT(A.RID) FROM (
        SELECT T.RID,
        <if test="null != checkLevel and 2 == checkLevel">
            <choose>
                <when test="null != zoneType and zoneType lte 3">
                    CASE WHEN T.STATE <![CDATA[ < ]]> 4 THEN NULL WHEN T6.IF_PROV_DIRECT = 1 THEN T.DEAL_COMPLETE_DATE ELSE T.COUNTY_SMT_DATE END AS revDate
                </when>
                <otherwise>
                    T.DEAL_COMPLETE_DATE AS revDate
                </otherwise>
            </choose>
        </if>
        <if test="null != checkLevel and 3 == checkLevel">
            <choose>
                <when test="null != zoneType and 2 == zoneType">
                    CASE WHEN T.STATE <![CDATA[ < ]]> 4 THEN NULL WHEN T6.IF_CITY_DIRECT = 1 THEN T.COUNTY_SMT_DATE ELSE T.CITY_SMT_DATE END AS revDate
                </when>
                <when test="null != zoneType and 3 == zoneType">
                    CASE WHEN T6.IF_CITY_DIRECT = 1 THEN T.DEAL_COMPLETE_DATE WHEN T.STATE <![CDATA[ < ]]> 2 THEN NULL ELSE T.COUNTY_SMT_DATE END AS revDate
                </when>
                <otherwise>
                    T.DEAL_COMPLETE_DATE AS revDate
                </otherwise>
            </choose>
        </if>
        <if test="null == checkLevel or (2 != checkLevel and 3 != checkLevel)">
            '' AS revDate
        </if>
        ,''
        FROM TD_TJ_BHK T
        LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID
        LEFT JOIN TB_TJ_CRPT T5 ON T5.RID = T.ENTRUST_CRPT_ID
        LEFT JOIN TS_SIMPLE_CODE T2 ON T2.RID = T.ONGUARD_STATEID
        LEFT JOIN TB_TJ_SRVORG T3 ON T3.RID = T.BHKORG_ID
        LEFT JOIN TS_ZONE T4 ON T4.RID = T1.ZONE_ID
        LEFT JOIN TS_ZONE T6 ON T6.RID = T5.ZONE_ID
        WHERE 1=1 AND T.IF_INTO_CHECK =1
        <if test="searchUnitIdList != null and searchUnitIdList.size > 0">
            AND T.BHKORG_ID IN
            <foreach collection="searchUnitIdList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
        <if test="searchZoneCodeEmp != null and searchZoneCodeEmp != ''">
            AND T6.ZONE_GB LIKE #{queryZoneCodeEmp} escape  '\'
        </if>
        <if test="searchCrptNameEmp != null and searchCrptNameEmp != ''">
            AND T5.CRPT_NAME LIKE #{queryCrptNameEmp} escape  '\'
        </if>
        <if test="searchCreditCodeEmp != null and searchCreditCodeEmp != ''">
            AND T5.INSTITUTION_CODE = #{searchCreditCodeEmp}
        </if>
        <if test="searchZoneCode != null and searchZoneCode != ''">
            AND T4.ZONE_GB LIKE #{queryZoneCode} escape  '\'
        </if>
        <if test="searchCrptName != null and searchCrptName != ''">
            AND T1.CRPT_NAME LIKE #{queryCrptName} escape  '\'
        </if>
        <if test="searchCreditCode != null and searchCreditCode != ''">
            AND T1.INSTITUTION_CODE = #{searchCreditCode}
        </if>
        <if test="searchPersonName != null and searchPersonName != ''">
            AND T.PERSON_NAME LIKE #{queryPersonName} escape  '\'
        </if>
        <if test="searchIdc != null and searchIdc != ''">
            AND T.IDC = #{searchIdc}
        </if>
        <if test="zkBhkCode != null and zkBhkCode != '' ">
            AND T.ZK_BHK_CODE = #{zkBhkCode}
        </if>
        <if test="searchBhkBdate != null and searchBhkBdate != ''">
            AND T.BHK_DATE &gt;= TO_DATE(#{searchBhkBdate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="searchBhkEdate != null and searchBhkEdate != ''">
            AND T.BHK_DATE &lt;= TO_DATE(#{searchBhkEdate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="selectOnGuardIds != null and selectOnGuardIds.size > 0">
            AND T.ONGUARD_STATEID IN
            <foreach collection="selectOnGuardIds" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
        <if test="(selectBadRsnIds != null and selectBadRsnIds.size > 0) or (searchSelBhkrstIds != null and searchSelBhkrstIds.size > 0)">
            AND EXISTS(SELECT 1 FROM TD_TJ_BADRSNS TT WHERE TT.BHK_ID = T.RID
        <if test="selectBadRsnIds != null and selectBadRsnIds.size > 0">
            AND TT.BADRSN_ID IN
            <foreach collection="selectBadRsnIds" item="item" index="index" separator="," open="(" close=")">
                <trim suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>

        </if>
        <if test="searchSelBhkrstIds != null and searchSelBhkrstIds.size > 0">
            AND TT.EXAM_CONCLUSION_ID IN
            <foreach collection="searchSelBhkrstIds" item="item" index="index" separator="," open="(" close=")">
                <trim suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
            )
        </if>
        <if test="jcTypeList != null and jcTypeList.size > 0">
            AND T.JC_TYPE IN
            <foreach collection="jcTypeList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
        <if test="null != ifRhk or 0 == ifRhk">
            AND T.IF_RHK = #{ifRhk}
        </if>
        <if test="null != ifAbnormal or 0 == ifAbnormal">
            AND T.IF_ABNOMAL = #{ifAbnormal}
        </if>
        <if test="searchAbnormals != null and searchAbnormals != ''">
            AND ( EXISTS( SELECT 1 FROM TD_TJ_BHK_ABNOMAL abnomal WHERE abnomal.BHK_ID = T.RID
            AND abnomal.ABNOMAL_INFO LIKE #{queryAbnormals} escape '\'
            )
            OR T.LACK_MSG LIKE #{queryAbnormals} escape '\'
            )
        </if>
        <if test="stateList != null and stateList.size > 0">
            AND (
            T.STATE IN
            <foreach collection="searchStateList" item="item" index="index" separator="," open="(" close=")">
                <trim suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
            <if test="null != ifConZero and 1 == ifConZero">
                OR (T.STATE = 0 AND NVL(T6.IF_CITY_DIRECT, 0) = 0)
            </if>
            <if test="null != ifConOne and 1 == ifConOne">
                OR (T.STATE = 1 AND NVL(T6.IF_CITY_DIRECT, 0) = 0)
            </if>
            <if test="null != ifConTwo and 1 == ifConTwo">
                OR (T.STATE = 0 AND NVL(T6.IF_CITY_DIRECT, 0) = 1)
            </if>
            <if test="null != ifConThree and 1 == ifConThree">
                OR (T.STATE = 1 AND NVL(T6.IF_CITY_DIRECT, 0) = 1)
            </if>
            )
        </if>
        <if test="bhkTypeList != null and bhkTypeList.size > 0">
            AND T.BHK_TYPE IN
            <foreach collection="bhkTypeList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
        <if test="startCreateDate != null and startCreateDate != ''">
            AND T.CREATE_DATE &gt;= TO_DATE(#{startCreateDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endCreateDate != null and endCreateDate != ''">
            AND T.CREATE_DATE &lt;= TO_DATE(#{endCreateDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="startRptPrintDate != null and startRptPrintDate != ''">
            AND T.RPT_PRINT_DATE &gt;= TO_DATE(#{startRptPrintDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endRptPrintDate != null and endRptPrintDate != ''">
            AND T.RPT_PRINT_DATE &lt;= TO_DATE(#{endRptPrintDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        ) A WHERE 1 = 1
        <if test="searchRcvBdate != null and searchRcvBdate != ''">
            AND revDate &gt;= TO_DATE(#{searchRcvBdate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="searchRcvEdate != null and searchRcvEdate != ''">
            AND revDate &lt;= TO_DATE(#{searchRcvEdate},'YYYY-MM-DD HH24:MI:SS')
        </if>
    </select>

    <!-- 体检最新档案初检rid集合 -->
    <select id="findTjNewestRecRidList" resultType="java.lang.Integer"
            parameterType="com.chis.modules.timer.heth.logic.vo.TjPersonSearchConditionPO">
        <if test="selectBadRsnIds != null and selectBadRsnIds != ''">
            WITH TEMP_TABLE AS (
            SELECT REGEXP_SUBSTR(IDS, '[^,]+', 1, LEVEL, 'i') AS ID
            FROM (SELECT #{selectBadRsnIds} IDS FROM DUAL)
            CONNECT BY LEVEL &lt;= LENGTH(IDS) - LENGTH(REGEXP_REPLACE(IDS, ',', '')) + 1
            )
        </if>
        SELECT DISTINCT RID FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
        SELECT DISTINCT RID FROM (
        SELECT T.RID
        <include refid="commTjNewestRecSql"/>
        ORDER BY T2.ZONE_GB,T.BHK_DATE,T.PERSON_NAME
        )
        ) ZWX
        ) WHERE 1=1
        <if test="startRow != null and endRow != null">
            AND RN BETWEEN #{startRow} AND #{endRow}
        </if>
    </select>
    <!-- 体检最新档案初检数量 -->
    <select id="findTjNewestRecCount" resultType="java.lang.Integer"
            parameterType="com.chis.modules.timer.heth.logic.vo.TjPersonSearchConditionPO">
        <if test="selectBadRsnIds != null and selectBadRsnIds != ''">
            WITH TEMP_TABLE AS (
                SELECT REGEXP_SUBSTR(IDS, '[^,]+', 1, LEVEL, 'i') AS ID
                FROM (SELECT #{selectBadRsnIds} IDS FROM DUAL)
                CONNECT BY LEVEL &lt;= LENGTH(IDS) - LENGTH(REGEXP_REPLACE(IDS, ',', '')) + 1
            )
        </if>
        SELECT COUNT(1) FROM (
        SELECT T.RID
        <include refid="commTjNewestRecSql"/>
        GROUP BY T.RID
        )
    </select>
    <!-- 体检最新档案 -->
    <sql id="commTjNewestRecSql">
        FROM TD_TJ_BHK T
        INNER JOIN TB_TJ_CRPT T1 ON T1.RID = T.ENTRUST_CRPT_ID
        INNER JOIN TS_ZONE T2 ON T2.RID = T1.ZONE_ID
        INNER JOIN TS_SIMPLE_CODE T3 ON T.CARD_TYPE_ID=T3.RID
        INNER JOIN TS_SIMPLE_CODE T4 ON T.ONGUARD_STATEID=T4.RID
        INNER JOIN TB_TJ_SRVORG T5 ON T.BHKORG_ID=T5.RID
        LEFT JOIN TD_TJ_BADRSNS BADRSN ON BADRSN.BHK_ID = T.RID
        WHERE T.IF_RHK=0 and T1.INTER_PRC_TAG = 1 and T.BHK_TYPE IN (3,4)
        <if test="searchPersonName != null and searchPersonName != '' ">
            AND T.PERSON_NAME LIKE '%'||#{searchPersonName}||'%' escape '\'
        </if>
        <if test="searchPsnType != null ">
            AND T.CARD_TYPE_ID = #{searchPsnType}
        </if>
        <if test='searchIDC != null and searchIDC != "" and searchPsnType != 3 '>
            AND T.IDC = #{searchIDC}
        </if>
        <if test='searchZoneCode != null and searchZoneCode != ""'>
            AND T2.ZONE_GB LIKE #{searchZoneCode}||'%'
        </if>
        <if test='searchCrptName != null and searchCrptName != ""'>
            AND T1.CRPT_NAME LIKE '%'||#{searchCrptName}||'%' escape '\'
        </if>
        <if test='searchBhkType!=null and searchBhkType.length>0'>
            AND T.BHK_TYPE IN
            <foreach collection="searchBhkType" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="searchStartTime != null and searchStartTime != ''">
            AND T.BHK_DATE &gt;= TO_DATE(#{searchStartTime},'yyyy-MM-dd')
        </if>
        <if test="searchEndTime != null and searchEndTime != ''">
            AND T.BHK_DATE &lt;= TO_DATE(#{searchEndTime}||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test="startRptPrintDate != null and startRptPrintDate != ''">
            AND T.RPT_PRINT_DATE &gt;= TO_DATE(#{startRptPrintDate},'yyyy-MM-dd')
        </if>
        <if test="endRptPrintDate != null and endRptPrintDate != ''">
            AND T.RPT_PRINT_DATE &lt;= TO_DATE(#{endRptPrintDate}||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test="startCreateDate != null and startCreateDate != ''">
            AND T.CREATE_DATE &gt;= TO_DATE(#{startCreateDate},'yyyy-MM-dd')
        </if>
        <if test="endCreateDate != null and endCreateDate != ''">
            AND T.CREATE_DATE &lt;= TO_DATE(#{endCreateDate}||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test="selectOnGuardIds != null and selectOnGuardIds != ''">
            AND T.ONGUARD_STATEID IN
            <foreach collection='selectOnGuardIds.split(",")' item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="selectBadRsnIds != null and selectBadRsnIds != ''">
            AND EXISTS(SELECT 1 FROM TEMP_TABLE TEMP WHERE BADRSN.BADRSN_ID = TEMP.ID)
        </if>
        <if test="searchJcType != null and searchJcType.length>0">
            AND T.JC_TYPE IN
            <foreach collection="searchJcType" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test='selectAgeAnalyDetails!=null and selectAgeAnalyDetails.size()>0'>
            AND
            <foreach collection="selectAgeAnalyDetails" item="item" index="index" open="(" close=")"
                     separator="or">
                (
                <if test="item.geNum!=null">
                    T.AGE  &gt;= #{item.geNum}
                </if>
                <if test="item.geNum!=null and item.gtNum!=null">
                    AND
                </if>
                <if test="item.gtNum!=null">
                    T.AGE &gt; #{item.gtNum}
                </if>
                <if test="item.leNum!=null and (null!=item.geNum or null!=item.gtNum)">
                    AND
                </if>
                <if test="item.leNum!=null">
                    T.AGE  &lt;= #{item.leNum}
                </if>
                <if test="item.ltNum!=null and (null!=item.geNum or null!=item.gtNum or null!=item.leNum)">
                    AND
                </if>
                <if test="item.ltNum!=null">
                    T.AGE &lt; #{item.ltNum}
                </if>
                )
            </foreach>
        </if>
        <if test='selectWorkAnalyDetails!=null and selectWorkAnalyDetails.size()>0'>
            AND
            <foreach collection="selectWorkAnalyDetails" item="item" index="index" open="(" close=")"
                     separator="or">
                (
                <if test="item.leNum == null and item.ltNum == null and null == item.geNum and null==item.gtNum ">
                    T.TCHBADRSNTIM is null
                </if>
                <if test="item.geNum!=null">
                    T.TCHBADRSNTIM  &gt;= #{item.geNum}
                </if>
                <if test="item.geNum!=null and item.gtNum!=null">
                    AND
                </if>
                <if test="item.gtNum!=null">
                    T.TCHBADRSNTIM &gt; #{item.gtNum}
                </if>
                <if test="item.leNum!=null and (null!=item.geNum or null!=item.gtNum)">
                    AND
                </if>
                <if test="item.leNum!=null">
                    T.TCHBADRSNTIM  &lt;= #{item.leNum}
                </if>
                <if test="item.ltNum!=null and (null!=item.geNum or null!=item.gtNum or null!=item.leNum)">
                    AND
                </if>
                <if test="item.ltNum!=null">
                    T.TCHBADRSNTIM &lt; #{item.ltNum}
                </if>
                )
            </foreach>
        </if>
        <if test="searchSelBhkrstIds != null and searchSelBhkrstIds != ''">
            AND T.NEW_BHKRST_ID IN
            <foreach collection='searchSelBhkrstIds.split(",")' item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="searchUnitId!=null and searchUnitId != ''">
            AND T.BHKORG_ID in
            <foreach collection='searchUnitId.split(",")' item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="searchItemIds != null and searchItemIds != ''">
            AND EXISTS ( SELECT 1 FROM TD_TJ_BHKSUB TSUB WHERE TSUB.BHK_ID=T.RID
            AND
            <foreach collection='searchItemIds.split(",")' item="item" index="index" open="(" close=")"
                     separator="OR">
                (TSUB.ITEM_ID =
                <trim suffixOverrides="@@0">
                    ${item}
                </trim>
                <if test='item.contains("@@0")'>
                    and TSUB.RGLTAG = 0
                </if>
                )
            </foreach>
            )
        </if>
    </sql>
   <!--个案审核查询-->
    <select id="findAuditGenerateQueryCount" resultType="java.lang.Integer">
        <if test="selectBadRsnIds != null and selectBadRsnIds != ''">
            WITH TEMP_TABLE AS (
            SELECT REGEXP_SUBSTR(IDS, '[^,]+', 1, LEVEL, 'i') AS ID
            FROM (SELECT #{selectBadRsnIds} IDS FROM DUAL)
            CONNECT BY LEVEL &lt;= LENGTH(IDS) - LENGTH(REGEXP_REPLACE(IDS, ',', '')) + 1
            )
        </if>
        SELECT COUNT(1) FROM (
        SELECT T.RID
        <include refid="auditGenerateQuerySql"/>
        GROUP BY T.RID
        )
    </select>
    <select id="findAuditGenerateQueryList" resultType="java.lang.Integer" >
        <if test="selectBadRsnIds != null and selectBadRsnIds != ''">
            WITH TEMP_TABLE AS (
            SELECT REGEXP_SUBSTR(IDS, '[^,]+', 1, LEVEL, 'i') AS ID
            FROM (SELECT #{selectBadRsnIds} IDS FROM DUAL)
            CONNECT BY LEVEL &lt;= LENGTH(IDS) - LENGTH(REGEXP_REPLACE(IDS, ',', '')) + 1
            )
        </if>
        SELECT
         A.RID  FROM
        ( SELECT
        ZWX.*,
        ROWNUM AS RN
        FROM
        ( select
        DISTINCT T.RID ,
        T6.ZONE_GB,
        T5.CRPT_NAME,
        T.PERSON_NAME,
        T.BHK_DATE
        <include refid="auditGenerateQuerySql"/>
        ORDER BY
        T6.ZONE_GB,
        T5.CRPT_NAME,
        T.PERSON_NAME,
        T.BHK_DATE
        ) ZWX )A
        WHERE
        1=1
        <if test="startRow != null and endRow != null">
            AND RN BETWEEN #{startRow} AND #{endRow}
        </if>
    </select>
    <sql id="auditGenerateQuerySql">
        FROM
        TD_TJ_BHK T
        LEFT JOIN TB_TJ_CRPT T1 ON
        T1.RID = T.CRPT_ID
        LEFT JOIN TS_SIMPLE_CODE T2 ON
        T2.RID = T.ONGUARD_STATEID
        LEFT JOIN TB_TJ_SRVORG T3 ON
        T3.RID = T.BHKORG_ID
        LEFT JOIN TS_ZONE T4 ON
        T4.RID = T1.ZONE_ID
        LEFT JOIN TB_TJ_CRPT T5 ON
        T5.RID = T.ENTRUST_CRPT_ID
        LEFT JOIN TS_ZONE T6 ON
        T6.RID = T5.ZONE_ID
        LEFT JOIN TD_TJ_BADRSNS BADRSN ON BADRSN.BHK_ID = T.RID
        WHERE
        T.IF_INTO_CHECK = 1
        <if test="searchUnitId != null ">
            AND T.BHKORG_ID = #{searchUnitId}
        </if>
        <if test='searchEntrustCrptZoneCode != null and searchEntrustCrptZoneCode != ""'>
            AND T6.ZONE_GB LIKE #{searchEntrustCrptZoneCode}||'%'
        </if>
        <if test="searchEntrustCrptName != null and searchEntrustCrptName != '' ">
            AND T5.CRPT_NAME LIKE '%'||#{searchEntrustCrptName}||'%' escape '\'
        </if>
        <if test="searchEntrustCreditCode != null and searchEntrustCreditCode != '' ">
            AND T5.INSTITUTION_CODE =  #{searchEntrustCreditCode}
        </if>
        <if test='searchZoneCode != null and searchZoneCode != ""'>
            AND T4.ZONE_GB LIKE #{searchZoneCode}||'%'
        </if>
        <if test="searchCrptName != null and searchCrptName != '' ">
            AND T1.CRPT_NAME LIKE '%'||#{searchCrptName}||'%' escape '\'
        </if>
        <if test="searchCreditCode != null and searchCreditCode != '' ">
            AND T1.INSTITUTION_CODE =  #{searchCreditCode}
        </if>
        <if test="searchBhkCode != null and searchBhkCode != '' ">
            AND T.BHK_CODE = #{searchBhkCode}
        </if>
        <if test="searchIdc != null and searchIdc != '' ">
            AND T.IDC = #{searchIdc}
        </if>
        <if test="zkBhkCode != null and zkBhkCode != '' ">
            AND T.ZK_BHK_CODE = #{zkBhkCode}
        </if>
        <if test="searchPersonName != null and searchPersonName != '' ">
            AND T.PERSON_NAME  LIKE '%'||#{searchPersonName}||'%' escape '\'
        </if>
        <if test="searchBhkBdate != null and searchBhkBdate != ''">
            AND T.BHK_DATE &gt;= TO_DATE(#{searchBhkBdate},'yyyy-MM-dd')
        </if>
        <if test="searchBhkEdate != null and searchBhkEdate != ''">
            AND T.BHK_DATE &lt;= TO_DATE(#{searchBhkEdate}||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test="selectOnGuardIds != null and selectOnGuardIds != ''">
            AND T.ONGUARD_STATEID IN
            <foreach collection='selectOnGuardIds.split(",")' item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="selectBadRsnIds != null and selectBadRsnIds != ''">
            AND EXISTS(SELECT 1 FROM TEMP_TABLE TEMP WHERE BADRSN.BADRSN_ID = TEMP.ID)
        </if>
        <if test="startRptPrintDate != null and startRptPrintDate != ''">
            AND T.RPT_PRINT_DATE &gt;= TO_DATE(#{startRptPrintDate},'yyyy-MM-dd')
        </if>
        <if test="endRptPrintDate != null and endRptPrintDate != ''">
            AND T.RPT_PRINT_DATE &lt;= TO_DATE(#{endRptPrintDate}||' 23:59:59','yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test="jcTypes != null and  jcTypes != ''">
            AND T.JC_TYPE IN
            <foreach collection='jcTypes.split(",")' item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="ifRhks != null and  ifRhks != ''">
            AND T.IF_RHK IN
            <foreach collection='ifRhks.split(",")' item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="ifAbnormals != null and  ifAbnormals != ''">
            AND T.IF_ABNOMAL IN
            <foreach collection='ifAbnormals.split(",")' item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="states != null and  states != ''">
            AND  T.STATE IN
            <foreach collection='states.split(",")' item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="searchAbnomalInfo != null and searchAbnomalInfo != ''">
            AND T.IF_ABNOMAL = 1
            AND ( ( T.IF_INTEITM_LACK = 1
            AND T.LACK_MSG LIKE '%'||#{searchAbnomalInfo}||'%' escape '\' )
            OR EXISTS(
            SELECT
            1
            FROM
            TD_TJ_BHK_ABNOMAL K
            WHERE
            K.BHK_ID = T.RID
            AND K.ABNOMAL_INFO LIKE '%'||#{searchAbnomalInfo}||'%' escape '\' ) )
        </if>
        <if test="searchBackUpRsn != null and searchBackUpRsn != ''">
            AND ( ( T.STATE = 0
            AND T.COUNTY_AUDIT_ADV LIKE '%'||#{searchBackUpRsn}||'%' escape '\'  )
            OR ( T.STATE = 2
            AND T.CITY_AUDIT_ADV LIKE '%'||#{searchBackUpRsn}||'%' escape '\'  )
            OR ( T.STATE = 4
            AND T.PRO_AUDIT_ADV LIKE '%'||#{searchBackUpRsn}||'%' escape '\'  )
            OR ( T.STATE = 7
            AND T.ERR_MSG LIKE '%'||#{searchBackUpRsn}||'%' escape '\'  ) )
        </if>
    </sql>
</mapper>
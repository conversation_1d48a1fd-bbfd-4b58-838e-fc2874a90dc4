<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdZwWarnInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdZwWarnInfo">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MODEL_ID" property="fkByModelId.rid" />
        <result column="WARN_ZONE" property="fkByWarnZone.rid" />
        <result column="BUS_TYPE" property="busType" />
        <result column="BUS_ID" property="busId" />
        <result column="WARN_CONT" property="warnCont" />
        <result column="HAPPEN_DATE" property="happenDate" />
        <result column="JC_BEGIN_DATE" property="jcBeginDate" />
        <result column="JC_END_DATE" property="jcEndDate" />
        <result column="DEAL_DATE" property="dealDate" />
        <result column="DEAL_ORGID" property="fkByDealOrgid.rid" />
        <result column="HAPPEN_NUM" property="happenNum" />
        <result column="EXCLUDE_RSN" property="excludeRsn" />
        <result column="STATE_MARK" property="stateMark" />
        <result column="VIEW_LEVEL" property="viewLevel" />
        <result column="ANNEX_PATH" property="annexPath" />
    </resultMap>

    <resultMap id="BaseAndPsnResultMap" type="com.chis.modules.timer.heth.entity.TdZwWarnInfo">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MODEL_ID" property="fkByModelId.rid" />
        <result column="WARN_ZONE" property="fkByWarnZone.rid" />
        <result column="BUS_TYPE" property="busType" />
        <result column="BUS_ID" property="busId" />
        <result column="WARN_CONT" property="warnCont" />
        <result column="HAPPEN_DATE" property="happenDate" />
        <result column="JC_BEGIN_DATE" property="jcBeginDate" />
        <result column="JC_END_DATE" property="jcEndDate" />
        <result column="DEAL_DATE" property="dealDate" />
        <result column="DEAL_ORGID" property="fkByDealOrgid.rid" />
        <result column="HAPPEN_NUM" property="happenNum" />
        <result column="EXCLUDE_RSN" property="excludeRsn" />
        <result column="STATE_MARK" property="stateMark" />
        <result column="VIEW_LEVEL" property="viewLevel" />
        <result column="ANNEX_PATH" property="annexPath" />
        <collection property="psnList" ofType="com.chis.modules.timer.heth.entity.TdZwWarnPsns">
            <id property="rid" column="PSN_RID" jdbcType="INTEGER"/>
            <result column="PSN_BUS_ID" property="busId"/>
            <result column="PSN_RCV_DATE" property="rcvDate"/>
            <result column="PSN_DIS_ID" property="fkByDisId.rid" />
            <result column="PSN_DEAL_DATE" property="dealDate" />
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MODEL_ID,t.WARN_ZONE,t.BUS_TYPE,t.BUS_ID,t.WARN_CONT,t.HAPPEN_DATE,t.JC_BEGIN_DATE,t.JC_END_DATE,t.DEAL_DATE,t.DEAL_ORGID,t.HAPPEN_NUM,t.EXCLUDE_RSN,t.STATE_MARK,t.VIEW_LEVEL,t.ANNEX_PATH,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByModelId != null and ${joiner}fkByModelId.rid != null">
            and ${mAlias}MODEL_ID = #{${joiner}fkByModelId.rid}
        </if>
        <if test="${joiner}fkByWarnZone != null and ${joiner}fkByWarnZone.rid != null">
            and ${mAlias}WARN_ZONE = #{${joiner}fkByWarnZone.rid}
        </if>
        <if test="${joiner}busType != null">
            and ${mAlias}BUS_TYPE = #{${joiner}busType}
        </if>
        <if test="${joiner}busId != null">
            and ${mAlias}BUS_ID = #{${joiner}busId}
        </if>
        <if test="${joiner}warnCont != null">
            and ${mAlias}WARN_CONT = #{${joiner}warnCont}
        </if>
        <if test="${joiner}happenDate != null">
            and ${mAlias}HAPPEN_DATE = #{${joiner}happenDate}
        </if>
        <if test="${joiner}jcBeginDate != null">
            and ${mAlias}JC_BEGIN_DATE = #{${joiner}jcBeginDate}
        </if>
        <if test="${joiner}jcEndDate != null">
            and ${mAlias}JC_END_DATE = #{${joiner}jcEndDate}
        </if>
        <if test="${joiner}dealDate != null">
            and ${mAlias}DEAL_DATE = #{${joiner}dealDate}
        </if>
        <if test="${joiner}fkByDealOrgid != null and ${joiner}fkByDealOrgid.rid != null">
            and ${mAlias}DEAL_ORGID = #{${joiner}fkByDealOrgid.rid}
        </if>
        <if test="${joiner}happenNum != null">
            and ${mAlias}HAPPEN_NUM = #{${joiner}happenNum}
        </if>
        <if test="${joiner}excludeRsn != null">
            and ${mAlias}EXCLUDE_RSN = #{${joiner}excludeRsn}
        </if>
        <if test="${joiner}stateMark != null">
            and ${mAlias}STATE_MARK = #{${joiner}stateMark}
        </if>
        <if test="${joiner}viewLevel != null">
            and ${mAlias}VIEW_LEVEL = #{${joiner}viewLevel}
        </if>
        <if test="${joiner}annexPath != null">
            and ${mAlias}ANNEX_PATH = #{${joiner}annexPath}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByModelId != null and ${joiner}fkByModelId.rid != null">
                t.MODEL_ID = #{${joiner}fkByModelId.rid},
            </when>
            <otherwise>
                t.MODEL_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByWarnZone != null and ${joiner}fkByWarnZone.rid != null">
                t.WARN_ZONE = #{${joiner}fkByWarnZone.rid},
            </when>
            <otherwise>
                t.WARN_ZONE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}busType != null">
                t.BUS_TYPE = #{${joiner}busType},
            </when>
            <otherwise>
                t.BUS_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}busId != null">
                t.BUS_ID = #{${joiner}busId},
            </when>
            <otherwise>
                t.BUS_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}warnCont != null">
                t.WARN_CONT = #{${joiner}warnCont},
            </when>
            <otherwise>
                t.WARN_CONT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}happenDate != null">
                t.HAPPEN_DATE = #{${joiner}happenDate},
            </when>
            <otherwise>
                t.HAPPEN_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}jcBeginDate != null">
                t.JC_BEGIN_DATE = #{${joiner}jcBeginDate},
            </when>
            <otherwise>
                t.JC_BEGIN_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}jcEndDate != null">
                t.JC_END_DATE = #{${joiner}jcEndDate},
            </when>
            <otherwise>
                t.JC_END_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dealDate != null">
                t.DEAL_DATE = #{${joiner}dealDate},
            </when>
            <otherwise>
                t.DEAL_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByDealOrgid != null and ${joiner}fkByDealOrgid.rid != null">
                t.DEAL_ORGID = #{${joiner}fkByDealOrgid.rid},
            </when>
            <otherwise>
                t.DEAL_ORGID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}happenNum != null">
                t.HAPPEN_NUM = #{${joiner}happenNum},
            </when>
            <otherwise>
                t.HAPPEN_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}excludeRsn != null">
                t.EXCLUDE_RSN = #{${joiner}excludeRsn},
            </when>
            <otherwise>
                t.EXCLUDE_RSN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}stateMark != null">
                t.STATE_MARK = #{${joiner}stateMark},
            </when>
            <otherwise>
                t.STATE_MARK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}viewLevel != null">
                t.VIEW_LEVEL = #{${joiner}viewLevel},
            </when>
            <otherwise>
                t.VIEW_LEVEL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}annexPath != null">
                t.ANNEX_PATH = #{${joiner}annexPath},
            </when>
            <otherwise>
                t.ANNEX_PATH = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZW_WARN_INFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZW_WARN_INFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_ZW_WARN_INFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" useGeneratedKeys="true"  keyProperty="rid" parameterType="TdZwWarnInfo">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_ZW_WARN_INFO_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_ZW_WARN_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MODEL_ID,
            WARN_ZONE,
            BUS_TYPE,
            BUS_ID,
            WARN_CONT,
            HAPPEN_DATE,
            JC_BEGIN_DATE,
            JC_END_DATE,
            DEAL_DATE,
            DEAL_ORGID,
            HAPPEN_NUM,
            EXCLUDE_RSN,
            STATE_MARK,
            VIEW_LEVEL,
            ANNEX_PATH,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{fkByModelId.rid},
            #{fkByWarnZone.rid},
            #{busType},
            #{busId},
            #{warnCont},
            #{happenDate},
            #{jcBeginDate},
            #{jcEndDate},
            #{dealDate},
            #{fkByDealOrgid.rid},
            #{happenNum},
            #{excludeRsn},
            #{stateMark},
            #{viewLevel},
            #{annexPath},
        </trim>
    </insert>

    <insert id="insertBatch"  parameterType="java.util.List">
        insert into TD_ZW_WARN_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            MODEL_ID,
            WARN_ZONE,
            BUS_TYPE,
            BUS_ID,
            WARN_CONT,
            HAPPEN_DATE,
            JC_BEGIN_DATE,
            JC_END_DATE,
            DEAL_DATE,
            DEAL_ORGID,
            HAPPEN_NUM,
            EXCLUDE_RSN,
            STATE_MARK,
            VIEW_LEVEL,
            ANNEX_PATH,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_ZW_WARN_INFO_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByModelId != null and item.fkByModelId.rid != null">
                            #{item.fkByModelId.rid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByWarnZone != null and item.fkByWarnZone.rid != null">
                            #{item.fkByWarnZone.rid} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.busType != null">
                            #{item.busType} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.busId != null">
                            #{item.busId} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.warnCont != null">
                            #{item.warnCont} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.happenDate != null">
                            #{item.happenDate} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.jcBeginDate != null">
                            #{item.jcBeginDate} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.jcEndDate != null">
                            #{item.jcEndDate} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.dealDate != null">
                            #{item.dealDate} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByDealOrgid != null and item.fkByDealOrgid.rid != null">
                            #{item.fkByDealOrgid.rid} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.happenNum != null">
                            #{item.happenNum} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.excludeRsn != null">
                            #{item.excludeRsn} AS C16,
                        </when>
                        <otherwise>
                            NULL AS C16,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.stateMark != null">
                            #{item.stateMark} AS C17,
                        </when>
                        <otherwise>
                            NULL AS C17,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.viewLevel != null">
                            #{item.viewLevel} AS C18,
                        </when>
                        <otherwise>
                            NULL AS C18,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.annexPath != null">
                            #{item.annexPath} AS C19,
                        </when>
                        <otherwise>
                            NULL AS C19,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdZwWarnInfo" >
        update TD_ZW_WARN_INFO t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_ZW_WARN_INFO t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 注意 joiner 需要加入 -->
    <delete id="removeByEntity" parameterType="TdZwWarnInfo">
        delete from TD_ZW_WARN_INFO
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->

    <select id="selectNotDisposalList" resultMap="BaseAndPsnResultMap">
        SELECT WI.RID,
        WI.MODEL_ID,
        WI.WARN_ZONE,
        WI.WARN_CONT,
        WI.HAPPEN_DATE,
        WI.JC_BEGIN_DATE,
        WI.JC_END_DATE,
        WI.DEAL_DATE,
        WI.DEAL_ORGID,
        WI.HAPPEN_NUM,
        WI.EXCLUDE_RSN,
        WI.STATE_MARK,
        WI.CREATE_DATE,
        WI.CREATE_MANID,
        WI.MODIFY_DATE,
        WI.MODIFY_MANID,
        WI.BUS_TYPE,
        WI.BUS_ID,
        WI.VIEW_LEVEL,
        WP.RID AS PSN_RID,
        WP.BUS_ID AS PSN_BUS_ID,
        WP.RCV_DATE AS PSN_RCV_DATE,
        WP.DIS_ID AS PSN_DIS_ID,
        WP.DEAL_DATE AS PSN_DEAL_DATE
        FROM TD_ZW_WARN_INFO WI
        LEFT JOIN TD_ZW_WARN_PSNS WP ON WI.RID = WP.MAIN_ID
        WHERE WI.MODEL_ID = #{modelId}
        AND WI.BUS_TYPE = #{busType}
        AND WI.BUS_ID = #{busId}
        AND NVL(WI.STATE_MARK, 0) = 0
    </select>

</mapper>

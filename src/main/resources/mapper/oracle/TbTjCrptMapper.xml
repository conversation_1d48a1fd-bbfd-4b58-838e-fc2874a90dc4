<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TbTjCrptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TbTjCrpt">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="CRPT_NAME" property="crptName" />
        <result column="ADDRESS" property="address" />
        <result column="PHONE" property="phone" />
        <result column="CORPORATE_DEPUTY" property="corporateDeputy" />
        <result column="WORK_FORCE" property="workForce" />
        <result column="WORKMAN_NUM" property="workmanNum" />
        <result column="WORKMISTRESS_NUM" property="workmistressNum" />
        <result column="HOLD_CARD_MAN" property="holdCardMan" />
        <result column="POSTALCODE" property="postalcode" />
        <result column="WORK_AREA" property="workArea" />
        <result column="REGISTER_FUND" property="registerFund" />
        <result column="INDUS_TYPE_ID" property="fkByIndusTypeId.rid" />
        <result column="ZONE_ID" property="fkByZoneId.rid" />
        <result column="TOWN_ID" property="fkByTownId.rid" />
        <result column="ECONOMY_ID" property="fkByEconomyId.rid" />
        <result column="INSTITUTION_CODE" property="institutionCode" />
        <result column="CRPT_SIZE_ID" property="fkByCrptSizeId.rid" />
        <result column="SAFETY_PRINCIPAL" property="safetyPrincipal" />
        <result column="FILING_DATE" property="filingDate" />
        <result column="BUILD_DATE" property="buildDate" />
        <result column="LINKMAN1" property="linkman1" />
        <result column="POSITION1" property="position1" />
        <result column="LINKPHONE1" property="linkphone1" />
        <result column="LINKMAN2" property="linkman2" />
        <result column="POSITION2" property="position2" />
        <result column="LINKPHONE2" property="linkphone2" />
        <result column="SAFEPOSITION" property="safeposition" />
        <result column="SAFEPHONE" property="safephone" />
        <result column="SUBJE_CONN" property="subjeConn" />
        <result column="ENROL_ADDRESS" property="enrolAddress" />
        <result column="ENROL_POSTALCODE" property="enrolPostalcode" />
        <result column="OCC_MANA_OFFICE" property="occManaOffice" />
        <result column="REG_CODE" property="regCode" />
        <result column="REG_MARK" property="regMark" />
        <result column="UUID" property="uuid" />
        <result column="TAR_UUID" property="tarUuid" />
        <result column="POSTCODE" property="postcode" />
        <result column="PYM" property="pym" />
        <result column="INTER_PRC_TAG" property="interPrcTag" />
        <result column="FEMALE_STAFF_NUM" property="femaleStaffNum" />
        <result column="FEMALE_WORK_NUM" property="femaleWorkNum" />
        <result column="CREATE_ORG_ID" property="createOrgId" />
        <result column="ZONE_NAME" property="fkByZoneId.zoneName" />
        <result column="ZONE_FULL_NAME" property="fkByZoneId.fullName" />
        <result column="IF_SUB_ORG" property="ifSubOrg" />
        <result column="OUTSOURCE_NUM" property="outsourceNum" />
        <result column="UPPER_UNIT_ID" property="fkByUpperUnitId.rid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,
        t.CRPT_NAME,t.ADDRESS,t.PHONE,t.CORPORATE_DEPUTY,t.WORK_FORCE,t.WORKMAN_NUM,t.WORKMISTRESS_NUM,
        t.HOLD_CARD_MAN,t.POSTALCODE,t.WORK_AREA,t.REGISTER_FUND,t.INDUS_TYPE_ID,t.ZONE_ID,t.TOWN_ID,t.ECONOMY_ID,
        t.INSTITUTION_CODE,t.CRPT_SIZE_ID,t.SAFETY_PRINCIPAL,t.FILING_DATE,t.BUILD_DATE,t.LINKMAN1,t.POSITION1,
        t.LINKPHONE1,t.LINKMAN2,t.POSITION2,t.LINKPHONE2,t.SAFEPOSITION,t.SAFEPHONE,t.SUBJE_CONN,t.ENROL_ADDRESS,
        t.ENROL_POSTALCODE,t.OCC_MANA_OFFICE,t.REG_CODE,t.REG_MARK,t.UUID,t.TAR_UUID,t.POSTCODE,t.PYM,t.INTER_PRC_TAG,
        t.FEMALE_STAFF_NUM,t.FEMALE_WORK_NUM,t.CREATE_ORG_ID,t.IF_SUB_ORG,t.OUTSOURCE_NUM,t.UPPER_UNIT_ID,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}crptName != null and ${joiner}crptName != ''">
            and ${mAlias}CRPT_NAME = #{${joiner}crptName}
        </if>
        <if test="${joiner}address != null and ${joiner}address != ''">
            and ${mAlias}ADDRESS = #{${joiner}address}
        </if>
        <if test="${joiner}phone != null and ${joiner}phone != ''">
            and ${mAlias}PHONE = #{${joiner}phone}
        </if>
        <if test="${joiner}corporateDeputy != null and ${joiner}corporateDeputy != ''">
            and ${mAlias}CORPORATE_DEPUTY = #{${joiner}corporateDeputy}
        </if>
        <if test="${joiner}workForce != null and ${joiner}workForce != ''">
            and ${mAlias}WORK_FORCE = #{${joiner}workForce}
        </if>
        <if test="${joiner}workmanNum != null and ${joiner}workmanNum != ''">
            and ${mAlias}WORKMAN_NUM = #{${joiner}workmanNum}
        </if>
        <if test="${joiner}workmistressNum != null and ${joiner}workmistressNum != ''">
            and ${mAlias}WORKMISTRESS_NUM = #{${joiner}workmistressNum}
        </if>
        <if test="${joiner}holdCardMan != null and ${joiner}holdCardMan != ''">
            and ${mAlias}HOLD_CARD_MAN = #{${joiner}holdCardMan}
        </if>
        <if test="${joiner}postalcode != null and ${joiner}postalcode != ''">
            and ${mAlias}POSTALCODE = #{${joiner}postalcode}
        </if>
        <if test="${joiner}workArea != null and ${joiner}workArea != ''">
            and ${mAlias}WORK_AREA = #{${joiner}workArea}
        </if>
        <if test="${joiner}registerFund != null and ${joiner}registerFund != ''">
            and ${mAlias}REGISTER_FUND = #{${joiner}registerFund}
        </if>
        <if test="${joiner}fkByIndusTypeId != null and ${joiner}fkByIndusTypeId.rid != null">
            and ${mAlias}INDUS_TYPE_ID = #{${joiner}fkByIndusTypeId.rid}
        </if>
        <if test="${joiner}fkByZoneId != null and ${joiner}fkByZoneId.rid != null">
            and ${mAlias}ZONE_ID = #{${joiner}fkByZoneId.rid}
        </if>
        <if test="${joiner}fkByTownId != null and ${joiner}fkByTownId.rid != null">
            and ${mAlias}TOWN_ID = #{${joiner}fkByTownId.rid}
        </if>
        <if test="${joiner}fkByEconomyId != null and ${joiner}fkByEconomyId.rid != null">
            and ${mAlias}ECONOMY_ID = #{${joiner}fkByEconomyId.rid}
        </if>
        <if test="${joiner}institutionCode != null and ${joiner}institutionCode != ''">
            and ${mAlias}INSTITUTION_CODE = #{${joiner}institutionCode}
        </if>
        <if test="${joiner}fkByCrptSizeId != null and ${joiner}fkByCrptSizeId.rid != null">
            and ${mAlias}CRPT_SIZE_ID = #{${joiner}fkByCrptSizeId.rid}
        </if>
        <if test="${joiner}safetyPrincipal != null and ${joiner}safetyPrincipal != ''">
            and ${mAlias}SAFETY_PRINCIPAL = #{${joiner}safetyPrincipal}
        </if>
        <if test="${joiner}filingDate != null">
            and ${mAlias}FILING_DATE = #{${joiner}filingDate}
        </if>
        <if test="${joiner}buildDate != null">
            and ${mAlias}BUILD_DATE = #{${joiner}buildDate}
        </if>
        <if test="${joiner}linkman1 != null and ${joiner}linkman1 != ''">
            and ${mAlias}LINKMAN1 = #{${joiner}linkman1}
        </if>
        <if test="${joiner}position1 != null and ${joiner}position1 != ''">
            and ${mAlias}POSITION1 = #{${joiner}position1}
        </if>
        <if test="${joiner}linkphone1 != null and ${joiner}linkphone1 != ''">
            and ${mAlias}LINKPHONE1 = #{${joiner}linkphone1}
        </if>
        <if test="${joiner}linkman2 != null and ${joiner}linkman2 != ''">
            and ${mAlias}LINKMAN2 = #{${joiner}linkman2}
        </if>
        <if test="${joiner}position2 != null and ${joiner}position2 != ''">
            and ${mAlias}POSITION2 = #{${joiner}position2}
        </if>
        <if test="${joiner}linkphone2 != null and ${joiner}linkphone2 != ''">
            and ${mAlias}LINKPHONE2 = #{${joiner}linkphone2}
        </if>
        <if test="${joiner}safeposition != null and ${joiner}safeposition != ''">
            and ${mAlias}SAFEPOSITION = #{${joiner}safeposition}
        </if>
        <if test="${joiner}safephone != null and ${joiner}safephone != ''">
            and ${mAlias}SAFEPHONE = #{${joiner}safephone}
        </if>
        <if test="${joiner}subjeConn != null and ${joiner}subjeConn != ''">
            and ${mAlias}SUBJE_CONN = #{${joiner}subjeConn}
        </if>
        <if test="${joiner}enrolAddress != null and ${joiner}enrolAddress != ''">
            and ${mAlias}ENROL_ADDRESS = #{${joiner}enrolAddress}
        </if>
        <if test="${joiner}enrolPostalcode != null and ${joiner}enrolPostalcode != ''">
            and ${mAlias}ENROL_POSTALCODE = #{${joiner}enrolPostalcode}
        </if>
        <if test="${joiner}occManaOffice != null and ${joiner}occManaOffice != ''">
            and ${mAlias}OCC_MANA_OFFICE = #{${joiner}occManaOffice}
        </if>
        <if test="${joiner}regCode != null and ${joiner}regCode != ''">
            and ${mAlias}REG_CODE = #{${joiner}regCode}
        </if>
        <if test="${joiner}regMark != null and ${joiner}regMark != ''">
            and ${mAlias}REG_MARK = #{${joiner}regMark}
        </if>
        <if test="${joiner}uuid != null and ${joiner}uuid != ''">
            and ${mAlias}UUID = #{${joiner}uuid}
        </if>
        <if test="${joiner}tarUuid != null and ${joiner}tarUuid != ''">
            and ${mAlias}TAR_UUID = #{${joiner}tarUuid}
        </if>
        <if test="${joiner}postcode != null and ${joiner}postcode != ''">
            and ${mAlias}POSTCODE = #{${joiner}postcode}
        </if>
        <if test="${joiner}pym != null and ${joiner}pym != ''">
            and ${mAlias}PYM = #{${joiner}pym}
        </if>
        <if test="${joiner}interPrcTag != null and ${joiner}interPrcTag != ''">
            and ${mAlias}INTER_PRC_TAG = #{${joiner}interPrcTag}
        </if>
        <if test="${joiner}femaleStaffNum != null and ${joiner}femaleStaffNum != ''">
            and ${mAlias}FEMALE_STAFF_NUM = #{${joiner}femaleStaffNum}
        </if>
        <if test="${joiner}femaleWorkNum != null and ${joiner}femaleWorkNum != ''">
            and ${mAlias}FEMALE_WORK_NUM = #{${joiner}femaleWorkNum}
        </if>
        <if test="${joiner}createOrgId != null and ${joiner}createOrgId != ''">
            and ${mAlias}CREATE_ORG_ID = #{${joiner}createOrgId}
        </if>
        <if test="${joiner}ifSubOrg != null">
            and ${mAlias}IF_SUB_ORG = #{${joiner}ifSubOrg}
        </if>
        <if test="${joiner}outsourceNum != null and ${joiner}outsourceNum != ''">
            and ${mAlias}OUTSOURCE_NUM = #{${joiner}outsourceNum}
        </if>
        <if test="${joiner}fkByUpperUnitId != null and ${joiner}fkByUpperUnitId.rid != null">
            and ${mAlias}UPPER_UNIT_ID = #{${joiner}fkByUpperUnitId.rid}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}crptName != null and ${joiner}crptName != ''">
                t.CRPT_NAME = #{${joiner}crptName},
            </when>
            <otherwise>
                t.CRPT_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}address != null and ${joiner}address != ''">
                t.ADDRESS = #{${joiner}address},
            </when>
            <otherwise>
                t.ADDRESS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}phone != null and ${joiner}phone != ''">
                t.PHONE = #{${joiner}phone},
            </when>
            <otherwise>
                t.PHONE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}corporateDeputy != null and ${joiner}corporateDeputy != ''">
                t.CORPORATE_DEPUTY = #{${joiner}corporateDeputy},
            </when>
            <otherwise>
                t.CORPORATE_DEPUTY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}workForce != null and ${joiner}workForce != ''">
                t.WORK_FORCE = #{${joiner}workForce},
            </when>
            <otherwise>
                t.WORK_FORCE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}workmanNum != null and ${joiner}workmanNum != ''">
                t.WORKMAN_NUM = #{${joiner}workmanNum},
            </when>
            <otherwise>
                t.WORKMAN_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}workmistressNum != null and ${joiner}workmistressNum != ''">
                t.WORKMISTRESS_NUM = #{${joiner}workmistressNum},
            </when>
            <otherwise>
                t.WORKMISTRESS_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}holdCardMan != null and ${joiner}holdCardMan != ''">
                t.HOLD_CARD_MAN = #{${joiner}holdCardMan},
            </when>
            <otherwise>
                t.HOLD_CARD_MAN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}postalcode != null and ${joiner}postalcode != ''">
                t.POSTALCODE = #{${joiner}postalcode},
            </when>
            <otherwise>
                t.POSTALCODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}workArea != null and ${joiner}workArea != ''">
                t.WORK_AREA = #{${joiner}workArea},
            </when>
            <otherwise>
                t.WORK_AREA = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}registerFund != null and ${joiner}registerFund != ''">
                t.REGISTER_FUND = #{${joiner}registerFund},
            </when>
            <otherwise>
                t.REGISTER_FUND = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByIndusTypeId != null and ${joiner}fkByIndusTypeId.rid != null">
                t.INDUS_TYPE_ID = #{${joiner}fkByIndusTypeId.rid},
            </when>
            <otherwise>
                t.INDUS_TYPE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByZoneId != null and ${joiner}fkByZoneId.rid != null">
                t.ZONE_ID = #{${joiner}fkByZoneId.rid},
            </when>
            <otherwise>
                t.ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByTownId != null and ${joiner}fkByTownId.rid != null">
                t.TOWN_ID = #{${joiner}fkByTownId.rid},
            </when>
            <otherwise>
                t.TOWN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByEconomyId != null and ${joiner}fkByEconomyId.rid != null">
                t.ECONOMY_ID = #{${joiner}fkByEconomyId.rid},
            </when>
            <otherwise>
                t.ECONOMY_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}institutionCode != null and ${joiner}institutionCode != ''">
                t.INSTITUTION_CODE = #{${joiner}institutionCode},
            </when>
            <otherwise>
                t.INSTITUTION_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCrptSizeId != null and ${joiner}fkByCrptSizeId.rid != null">
                t.CRPT_SIZE_ID = #{${joiner}fkByCrptSizeId.rid},
            </when>
            <otherwise>
                t.CRPT_SIZE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}safetyPrincipal != null and ${joiner}safetyPrincipal != ''">
                t.SAFETY_PRINCIPAL = #{${joiner}safetyPrincipal},
            </when>
            <otherwise>
                t.SAFETY_PRINCIPAL = null,
            </otherwise>
        </choose>
            t.FILING_DATE = #{${joiner}filingDate},
            t.BUILD_DATE = #{${joiner}buildDate},
        <choose>
            <when test="${joiner}linkman1 != null and ${joiner}linkman1 != ''">
                t.LINKMAN1 = #{${joiner}linkman1},
            </when>
            <otherwise>
                t.LINKMAN1 = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}position1 != null and ${joiner}position1 != ''">
                t.POSITION1 = #{${joiner}position1},
            </when>
            <otherwise>
                t.POSITION1 = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkphone1 != null and ${joiner}linkphone1 != ''">
                t.LINKPHONE1 = #{${joiner}linkphone1},
            </when>
            <otherwise>
                t.LINKPHONE1 = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkman2 != null and ${joiner}linkman2 != ''">
                t.LINKMAN2 = #{${joiner}linkman2},
            </when>
            <otherwise>
                t.LINKMAN2 = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}position2 != null and ${joiner}position2 != ''">
                t.POSITION2 = #{${joiner}position2},
            </when>
            <otherwise>
                t.POSITION2 = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkphone2 != null and ${joiner}linkphone2 != ''">
                t.LINKPHONE2 = #{${joiner}linkphone2},
            </when>
            <otherwise>
                t.LINKPHONE2 = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}safeposition != null and ${joiner}safeposition != ''">
                t.SAFEPOSITION = #{${joiner}safeposition},
            </when>
            <otherwise>
                t.SAFEPOSITION = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}safephone != null and ${joiner}safephone != ''">
                t.SAFEPHONE = #{${joiner}safephone},
            </when>
            <otherwise>
                t.SAFEPHONE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}subjeConn != null and ${joiner}subjeConn != ''">
                t.SUBJE_CONN = #{${joiner}subjeConn},
            </when>
            <otherwise>
                t.SUBJE_CONN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}enrolAddress != null and ${joiner}enrolAddress != ''">
                t.ENROL_ADDRESS = #{${joiner}enrolAddress},
            </when>
            <otherwise>
                t.ENROL_ADDRESS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}enrolPostalcode != null and ${joiner}enrolPostalcode != ''">
                t.ENROL_POSTALCODE = #{${joiner}enrolPostalcode},
            </when>
            <otherwise>
                t.ENROL_POSTALCODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}occManaOffice != null and ${joiner}occManaOffice != ''">
                t.OCC_MANA_OFFICE = #{${joiner}occManaOffice},
            </when>
            <otherwise>
                t.OCC_MANA_OFFICE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}regCode != null and ${joiner}regCode != ''">
                t.REG_CODE = #{${joiner}regCode},
            </when>
            <otherwise>
                t.REG_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}regMark != null and ${joiner}regMark != ''">
                t.REG_MARK = #{${joiner}regMark},
            </when>
            <otherwise>
                t.REG_MARK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}uuid != null and ${joiner}uuid != ''">
                t.UUID = #{${joiner}uuid},
            </when>
            <otherwise>
                t.UUID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}tarUuid != null and ${joiner}tarUuid != ''">
                t.TAR_UUID = #{${joiner}tarUuid},
            </when>
            <otherwise>
                t.TAR_UUID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}postcode != null and ${joiner}postcode != ''">
                t.POSTCODE = #{${joiner}postcode},
            </when>
            <otherwise>
                t.POSTCODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}pym != null and ${joiner}pym != ''">
                t.PYM = #{${joiner}pym},
            </when>
            <otherwise>
                t.PYM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}interPrcTag != null and ${joiner}interPrcTag != ''">
                t.INTER_PRC_TAG = #{${joiner}interPrcTag},
            </when>
            <otherwise>
                t.INTER_PRC_TAG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}femaleStaffNum != null and ${joiner}femaleStaffNum != ''">
                t.FEMALE_STAFF_NUM = #{${joiner}femaleStaffNum},
            </when>
            <otherwise>
                t.FEMALE_STAFF_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}femaleWorkNum != null and ${joiner}femaleWorkNum != ''">
                t.FEMALE_WORK_NUM = #{${joiner}femaleWorkNum},
            </when>
            <otherwise>
                t.FEMALE_WORK_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}createOrgId != null and ${joiner}createOrgId != ''">
                t.CREATE_ORG_ID = #{${joiner}createOrgId},
            </when>
            <otherwise>
                t.CREATE_ORG_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifSubOrg != null">
                t.IF_SUB_ORG = #{${joiner}ifSubOrg},
            </when>
            <otherwise>
                t.IF_SUB_ORG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}outsourceNum != null and ${joiner}outsourceNum != ''">
                t.OUTSOURCE_NUM = #{${joiner}outsourceNum},
            </when>
            <otherwise>
                t.OUTSOURCE_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByUpperUnitId != null and ${joiner}fkByUpperUnitId.rid != null">
                t.UPPER_UNIT_ID = #{${joiner}fkByUpperUnitId.rid},
            </when>
            <otherwise>
                t.UPPER_UNIT_ID = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_CRPT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_CRPT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_CRPT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TbTjCrpt" >
        update TB_TJ_CRPT t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TB_TJ_CRPT t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TbTjCrpt">
        delete from TB_TJ_CRPT
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <select id="findMainCrptInfoByInstitutionCode" resultMap="BaseResultMap" parameterType="java.util.List">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_CRPT t
        where t.IF_SUB_ORG = 0 and NVL(t.DEL_MARK, 0) = 0
        <if test="list != null and list.size > 0">
            AND t.INSTITUTION_CODE IN
            <foreach collection="list" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>

    <select id="findCrptWithZoneInfoByRids" resultMap="BaseResultMap" parameterType="java.util.List">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
            t1.FULL_NAME AS ZONE_FULL_NAME,
            t1.ZONE_NAME AS ZONE_NAME,
        </trim>
        from  TB_TJ_CRPT t
        left join TS_ZONE t1 on t.ZONE_ID = t1.RID
        where 1=1
        <if test="list != null and list.size > 0">
            AND t.RID IN
            <foreach collection="list" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>




</mapper>

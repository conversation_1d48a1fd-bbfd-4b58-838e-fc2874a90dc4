<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.timer.heth.mapper.TdTjCheckTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdTjCheckTask">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="ZONE_ID" property="fkByZoneId.rid" />
        <result column="ZONE_GB" property="fkByZoneId.zoneGb" />
        <result column="EXPORT_CONDITION" property="exportCondition" />
        <result column="CHECK_RST" property="checkRst" />
        <result column="CHECK_ADV" property="checkAdv" />
        <result column="CHECK_UNIT_ID" property="fkByCheckUnitId.rid" />
        <result column="CHECK_RSN_ID" property="fkByCheckRsnId.rid" />
        <result column="CHECK_DATE" property="checkDate" />
        <result column="TOTAL_NUM" property="totalNum" />
        <result column="STATE" property="state" />
        <result column="ERROR_MSG" property="errorMsg" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.ZONE_ID,t.EXPORT_CONDITION,t.CHECK_RST,t.CHECK_ADV,t.CHECK_UNIT_ID,t.CHECK_RSN_ID,t.CHECK_DATE,t.TOTAL_NUM,t.STATE,t.ERROR_MSG,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}fkByZoneId != null and ${joiner}fkByZoneId.rid != null">
            and ${mAlias}ZONE_ID = #{${joiner}fkByZoneId.rid}
        </if>
        <if test="${joiner}exportCondition != null">
            and ${mAlias}EXPORT_CONDITION = #{${joiner}exportCondition}
        </if>
        <if test="${joiner}checkRst != null">
            and ${mAlias}CHECK_RST = #{${joiner}checkRst}
        </if>
        <if test="${joiner}checkAdv != null">
            and ${mAlias}CHECK_ADV = #{${joiner}checkAdv}
        </if>
        <if test="${joiner}fkByCheckUnitId != null and ${joiner}fkByCheckUnitId.rid != null">
            and ${mAlias}CHECK_UNIT_ID = #{${joiner}fkByCheckUnitId.rid}
        </if>
        <if test="${joiner}fkByCheckRsnId != null and ${joiner}fkByCheckRsnId.rid != null">
            and ${mAlias}CHECK_RSN_ID = #{${joiner}fkByCheckRsnId.rid}
        </if>
        <if test="${joiner}checkDate != null">
            and ${mAlias}CHECK_DATE = #{${joiner}checkDate}
        </if>
        <if test="${joiner}totalNum != null">
            and ${mAlias}TOTAL_NUM = #{${joiner}totalNum}
        </if>
        <if test="${joiner}state != null">
            and ${mAlias}STATE = #{${joiner}state}
        </if>
        <if test="${joiner}errorMsg != null">
            and ${mAlias}ERROR_MSG = #{${joiner}errorMsg}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}modifyDate != null">
                t.MODIFY_DATE = #{${joiner}modifyDate},
            </when>
            <otherwise>
                t.MODIFY_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}modifyManid != null">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByZoneId != null and ${joiner}fkByZoneId.rid != null">
                t.ZONE_ID = #{${joiner}fkByZoneId.rid},
            </when>
            <otherwise>
                t.ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}exportCondition != null">
                t.EXPORT_CONDITION = #{${joiner}exportCondition},
            </when>
            <otherwise>
                t.EXPORT_CONDITION = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}checkRst != null">
                t.CHECK_RST = #{${joiner}checkRst},
            </when>
            <otherwise>
                t.CHECK_RST = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}checkAdv != null">
                t.CHECK_ADV = #{${joiner}checkAdv},
            </when>
            <otherwise>
                t.CHECK_ADV = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCheckUnitId != null and ${joiner}fkByCheckUnitId.rid != null">
                t.CHECK_UNIT_ID = #{${joiner}fkByCheckUnitId.rid},
            </when>
            <otherwise>
                t.CHECK_UNIT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByCheckRsnId != null and ${joiner}fkByCheckRsnId.rid != null">
                t.CHECK_RSN_ID = #{${joiner}fkByCheckRsnId.rid},
            </when>
            <otherwise>
                t.CHECK_RSN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}checkDate != null">
                t.CHECK_DATE = #{${joiner}checkDate},
            </when>
            <otherwise>
                t.CHECK_DATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}totalNum != null">
                t.TOTAL_NUM = #{${joiner}totalNum},
            </when>
            <otherwise>
                t.TOTAL_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}state != null">
                t.STATE = #{${joiner}state},
            </when>
            <otherwise>
                t.STATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}errorMsg != null">
                t.ERROR_MSG = #{${joiner}errorMsg},
            </when>
            <otherwise>
                t.ERROR_MSG = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_CHECK_TASK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_CHECK_TASK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_TJ_CHECK_TASK t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdTjCheckTask">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_TJ_CHECK_TASK_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_TJ_CHECK_TASK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            ZONE_ID,
            EXPORT_CONDITION,
            CHECK_RST,
            CHECK_ADV,
            CHECK_UNIT_ID,
            CHECK_RSN_ID,
            CHECK_DATE,
            TOTAL_NUM,
            STATE,
            ERROR_MSG,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{modifyDate},
            #{modifyManid},
            #{fkByZoneId.rid},
            #{exportCondition},
            #{checkRst},
            #{checkAdv},
            #{fkByCheckUnitId.rid},
            #{fkByCheckRsnId.rid},
            #{checkDate},
            #{totalNum},
            #{state},
            #{errorMsg},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_TJ_CHECK_TASK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            ZONE_ID,
            EXPORT_CONDITION,
            CHECK_RST,
            CHECK_ADV,
            CHECK_UNIT_ID,
            CHECK_RSN_ID,
            CHECK_DATE,
            TOTAL_NUM,
            STATE,
            ERROR_MSG,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_TJ_CHECK_TASK_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByZoneId != null and item.fkByZoneId.rid != null">
                            #{item.fkByZoneId.rid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.exportCondition != null">
                            #{item.exportCondition} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.checkRst != null">
                            #{item.checkRst} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.checkAdv != null">
                            #{item.checkAdv} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByCheckUnitId != null and item.fkByCheckUnitId.rid != null">
                            #{item.fkByCheckUnitId.rid} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByCheckRsnId != null and item.fkByCheckRsnId.rid != null">
                            #{item.fkByCheckRsnId.rid} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.checkDate != null">
                            #{item.checkDate} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.totalNum != null">
                            #{item.totalNum} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.state != null">
                            #{item.state} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.errorMsg != null">
                            #{item.errorMsg} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdTjCheckTask" >
        update TD_TJ_CHECK_TASK t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_TJ_CHECK_TASK t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>


    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdTjCheckTask">
        delete from TD_TJ_CHECK_TASK
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <select id="queryTasks" resultMap="BaseResultMap">
        select T.*
        from TD_TJ_CHECK_TASK T
        where nvl(T.STATE, 0) = 0
          and nvl(T.TASK_TYPE, 0) = 0
        order by T.CHECK_DATE
    </select>

    <update id="updateTaskByRid">
        update TD_TJ_CHECK_TASK
        set
            <if test="statue!=null">
                STATE = #{statue},
            </if>
            <if test="totalNum!=null">
                TOTAL_NUM= #{totalNum},
            </if>
            ERROR_MSG=#{errorMsg},
            MODIFY_DATE=sysdate,
            MODIFY_MANID=#{psnId}
        where RID = #{rid}
    </update>
    <!-- 更新任务信息 -->
    <update id="updateTask">
        UPDATE TD_TJ_CHECK_TASK
        <set>
            <if test="task.state != null">
                STATE = #{task.state},
            </if>
            <if test="task.errorMsg != null">
                ERROR_MSG = #{task.errorMsg},
            </if>
            <if test="task.errorFilePath != null">
                ERROR_FILE_PATH = #{task.errorFilePath},
            </if>
            <if test="task.modifyDate != null">
                MODIFY_DATE = #{task.modifyDate},
            </if>
            <if test="task.modifyManid != null">
                MODIFY_MANID = #{task.modifyManid},
            </if>
            <if test="task.checkDate != null">
                CHECK_DATE = #{task.checkDate},
            </if>
            <if test="task.checkRst != null">
                CHECK_RST = #{task.checkRst},
            </if>
            <if test="task.checkAdv != null">
                CHECK_ADV = #{task.checkAdv},
            </if>
            <if test="task.totalNum != null">
                TOTAL_NUM = #{task.totalNum},
            </if>
        </set>
        WHERE RID = #{task.rid}
    </update>

    <!-- 查询待处理的批量退回任务 -->
    <select id="selectPendingTasks" resultMap="BaseResultMap">
        SELECT T.*,Z.ZONE_GB
        FROM 
            TD_TJ_CHECK_TASK T
            INNER JOIN TS_ZONE Z on Z.RID = T.ZONE_ID
        WHERE 
            T.TASK_TYPE = 1 
            AND  nvl(T.STATE, 0) = 0
        ORDER BY 
            T.CREATE_DATE
    </select>
</mapper>

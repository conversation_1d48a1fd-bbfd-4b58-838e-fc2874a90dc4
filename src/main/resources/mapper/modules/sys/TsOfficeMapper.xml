<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TsOfficeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TsOffice">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="UNIT_RID" property="fkByUnitRid.rid" />
        <result column="NUM" property="num" />
        <result column="OFFICECODE" property="officecode" />
        <result column="OFFICENAME" property="officename" />
        <result column="SIMPL_NAME" property="simplName" />
        <result column="OFFICETYPE" property="officetype" />
        <result column="OFFICETEL" property="officetel" />
        <result column="OFFICEFAX" property="officefax" />
        <result column="DEPT_LEADER_ID" property="fkByDeptLeaderId.rid" />
        <result column="MANAGE_MANID" property="fkByManageManid.rid" />
        <result column="SPLSHT" property="splsht" />
        <result column="IF_REVEAL" property="ifReveal" />
        <result column="UP_ID" property="fkByUpId.rid" />
        <result column="IS_YJOFFICE" property="isYjoffice" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.UNIT_RID,t.NUM,t.OFFICECODE,t.OFFICENAME,t.SIMPL_NAME,t.OFFICETYPE,t.OFFICETEL,t.OFFICEFAX,t.DEPT_LEADER_ID,t.MANAGE_MANID,t.SPLSHT,t.IF_REVEAL,t.UP_ID,t.IS_YJOFFICE,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="rid != null">
            and ${mAlias}RID = #{rid}
        </if>
        <if test="createDate != null">
            and ${mAlias}CREATE_DATE = #{createDate}
        </if>
        <if test="createManid != null">
            and ${mAlias}CREATE_MANID = #{createManid}
        </if>
        <if test="modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{modifyDate}
        </if>
        <if test="modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{modifyManid}
        </if>
        <if test="fkByUnitRid != null and fkByUnitRid.rid != null">
            and ${mAlias}UNIT_RID = #{fkByUnitRid.rid}
        </if>
        <if test="num != null and num != ''">
            and ${mAlias}NUM = #{num}
        </if>
        <if test="officecode != null and officecode != ''">
            and ${mAlias}OFFICECODE = #{officecode}
        </if>
        <if test="officename != null and officename != ''">
            and ${mAlias}OFFICENAME = #{officename}
        </if>
        <if test="simplName != null and simplName != ''">
            and ${mAlias}SIMPL_NAME = #{simplName}
        </if>
        <if test="officetype != null and officetype != ''">
            and ${mAlias}OFFICETYPE = #{officetype}
        </if>
        <if test="officetel != null and officetel != ''">
            and ${mAlias}OFFICETEL = #{officetel}
        </if>
        <if test="officefax != null and officefax != ''">
            and ${mAlias}OFFICEFAX = #{officefax}
        </if>
        <if test="fkByDeptLeaderId != null and fkByDeptLeaderId.rid != null">
            and ${mAlias}DEPT_LEADER_ID = #{fkByDeptLeaderId.rid}
        </if>
        <if test="fkByManageManid != null and fkByManageManid.rid != null">
            and ${mAlias}MANAGE_MANID = #{fkByManageManid.rid}
        </if>
        <if test="splsht != null and splsht != ''">
            and ${mAlias}SPLSHT = #{splsht}
        </if>
        <if test="ifReveal != null and ifReveal != ''">
            and ${mAlias}IF_REVEAL = #{ifReveal}
        </if>
        <if test="fkByUpId != null and fkByUpId.rid != null">
            and ${mAlias}UP_ID = #{fkByUpId.rid}
        </if>
        <if test="isYjoffice != null and isYjoffice != ''">
            and ${mAlias}IS_YJOFFICE = #{isYjoffice}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByUnitRid != null and ${joiner}fkByUnitRid.rid != null">
                t.UNIT_RID = #{${joiner}fkByUnitRid.rid},
            </when>
            <otherwise>
                t.UNIT_RID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}num != null and ${joiner}num != ''">
                t.NUM = #{${joiner}num},
            </when>
            <otherwise>
                t.NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}officecode != null and ${joiner}officecode != ''">
                t.OFFICECODE = #{${joiner}officecode},
            </when>
            <otherwise>
                t.OFFICECODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}officename != null and ${joiner}officename != ''">
                t.OFFICENAME = #{${joiner}officename},
            </when>
            <otherwise>
                t.OFFICENAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}simplName != null and ${joiner}simplName != ''">
                t.SIMPL_NAME = #{${joiner}simplName},
            </when>
            <otherwise>
                t.SIMPL_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}officetype != null and ${joiner}officetype != ''">
                t.OFFICETYPE = #{${joiner}officetype},
            </when>
            <otherwise>
                t.OFFICETYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}officetel != null and ${joiner}officetel != ''">
                t.OFFICETEL = #{${joiner}officetel},
            </when>
            <otherwise>
                t.OFFICETEL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}officefax != null and ${joiner}officefax != ''">
                t.OFFICEFAX = #{${joiner}officefax},
            </when>
            <otherwise>
                t.OFFICEFAX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByDeptLeaderId != null and ${joiner}fkByDeptLeaderId.rid != null">
                t.DEPT_LEADER_ID = #{${joiner}fkByDeptLeaderId.rid},
            </when>
            <otherwise>
                t.DEPT_LEADER_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByManageManid != null and ${joiner}fkByManageManid.rid != null">
                t.MANAGE_MANID = #{${joiner}fkByManageManid.rid},
            </when>
            <otherwise>
                t.MANAGE_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}splsht != null and ${joiner}splsht != ''">
                t.SPLSHT = #{${joiner}splsht},
            </when>
            <otherwise>
                t.SPLSHT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifReveal != null and ${joiner}ifReveal != ''">
                t.IF_REVEAL = #{${joiner}ifReveal},
            </when>
            <otherwise>
                t.IF_REVEAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByUpId != null and ${joiner}fkByUpId.rid != null">
                t.UP_ID = #{${joiner}fkByUpId.rid},
            </when>
            <otherwise>
                t.UP_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}isYjoffice != null and ${joiner}isYjoffice != ''">
                t.IS_YJOFFICE = #{${joiner}isYjoffice},
            </when>
            <otherwise>
                t.IS_YJOFFICE = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_OFFICE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_OFFICE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_OFFICE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TsOffice" >
        update TS_OFFICE t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TS_OFFICE t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TsOffice">
        delete from TS_OFFICE
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

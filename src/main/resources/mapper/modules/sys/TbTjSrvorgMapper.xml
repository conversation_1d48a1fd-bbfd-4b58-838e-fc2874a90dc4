<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TbTjSrvorgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TbTjSrvorg">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="ZONE_ID" property="zoneId" />
        <result column="UNIT_CODE" property="unitCode" />
        <result column="UNIT_NAME" property="unitName" />
        <result column="APT_SORTID" property="aptSortid" />
        <result column="STOP_TAG" property="stopTag" />
        <result column="REG_CODE" property="regCode" />
        <result column="REG_ORGID" property="regOrgid" />
        <result column="UUID" property="uuid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,
        t.ZONE_ID,t.UNIT_CODE,t.UNIT_NAME,t.APT_SORTID,t.STOP_TAG,t.REG_CODE,t.REG_ORGID,t.UUID,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="rid != null">
            and ${mAlias}RID = #{rid}
        </if>
        <if test="createDate != null">
            and ${mAlias}CREATE_DATE = #{createDate}
        </if>
        <if test="createManid != null">
            and ${mAlias}CREATE_MANID = #{createManid}
        </if>
        <if test="zoneId != null and zoneId != ''">
            and ${mAlias}ZONE_ID = #{zoneId}
        </if>
        <if test="unitCode != null and unitCode != ''">
            and ${mAlias}UNIT_CODE = #{unitCode}
        </if>
        <if test="unitName != null and unitName != ''">
            and ${mAlias}UNIT_NAME = #{unitName}
        </if>
        <if test="aptSortid != null and aptSortid != ''">
            and ${mAlias}APT_SORTID = #{aptSortid}
        </if>
        <if test="stopTag != null and stopTag != ''">
            and ${mAlias}STOP_TAG = #{stopTag}
        </if>
        <if test="regCode != null and regCode != ''">
            and ${mAlias}REG_CODE = #{regCode}
        </if>
        <if test="regOrgid != null and regOrgid != ''">
            and ${mAlias}REG_ORGID = #{regOrgid}
        </if>
        <if test="uuid != null and uuid != ''">
            and ${mAlias}UUID = #{uuid}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}zoneId != null and ${joiner}zoneId!= ''">
                t.ZONE_ID = #{${joiner}zoneId},
            </when>
            <otherwise>
                t.ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}unitCode != null and ${joiner}unitCode != ''">
                t.UNIT_CODE = #{${joiner}unitCode},
            </when>
            <otherwise>
                t.UNIT_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}unitName != null and ${joiner}unitName != ''">
                t.UNIT_NAME = #{${joiner}unitName},
            </when>
            <otherwise>
                t.UNIT_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}aptSortid != null and ${joiner}aptSortid != ''">
                t.APT_SORTID = #{${joiner}aptSortid},
            </when>
            <otherwise>
                t.APT_SORTID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}stopTag != null and ${joiner}stopTag != ''">
                t.STOP_TAG = #{${joiner}stopTag},
            </when>
            <otherwise>
                t.STOP_TAG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}regCode != null and ${joiner}regCode != ''">
                t.REG_CODE = #{${joiner}regCode},
            </when>
            <otherwise>
                t.REG_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}regOrgid != null and ${joiner}regOrgid != ''">
                t.REG_ORGID = #{${joiner}regOrgid},
            </when>
            <otherwise>
                t.REG_ORGID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}uuid != null and ${joiner}uuid != ''">
                t.UUID = #{${joiner}uuid},
            </when>
            <otherwise>
                t.UUID = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_SRVORG t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_SRVORG t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_SRVORG t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TbTjSrvorg" >
        update TB_TJ_SRVORG t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TB_TJ_SRVORG t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TbTjSrvorg">
        delete from TB_TJ_SRVORG
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->

	<!-- 批量查询 -->
    <select id="selectListByZoneCode" resultType="TbTjSrvorg">
        select
        <trim suffixOverrides=",">
        	<include refid="BaseColumnList"/>
	        T1.ZONE_GB AS "fkByZoneId.zoneGb",
	        T1.ZONE_NAME AS "fkByZoneId.zoneName",
	        T1.FULL_NAME AS "fkByZoneId.fullName",
	        T1.ZONE_TYPE AS "fkByZoneId.zoneType",
        </trim>
        from  TB_TJ_SRVORG t
        left join TS_ZONE T1 ON T1.RID = T.zone_id
        WHERE 1=1
        <if test="zoneGb != null and zoneGb!=''">
            and T1.zone_gb like ''||#{zoneGb}||'%'
        </if>
       	<if test="ifContainsProv != null and !ifContainsProv">
            and T1.ZONE_TYPE > 2
        </if>
        ORDER BY T1.ZONE_GB,t.UNIT_CODE
    </select>

    <select id="pageListToFJ" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
            t1.rid as uploadRcdId,
        </trim>
        from  TB_TJ_SRVORG t
        LEFT JOIN TD_ZYWS_UPLOAD_RCD T1 ON T1.BUS_ID = T.RID AND T1.BUS_TYPE = 3
        <where>
             and NVL(T1.STATE, 0) = 0
        </where>
        ) ZWX
        ) WHERE 1=1
        <if test="first != null and pageSize != null">
            AND RN BETWEEN #{first} AND #{pageSize}
        </if>
    </select>

    <select id="findGjSrvorg" resultType="TbTjSrvorg">
        select
        <trim suffixOverrides=",">
            t.RID as rid,
        </trim>
        from  TB_TJ_SRVORG t
        INNER JOIN TS_SYSTEM_PARAM t1 on t1.PARAM_VALUE = t.UNIT_CODE
        <where>
            t1.PARAM_NAME =#{paramName}
        </where>
    </select>

    <select id="findTbTjSrvorgAll" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_TJ_SRVORG t
        ORDER BY  NVL(t.STOP_TAG,1) ,t.rid
    </select>
    <select id="findTbTjSrvorgByUnitCode" resultType="com.chis.modules.sys.entity.TbTjSrvorg">
        SELECT T.RID,T1.UNIT_CODE,T.UNIT_NAME,T.ZONE_ID  FROM TB_TJ_SRVORG T
         LEFT JOIN TS_UNIT T1 ON T.REG_ORGID =T1.RID
        <if test="unitList != null and unitList.size > 0">
            WHERE  T1.UNIT_CODE IN
            <foreach collection="unitList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>

</mapper>

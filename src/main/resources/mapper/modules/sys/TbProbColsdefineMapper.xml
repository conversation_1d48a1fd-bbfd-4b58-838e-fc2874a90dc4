<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TbProbColsdefineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TbProbColsdefine">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="TABLE_ID" property="fkByTableId.rid" />
        <result column="NUM" property="num" />
        <result column="COL_NAME" property="colName" />
        <result column="COL_DESC" property="colDesc" />
        <result column="COL_TYPE" property="colType" />
        <result column="COLS" property="cols" />
        <result column="COL_EXPR" property="colExpr" />
        <result column="COL_LENTH" property="colLenth" />
        <result column="COL_PREC" property="colPrec" />
        <result column="COL_MUST" property="colMust" />
        <result column="COL_DEFVALUE" property="colDefvalue" />
        <result column="SCOPE_CONS" property="scopeCons" />
        <result column="MIN_VALUE" property="minValue" />
        <result column="MAX_VALUE" property="maxValue" />
        <result column="DS_TYPE" property="dsType" />
        <result column="DS_CDCODE" property="dsCdcode" />
        <result column="DS_SQL" property="dsSql" />
        <result column="ROW_INDEX" property="rowIndex" />
        <result column="COL_INDEX" property="colIndex" />
        <result column="ROWSPAN" property="rowspan" />
        <result column="STATE" property="state" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,
        t.TABLE_ID,t.NUM,t.COL_NAME,t.COL_DESC,t.COL_TYPE,t.COLS,t.COL_EXPR,t.COL_LENTH,t.COL_PREC,t.COL_MUST,t.COL_DEFVALUE,t.SCOPE_CONS,t.MIN_VALUE,t.MAX_VALUE,t.DS_TYPE,t.DS_CDCODE,t.DS_SQL,t.ROW_INDEX,t.COL_INDEX,t.ROWSPAN,t.STATE,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}fkByTableId != null and ${joiner}fkByTableId.rid != null">
            and ${mAlias}TABLE_ID = #{${joiner}fkByTableId.rid}
        </if>
        <if test="${joiner}num != null and ${joiner}num != ''">
            and ${mAlias}NUM = #{${joiner}num}
        </if>
        <if test="${joiner}colName != null and ${joiner}colName != ''">
            and ${mAlias}COL_NAME = #{${joiner}colName}
        </if>
        <if test="${joiner}colDesc != null and ${joiner}colDesc != ''">
            and ${mAlias}COL_DESC = #{${joiner}colDesc}
        </if>
        <if test="${joiner}colType != null and ${joiner}colType != ''">
            and ${mAlias}COL_TYPE = #{${joiner}colType}
        </if>
        <if test="${joiner}cols != null and ${joiner}cols != ''">
            and ${mAlias}COLS = #{${joiner}cols}
        </if>
        <if test="${joiner}colExpr != null and ${joiner}colExpr != ''">
            and ${mAlias}COL_EXPR = #{${joiner}colExpr}
        </if>
        <if test="${joiner}colLenth != null and ${joiner}colLenth != ''">
            and ${mAlias}COL_LENTH = #{${joiner}colLenth}
        </if>
        <if test="${joiner}colPrec != null and ${joiner}colPrec != ''">
            and ${mAlias}COL_PREC = #{${joiner}colPrec}
        </if>
        <if test="${joiner}colMust != null and ${joiner}colMust != ''">
            and ${mAlias}COL_MUST = #{${joiner}colMust}
        </if>
        <if test="${joiner}colDefvalue != null and ${joiner}colDefvalue != ''">
            and ${mAlias}COL_DEFVALUE = #{${joiner}colDefvalue}
        </if>
        <if test="${joiner}scopeCons != null and ${joiner}scopeCons != ''">
            and ${mAlias}SCOPE_CONS = #{${joiner}scopeCons}
        </if>
        <if test="${joiner}minValue != null and ${joiner}minValue != ''">
            and ${mAlias}MIN_VALUE = #{${joiner}minValue}
        </if>
        <if test="${joiner}maxValue != null and ${joiner}maxValue != ''">
            and ${mAlias}MAX_VALUE = #{${joiner}maxValue}
        </if>
        <if test="${joiner}dsType != null and ${joiner}dsType != ''">
            and ${mAlias}DS_TYPE = #{${joiner}dsType}
        </if>
        <if test="${joiner}dsCdcode != null and ${joiner}dsCdcode != ''">
            and ${mAlias}DS_CDCODE = #{${joiner}dsCdcode}
        </if>
        <if test="${joiner}dsSql != null and ${joiner}dsSql != ''">
            and ${mAlias}DS_SQL = #{${joiner}dsSql}
        </if>
        <if test="${joiner}rowIndex != null and ${joiner}rowIndex != ''">
            and ${mAlias}ROW_INDEX = #{${joiner}rowIndex}
        </if>
        <if test="${joiner}colIndex != null and ${joiner}colIndex != ''">
            and ${mAlias}COL_INDEX = #{${joiner}colIndex}
        </if>
        <if test="${joiner}rowspan != null and ${joiner}rowspan != ''">
            and ${mAlias}ROWSPAN = #{${joiner}rowspan}
        </if>
        <if test="${joiner}state != null and ${joiner}state != ''">
            and ${mAlias}STATE = #{${joiner}state}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}fkByTableId != null and ${joiner}fkByTableId.rid != null">
                t.TABLE_ID = #{${joiner}fkByTableId.rid},
            </when>
            <otherwise>
                t.TABLE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}num != null and ${joiner}num != ''">
                t.NUM = #{${joiner}num},
            </when>
            <otherwise>
                t.NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}colName != null and ${joiner}colName != ''">
                t.COL_NAME = #{${joiner}colName},
            </when>
            <otherwise>
                t.COL_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}colDesc != null and ${joiner}colDesc != ''">
                t.COL_DESC = #{${joiner}colDesc},
            </when>
            <otherwise>
                t.COL_DESC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}colType != null and ${joiner}colType != ''">
                t.COL_TYPE = #{${joiner}colType},
            </when>
            <otherwise>
                t.COL_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}cols != null and ${joiner}cols != ''">
                t.COLS = #{${joiner}cols},
            </when>
            <otherwise>
                t.COLS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}colExpr != null and ${joiner}colExpr != ''">
                t.COL_EXPR = #{${joiner}colExpr},
            </when>
            <otherwise>
                t.COL_EXPR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}colLenth != null and ${joiner}colLenth != ''">
                t.COL_LENTH = #{${joiner}colLenth},
            </when>
            <otherwise>
                t.COL_LENTH = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}colPrec != null and ${joiner}colPrec != ''">
                t.COL_PREC = #{${joiner}colPrec},
            </when>
            <otherwise>
                t.COL_PREC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}colMust != null and ${joiner}colMust != ''">
                t.COL_MUST = #{${joiner}colMust},
            </when>
            <otherwise>
                t.COL_MUST = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}colDefvalue != null and ${joiner}colDefvalue != ''">
                t.COL_DEFVALUE = #{${joiner}colDefvalue},
            </when>
            <otherwise>
                t.COL_DEFVALUE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}scopeCons != null and ${joiner}scopeCons != ''">
                t.SCOPE_CONS = #{${joiner}scopeCons},
            </when>
            <otherwise>
                t.SCOPE_CONS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}minValue != null and ${joiner}minValue != ''">
                t.MIN_VALUE = #{${joiner}minValue},
            </when>
            <otherwise>
                t.MIN_VALUE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}maxValue != null and ${joiner}maxValue != ''">
                t.MAX_VALUE = #{${joiner}maxValue},
            </when>
            <otherwise>
                t.MAX_VALUE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dsType != null and ${joiner}dsType != ''">
                t.DS_TYPE = #{${joiner}dsType},
            </when>
            <otherwise>
                t.DS_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dsCdcode != null and ${joiner}dsCdcode != ''">
                t.DS_CDCODE = #{${joiner}dsCdcode},
            </when>
            <otherwise>
                t.DS_CDCODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dsSql != null and ${joiner}dsSql != ''">
                t.DS_SQL = #{${joiner}dsSql},
            </when>
            <otherwise>
                t.DS_SQL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rowIndex != null and ${joiner}rowIndex != ''">
                t.ROW_INDEX = #{${joiner}rowIndex},
            </when>
            <otherwise>
                t.ROW_INDEX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}colIndex != null and ${joiner}colIndex != ''">
                t.COL_INDEX = #{${joiner}colIndex},
            </when>
            <otherwise>
                t.COL_INDEX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rowspan != null and ${joiner}rowspan != ''">
                t.ROWSPAN = #{${joiner}rowspan},
            </when>
            <otherwise>
                t.ROWSPAN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}state != null and ${joiner}state != ''">
                t.STATE = #{${joiner}state},
            </when>
            <otherwise>
                t.STATE = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_PROB_COLSDEFINE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_PROB_COLSDEFINE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_PROB_COLSDEFINE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TB_PROB_COLSDEFINE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            TABLE_ID,
            NUM,
            COL_NAME,
            COL_DESC,
            COL_TYPE,
            COLS,
            COL_EXPR,
            COL_LENTH,
            COL_PREC,
            COL_MUST,
            COL_DEFVALUE,
            SCOPE_CONS,
            MIN_VALUE,
            MAX_VALUE,
            DS_TYPE,
            DS_CDCODE,
            DS_SQL,
            ROW_INDEX,
            COL_INDEX,
            ROWSPAN,
            STATE,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TB_PROB_COLSDEFINE_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByTableId != null and item.fkByTableId.rid != null">
                            #{item.fkByTableId.rid} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.num != null">
                            #{item.num} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.colName != null">
                            #{item.colName} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.colDesc != null">
                            #{item.colDesc} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.colType != null">
                            #{item.colType} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.cols != null">
                            #{item.cols} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.colExpr != null">
                            #{item.colExpr} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.colLenth != null">
                            #{item.colLenth} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.colPrec != null">
                            #{item.colPrec} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.colMust != null">
                            #{item.colMust} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.colDefvalue != null">
                            #{item.colDefvalue} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.scopeCons != null">
                            #{item.scopeCons} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.minValue != null">
                            #{item.minValue} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.maxValue != null">
                            #{item.maxValue} AS C16,
                        </when>
                        <otherwise>
                            NULL AS C16,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.dsType != null">
                            #{item.dsType} AS C17,
                        </when>
                        <otherwise>
                            NULL AS C17,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.dsCdcode != null">
                            #{item.dsCdcode} AS C18,
                        </when>
                        <otherwise>
                            NULL AS C18,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.dsSql != null">
                            #{item.dsSql} AS C19,
                        </when>
                        <otherwise>
                            NULL AS C19,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rowIndex != null">
                            #{item.rowIndex} AS C20,
                        </when>
                        <otherwise>
                            NULL AS C20,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.colIndex != null">
                            #{item.colIndex} AS C21,
                        </when>
                        <otherwise>
                            NULL AS C21,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rowspan != null">
                            #{item.rowspan} AS C22,
                        </when>
                        <otherwise>
                            NULL AS C22,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.state != null">
                            #{item.state} AS C23,
                        </when>
                        <otherwise>
                            NULL AS C23,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TbProbColsdefine" >
        update TB_PROB_COLSDEFINE t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TB_PROB_COLSDEFINE t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TbProbColsdefine">
        delete from TB_PROB_COLSDEFINE
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->

    <select id="findTbProbColsdefinesByRidList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_PROB_COLSDEFINE t
        where 1=1
        <if test="ridList != null and ridList.size > 0">
            AND t.rid IN
            <foreach collection="ridList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>

    <select id="findTbProbColsdefinesByTableId" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_PROB_COLSDEFINE t
        where STATE = 1
        <if test="tableId != null and tableId!=''">
                and t.TABLE_ID=#{tableId}
        </if>
    </select>

    <select id="findTbProbColsdefinesByTableIdList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_PROB_COLSDEFINE t
        where STATE = 1
        <if test="list != null and list.size > 0">
            AND t.TABLE_ID IN
            <foreach collection="list" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>


</mapper>

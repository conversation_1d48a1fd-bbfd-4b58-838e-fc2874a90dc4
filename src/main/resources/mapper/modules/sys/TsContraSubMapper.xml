<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TsContraSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TsContraSub">
        <result column="RID" property="rid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="BUSI_TYPE" property="busiType" />
        <result column="LEFT_CODE" property="leftCode" />
        <result column="RIGHT_CODE" property="rightCode" />
        <result column="DESCR" property="descr" />
        <result column="DSF_TAG" property="dsfTag" />
        <result column="DSF_SPECIAL_DESC" property="dsfSpecialDesc" />
        <result column="SIMP_RID" property="simpRid" />
        <result column="EXTENDS2" property="extends2" />
        <result column="EXTENDS3" property="extends3" />
        <result column="LEFT_DESC" property="leftDesc" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,
        t.MAIN_ID,t.BUSI_TYPE,t.LEFT_CODE,t.RIGHT_CODE,t.DESCR,t.DSF_TAG,t.DSF_SPECIAL_DESC,t.LEFT_DESC
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{${joiner}fkByMainId.rid}
        </if>
        <if test="${joiner}busiType != null and ${joiner}busiType != ''">
            and ${mAlias}BUSI_TYPE = #{${joiner}busiType}
        </if>
        <if test="${joiner}leftCode != null and ${joiner}leftCode != ''">
            and ${mAlias}LEFT_CODE = #{${joiner}leftCode}
        </if>
        <if test="${joiner}rightCode != null and ${joiner}rightCode != ''">
            and ${mAlias}RIGHT_CODE = #{${joiner}rightCode}
        </if>
        <if test="${joiner}descr != null and ${joiner}descr != ''">
            and ${mAlias}DESCR = #{${joiner}descr}
        </if>
        <if test="${joiner}dsfTag != null and ${joiner}dsfTag != ''">
            and ${mAlias}DSF_TAG = #{${joiner}dsfTag}
        </if>
        <if test="${joiner}dsfSpecialDesc != null and ${joiner}dsfSpecialDesc != ''">
            and ${mAlias}DSF_SPECIAL_DESC = #{${joiner}dsfSpecialDesc}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}busiType != null and ${joiner}busiType != ''">
                t.BUSI_TYPE = #{${joiner}busiType},
            </when>
            <otherwise>
                t.BUSI_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}leftCode != null and ${joiner}leftCode != ''">
                t.LEFT_CODE = #{${joiner}leftCode},
            </when>
            <otherwise>
                t.LEFT_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rightCode != null and ${joiner}rightCode != ''">
                t.RIGHT_CODE = #{${joiner}rightCode},
            </when>
            <otherwise>
                t.RIGHT_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}descr != null and ${joiner}descr != ''">
                t.DESCR = #{${joiner}descr},
            </when>
            <otherwise>
                t.DESCR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dsfTag != null and ${joiner}dsfTag != ''">
                t.DSF_TAG = #{${joiner}dsfTag},
            </when>
            <otherwise>
                t.DSF_TAG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dsfSpecialDesc != null and ${joiner}dsfSpecialDesc != ''">
                t.DSF_SPECIAL_DESC = #{${joiner}dsfSpecialDesc},
            </when>
            <otherwise>
                t.DSF_SPECIAL_DESC = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_CONTRA_SUB t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_CONTRA_SUB t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_CONTRA_SUB t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TsContraSub" >
        update TS_CONTRA_SUB t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TS_CONTRA_SUB t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TsContraSub">
        delete from TS_CONTRA_SUB
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <select id="findTsContraSub" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_CONTRA_SUB t
        INNER JOIN TS_CONTRA_MAIN T1 ON T1.RID = T.MAIN_ID
        <where>
            <if test="contraCode != null and contraCode != ''">
                AND T1.CONTRA_CODE = #{contraCode}
            </if>
            <if test="busType != null and busType != ''">
                AND T.BUSI_TYPE = #{busType}
            </if>
        </where>
    </select>

    <select id="selectContraSubToCountry" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_CONTRA_SUB t
        left join TS_CONTRA_MAIN t1 on t.main_Id = t1.rid
        <where>
            and t1.CONTRA_CODE = 7
        </where>
    </select>

    <select id="selectParam" resultType="String">
        select
        t.PARAM_VALUE
        from  ts_system_param t
        <where>
            1 =1
            AND t.IF_REVEAL =1
            <if test="type!=null ">
                　　AND t.PARAM_TYPE =#{type}
            </if>
            <if test="name!=null and name !=''" >
                　　AND t.PARAM_NAME =#{name}
            </if>
        </where>
    </select>

    <select id="findSimpByContraSub" resultMap="BaseResultMap">
        select T.RIGHT_CODE,T.DESCR,TSC.RID as SIMP_RID,T.DSF_SPECIAL_DESC,TSC.EXTENDS2,TSC.EXTENDS1,T.LEFT_CODE,TSC.EXTENDS3
        from  TS_CONTRA_SUB T
                  INNER JOIN TS_CONTRA_MAIN T1 ON T1.RID = T.MAIN_ID
                  left join TS_SIMPLE_CODE TSC on T.RIGHT_CODE = TSC.CODE_NO
                  left join TS_CODE_TYPE TCT on TSC.CODE_TYPE_ID = TCT.RID
        where T1.CONTRA_CODE=#{contraCode} and T.BUSI_TYPE=#{busiType}
        <if test="codeTypeName!=null and codeTypeName !=''" >
            and TCT.CODE_TYPE_NAME=#{codeTypeName}
        </if>
        order by TSC.IF_REVEAL desc,TSC.RID desc
    </select>

    <select id="findSimpLeftCodeByContraSub" resultMap="BaseResultMap">
        select T.RIGHT_CODE,T.DESCR,TSC.RID as SIMP_RID,T.DSF_SPECIAL_DESC,TSC.EXTENDS2,TSC.EXTENDS1,T.LEFT_CODE,TSC.EXTENDS3,T.LEFT_DESC
        from  TS_CONTRA_SUB T
        INNER JOIN TS_CONTRA_MAIN T1 ON T1.RID = T.MAIN_ID
        left join TS_SIMPLE_CODE TSC on T.LEFT_CODE = TSC.CODE_NO
        left join TS_CODE_TYPE TCT on TSC.CODE_TYPE_ID = TCT.RID
        where T1.CONTRA_CODE=#{contraCode} and T.BUSI_TYPE=#{busiType}
        <if test="codeTypeName!=null and codeTypeName !=''" >
            and TCT.CODE_TYPE_NAME=#{codeTypeName}
        </if>
        order by TSC.IF_REVEAL desc,TSC.RID desc
    </select>

</mapper>

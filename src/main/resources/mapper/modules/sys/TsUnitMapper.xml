<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TsUnitMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TsUnit">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="ZONE_ID" property="zoneId" />
        <result column="UNITNAME" property="unitname" />
        <result column="UNIT_SIMPNAME" property="unitSimpname" />
        <result column="UNITZIP" property="unitzip" />
        <result column="UNITADDR" property="unitaddr" />
        <result column="UNITAERA" property="unitaera" />
        <result column="UNITTEL" property="unittel" />
        <result column="UNITFAX" property="unitfax" />
        <result column="UNITEMAIL" property="unitemail" />
        <result column="IF_REVEAL" property="ifReveal" />
        <result column="STOP_DATE" property="stopDate" />
        <result column="SPLSHT" property="splsht" />
        <result column="UNIT_CODE" property="unitCode" />
        <result column="REG_CODE" property="regCode" />
        <result column="LNG" property="lng" />
        <result column="LAT" property="lat" />
        <result column="LINK_MAN" property="linkMan" />
        <result column="MEDI_LIC" property="mediLic" />
        <result column="RAD_LIC" property="radLic" />
        <result column="ORG_FZ" property="orgFz" />
        <result column="ORG_FZZW" property="orgFzzw" />
        <result column="ORG_TEL" property="orgTel" />
        <result column="CREDIT_CODE" property="creditCode" />
        <result column="SAFE_UNITNAME" property="safeUnitname" />
        <result column="JD_UNITNAME" property="jdUnitname" />
        <result column="PROVE_BAK" property="proveBak" />
        <result column="IF_COMPLETE" property="ifComplete" />
        <result column="UPDATETAG" property="updatetag" />
        <result column="ERROR_MSG" property="errorMsg" />
        <result column="GPY_VERSION" property="gpyVersion" />
        <result column="MANAGE_ZONE_ID" property="manageZoneId" />
        <result column="WRITE_NO" property="writeNo" />
        <result column="TRUST_WRITE_NO" property="trustWriteNo" />
        <result column="PROVE_WRITE_NO" property="proveWriteNo" />
        <result column="RED_UNIT_NAME" property="redUnitName" />
        <result column="WRITE_NO_RULE" property="writeNoRule" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.ZONE_ID,t.UNITNAME,t.UNIT_SIMPNAME,t.UNITZIP,t.UNITADDR,t.UNITAERA,t.UNITTEL,t.UNITFAX,t.UNITEMAIL,
        t.IF_REVEAL,t.STOP_DATE,t.SPLSHT,t.UNIT_CODE,t.REG_CODE,t.LNG,t.LAT,t.LINK_MAN,t.MEDI_LIC,t.RAD_LIC,t.ORG_FZ,
        t.ORG_FZZW,t.ORG_TEL,t.CREDIT_CODE,t.SAFE_UNITNAME,t.JD_UNITNAME,t.PROVE_BAK,t.IF_COMPLETE,t.UPDATETAG,
        t.ERROR_MSG,t.GPY_VERSION,t.MANAGE_ZONE_ID,t.WRITE_NO,t.TRUST_WRITE_NO,t.PROVE_WRITE_NO,t.RED_UNIT_NAME,t.WRITE_NO_RULE,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="rid != null">
            and ${mAlias}RID = #{rid}
        </if>
        <if test="createDate != null">
            and ${mAlias}CREATE_DATE = #{createDate}
        </if>
        <if test="createManid != null">
            and ${mAlias}CREATE_MANID = #{createManid}
        </if>
        <if test="modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{modifyDate}
        </if>
        <if test="modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{modifyManid}
        </if>
        <if test="zoneId != null and zoneId != ''">
            and ${mAlias}ZONE_ID = #{zoneId}
        </if>
        <if test="unitname != null and unitname != ''">
            and ${mAlias}UNITNAME = #{unitname}
        </if>
        <if test="unitSimpname != null and unitSimpname != ''">
            and ${mAlias}UNIT_SIMPNAME = #{unitSimpname}
        </if>
        <if test="unitzip != null and unitzip != ''">
            and ${mAlias}UNITZIP = #{unitzip}
        </if>
        <if test="unitaddr != null and unitaddr != ''">
            and ${mAlias}UNITADDR = #{unitaddr}
        </if>
        <if test="unitaera != null and unitaera != ''">
            and ${mAlias}UNITAERA = #{unitaera}
        </if>
        <if test="unittel != null and unittel != ''">
            and ${mAlias}UNITTEL = #{unittel}
        </if>
        <if test="unitfax != null and unitfax != ''">
            and ${mAlias}UNITFAX = #{unitfax}
        </if>
        <if test="unitemail != null and unitemail != ''">
            and ${mAlias}UNITEMAIL = #{unitemail}
        </if>
        <if test="ifReveal != null and ifReveal != ''">
            and ${mAlias}IF_REVEAL = #{ifReveal}
        </if>
        <if test="stopDate != null">
            and ${mAlias}STOP_DATE = #{stopDate}
        </if>
        <if test="splsht != null and splsht != ''">
            and ${mAlias}SPLSHT = #{splsht}
        </if>
        <if test="unitCode != null and unitCode != ''">
            and ${mAlias}UNIT_CODE = #{unitCode}
        </if>
        <if test="regCode != null and regCode != ''">
            and ${mAlias}REG_CODE = #{regCode}
        </if>
        <if test="lng != null and lng != ''">
            and ${mAlias}LNG = #{lng}
        </if>
        <if test="lat != null and lat != ''">
            and ${mAlias}LAT = #{lat}
        </if>
        <if test="linkMan != null and linkMan != ''">
            and ${mAlias}LINK_MAN = #{linkMan}
        </if>
        <if test="mediLic != null and mediLic != ''">
            and ${mAlias}MEDI_LIC = #{mediLic}
        </if>
        <if test="radLic != null and radLic != ''">
            and ${mAlias}RAD_LIC = #{radLic}
        </if>
        <if test="orgFz != null and orgFz != ''">
            and ${mAlias}ORG_FZ = #{orgFz}
        </if>
        <if test="orgFzzw != null and orgFzzw != ''">
            and ${mAlias}ORG_FZZW = #{orgFzzw}
        </if>
        <if test="orgTel != null and orgTel != ''">
            and ${mAlias}ORG_TEL = #{orgTel}
        </if>
        <if test="creditCode != null and creditCode != ''">
            and ${mAlias}CREDIT_CODE = #{creditCode}
        </if>
        <if test="safeUnitname != null and safeUnitname != ''">
            and ${mAlias}SAFE_UNITNAME = #{safeUnitname}
        </if>
        <if test="jdUnitname != null and jdUnitname != ''">
            and ${mAlias}JD_UNITNAME = #{jdUnitname}
        </if>
        <if test="proveBak != null and proveBak != ''">
            and ${mAlias}PROVE_BAK = #{proveBak}
        </if>
        <if test="ifComplete != null and ifComplete != ''">
            and ${mAlias}IF_COMPLETE = #{ifComplete}
        </if>
        <if test="updatetag != null and updatetag != ''">
            and ${mAlias}UPDATETAG = #{updatetag}
        </if>
        <if test="errorMsg != null and errorMsg != ''">
            and ${mAlias}ERROR_MSG = #{errorMsg}
        </if>
        <if test="gpyVersion != null and gpyVersion != ''">
            and ${mAlias}GPY_VERSION = #{gpyVersion}
        </if>
        <if test="manageZoneId != null and manageZoneId != ''">
            and ${mAlias}MANAGE_ZONE_ID = #{manageZoneId}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}zoneId != null and ${joiner}zoneId != ''">
                t.ZONE_ID = #{${joiner}zoneId},
            </when>
            <otherwise>
                t.ZONE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}unitname != null and ${joiner}unitname != ''">
                t.UNITNAME = #{${joiner}unitname},
            </when>
            <otherwise>
                t.UNITNAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}unitSimpname != null and ${joiner}unitSimpname != ''">
                t.UNIT_SIMPNAME = #{${joiner}unitSimpname},
            </when>
            <otherwise>
                t.UNIT_SIMPNAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}unitzip != null and ${joiner}unitzip != ''">
                t.UNITZIP = #{${joiner}unitzip},
            </when>
            <otherwise>
                t.UNITZIP = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}unitaddr != null and ${joiner}unitaddr != ''">
                t.UNITADDR = #{${joiner}unitaddr},
            </when>
            <otherwise>
                t.UNITADDR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}unitaera != null and ${joiner}unitaera != ''">
                t.UNITAERA = #{${joiner}unitaera},
            </when>
            <otherwise>
                t.UNITAERA = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}unittel != null and ${joiner}unittel != ''">
                t.UNITTEL = #{${joiner}unittel},
            </when>
            <otherwise>
                t.UNITTEL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}unitfax != null and ${joiner}unitfax != ''">
                t.UNITFAX = #{${joiner}unitfax},
            </when>
            <otherwise>
                t.UNITFAX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}unitemail != null and ${joiner}unitemail != ''">
                t.UNITEMAIL = #{${joiner}unitemail},
            </when>
            <otherwise>
                t.UNITEMAIL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifReveal != null and ${joiner}ifReveal != ''">
                t.IF_REVEAL = #{${joiner}ifReveal},
            </when>
            <otherwise>
                t.IF_REVEAL = null,
            </otherwise>
        </choose>
            t.STOP_DATE = #{${joiner}stopDate},
        <choose>
            <when test="${joiner}splsht != null and ${joiner}splsht != ''">
                t.SPLSHT = #{${joiner}splsht},
            </when>
            <otherwise>
                t.SPLSHT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}unitCode != null and ${joiner}unitCode != ''">
                t.UNIT_CODE = #{${joiner}unitCode},
            </when>
            <otherwise>
                t.UNIT_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}regCode != null and ${joiner}regCode != ''">
                t.REG_CODE = #{${joiner}regCode},
            </when>
            <otherwise>
                t.REG_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lng != null and ${joiner}lng != ''">
                t.LNG = #{${joiner}lng},
            </when>
            <otherwise>
                t.LNG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lat != null and ${joiner}lat != ''">
                t.LAT = #{${joiner}lat},
            </when>
            <otherwise>
                t.LAT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}linkMan != null and ${joiner}linkMan != ''">
                t.LINK_MAN = #{${joiner}linkMan},
            </when>
            <otherwise>
                t.LINK_MAN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}mediLic != null and ${joiner}mediLic != ''">
                t.MEDI_LIC = #{${joiner}mediLic},
            </when>
            <otherwise>
                t.MEDI_LIC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}radLic != null and ${joiner}radLic != ''">
                t.RAD_LIC = #{${joiner}radLic},
            </when>
            <otherwise>
                t.RAD_LIC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}orgFz != null and ${joiner}orgFz != ''">
                t.ORG_FZ = #{${joiner}orgFz},
            </when>
            <otherwise>
                t.ORG_FZ = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}orgFzzw != null and ${joiner}orgFzzw != ''">
                t.ORG_FZZW = #{${joiner}orgFzzw},
            </when>
            <otherwise>
                t.ORG_FZZW = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}orgTel != null and ${joiner}orgTel != ''">
                t.ORG_TEL = #{${joiner}orgTel},
            </when>
            <otherwise>
                t.ORG_TEL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}creditCode != null and ${joiner}creditCode != ''">
                t.CREDIT_CODE = #{${joiner}creditCode},
            </when>
            <otherwise>
                t.CREDIT_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}safeUnitname != null and ${joiner}safeUnitname != ''">
                t.SAFE_UNITNAME = #{${joiner}safeUnitname},
            </when>
            <otherwise>
                t.SAFE_UNITNAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}jdUnitname != null and ${joiner}jdUnitname != ''">
                t.JD_UNITNAME = #{${joiner}jdUnitname},
            </when>
            <otherwise>
                t.JD_UNITNAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}proveBak != null and ${joiner}proveBak != ''">
                t.PROVE_BAK = #{${joiner}proveBak},
            </when>
            <otherwise>
                t.PROVE_BAK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifComplete != null and ${joiner}ifComplete != ''">
                t.IF_COMPLETE = #{${joiner}ifComplete},
            </when>
            <otherwise>
                t.IF_COMPLETE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}updatetag != null and ${joiner}updatetag != ''">
                t.UPDATETAG = #{${joiner}updatetag},
            </when>
            <otherwise>
                t.UPDATETAG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}errorMsg != null and ${joiner}errorMsg != ''">
                t.ERROR_MSG = #{${joiner}errorMsg},
            </when>
            <otherwise>
                t.ERROR_MSG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}gpyVersion != null and ${joiner}gpyVersion != ''">
                t.GPY_VERSION = #{${joiner}gpyVersion},
            </when>
            <otherwise>
                t.GPY_VERSION = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}manageZoneId != null and ${joiner}manageZoneId != ''">
                t.MANAGE_ZONE_ID = #{${joiner}manageZoneId},
            </when>
            <otherwise>
                t.MANAGE_ZONE_ID = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_UNIT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_UNIT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_UNIT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TsUnit" >
        update TS_UNIT t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TS_UNIT t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TsUnit">
        delete from TS_UNIT
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <select id="pageListToFj" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
            t1.rid as uploadRcdId,
        </trim>
        from  TS_UNIT t
        LEFT JOIN TD_ZYWS_UPLOAD_RCD T1 ON T1.BUS_ID = T.RID AND T1.BUS_TYPE = 1
        <where>
            and NVL(T1.STATE, 0) = 0
        </where>
        ) ZWX
        ) WHERE 1=1
        <if test="first != null and pageSize != null">
            AND RN BETWEEN #{first} AND #{pageSize}
        </if>
    </select>

    <select id="findTsUnitAll" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_UNIT t
    </select>
    <select id="findTsUnitAllByZoneCode" resultType="com.chis.modules.sys.entity.TsUnit">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_UNIT t
        left join TS_ZONE T1 on t.ZONE_ID=T1.RID
        where T1.ZONE_GB like ''||#{zoneCode}||'%'
    </select>

    <select id="findTsUnitByDiagOrg" resultMap="BaseResultMap">
        SELECT
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        FROM TS_UNIT t
        WHERE EXISTS(SELECT 1 FROM TD_ZW_DIAGORGINFO D WHERE D.ORG_ID = T.RID)
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TsProPoolOptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TsProPoolOpt">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="QUEST_ID" property="fkByQuestId.rid" />
        <result column="NUM" property="num" />
        <result column="OPTION_DESC" property="optionDesc" />
        <result column="OPTION_IMG" property="optionImg" />
        <result column="OPTION_VALUE" property="optionValue" />
        <result column="OPTION_SCORE" property="optionScore" />
        <result column="NEED_FILL" property="needFill" />
        <result column="RMK" property="rmk" />
        <result column="STATE" property="state" />
        <result column="OTHER_DESC" property="otherDesc" />
        <result column="OTHER_IMG" property="otherImg" />
        <result column="JUMP_TYPE" property="jumpType" />
        <result column="JUMP_QUEST_CODE" property="jumpQuestCode" />
        <result column="IS_ALTER" property="isAlter" />
        <result column="IS_CORRECT" property="isCorrect" />
        <result column="IS_MULTI" property="isMulti" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,
        t.QUEST_ID,t.NUM,t.OPTION_DESC,t.OPTION_IMG,t.OPTION_VALUE,t.OPTION_SCORE,t.NEED_FILL,t.RMK,t.STATE,t.OTHER_DESC,t.OTHER_IMG,t.JUMP_TYPE,t.JUMP_QUEST_CODE,t.IS_ALTER,t.IS_CORRECT,t.IS_MULTI,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}fkByQuestId != null and ${joiner}fkByQuestId.rid != null">
            and ${mAlias}QUEST_ID = #{${joiner}fkByQuestId.rid}
        </if>
        <if test="${joiner}num != null">
            and ${mAlias}NUM = #{${joiner}num}
        </if>
        <if test="${joiner}optionDesc != null">
            and ${mAlias}OPTION_DESC = #{${joiner}optionDesc}
        </if>
        <if test="${joiner}optionImg != null">
            and ${mAlias}OPTION_IMG = #{${joiner}optionImg}
        </if>
        <if test="${joiner}optionValue != null">
            and ${mAlias}OPTION_VALUE = #{${joiner}optionValue}
        </if>
        <if test="${joiner}optionScore != null">
            and ${mAlias}OPTION_SCORE = #{${joiner}optionScore}
        </if>
        <if test="${joiner}needFill != null">
            and ${mAlias}NEED_FILL = #{${joiner}needFill}
        </if>
        <if test="${joiner}rmk != null">
            and ${mAlias}RMK = #{${joiner}rmk}
        </if>
        <if test="${joiner}state != null">
            and ${mAlias}STATE = #{${joiner}state}
        </if>
        <if test="${joiner}otherDesc != null">
            and ${mAlias}OTHER_DESC = #{${joiner}otherDesc}
        </if>
        <if test="${joiner}otherImg != null">
            and ${mAlias}OTHER_IMG = #{${joiner}otherImg}
        </if>
        <if test="${joiner}jumpType != null">
            and ${mAlias}JUMP_TYPE = #{${joiner}jumpType}
        </if>
        <if test="${joiner}jumpQuestCode != null">
            and ${mAlias}JUMP_QUEST_CODE = #{${joiner}jumpQuestCode}
        </if>
        <if test="${joiner}isAlter != null">
            and ${mAlias}IS_ALTER = #{${joiner}isAlter}
        </if>
        <if test="${joiner}isCorrect != null">
            and ${mAlias}IS_CORRECT = #{${joiner}isCorrect}
        </if>
        <if test="${joiner}isMulti != null">
            and ${mAlias}IS_MULTI = #{${joiner}isMulti}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}fkByQuestId != null and ${joiner}fkByQuestId.rid != null">
                t.QUEST_ID = #{${joiner}fkByQuestId.rid},
            </when>
            <otherwise>
                t.QUEST_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}num != null">
                t.NUM = #{${joiner}num},
            </when>
            <otherwise>
                t.NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}optionDesc != null">
                t.OPTION_DESC = #{${joiner}optionDesc},
            </when>
            <otherwise>
                t.OPTION_DESC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}optionImg != null">
                t.OPTION_IMG = #{${joiner}optionImg},
            </when>
            <otherwise>
                t.OPTION_IMG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}optionValue != null">
                t.OPTION_VALUE = #{${joiner}optionValue},
            </when>
            <otherwise>
                t.OPTION_VALUE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}optionScore != null">
                t.OPTION_SCORE = #{${joiner}optionScore},
            </when>
            <otherwise>
                t.OPTION_SCORE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}needFill != null">
                t.NEED_FILL = #{${joiner}needFill},
            </when>
            <otherwise>
                t.NEED_FILL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rmk != null">
                t.RMK = #{${joiner}rmk},
            </when>
            <otherwise>
                t.RMK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}state != null">
                t.STATE = #{${joiner}state},
            </when>
            <otherwise>
                t.STATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}otherDesc != null">
                t.OTHER_DESC = #{${joiner}otherDesc},
            </when>
            <otherwise>
                t.OTHER_DESC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}otherImg != null">
                t.OTHER_IMG = #{${joiner}otherImg},
            </when>
            <otherwise>
                t.OTHER_IMG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}jumpType != null">
                t.JUMP_TYPE = #{${joiner}jumpType},
            </when>
            <otherwise>
                t.JUMP_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}jumpQuestCode != null">
                t.JUMP_QUEST_CODE = #{${joiner}jumpQuestCode},
            </when>
            <otherwise>
                t.JUMP_QUEST_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}isAlter != null">
                t.IS_ALTER = #{${joiner}isAlter},
            </when>
            <otherwise>
                t.IS_ALTER = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}isCorrect != null">
                t.IS_CORRECT = #{${joiner}isCorrect},
            </when>
            <otherwise>
                t.IS_CORRECT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}isMulti != null">
                t.IS_MULTI = #{${joiner}isMulti},
            </when>
            <otherwise>
                t.IS_MULTI = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_PRO_POOL_OPT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_PRO_POOL_OPT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_PRO_POOL_OPT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TsProPoolOpt">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TS_PRO_POOL_OPT_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TS_PRO_POOL_OPT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            QUEST_ID,
            NUM,
            OPTION_DESC,
            OPTION_IMG,
            OPTION_VALUE,
            OPTION_SCORE,
            NEED_FILL,
            RMK,
            STATE,
            OTHER_DESC,
            OTHER_IMG,
            JUMP_TYPE,
            JUMP_QUEST_CODE,
            IS_ALTER,
            IS_CORRECT,
            IS_MULTI,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{fkByQuestId.rid},
            #{num},
            #{optionDesc},
            #{optionImg},
            #{optionValue},
            #{optionScore},
            #{needFill},
            #{rmk},
            #{state},
            #{otherDesc},
            #{otherImg},
            #{jumpType},
            #{jumpQuestCode},
            #{isAlter},
            #{isCorrect},
            #{isMulti},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TS_PRO_POOL_OPT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            QUEST_ID,
            NUM,
            OPTION_DESC,
            OPTION_IMG,
            OPTION_VALUE,
            OPTION_SCORE,
            NEED_FILL,
            RMK,
            STATE,
            OTHER_DESC,
            OTHER_IMG,
            JUMP_TYPE,
            JUMP_QUEST_CODE,
            IS_ALTER,
            IS_CORRECT,
            IS_MULTI,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TS_PRO_POOL_OPT_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByQuestId != null and item.fkByQuestId.rid != null">
                            #{item.fkByQuestId.rid} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.num != null">
                            #{item.num} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.optionDesc != null">
                            #{item.optionDesc} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.optionImg != null">
                            #{item.optionImg} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.optionValue != null">
                            #{item.optionValue} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.optionScore != null">
                            #{item.optionScore} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.needFill != null">
                            #{item.needFill} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rmk != null">
                            #{item.rmk} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.state != null">
                            #{item.state} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.otherDesc != null">
                            #{item.otherDesc} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.otherImg != null">
                            #{item.otherImg} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.jumpType != null">
                            #{item.jumpType} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.jumpQuestCode != null">
                            #{item.jumpQuestCode} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.isAlter != null">
                            #{item.isAlter} AS C16,
                        </when>
                        <otherwise>
                            NULL AS C16,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.isCorrect != null">
                            #{item.isCorrect} AS C17,
                        </when>
                        <otherwise>
                            NULL AS C17,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.isMulti != null">
                            #{item.isMulti} AS C18,
                        </when>
                        <otherwise>
                            NULL AS C18,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TsProPoolOpt" >
        update TS_PRO_POOL_OPT t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TS_PRO_POOL_OPT t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TsProPoolOpt">
        delete from TS_PRO_POOL_OPT
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <select id="findTsProPoolOptsByQuestIds" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_PRO_POOL_OPT t
        where 1=1
        <if test="list != null and list.size > 0">
            AND t.QUEST_ID IN
            <foreach collection="list" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>



</mapper>

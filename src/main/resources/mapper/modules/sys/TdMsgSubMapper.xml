<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TdMsgSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdMsgSub">
        <result column="RID" property="rid" />
        <result column="MAIN_ID" property="fkByMainId.rid" />
        <result column="PUBLISH_MAN" property="fkByPublishMan.rid" />
        <result column="PUBLISH_TIME" property="publishTime" />
        <result column="ACCEPT_STATE" property="acceptState" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,
        t.MAIN_ID,t.PUBLISH_MAN,t.PUBLISH_TIME,t.ACCEPT_STATE,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="rid != null">
            and ${mAlias}RID = #{rid}
        </if>
        <if test="fkByMainId != null and fkByMainId.rid != null">
            and ${mAlias}MAIN_ID = #{fkByMainId.rid}
        </if>
        <if test="fkByPublishMan != null and fkByPublishMan.rid != null">
            and ${mAlias}PUBLISH_MAN = #{fkByPublishMan.rid}
        </if>
        <if test="publishTime != null">
            and ${mAlias}PUBLISH_TIME = #{publishTime}
        </if>
        <if test="acceptState != null and acceptState != ''">
            and ${mAlias}ACCEPT_STATE = #{acceptState}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}fkByMainId != null and ${joiner}fkByMainId.rid != null">
                t.MAIN_ID = #{${joiner}fkByMainId.rid},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByPublishMan != null and ${joiner}fkByPublishMan.rid != null">
                t.PUBLISH_MAN = #{${joiner}fkByPublishMan.rid},
            </when>
            <otherwise>
                t.PUBLISH_MAN = null,
            </otherwise>
        </choose>
            t.PUBLISH_TIME = #{${joiner}publishTime},
        <choose>
            <when test="${joiner}acceptState != null and ${joiner}acceptState != ''">
                t.ACCEPT_STATE = #{${joiner}acceptState},
            </when>
            <otherwise>
                t.ACCEPT_STATE = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_MSG_SUB t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_MSG_SUB t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_MSG_SUB t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdMsgSub" >
        update TD_MSG_SUB t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_MSG_SUB t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdMsgSub">
        delete from TD_MSG_SUB
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
	<select id="selectListByMainIdAndZone" resultType="TdMsgSub">
        select
        <trim suffixOverrides=",">
           t.rid,
        </trim>
        from  TD_MSG_SUB t
        left join TS_USER_INFO t1 on t.PUBLISH_MAN = t1.rid
        left join TS_UNIT T2 ON T2.RID = T1.UNIT_RID
        LEFT JOIN TS_ZONE T3 ON T3.RID = T2.MANAGE_ZONE_ID
        <where>
            <if test="mainId != null">
	            and t.MAIN_ID = #{mainId}
	        </if>
	        <if test="zoneGb != null">
	            and T3.zone_gb like concat(#{zoneGb},'%') 
	        </if>
	        <if test="zoneType != null">
	            and T3.zone_type = #{zoneType}
	        </if>
        </where>
    </select>



</mapper>

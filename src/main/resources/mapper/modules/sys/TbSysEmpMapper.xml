<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TbSysEmpMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TbSysEmp">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="NUM" property="num" />
        <result column="EMP_CODE" property="empCode" />
        <result column="EMP_NAME" property="empName" />
        <result column="EMP_SEX" property="empSex" />
        <result column="EMP_NATION" property="empNation" />
        <result column="DEPT_ID" property="fkByDeptId.rid" />
        <result column="GROUP_ID" property="groupId" />
        <result column="IDC" property="idc" />
        <result column="WORK_TYPEID" property="workTypeid" />
        <result column="BIRTHDAY" property="birthday" />
        <result column="IS_LEADER" property="isLeader" />
        <result column="MB_NUM" property="mbNum" />
        <result column="DUTY" property="duty" />
        <result column="POSITION" property="position" />
        <result column="EDU_DEGREE" property="eduDegree" />
        <result column="WORK_DAY" property="workDay" />
        <result column="REGULAR_DAY" property="regularDay" />
        <result column="POLITICS" property="politics" />
        <result column="USED_NAME" property="usedName" />
        <result column="MARITAL_STATUS" property="maritalStatus" />
        <result column="RELIGION" property="religion" />
        <result column="ONREG" property="onreg" />
        <result column="ONDUTY" property="onduty" />
        <result column="PSN_PROP" property="fkByPsnProp.rid" />
        <result column="PSN_SIGN" property="psnSign" />
        <result column="PROF_LEVELID" property="profLevelid" />
        <result column="WORK_YEARS" property="workYears" />
        <result column="NEWCHIL_INOC" property="newchilInoc" />
        <result column="ADDWORK" property="addwork" />
        <result column="FIRST_EDU" property="firstEdu" />
        <result column="FIRST_ACADEDU" property="firstAcadedu" />
        <result column="FIRST_PROF" property="firstProf" />
        <result column="FIRST_SCHL" property="firstSchl" />
        <result column="FIRSTGRD_TIME" property="firstgrdTime" />
        <result column="ACAD_DEGREE" property="acadDegree" />
        <result column="LAST_PROF" property="lastProf" />
        <result column="LAST_SCHL" property="lastSchl" />
        <result column="LASTGRD_TIME" property="lastgrdTime" />
        <result column="CREDITS_CARDNO" property="creditsCardno" />
        <result column="BIRTH_PLACE" property="birthPlace" />
        <result column="ADDRESS" property="address" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.NUM,t.EMP_CODE,t.EMP_NAME,t.EMP_SEX,t.EMP_NATION,t.DEPT_ID,t.GROUP_ID,t.IDC,t.WORK_TYPEID,t.BIRTHDAY,t.IS_LEADER,t.MB_NUM,t.DUTY,t.POSITION,t.EDU_DEGREE,t.WORK_DAY,t.REGULAR_DAY,t.POLITICS,t.USED_NAME,t.MARITAL_STATUS,t.RELIGION,t.ONREG,t.ONDUTY,t.PSN_PROP,t.PSN_SIGN,t.PROF_LEVELID,t.WORK_YEARS,t.NEWCHIL_INOC,t.ADDWORK,t.FIRST_EDU,t.FIRST_ACADEDU,t.FIRST_PROF,t.FIRST_SCHL,t.FIRSTGRD_TIME,t.ACAD_DEGREE,t.LAST_PROF,t.LAST_SCHL,t.LASTGRD_TIME,t.CREDITS_CARDNO,t.BIRTH_PLACE,t.ADDRESS,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="rid != null">
            and ${mAlias}RID = #{rid}
        </if>
        <if test="createDate != null">
            and ${mAlias}CREATE_DATE = #{createDate}
        </if>
        <if test="createManid != null">
            and ${mAlias}CREATE_MANID = #{createManid}
        </if>
        <if test="modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{modifyDate}
        </if>
        <if test="modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{modifyManid}
        </if>
        <if test="num != null and num != ''">
            and ${mAlias}NUM = #{num}
        </if>
        <if test="empCode != null and empCode != ''">
            and ${mAlias}EMP_CODE = #{empCode}
        </if>
        <if test="empName != null and empName != ''">
            and ${mAlias}EMP_NAME = #{empName}
        </if>
        <if test="empSex != null and empSex != ''">
            and ${mAlias}EMP_SEX = #{empSex}
        </if>
        <if test="empNation != null and empNation != ''">
            and ${mAlias}EMP_NATION = #{empNation}
        </if>
        <if test="fkByDeptId != null and fkByDeptId.rid != null">
            and ${mAlias}DEPT_ID = #{fkByDeptId.rid}
        </if>
        <if test="groupId != null and groupId != ''">
            and ${mAlias}GROUP_ID = #{groupId}
        </if>
        <if test="idc != null and idc != ''">
            and ${mAlias}IDC = #{idc}
        </if>
        <if test="workTypeid != null and workTypeid != ''">
            and ${mAlias}WORK_TYPEID = #{workTypeid}
        </if>
        <if test="birthday != null">
            and ${mAlias}BIRTHDAY = #{birthday}
        </if>
        <if test="isLeader != null and isLeader != ''">
            and ${mAlias}IS_LEADER = #{isLeader}
        </if>
        <if test="mbNum != null and mbNum != ''">
            and ${mAlias}MB_NUM = #{mbNum}
        </if>
        <if test="duty != null and duty != ''">
            and ${mAlias}DUTY = #{duty}
        </if>
        <if test="position != null and position != ''">
            and ${mAlias}POSITION = #{position}
        </if>
        <if test="eduDegree != null and eduDegree != ''">
            and ${mAlias}EDU_DEGREE = #{eduDegree}
        </if>
        <if test="workDay != null">
            and ${mAlias}WORK_DAY = #{workDay}
        </if>
        <if test="regularDay != null">
            and ${mAlias}REGULAR_DAY = #{regularDay}
        </if>
        <if test="politics != null and politics != ''">
            and ${mAlias}POLITICS = #{politics}
        </if>
        <if test="usedName != null and usedName != ''">
            and ${mAlias}USED_NAME = #{usedName}
        </if>
        <if test="maritalStatus != null and maritalStatus != ''">
            and ${mAlias}MARITAL_STATUS = #{maritalStatus}
        </if>
        <if test="religion != null and religion != ''">
            and ${mAlias}RELIGION = #{religion}
        </if>
        <if test="onreg != null and onreg != ''">
            and ${mAlias}ONREG = #{onreg}
        </if>
        <if test="onduty != null and onduty != ''">
            and ${mAlias}ONDUTY = #{onduty}
        </if>
        <if test="fkByPsnProp != null and fkByPsnProp.rid != null">
            and ${mAlias}PSN_PROP = #{fkByPsnProp.rid}
        </if>
        <if test="psnSign != null and psnSign != ''">
            and ${mAlias}PSN_SIGN = #{psnSign}
        </if>
        <if test="profLevelid != null and profLevelid != ''">
            and ${mAlias}PROF_LEVELID = #{profLevelid}
        </if>
        <if test="workYears != null and workYears != ''">
            and ${mAlias}WORK_YEARS = #{workYears}
        </if>
        <if test="newchilInoc != null and newchilInoc != ''">
            and ${mAlias}NEWCHIL_INOC = #{newchilInoc}
        </if>
        <if test="addwork != null">
            and ${mAlias}ADDWORK = #{addwork}
        </if>
        <if test="firstEdu != null and firstEdu != ''">
            and ${mAlias}FIRST_EDU = #{firstEdu}
        </if>
        <if test="firstAcadedu != null and firstAcadedu != ''">
            and ${mAlias}FIRST_ACADEDU = #{firstAcadedu}
        </if>
        <if test="firstProf != null and firstProf != ''">
            and ${mAlias}FIRST_PROF = #{firstProf}
        </if>
        <if test="firstSchl != null and firstSchl != ''">
            and ${mAlias}FIRST_SCHL = #{firstSchl}
        </if>
        <if test="firstgrdTime != null">
            and ${mAlias}FIRSTGRD_TIME = #{firstgrdTime}
        </if>
        <if test="acadDegree != null and acadDegree != ''">
            and ${mAlias}ACAD_DEGREE = #{acadDegree}
        </if>
        <if test="lastProf != null and lastProf != ''">
            and ${mAlias}LAST_PROF = #{lastProf}
        </if>
        <if test="lastSchl != null and lastSchl != ''">
            and ${mAlias}LAST_SCHL = #{lastSchl}
        </if>
        <if test="lastgrdTime != null">
            and ${mAlias}LASTGRD_TIME = #{lastgrdTime}
        </if>
        <if test="creditsCardno != null and creditsCardno != ''">
            and ${mAlias}CREDITS_CARDNO = #{creditsCardno}
        </if>
        <if test="birthPlace != null and birthPlace != ''">
            and ${mAlias}BIRTH_PLACE = #{birthPlace}
        </if>
        <if test="address != null and address != ''">
            and ${mAlias}ADDRESS = #{address}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}num != null and ${joiner}num != ''">
                t.NUM = #{${joiner}num},
            </when>
            <otherwise>
                t.NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}empCode != null and ${joiner}empCode != ''">
                t.EMP_CODE = #{${joiner}empCode},
            </when>
            <otherwise>
                t.EMP_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}empName != null and ${joiner}empName != ''">
                t.EMP_NAME = #{${joiner}empName},
            </when>
            <otherwise>
                t.EMP_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}empSex != null and ${joiner}empSex != ''">
                t.EMP_SEX = #{${joiner}empSex},
            </when>
            <otherwise>
                t.EMP_SEX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}empNation != null and ${joiner}empNation != ''">
                t.EMP_NATION = #{${joiner}empNation},
            </when>
            <otherwise>
                t.EMP_NATION = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByDeptId != null and ${joiner}fkByDeptId.rid != null">
                t.DEPT_ID = #{${joiner}fkByDeptId.rid},
            </when>
            <otherwise>
                t.DEPT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}groupId != null and ${joiner}groupId != ''">
                t.GROUP_ID = #{${joiner}groupId},
            </when>
            <otherwise>
                t.GROUP_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}idc != null and ${joiner}idc != ''">
                t.IDC = #{${joiner}idc},
            </when>
            <otherwise>
                t.IDC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}workTypeid != null and ${joiner}workTypeid != ''">
                t.WORK_TYPEID = #{${joiner}workTypeid},
            </when>
            <otherwise>
                t.WORK_TYPEID = null,
            </otherwise>
        </choose>
            t.BIRTHDAY = #{${joiner}birthday},
        <choose>
            <when test="${joiner}isLeader != null and ${joiner}isLeader != ''">
                t.IS_LEADER = #{${joiner}isLeader},
            </when>
            <otherwise>
                t.IS_LEADER = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}mbNum != null and ${joiner}mbNum != ''">
                t.MB_NUM = #{${joiner}mbNum},
            </when>
            <otherwise>
                t.MB_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}duty != null and ${joiner}duty != ''">
                t.DUTY = #{${joiner}duty},
            </when>
            <otherwise>
                t.DUTY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}position != null and ${joiner}position != ''">
                t.POSITION = #{${joiner}position},
            </when>
            <otherwise>
                t.POSITION = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}eduDegree != null and ${joiner}eduDegree != ''">
                t.EDU_DEGREE = #{${joiner}eduDegree},
            </when>
            <otherwise>
                t.EDU_DEGREE = null,
            </otherwise>
        </choose>
            t.WORK_DAY = #{${joiner}workDay},
            t.REGULAR_DAY = #{${joiner}regularDay},
        <choose>
            <when test="${joiner}politics != null and ${joiner}politics != ''">
                t.POLITICS = #{${joiner}politics},
            </when>
            <otherwise>
                t.POLITICS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}usedName != null and ${joiner}usedName != ''">
                t.USED_NAME = #{${joiner}usedName},
            </when>
            <otherwise>
                t.USED_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}maritalStatus != null and ${joiner}maritalStatus != ''">
                t.MARITAL_STATUS = #{${joiner}maritalStatus},
            </when>
            <otherwise>
                t.MARITAL_STATUS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}religion != null and ${joiner}religion != ''">
                t.RELIGION = #{${joiner}religion},
            </when>
            <otherwise>
                t.RELIGION = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}onreg != null and ${joiner}onreg != ''">
                t.ONREG = #{${joiner}onreg},
            </when>
            <otherwise>
                t.ONREG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}onduty != null and ${joiner}onduty != ''">
                t.ONDUTY = #{${joiner}onduty},
            </when>
            <otherwise>
                t.ONDUTY = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByPsnProp != null and ${joiner}fkByPsnProp.rid != null">
                t.PSN_PROP = #{${joiner}fkByPsnProp.rid},
            </when>
            <otherwise>
                t.PSN_PROP = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}psnSign != null and ${joiner}psnSign != ''">
                t.PSN_SIGN = #{${joiner}psnSign},
            </when>
            <otherwise>
                t.PSN_SIGN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}profLevelid != null and ${joiner}profLevelid != ''">
                t.PROF_LEVELID = #{${joiner}profLevelid},
            </when>
            <otherwise>
                t.PROF_LEVELID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}workYears != null and ${joiner}workYears != ''">
                t.WORK_YEARS = #{${joiner}workYears},
            </when>
            <otherwise>
                t.WORK_YEARS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}newchilInoc != null and ${joiner}newchilInoc != ''">
                t.NEWCHIL_INOC = #{${joiner}newchilInoc},
            </when>
            <otherwise>
                t.NEWCHIL_INOC = null,
            </otherwise>
        </choose>
            t.ADDWORK = #{${joiner}addwork},
        <choose>
            <when test="${joiner}firstEdu != null and ${joiner}firstEdu != ''">
                t.FIRST_EDU = #{${joiner}firstEdu},
            </when>
            <otherwise>
                t.FIRST_EDU = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}firstAcadedu != null and ${joiner}firstAcadedu != ''">
                t.FIRST_ACADEDU = #{${joiner}firstAcadedu},
            </when>
            <otherwise>
                t.FIRST_ACADEDU = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}firstProf != null and ${joiner}firstProf != ''">
                t.FIRST_PROF = #{${joiner}firstProf},
            </when>
            <otherwise>
                t.FIRST_PROF = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}firstSchl != null and ${joiner}firstSchl != ''">
                t.FIRST_SCHL = #{${joiner}firstSchl},
            </when>
            <otherwise>
                t.FIRST_SCHL = null,
            </otherwise>
        </choose>
            t.FIRSTGRD_TIME = #{${joiner}firstgrdTime},
        <choose>
            <when test="${joiner}acadDegree != null and ${joiner}acadDegree != ''">
                t.ACAD_DEGREE = #{${joiner}acadDegree},
            </when>
            <otherwise>
                t.ACAD_DEGREE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lastProf != null and ${joiner}lastProf != ''">
                t.LAST_PROF = #{${joiner}lastProf},
            </when>
            <otherwise>
                t.LAST_PROF = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}lastSchl != null and ${joiner}lastSchl != ''">
                t.LAST_SCHL = #{${joiner}lastSchl},
            </when>
            <otherwise>
                t.LAST_SCHL = null,
            </otherwise>
        </choose>
            t.LASTGRD_TIME = #{${joiner}lastgrdTime},
        <choose>
            <when test="${joiner}creditsCardno != null and ${joiner}creditsCardno != ''">
                t.CREDITS_CARDNO = #{${joiner}creditsCardno},
            </when>
            <otherwise>
                t.CREDITS_CARDNO = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}birthPlace != null and ${joiner}birthPlace != ''">
                t.BIRTH_PLACE = #{${joiner}birthPlace},
            </when>
            <otherwise>
                t.BIRTH_PLACE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}address != null and ${joiner}address != ''">
                t.ADDRESS = #{${joiner}address},
            </when>
            <otherwise>
                t.ADDRESS = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_SYS_EMP t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_SYS_EMP t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_SYS_EMP t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TbSysEmp" >
        update TB_SYS_EMP t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TB_SYS_EMP t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TbSysEmp">
        delete from TB_SYS_EMP
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

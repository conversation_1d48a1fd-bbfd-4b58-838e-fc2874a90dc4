<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TdProbAnsTableMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdProbAnsTable">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="SUR_SUBJECTID" property="fkBySurSubjectid.rid" />
        <result column="COL_ID" property="fkByColId.rid" />
        <result column="NUM" property="num" />
        <result column="COL_VALUE" property="colValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,
        t.SUR_SUBJECTID,t.COL_ID,t.NUM,t.COL_VALUE,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}fkBySurSubjectid != null and ${joiner}fkBySurSubjectid.rid != null">
            and ${mAlias}SUR_SUBJECTID = #{${joiner}fkBySurSubjectid.rid}
        </if>
        <if test="${joiner}fkByColId != null and ${joiner}fkByColId.rid != null">
            and ${mAlias}COL_ID = #{${joiner}fkByColId.rid}
        </if>
        <if test="${joiner}num != null and ${joiner}num != ''">
            and ${mAlias}NUM = #{${joiner}num}
        </if>
        <if test="${joiner}colValue != null and ${joiner}colValue != ''">
            and ${mAlias}COL_VALUE = #{${joiner}colValue}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}fkBySurSubjectid != null and ${joiner}fkBySurSubjectid.rid != null">
                t.SUR_SUBJECTID = #{${joiner}fkBySurSubjectid.rid},
            </when>
            <otherwise>
                t.SUR_SUBJECTID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByColId != null and ${joiner}fkByColId.rid != null">
                t.COL_ID = #{${joiner}fkByColId.rid},
            </when>
            <otherwise>
                t.COL_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}num != null and ${joiner}num != ''">
                t.NUM = #{${joiner}num},
            </when>
            <otherwise>
                t.NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}colValue != null and ${joiner}colValue != ''">
                t.COL_VALUE = #{${joiner}colValue},
            </when>
            <otherwise>
                t.COL_VALUE = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_PROB_ANS_TABLE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_PROB_ANS_TABLE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_PROB_ANS_TABLE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_PROB_ANS_TABLE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            SUR_SUBJECTID,
            COL_ID,
            NUM,
            COL_VALUE,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_PROB_ANS_TABLE_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkBySurSubjectid != null and item.fkBySurSubjectid.rid != null">
                            #{item.fkBySurSubjectid.rid} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByColId != null and item.fkByColId.rid != null">
                            #{item.fkByColId.rid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.num != null">
                            #{item.num} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.colValue != null">
                            #{item.colValue} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdProbAnsTable" >
        update TD_PROB_ANS_TABLE t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_PROB_ANS_TABLE t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdProbAnsTable">
        delete from TD_PROB_ANS_TABLE
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <delete id="deleteTdProbAnsTable">
        delete from TD_PROB_ANS_TABLE WHERE SUR_SUBJECTID IN (SELECT RID FROM TD_PROB_ANS_DETAIL WHERE QUE_TYPE_ID = #{busType} AND MAIN_ID = #{mainId})
    </delete>

    <delete id="deletePsnCheckTdProbAnsTable">
        delete from TD_PROB_ANS_TABLE WHERE SUR_SUBJECTID IN (SELECT RID FROM TD_PROB_ANS_DETAIL WHERE MAIN_TYPE = #{mainType} AND MAIN_ID = #{mainId})
    </delete>

    <select id="findTdProbAnsTablesByDetailRidList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_PROB_ANS_TABLE t
        where 1=1
        <if test="detailRidList != null and detailRidList.size > 0">
            AND t.SUR_SUBJECTID IN
            <foreach collection="detailRidList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
        ORDER BY t.NUM
    </select>

    <select id="selectPsnCheckTdProbAnsTableRids" resultType="java.lang.Integer">
        select rid from TD_PROB_ANS_TABLE WHERE SUR_SUBJECTID IN (SELECT RID FROM TD_PROB_ANS_DETAIL WHERE MAIN_TYPE = #{mainType} AND MAIN_ID = #{mainId})
    </select>



</mapper>

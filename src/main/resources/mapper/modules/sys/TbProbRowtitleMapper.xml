<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TbProbRowtitleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TbProbRowtitle">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="TABLE_ID" property="fkByTableId.rid" />
        <result column="TITLE" property="title" />
        <result column="ROW_INDEX" property="rowIndex" />
        <result column="COL_INDEX" property="colIndex" />
        <result column="COLSPAN" property="colspan" />
        <result column="ROWSPAN" property="rowspan" />
        <result column="STATE" property="state" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,
        t.TABLE_ID,t.TITLE,t.ROW_INDEX,t.COL_INDEX,t.COLSPAN,t.ROWSPAN,t.STATE,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}fkByTableId != null and ${joiner}fkByTableId.rid != null">
            and ${mAlias}TABLE_ID = #{${joiner}fkByTableId.rid}
        </if>
        <if test="${joiner}title != null and ${joiner}title != ''">
            and ${mAlias}TITLE = #{${joiner}title}
        </if>
        <if test="${joiner}rowIndex != null and ${joiner}rowIndex != ''">
            and ${mAlias}ROW_INDEX = #{${joiner}rowIndex}
        </if>
        <if test="${joiner}colIndex != null and ${joiner}colIndex != ''">
            and ${mAlias}COL_INDEX = #{${joiner}colIndex}
        </if>
        <if test="${joiner}colspan != null and ${joiner}colspan != ''">
            and ${mAlias}COLSPAN = #{${joiner}colspan}
        </if>
        <if test="${joiner}rowspan != null and ${joiner}rowspan != ''">
            and ${mAlias}ROWSPAN = #{${joiner}rowspan}
        </if>
        <if test="${joiner}state != null and ${joiner}state != ''">
            and ${mAlias}STATE = #{${joiner}state}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}fkByTableId != null and ${joiner}fkByTableId.rid != null">
                t.TABLE_ID = #{${joiner}fkByTableId.rid},
            </when>
            <otherwise>
                t.TABLE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}title != null and ${joiner}title != ''">
                t.TITLE = #{${joiner}title},
            </when>
            <otherwise>
                t.TITLE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rowIndex != null and ${joiner}rowIndex != ''">
                t.ROW_INDEX = #{${joiner}rowIndex},
            </when>
            <otherwise>
                t.ROW_INDEX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}colIndex != null and ${joiner}colIndex != ''">
                t.COL_INDEX = #{${joiner}colIndex},
            </when>
            <otherwise>
                t.COL_INDEX = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}colspan != null and ${joiner}colspan != ''">
                t.COLSPAN = #{${joiner}colspan},
            </when>
            <otherwise>
                t.COLSPAN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rowspan != null and ${joiner}rowspan != ''">
                t.ROWSPAN = #{${joiner}rowspan},
            </when>
            <otherwise>
                t.ROWSPAN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}state != null and ${joiner}state != ''">
                t.STATE = #{${joiner}state},
            </when>
            <otherwise>
                t.STATE = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_PROB_ROWTITLE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_PROB_ROWTITLE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_PROB_ROWTITLE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TB_PROB_ROWTITLE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            TABLE_ID,
            TITLE,
            ROW_INDEX,
            COL_INDEX,
            COLSPAN,
            ROWSPAN,
            STATE,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TB_PROB_ROWTITLE_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByTableId != null and item.fkByTableId.rid != null">
                            #{item.fkByTableId.rid} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.title != null">
                            #{item.title} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rowIndex != null">
                            #{item.rowIndex} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.colIndex != null">
                            #{item.colIndex} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.colspan != null">
                            #{item.colspan} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rowspan != null">
                            #{item.rowspan} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.state != null">
                            #{item.state} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TbProbRowtitle" >
        update TB_PROB_ROWTITLE t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TB_PROB_ROWTITLE t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TbProbRowtitle">
        delete from TB_PROB_ROWTITLE
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TdInfoMainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdInfoMain">
        <result column="RID" property="rid" />
        <result column="INFO_TITLE" property="infoTitle" />
        <result column="INFO_CONTENT" property="infoContent" />
        <result column="PUBLISH_MAN" property="fkByPublishMan.rid" />
        <result column="PUBLISH_TIME" property="publishTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,
        t.INFO_TITLE,t.INFO_CONTENT,t.PUBLISH_MAN,t.PUBLISH_TIME,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="rid != null">
            and ${mAlias}RID = #{rid}
        </if>
        <if test="infoTitle != null and infoTitle != ''">
            and ${mAlias}INFO_TITLE = #{infoTitle}
        </if>
        <if test="infoContent != null">
            and ${mAlias}INFO_CONTENT = #{infoContent}
        </if>
        <if test="fkByPublishMan != null and fkByPublishMan.rid != null">
            and ${mAlias}PUBLISH_MAN = #{fkByPublishMan.rid}
        </if>
        <if test="publishTime != null">
            and ${mAlias}PUBLISH_TIME = #{publishTime}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}infoTitle != null and ${joiner}infoTitle != ''">
                t.INFO_TITLE = #{${joiner}infoTitle},
            </when>
            <otherwise>
                t.INFO_TITLE = null,
            </otherwise>
        </choose>
            t.INFO_CONTENT = #{${joiner}infoContent},
        <choose>
            <when test="${joiner}fkByPublishMan != null and ${joiner}fkByPublishMan.rid != null">
                t.PUBLISH_MAN = #{${joiner}fkByPublishMan.rid},
            </when>
            <otherwise>
                t.PUBLISH_MAN = null,
            </otherwise>
        </choose>
            t.PUBLISH_TIME = #{${joiner}publishTime},
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_INFO_MAIN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_INFO_MAIN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_INFO_MAIN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdInfoMain" >
        update TD_INFO_MAIN t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_INFO_MAIN t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdInfoMain">
        delete from TD_INFO_MAIN
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

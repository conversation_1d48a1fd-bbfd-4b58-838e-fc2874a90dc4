<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TdMsgMainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdMsgMain">
        <result column="RID" property="rid" />
        <result column="INFO_TITLE" property="infoTitle" />
        <result column="MSG_TYPE" property="msgType" />
        <result column="PUBLISH_MAN" property="fkByPublishMan.rid" />
        <result column="PUBLISH_TIME" property="publishTime" />
        <result column="NET_ADR" property="netAdr" />
        <result column="NET_NAME" property="netName" />
        <result column="INFO_ID" property="fkByInfoId.rid" />
        <result column="APPEND_KEYS" property="appendKeys" />
        <result column="SUB_TYPE" property="subType" />
        <result column="MENU_ID" property="menuId" />
        <result column="IS_TODO" property="isTodo" />
        <result column="TODO_STATE" property="todoState" />
        <result column="BUS_ID" property="busId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,
        t.INFO_TITLE,t.MSG_TYPE,t.PUBLISH_MAN,t.PUBLISH_TIME,t.NET_ADR,t.NET_NAME,t.INFO_ID,t.APPEND_KEYS,t.SUB_TYPE,t.MENU_ID,t.IS_TODO,t.TODO_STATE,
    	t.BUS_ID,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="rid != null">
            and ${mAlias}RID = #{rid}
        </if>
        <if test="infoTitle != null and infoTitle != ''">
            and ${mAlias}INFO_TITLE = #{infoTitle}
        </if>
        <if test="msgType != null and msgType != ''">
            and ${mAlias}MSG_TYPE = #{msgType}
        </if>
        <if test="fkByPublishMan != null and fkByPublishMan.rid != null">
            and ${mAlias}PUBLISH_MAN = #{fkByPublishMan.rid}
        </if>
        <if test="publishTime != null">
            and ${mAlias}PUBLISH_TIME = #{publishTime}
        </if>
        <if test="netAdr != null and netAdr != ''">
            and ${mAlias}NET_ADR = #{netAdr}
        </if>
        <if test="netName != null and netName != ''">
            and ${mAlias}NET_NAME = #{netName}
        </if>
        <if test="fkByInfoId != null and fkByInfoId.rid != null">
            and ${mAlias}INFO_ID = #{fkByInfoId.rid}
        </if>
        <if test="appendKeys != null and appendKeys != ''">
            and ${mAlias}APPEND_KEYS = #{appendKeys}
        </if>
        <if test="subType != null and subType != ''">
            and ${mAlias}SUB_TYPE = #{subType}
        </if>
        <if test="menuId != null and menuId != ''">
            and ${mAlias}MENU_ID = #{menuId}
        </if>
        <if test="isTodo != null and isTodo != ''">
            and ${mAlias}IS_TODO = #{isTodo}
        </if>
        <if test="todoState != null and todoState != ''">
            and ${mAlias}TODO_STATE = #{todoState}
        </if>
        <if test="busId != null">
            and ${mAlias}BUS_ID = #{busId}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}infoTitle != null and ${joiner}infoTitle != ''">
                t.INFO_TITLE = #{${joiner}infoTitle},
            </when>
            <otherwise>
                t.INFO_TITLE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}msgType != null and ${joiner}msgType != ''">
                t.MSG_TYPE = #{${joiner}msgType},
            </when>
            <otherwise>
                t.MSG_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByPublishMan != null and ${joiner}fkByPublishMan.rid != null">
                t.PUBLISH_MAN = #{${joiner}fkByPublishMan.rid},
            </when>
            <otherwise>
                t.PUBLISH_MAN = null,
            </otherwise>
        </choose>
            t.PUBLISH_TIME = #{${joiner}publishTime},
        <choose>
            <when test="${joiner}netAdr != null and ${joiner}netAdr != ''">
                t.NET_ADR = #{${joiner}netAdr},
            </when>
            <otherwise>
                t.NET_ADR = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}netName != null and ${joiner}netName != ''">
                t.NET_NAME = #{${joiner}netName},
            </when>
            <otherwise>
                t.NET_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByInfoId != null and ${joiner}fkByInfoId.rid != null">
                t.INFO_ID = #{${joiner}fkByInfoId.rid},
            </when>
            <otherwise>
                t.INFO_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}appendKeys != null and ${joiner}appendKeys != ''">
                t.APPEND_KEYS = #{${joiner}appendKeys},
            </when>
            <otherwise>
                t.APPEND_KEYS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}subType != null and ${joiner}subType != ''">
                t.SUB_TYPE = #{${joiner}subType},
            </when>
            <otherwise>
                t.SUB_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}menuId != null and ${joiner}menuId != ''">
                t.MENU_ID = #{${joiner}menuId},
            </when>
            <otherwise>
                t.MENU_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}isTodo != null and ${joiner}isTodo != ''">
                t.IS_TODO = #{${joiner}isTodo},
            </when>
            <otherwise>
                t.IS_TODO = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}todoState != null and ${joiner}todoState != ''">
                t.TODO_STATE = #{${joiner}todoState},
            </when>
            <otherwise>
                t.TODO_STATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}busId != null">
                t.BUS_ID = #{${joiner}busId},
            </when>
            <otherwise>
                t.BUS_ID = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_MSG_MAIN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_MSG_MAIN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_MSG_MAIN t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdMsgMain" >
        update TD_MSG_MAIN t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TD_MSG_MAIN t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdMsgMain">
        delete from TD_MSG_MAIN
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

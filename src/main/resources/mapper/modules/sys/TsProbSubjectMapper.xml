<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TsProbSubjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TsProbSubject">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="QUESTLIB_ID" property="fkByQuestlibId.rid" />
        <result column="SHOW_CODE" property="showCode" />
        <result column="QES_CODE" property="qesCode" />
        <result column="QES_LEVEL_CODE" property="qesLevelCode" />
        <result column="NUM" property="num" />
        <result column="TITLE_DESC" property="titleDesc" />
        <result column="QUEST_TYPE" property="questType" />
        <result column="MUST_ASK" property="mustAsk" />
        <result column="MIN_SELECT_NUM" property="minSelectNum" />
        <result column="OPT_LAYOUT" property="optLayout" />
        <result column="COLS" property="cols" />
        <result column="SHOW_SCRIPT" property="showScript" />
        <result column="QUEST_UNIT" property="questUnit" />
        <result column="STATE" property="state" />
        <result column="OTHER_DESC" property="otherDesc" />
        <result column="OTHER_IMG" property="otherImg" />
        <result column="JUMP_TYPE" property="jumpType" />
        <result column="JUMP_QUEST_CODE" property="jumpQuestCode" />
        <result column="SLIDE_MAXVAL" property="slideMaxval" />
        <result column="SLIDE_MINVAL" property="slideMinval" />
        <result column="SLIDE_MAX_DESC" property="slideMaxDesc" />
        <result column="SLIDE_MIN_DESC" property="slideMinDesc" />
        <result column="POOL_ID" property="fkByPoolId.rid" />
        <result column="INVOKE_SCRT" property="invokeScrt" />
        <result column="OPTION_SCORE" property="optionScore" />
        <result column="IS_MULTI" property="isMulti" />
        <result column="TABLE_ID" property="fkByTableId.rid" />
        <result column="FILL_MAX_RANGE" property="fillMaxRange" />
        <result column="RIGHT_CODE" property="rightCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,
        t.QUESTLIB_ID,t.SHOW_CODE,t.QES_CODE,t.QES_LEVEL_CODE,t.NUM,t.TITLE_DESC,t.QUEST_TYPE,t.MUST_ASK,t.MIN_SELECT_NUM,t.OPT_LAYOUT,t.COLS,t.SHOW_SCRIPT,t.QUEST_UNIT,t.STATE,t.OTHER_DESC,t.OTHER_IMG,t.JUMP_TYPE,t.JUMP_QUEST_CODE,t.SLIDE_MAXVAL,t.SLIDE_MINVAL,t.SLIDE_MAX_DESC,t.SLIDE_MIN_DESC,t.POOL_ID,t.INVOKE_SCRT,t.OPTION_SCORE,t.IS_MULTI,t.TABLE_ID,t.FILL_MAX_RANGE,t.RIGHT_CODE,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}fkByQuestlibId != null and ${joiner}fkByQuestlibId.rid != null">
            and ${mAlias}QUESTLIB_ID = #{${joiner}fkByQuestlibId.rid}
        </if>
        <if test="${joiner}showCode != null and ${joiner}showCode != ''">
            and ${mAlias}SHOW_CODE = #{${joiner}showCode}
        </if>
        <if test="${joiner}qesCode != null and ${joiner}qesCode != ''">
            and ${mAlias}QES_CODE = #{${joiner}qesCode}
        </if>
        <if test="${joiner}qesLevelCode != null and ${joiner}qesLevelCode != ''">
            and ${mAlias}QES_LEVEL_CODE = #{${joiner}qesLevelCode}
        </if>
        <if test="${joiner}num != null and ${joiner}num != ''">
            and ${mAlias}NUM = #{${joiner}num}
        </if>
        <if test="${joiner}titleDesc != null and ${joiner}titleDesc != ''">
            and ${mAlias}TITLE_DESC = #{${joiner}titleDesc}
        </if>
        <if test="${joiner}questType != null and ${joiner}questType != ''">
            and ${mAlias}QUEST_TYPE = #{${joiner}questType}
        </if>
        <if test="${joiner}mustAsk != null and ${joiner}mustAsk != ''">
            and ${mAlias}MUST_ASK = #{${joiner}mustAsk}
        </if>
        <if test="${joiner}minSelectNum != null and ${joiner}minSelectNum != ''">
            and ${mAlias}MIN_SELECT_NUM = #{${joiner}minSelectNum}
        </if>
        <if test="${joiner}optLayout != null and ${joiner}optLayout != ''">
            and ${mAlias}OPT_LAYOUT = #{${joiner}optLayout}
        </if>
        <if test="${joiner}cols != null and ${joiner}cols != ''">
            and ${mAlias}COLS = #{${joiner}cols}
        </if>
        <if test="${joiner}showScript != null and ${joiner}showScript != ''">
            and ${mAlias}SHOW_SCRIPT = #{${joiner}showScript}
        </if>
        <if test="${joiner}questUnit != null and ${joiner}questUnit != ''">
            and ${mAlias}QUEST_UNIT = #{${joiner}questUnit}
        </if>
        <if test="${joiner}state != null and ${joiner}state != ''">
            and ${mAlias}STATE = #{${joiner}state}
        </if>
        <if test="${joiner}otherDesc != null and ${joiner}otherDesc != ''">
            and ${mAlias}OTHER_DESC = #{${joiner}otherDesc}
        </if>
        <if test="${joiner}otherImg != null and ${joiner}otherImg != ''">
            and ${mAlias}OTHER_IMG = #{${joiner}otherImg}
        </if>
        <if test="${joiner}jumpType != null and ${joiner}jumpType != ''">
            and ${mAlias}JUMP_TYPE = #{${joiner}jumpType}
        </if>
        <if test="${joiner}jumpQuestCode != null and ${joiner}jumpQuestCode != ''">
            and ${mAlias}JUMP_QUEST_CODE = #{${joiner}jumpQuestCode}
        </if>
        <if test="${joiner}slideMaxval != null and ${joiner}slideMaxval != ''">
            and ${mAlias}SLIDE_MAXVAL = #{${joiner}slideMaxval}
        </if>
        <if test="${joiner}slideMinval != null and ${joiner}slideMinval != ''">
            and ${mAlias}SLIDE_MINVAL = #{${joiner}slideMinval}
        </if>
        <if test="${joiner}slideMaxDesc != null and ${joiner}slideMaxDesc != ''">
            and ${mAlias}SLIDE_MAX_DESC = #{${joiner}slideMaxDesc}
        </if>
        <if test="${joiner}slideMinDesc != null and ${joiner}slideMinDesc != ''">
            and ${mAlias}SLIDE_MIN_DESC = #{${joiner}slideMinDesc}
        </if>
        <if test="${joiner}fkByPoolId != null and ${joiner}fkByPoolId.rid != null">
            and ${mAlias}POOL_ID = #{${joiner}fkByPoolId.rid}
        </if>
        <if test="${joiner}invokeScrt != null">
            and ${mAlias}INVOKE_SCRT = #{${joiner}invokeScrt}
        </if>
        <if test="${joiner}optionScore != null and ${joiner}optionScore != ''">
            and ${mAlias}OPTION_SCORE = #{${joiner}optionScore}
        </if>
        <if test="${joiner}isMulti != null and ${joiner}isMulti != ''">
            and ${mAlias}IS_MULTI = #{${joiner}isMulti}
        </if>
        <if test="${joiner}fkByTableId != null and ${joiner}fkByTableId.rid != null">
            and ${mAlias}TABLE_ID = #{${joiner}fkByTableId.rid}
        </if>
        <if test="${joiner}fillMaxRange != null and ${joiner}fillMaxRange != ''">
            and ${mAlias}FILL_MAX_RANGE = #{${joiner}fillMaxRange}
        </if>
        <if test="${joiner}rightCode != null and ${joiner}rightCode != ''">
            and ${mAlias}RIGHT_CODE = #{${joiner}rightCode}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}fkByQuestlibId != null and ${joiner}fkByQuestlibId.rid != null">
                t.QUESTLIB_ID = #{${joiner}fkByQuestlibId.rid},
            </when>
            <otherwise>
                t.QUESTLIB_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}showCode != null and ${joiner}showCode != ''">
                t.SHOW_CODE = #{${joiner}showCode},
            </when>
            <otherwise>
                t.SHOW_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}qesCode != null and ${joiner}qesCode != ''">
                t.QES_CODE = #{${joiner}qesCode},
            </when>
            <otherwise>
                t.QES_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}qesLevelCode != null and ${joiner}qesLevelCode != ''">
                t.QES_LEVEL_CODE = #{${joiner}qesLevelCode},
            </when>
            <otherwise>
                t.QES_LEVEL_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}num != null and ${joiner}num != ''">
                t.NUM = #{${joiner}num},
            </when>
            <otherwise>
                t.NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}titleDesc != null and ${joiner}titleDesc != ''">
                t.TITLE_DESC = #{${joiner}titleDesc},
            </when>
            <otherwise>
                t.TITLE_DESC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}questType != null and ${joiner}questType != ''">
                t.QUEST_TYPE = #{${joiner}questType},
            </when>
            <otherwise>
                t.QUEST_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}mustAsk != null and ${joiner}mustAsk != ''">
                t.MUST_ASK = #{${joiner}mustAsk},
            </when>
            <otherwise>
                t.MUST_ASK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}minSelectNum != null and ${joiner}minSelectNum != ''">
                t.MIN_SELECT_NUM = #{${joiner}minSelectNum},
            </when>
            <otherwise>
                t.MIN_SELECT_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}optLayout != null and ${joiner}optLayout != ''">
                t.OPT_LAYOUT = #{${joiner}optLayout},
            </when>
            <otherwise>
                t.OPT_LAYOUT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}cols != null and ${joiner}cols != ''">
                t.COLS = #{${joiner}cols},
            </when>
            <otherwise>
                t.COLS = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}showScript != null and ${joiner}showScript != ''">
                t.SHOW_SCRIPT = #{${joiner}showScript},
            </when>
            <otherwise>
                t.SHOW_SCRIPT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}questUnit != null and ${joiner}questUnit != ''">
                t.QUEST_UNIT = #{${joiner}questUnit},
            </when>
            <otherwise>
                t.QUEST_UNIT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}state != null and ${joiner}state != ''">
                t.STATE = #{${joiner}state},
            </when>
            <otherwise>
                t.STATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}otherDesc != null and ${joiner}otherDesc != ''">
                t.OTHER_DESC = #{${joiner}otherDesc},
            </when>
            <otherwise>
                t.OTHER_DESC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}otherImg != null and ${joiner}otherImg != ''">
                t.OTHER_IMG = #{${joiner}otherImg},
            </when>
            <otherwise>
                t.OTHER_IMG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}jumpType != null and ${joiner}jumpType != ''">
                t.JUMP_TYPE = #{${joiner}jumpType},
            </when>
            <otherwise>
                t.JUMP_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}jumpQuestCode != null and ${joiner}jumpQuestCode != ''">
                t.JUMP_QUEST_CODE = #{${joiner}jumpQuestCode},
            </when>
            <otherwise>
                t.JUMP_QUEST_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}slideMaxval != null and ${joiner}slideMaxval != ''">
                t.SLIDE_MAXVAL = #{${joiner}slideMaxval},
            </when>
            <otherwise>
                t.SLIDE_MAXVAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}slideMinval != null and ${joiner}slideMinval != ''">
                t.SLIDE_MINVAL = #{${joiner}slideMinval},
            </when>
            <otherwise>
                t.SLIDE_MINVAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}slideMaxDesc != null and ${joiner}slideMaxDesc != ''">
                t.SLIDE_MAX_DESC = #{${joiner}slideMaxDesc},
            </when>
            <otherwise>
                t.SLIDE_MAX_DESC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}slideMinDesc != null and ${joiner}slideMinDesc != ''">
                t.SLIDE_MIN_DESC = #{${joiner}slideMinDesc},
            </when>
            <otherwise>
                t.SLIDE_MIN_DESC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByPoolId != null and ${joiner}fkByPoolId.rid != null">
                t.POOL_ID = #{${joiner}fkByPoolId.rid},
            </when>
            <otherwise>
                t.POOL_ID = null,
            </otherwise>
        </choose>
            t.INVOKE_SCRT = #{${joiner}invokeScrt},
        <choose>
            <when test="${joiner}optionScore != null and ${joiner}optionScore != ''">
                t.OPTION_SCORE = #{${joiner}optionScore},
            </when>
            <otherwise>
                t.OPTION_SCORE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}isMulti != null and ${joiner}isMulti != ''">
                t.IS_MULTI = #{${joiner}isMulti},
            </when>
            <otherwise>
                t.IS_MULTI = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByTableId != null and ${joiner}fkByTableId.rid != null">
                t.TABLE_ID = #{${joiner}fkByTableId.rid},
            </when>
            <otherwise>
                t.TABLE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fillMaxRange != null and ${joiner}fillMaxRange != ''">
                t.FILL_MAX_RANGE = #{${joiner}fillMaxRange},
            </when>
            <otherwise>
                t.FILL_MAX_RANGE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rightCode != null and ${joiner}rightCode != ''">
                t.RIGHT_CODE = #{${joiner}rightCode},
            </when>
            <otherwise>
                t.RIGHT_CODE = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_PROB_SUBJECT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_PROB_SUBJECT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_PROB_SUBJECT t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TS_PROB_SUBJECT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            QUESTLIB_ID,
            SHOW_CODE,
            QES_CODE,
            QES_LEVEL_CODE,
            NUM,
            TITLE_DESC,
            QUEST_TYPE,
            MUST_ASK,
            MIN_SELECT_NUM,
            OPT_LAYOUT,
            COLS,
            SHOW_SCRIPT,
            QUEST_UNIT,
            STATE,
            OTHER_DESC,
            OTHER_IMG,
            JUMP_TYPE,
            JUMP_QUEST_CODE,
            SLIDE_MAXVAL,
            SLIDE_MINVAL,
            SLIDE_MAX_DESC,
            SLIDE_MIN_DESC,
            POOL_ID,
            INVOKE_SCRT,
            OPTION_SCORE,
            IS_MULTI,
            TABLE_ID,
            FILL_MAX_RANGE,
            RIGHT_CODE,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TS_PROB_SUBJECT_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByQuestlibId != null and item.fkByQuestlibId.rid != null">
                            #{item.fkByQuestlibId.rid} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.showCode != null">
                            #{item.showCode} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.qesCode != null">
                            #{item.qesCode} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.qesLevelCode != null">
                            #{item.qesLevelCode} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.num != null">
                            #{item.num} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.titleDesc != null">
                            #{item.titleDesc} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.questType != null">
                            #{item.questType} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.mustAsk != null">
                            #{item.mustAsk} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.minSelectNum != null">
                            #{item.minSelectNum} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.optLayout != null">
                            #{item.optLayout} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.cols != null">
                            #{item.cols} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.showScript != null">
                            #{item.showScript} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.questUnit != null">
                            #{item.questUnit} AS C15,
                        </when>
                        <otherwise>
                            NULL AS C15,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.state != null">
                            #{item.state} AS C16,
                        </when>
                        <otherwise>
                            NULL AS C16,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.otherDesc != null">
                            #{item.otherDesc} AS C17,
                        </when>
                        <otherwise>
                            NULL AS C17,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.otherImg != null">
                            #{item.otherImg} AS C18,
                        </when>
                        <otherwise>
                            NULL AS C18,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.jumpType != null">
                            #{item.jumpType} AS C19,
                        </when>
                        <otherwise>
                            NULL AS C19,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.jumpQuestCode != null">
                            #{item.jumpQuestCode} AS C20,
                        </when>
                        <otherwise>
                            NULL AS C20,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.slideMaxval != null">
                            #{item.slideMaxval} AS C21,
                        </when>
                        <otherwise>
                            NULL AS C21,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.slideMinval != null">
                            #{item.slideMinval} AS C22,
                        </when>
                        <otherwise>
                            NULL AS C22,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.slideMaxDesc != null">
                            #{item.slideMaxDesc} AS C23,
                        </when>
                        <otherwise>
                            NULL AS C23,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.slideMinDesc != null">
                            #{item.slideMinDesc} AS C24,
                        </when>
                        <otherwise>
                            NULL AS C24,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByPoolId != null and item.fkByPoolId.rid != null">
                            #{item.fkByPoolId.rid} AS C25,
                        </when>
                        <otherwise>
                            NULL AS C25,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.invokeScrt != null">
                            #{item.invokeScrt} AS C26,
                        </when>
                        <otherwise>
                            NULL AS C26,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.optionScore != null">
                            #{item.optionScore} AS C27,
                        </when>
                        <otherwise>
                            NULL AS C27,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.isMulti != null">
                            #{item.isMulti} AS C28,
                        </when>
                        <otherwise>
                            NULL AS C28,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByTableId != null and item.fkByTableId.rid != null">
                            #{item.fkByTableId.rid} AS C29,
                        </when>
                        <otherwise>
                            NULL AS C29,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fillMaxRange != null">
                            #{item.fillMaxRange} AS C30,
                        </when>
                        <otherwise>
                            NULL AS C30,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rightCode != null">
                            #{item.rightCode} AS C31,
                        </when>
                        <otherwise>
                            NULL AS C31,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TsProbSubject" >
        update TS_PROB_SUBJECT t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TS_PROB_SUBJECT t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TsProbSubject">
        delete from TS_PROB_SUBJECT
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TsProbLibMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TsProbLib">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="UNIT_ID" property="fkByUnitId.rid" />
        <result column="QUEST_SORTID" property="fkByQuestSortid.rid" />
        <result column="QUEST_NAME" property="questName" />
        <result column="RMK" property="rmk" />
        <result column="NUM" property="num" />
        <result column="BACK_IMAGE" property="backImage" />
        <result column="STATE" property="state" />
        <result column="HTML_NAME" property="htmlName" />
        <result column="LIB_SCRIPT" jdbcType="CLOB" property="libScript" />
        <result column="LIB_INIT_SRC" jdbcType="CLOB" property="libInitSrc" />
        <result column="VERIFY_SCRIPT" jdbcType="CLOB" property="verifyScript" />
        <result column="PARAM_TYPE" property="paramType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,
        t.UNIT_ID,t.QUEST_SORTID,t.QUEST_NAME,t.RMK,t.NUM,t.BACK_IMAGE,t.STATE,t.HTML_NAME,t.LIB_SCRIPT,t.LIB_INIT_SRC,t.VERIFY_SCRIPT,t.PARAM_TYPE,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}fkByUnitId != null and ${joiner}fkByUnitId.rid != null">
            and ${mAlias}UNIT_ID = #{${joiner}fkByUnitId.rid}
        </if>
        <if test="${joiner}fkByQuestSortid != null and ${joiner}fkByQuestSortid.rid != null">
            and ${mAlias}QUEST_SORTID = #{${joiner}fkByQuestSortid.rid}
        </if>
        <if test="${joiner}questName != null and ${joiner}questName != ''">
            and ${mAlias}QUEST_NAME = #{${joiner}questName}
        </if>
        <if test="${joiner}rmk != null and ${joiner}rmk != ''">
            and ${mAlias}RMK = #{${joiner}rmk}
        </if>
        <if test="${joiner}num != null and ${joiner}num != ''">
            and ${mAlias}NUM = #{${joiner}num}
        </if>
        <if test="${joiner}backImage != null and ${joiner}backImage != ''">
            and ${mAlias}BACK_IMAGE = #{${joiner}backImage}
        </if>
        <if test="${joiner}state != null and ${joiner}state != ''">
            and ${mAlias}STATE = #{${joiner}state}
        </if>
        <if test="${joiner}htmlName != null and ${joiner}htmlName != ''">
            and ${mAlias}HTML_NAME = #{${joiner}htmlName}
        </if>
        <if test="${joiner}libScript != null">
            and ${mAlias}LIB_SCRIPT = #{${joiner}libScript}
        </if>
        <if test="${joiner}libInitSrc != null">
            and ${mAlias}LIB_INIT_SRC = #{${joiner}libInitSrc}
        </if>
        <if test="${joiner}verifyScript != null">
            and ${mAlias}VERIFY_SCRIPT = #{${joiner}verifyScript}
        </if>
        <if test="${joiner}paramType != null and ${joiner}paramType != ''">
            and ${mAlias}PARAM_TYPE = #{${joiner}paramType}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}fkByUnitId != null and ${joiner}fkByUnitId.rid != null">
                t.UNIT_ID = #{${joiner}fkByUnitId.rid},
            </when>
            <otherwise>
                t.UNIT_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByQuestSortid != null and ${joiner}fkByQuestSortid.rid != null">
                t.QUEST_SORTID = #{${joiner}fkByQuestSortid.rid},
            </when>
            <otherwise>
                t.QUEST_SORTID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}questName != null and ${joiner}questName != ''">
                t.QUEST_NAME = #{${joiner}questName},
            </when>
            <otherwise>
                t.QUEST_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rmk != null and ${joiner}rmk != ''">
                t.RMK = #{${joiner}rmk},
            </when>
            <otherwise>
                t.RMK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}num != null and ${joiner}num != ''">
                t.NUM = #{${joiner}num},
            </when>
            <otherwise>
                t.NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}backImage != null and ${joiner}backImage != ''">
                t.BACK_IMAGE = #{${joiner}backImage},
            </when>
            <otherwise>
                t.BACK_IMAGE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}state != null and ${joiner}state != ''">
                t.STATE = #{${joiner}state},
            </when>
            <otherwise>
                t.STATE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}htmlName != null and ${joiner}htmlName != ''">
                t.HTML_NAME = #{${joiner}htmlName},
            </when>
            <otherwise>
                t.HTML_NAME = null,
            </otherwise>
        </choose>
            t.LIB_SCRIPT = #{${joiner}libScript},
            t.LIB_INIT_SRC = #{${joiner}libInitSrc},
            t.VERIFY_SCRIPT = #{${joiner}verifyScript},
        <choose>
            <when test="${joiner}paramType != null and ${joiner}paramType != ''">
                t.PARAM_TYPE = #{${joiner}paramType},
            </when>
            <otherwise>
                t.PARAM_TYPE = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_PROB_LIB t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_PROB_LIB t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_PROB_LIB t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TS_PROB_LIB
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            UNIT_ID,
            QUEST_SORTID,
            QUEST_NAME,
            RMK,
            NUM,
            BACK_IMAGE,
            STATE,
            HTML_NAME,
            LIB_SCRIPT,
            LIB_INIT_SRC,
            VERIFY_SCRIPT,
            PARAM_TYPE,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TS_PROB_LIB_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByUnitId != null and item.fkByUnitId.rid != null">
                            #{item.fkByUnitId.rid} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByQuestSortid != null and item.fkByQuestSortid.rid != null">
                            #{item.fkByQuestSortid.rid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.questName != null">
                            #{item.questName} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rmk != null">
                            #{item.rmk} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.num != null">
                            #{item.num} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.backImage != null">
                            #{item.backImage} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.state != null">
                            #{item.state} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.htmlName != null">
                            #{item.htmlName} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.libScript != null">
                            #{item.libScript} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.libInitSrc != null">
                            #{item.libInitSrc} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.verifyScript != null">
                            #{item.verifyScript} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.paramType != null">
                            #{item.paramType} AS C14,
                        </when>
                        <otherwise>
                            NULL AS C14,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TsProbLib" >
        update TS_PROB_LIB t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TS_PROB_LIB t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TsProbLib">
        delete from TS_PROB_LIB
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

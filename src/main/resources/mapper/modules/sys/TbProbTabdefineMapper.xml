<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TbProbTabdefineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TbProbTabdefine">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="TAB_NAME" property="tabName" />
        <result column="RMK" property="rmk" />
        <result column="ROW_FIXED" property="rowFixed" />
        <result column="DEFAULT_LINE_NUM" property="defaultLineNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,
        t.TAB_NAME,t.RMK,t.ROW_FIXED,t.DEFAULT_LINE_NUM,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}tabName != null and ${joiner}tabName != ''">
            and ${mAlias}TAB_NAME = #{${joiner}tabName}
        </if>
        <if test="${joiner}rmk != null and ${joiner}rmk != ''">
            and ${mAlias}RMK = #{${joiner}rmk}
        </if>
        <if test="${joiner}rowFixed != null and ${joiner}rowFixed != ''">
            and ${mAlias}ROW_FIXED = #{${joiner}rowFixed}
        </if>
        <if test="${joiner}defaultLineNum != null and ${joiner}defaultLineNum != ''">
            and ${mAlias}DEFAULT_LINE_NUM = #{${joiner}defaultLineNum}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        <choose>
            <when test="${joiner}tabName != null and ${joiner}tabName != ''">
                t.TAB_NAME = #{${joiner}tabName},
            </when>
            <otherwise>
                t.TAB_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rmk != null and ${joiner}rmk != ''">
                t.RMK = #{${joiner}rmk},
            </when>
            <otherwise>
                t.RMK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rowFixed != null and ${joiner}rowFixed != ''">
                t.ROW_FIXED = #{${joiner}rowFixed},
            </when>
            <otherwise>
                t.ROW_FIXED = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}defaultLineNum != null and ${joiner}defaultLineNum != ''">
                t.DEFAULT_LINE_NUM = #{${joiner}defaultLineNum},
            </when>
            <otherwise>
                t.DEFAULT_LINE_NUM = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_PROB_TABDEFINE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_PROB_TABDEFINE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TB_PROB_TABDEFINE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TB_PROB_TABDEFINE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            TAB_NAME,
            RMK,
            ROW_FIXED,
            DEFAULT_LINE_NUM,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TB_PROB_TABDEFINE_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.tabName != null">
                            #{item.tabName} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rmk != null">
                            #{item.rmk} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.rowFixed != null">
                            #{item.rowFixed} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.defaultLineNum != null">
                            #{item.defaultLineNum} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TbProbTabdefine" >
        update TB_PROB_TABDEFINE t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TB_PROB_TABDEFINE t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TbProbTabdefine">
        delete from TB_PROB_TABDEFINE
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->

    <select id="findTbProbTabdefineListByRids" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TB_PROB_TABDEFINE t
        <where>
            <if test="list != null and list.size > 0">
                AND t.RID IN
                <foreach collection="list" item="item" index="index" separator="," open="(" close=")" >
                    <trim  suffixOverrides=",">
                        #{item}
                    </trim>
                </foreach>
            </if>
        </where>
    </select>



</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TsSimpleCodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TsSimpleCode">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="CODE_TYPE_ID" property="fkByCodeTypeId.rid" />
        <result column="CODE_NO" property="codeNo" />
        <result column="CODE_NAME" property="codeName" />
        <result column="CODE_DESC" property="codeDesc" />
        <result column="CODE_LEVEL_NO" property="codeLevelNo" />
        <result column="CODE_PATH" property="codePath" />
        <result column="PUBLISH_TAG" property="publishTag" />
        <result column="IF_REVEAL" property="ifReveal" />
        <result column="STOP_DATE" property="stopDate" />
        <result column="SPLSHT" property="splsht" />
        <result column="NUM" property="num" />
        <result column="EXTENDS1" property="extends1" />
        <result column="EXTENDS2" property="extends2" />
        <result column="EXTENDS3" property="extends3" />
        <result column="EXTENDS4" property="extends4" />
        <result column="EXTENDS5" property="extends5" />
        <result column="EXTENDS6" property="extends6" />
        <result column="CODE_TYPE_NAME" property="fkByCodeTypeId.codeTypeName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.CODE_TYPE_ID,t.CODE_NO,t.CODE_NAME,t.CODE_DESC,t.CODE_LEVEL_NO,t.CODE_PATH,t.PUBLISH_TAG,t.IF_REVEAL,t.STOP_DATE,t.SPLSHT,t.NUM,t.EXTENDS1,t.EXTENDS2,t.EXTENDS3,t.EXTENDS4,t.EXTENDS5,t.EXTENDS6,t1.CODE_TYPE_NAME,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="rid != null">
            and ${mAlias}RID = #{rid}
        </if>
        <if test="createDate != null">
            and ${mAlias}CREATE_DATE = #{createDate}
        </if>
        <if test="createManid != null">
            and ${mAlias}CREATE_MANID = #{createManid}
        </if>
        <if test="modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{modifyDate}
        </if>
        <if test="modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{modifyManid}
        </if>
        <if test="fkByCodeTypeId != null and fkByCodeTypeId.rid != null">
            and ${mAlias}CODE_TYPE_ID = #{fkByCodeTypeId.rid}
        </if>
        <if test="codeNo != null and codeNo != ''">
            and ${mAlias}CODE_NO = #{codeNo}
        </if>
        <if test="codeName != null and codeName != ''">
            and ${mAlias}CODE_NAME = #{codeName}
        </if>
        <if test="codeDesc != null and codeDesc != ''">
            and ${mAlias}CODE_DESC = #{codeDesc}
        </if>
        <if test="codeLevelNo != null and codeLevelNo != ''">
            and ${mAlias}CODE_LEVEL_NO = #{codeLevelNo}
        </if>
        <if test="codePath != null and codePath != ''">
            and ${mAlias}CODE_PATH = #{codePath}
        </if>
        <if test="publishTag != null">
            and ${mAlias}PUBLISH_TAG = #{publishTag}
        </if>
        <if test="ifReveal != null">
            and ${mAlias}IF_REVEAL = #{ifReveal}
        </if>
        <if test="stopDate != null">
            and ${mAlias}STOP_DATE = #{stopDate}
        </if>
        <if test="splsht != null and splsht != ''">
            and ${mAlias}SPLSHT = #{splsht}
        </if>
        <if test="num != null">
            and ${mAlias}NUM = #{num}
        </if>
        <if test="extends1 != null and extends1 != ''">
            and ${mAlias}EXTENDS1 = #{extends1}
        </if>
        <if test="extends2 != null and extends2 != ''">
            and ${mAlias}EXTENDS2 = #{extends2}
        </if>
        <if test="extends3 != null and extends3 != ''">
            and ${mAlias}EXTENDS3 = #{extends3}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
            t.CREATE_MANID = #{${joiner}createManid},
            t.MODIFY_DATE = #{${joiner}modifyDate},
            t.MODIFY_MANID = #{${joiner}modifyManid},
        <choose>
            <when test="${joiner}fkByCodeTypeId != null and ${joiner}fkByCodeTypeId.rid != null">
                t.CODE_TYPE_ID = #{${joiner}fkByCodeTypeId.rid},
            </when>
            <otherwise>
                t.CODE_TYPE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}codeNo != null and ${joiner}codeNo != ''">
                t.CODE_NO = #{${joiner}codeNo},
            </when>
            <otherwise>
                t.CODE_NO = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}codeName != null and ${joiner}codeName != ''">
                t.CODE_NAME = #{${joiner}codeName},
            </when>
            <otherwise>
                t.CODE_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}codeDesc != null and ${joiner}codeDesc != ''">
                t.CODE_DESC = #{${joiner}codeDesc},
            </when>
            <otherwise>
                t.CODE_DESC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}codeLevelNo != null and ${joiner}codeLevelNo != ''">
                t.CODE_LEVEL_NO = #{${joiner}codeLevelNo},
            </when>
            <otherwise>
                t.CODE_LEVEL_NO = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}codePath != null and ${joiner}codePath != ''">
                t.CODE_PATH = #{${joiner}codePath},
            </when>
            <otherwise>
                t.CODE_PATH = null,
            </otherwise>
        </choose>
            t.PUBLISH_TAG = #{${joiner}publishTag},
            t.IF_REVEAL = #{${joiner}ifReveal},
            t.STOP_DATE = #{${joiner}stopDate},
        <choose>
            <when test="${joiner}splsht != null and ${joiner}splsht != ''">
                t.SPLSHT = #{${joiner}splsht},
            </when>
            <otherwise>
                t.SPLSHT = null,
            </otherwise>
        </choose>
            t.NUM = #{${joiner}num},
        <choose>
            <when test="${joiner}extends1 != null and ${joiner}extends1 != ''">
                t.EXTENDS1 = #{${joiner}extends1},
            </when>
            <otherwise>
                t.EXTENDS1 = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}extends2 != null and ${joiner}extends2 != ''">
                t.EXTENDS2 = #{${joiner}extends2},
            </when>
            <otherwise>
                t.EXTENDS2 = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}extends3 != null and ${joiner}extends3 != ''">
                t.EXTENDS3 = #{${joiner}extends3},
            </when>
            <otherwise>
                t.EXTENDS3 = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  ts_simple_code t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  ts_simple_code t
        left join ts_code_type t1 on t1.RID = t.CODE_TYPE_ID
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  ts_simple_code t
        left join ts_code_type t1 on t.code_type_id = t1.rid
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TsSimpleCode" >
        update ts_simple_code t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update ts_simple_code t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TsSimpleCode">
        delete from ts_simple_code
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->

	<select id="findTsSimpleCode" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  ts_simple_code t
        left join ts_code_type t1 on t.code_type_id = t1.rid
        <where>
        	<if test="null!=codeName">
	        	and t1.CODE_TYPE_NAME = #{codeName}
        	</if>
        	<if test="null!=codeNo">
	        	and t.code_No = #{codeNo}
        	</if>
        	and t.IF_REVEAL = 1
        </where>
    </select>

	<select id="findTsSimpleCodeList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  ts_simple_code t
        left join ts_code_type t1 on t.code_type_id = t1.rid
        <where>
            <if test="null!=codeNo">
	        	and t1.CODE_TYPE_NAME = #{codeNo}
        	</if>
        </where>
        and t.IF_REVEAL = 1
    </select>

    <select id="findTsSimpleCodeAllList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  ts_simple_code t
        left join ts_code_type t1 on t.code_type_id = t1.rid
        <where>
            <if test="null!=codeNo">
                and t1.CODE_TYPE_NAME = #{codeNo}
            </if>
        </where>
    </select>


    <select id="findTsSimpleCodeListOrderby" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  ts_simple_code t
        left join ts_code_type t1 on t.code_type_id = t1.rid
        <where>
            <if test="null!=codeNo">
                and t1.CODE_TYPE_NAME = #{codeNo}
            </if>
            and t.IF_REVEAL = 1
        </where>
        ORDER BY t.NUM
    </select>

    <select id="findAllTsSimpleCodeList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  ts_simple_code t
        left join ts_code_type t1 on t.code_type_id = t1.rid
        <where>
            <if test="null!=codeNo">
                and t1.CODE_TYPE_NAME = #{codeNo}
            </if>
        </where>
        ORDER BY  NVL(t.IF_REVEAL,1) ,t.rid
    </select>

    <select id="findAllTsSimpleCodeListByCodeNoList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  ts_simple_code t
        left join ts_code_type t1 on t.code_type_id = t1.rid
        <where>
            <if test="codeNoList != null and codeNoList.size > 0">
                AND t1.CODE_TYPE_NAME IN
                <foreach collection="codeNoList" item="item" index="index" separator="," open="(" close=")" >
                    <trim  suffixOverrides=",">
                        #{item}
                    </trim>
                </foreach>
            </if>
        </where>
    </select>

    <select id="findTsSimpleCodeExtends1" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  ts_simple_code t
        left join ts_code_type t1 on t.code_type_id = t1.rid
        <where>
            <if test="null!=codeName">
                and t1.CODE_TYPE_NAME = #{codeName}
            </if>
            <if test="null!=extends1">
                and t.extends1 = #{extends1}
            </if>
            and t.IF_REVEAL = 1
        </where>
    </select>
    <select id="findAllTsSimpleCodeListByCodeName" resultType="com.chis.modules.sys.entity.TsSimpleCode">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  ts_simple_code t
        left join ts_code_type t1 on t.code_type_id = t1.rid
        <where>
            <if test="null!=codeName">
                and t1.CODE_TYPE_NAME = #{codeName}
            </if>
        </where>
        ORDER BY IF_REVEAL,t.RID desc
    </select>
    <select id="selectListByQueId" resultType="com.chis.modules.sys.entity.TsSimpleCode">
        SELECT DISTINCT T3.* FROM  TS_PRO_OPT T
       LEFT JOIN TS_PROB_SUBJECT T1 ON T.QUEST_ID = T1.RID
       LEFT JOIN TS_SIMPLE_CODE T3 ON T.OPTION_VALUE = T3.RID
        WHERE T1.STATE = 1 AND T3.RID IS NOT NULL AND T1.QUESTLIB_ID = #{queId}
    </select>

    <select id="findAllTsSimpleCodeLists" resultType="com.chis.modules.sys.entity.TsSimpleCode">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  ts_simple_code t
        left join ts_code_type t1 on t.code_type_id = t1.rid
        <where>
            <if test="null!=codeName">
                and t1.CODE_TYPE_NAME = #{codeName}
            </if>
        </where>
        ORDER BY  NVL(IF_REVEAL,0) DESC,t.CODE_LEVEL_NO,t.NUM,t.CODE_NO
    </select>
</mapper>

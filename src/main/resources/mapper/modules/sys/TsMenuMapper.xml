<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TsMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TsMenu">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="MENU_LEVEL_NO" property="menuLevelNo" />
        <result column="MENU_CN" property="menuCn" />
        <result column="MENU_EN" property="menuEn" />
        <result column="MENU_SIMPLE" property="menuSimple" />
        <result column="ISFUNC" property="isfunc" />
        <result column="MENU_URI" property="menuUri" />
        <result column="MENU_ICON" property="menuIcon" />
        <result column="NUM" property="num" />
        <result column="BIG_ICON" property="bigIcon" />
        <result column="IF_POP" property="ifPop" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.MENU_LEVEL_NO,t.MENU_CN,t.MENU_EN,t.MENU_SIMPLE,t.ISFUNC,t.MENU_URI,t.MENU_ICON,t.NUM,t.BIG_ICON,t.IF_POP,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="rid != null">
            and ${mAlias}RID = #{rid}
        </if>
        <if test="createDate != null">
            and ${mAlias}CREATE_DATE = #{createDate}
        </if>
        <if test="createManid != null">
            and ${mAlias}CREATE_MANID = #{createManid}
        </if>
        <if test="modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{modifyDate}
        </if>
        <if test="modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{modifyManid}
        </if>
        <if test="menuLevelNo != null and menuLevelNo != ''">
            and ${mAlias}MENU_LEVEL_NO = #{menuLevelNo}
        </if>
        <if test="menuCn != null and menuCn != ''">
            and ${mAlias}MENU_CN = #{menuCn}
        </if>
        <if test="menuEn != null and menuEn != ''">
            and ${mAlias}MENU_EN = #{menuEn}
        </if>
        <if test="menuSimple != null and menuSimple != ''">
            and ${mAlias}MENU_SIMPLE = #{menuSimple}
        </if>
        <if test="isfunc != null and isfunc != ''">
            and ${mAlias}ISFUNC = #{isfunc}
        </if>
        <if test="menuUri != null and menuUri != ''">
            and ${mAlias}MENU_URI = #{menuUri}
        </if>
        <if test="menuIcon != null and menuIcon != ''">
            and ${mAlias}MENU_ICON = #{menuIcon}
        </if>
        <if test="num != null and num != ''">
            and ${mAlias}NUM = #{num}
        </if>
        <if test="bigIcon != null and bigIcon != ''">
            and ${mAlias}BIG_ICON = #{bigIcon}
        </if>
        <if test="ifPop != null and ifPop != ''">
            and ${mAlias}IF_POP = #{ifPop}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}menuLevelNo != null and ${joiner}menuLevelNo != ''">
                t.MENU_LEVEL_NO = #{${joiner}menuLevelNo},
            </when>
            <otherwise>
                t.MENU_LEVEL_NO = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}menuCn != null and ${joiner}menuCn != ''">
                t.MENU_CN = #{${joiner}menuCn},
            </when>
            <otherwise>
                t.MENU_CN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}menuEn != null and ${joiner}menuEn != ''">
                t.MENU_EN = #{${joiner}menuEn},
            </when>
            <otherwise>
                t.MENU_EN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}menuSimple != null and ${joiner}menuSimple != ''">
                t.MENU_SIMPLE = #{${joiner}menuSimple},
            </when>
            <otherwise>
                t.MENU_SIMPLE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}isfunc != null and ${joiner}isfunc != ''">
                t.ISFUNC = #{${joiner}isfunc},
            </when>
            <otherwise>
                t.ISFUNC = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}menuUri != null and ${joiner}menuUri != ''">
                t.MENU_URI = #{${joiner}menuUri},
            </when>
            <otherwise>
                t.MENU_URI = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}menuIcon != null and ${joiner}menuIcon != ''">
                t.MENU_ICON = #{${joiner}menuIcon},
            </when>
            <otherwise>
                t.MENU_ICON = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}num != null and ${joiner}num != ''">
                t.NUM = #{${joiner}num},
            </when>
            <otherwise>
                t.NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}bigIcon != null and ${joiner}bigIcon != ''">
                t.BIG_ICON = #{${joiner}bigIcon},
            </when>
            <otherwise>
                t.BIG_ICON = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifPop != null and ${joiner}ifPop != ''">
                t.IF_POP = #{${joiner}ifPop},
            </when>
            <otherwise>
                t.IF_POP = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_MENU t
        order by MENU_LEVEL_NO
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_MENU t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_MENU t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TsMenu" >
        update TS_MENU t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TS_MENU t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TsMenu">
        delete from TS_MENU
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

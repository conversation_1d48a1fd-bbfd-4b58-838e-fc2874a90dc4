<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TdProbAnsDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TdProbAnsDetail">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MAIN_TYPE" property="mainType" />
        <result column="MAIN_ID" property="mainId" />
        <result column="QUEST_ID" property="fkByQuestId.rid" />
        <result column="OPTION_VALUE" property="optionValue" />
        <result column="SCORE_VALUE" property="scoreValue" />
        <result column="FILL_VALUE" property="fillValue" />
        <result column="MULTI_NUM" property="multiNum" />
        <result column="QUE_TYPE_ID" property="fkByQueTypeId.rid" />
        <result column="EXAMPOOL_ID" property="fkByExampoolId.rid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.QUE_TYPE_ID,
        t.MAIN_TYPE,t.MAIN_ID,t.QUEST_ID,t.OPTION_VALUE,t.SCORE_VALUE,t.FILL_VALUE,t.MULTI_NUM,t.EXAMPOOL_ID
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}mainType != null and ${joiner}mainType != ''">
            and ${mAlias}MAIN_TYPE = #{${joiner}mainType}
        </if>
        <if test="${joiner}mainId != null and ${joiner}mainId != ''">
            and ${mAlias}MAIN_ID = #{${joiner}mainId}
        </if>
        <if test="${joiner}fkByQuestId != null and ${joiner}fkByQuestId.rid != null">
            and ${mAlias}QUEST_ID = #{${joiner}fkByQuestId.rid}
        </if>
        <if test="${joiner}optionValue != null and ${joiner}optionValue != ''">
            and ${mAlias}OPTION_VALUE = #{${joiner}optionValue}
        </if>
        <if test="${joiner}scoreValue != null and ${joiner}scoreValue != ''">
            and ${mAlias}SCORE_VALUE = #{${joiner}scoreValue}
        </if>
        <if test="${joiner}fillValue != null and ${joiner}fillValue != ''">
            and ${mAlias}FILL_VALUE = #{${joiner}fillValue}
        </if>
        <if test="${joiner}multiNum != null and ${joiner}multiNum != ''">
            and ${mAlias}MULTI_NUM = #{${joiner}multiNum}
        </if>
        <if test="${joiner}fkByQueTypeId != null and ${joiner}fkByQueTypeId.rid != null">
            and ${mAlias}QUE_TYPE_ID = #{${joiner}fkByQueTypeId.rid}
        </if>
        <if test="${joiner}fkByExampoolId != null and ${joiner}fkByExampoolId.rid != null">
            and ${mAlias}EXAMPOOL_ID = #{${joiner}fkByExampoolId.rid}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
        t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}mainType != null and ${joiner}mainType != ''">
                t.MAIN_TYPE = #{${joiner}mainType},
            </when>
            <otherwise>
                t.MAIN_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}mainId != null and ${joiner}mainId != ''">
                t.MAIN_ID = #{${joiner}mainId},
            </when>
            <otherwise>
                t.MAIN_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByQuestId != null and ${joiner}fkByQuestId.rid != null">
                t.QUEST_ID = #{${joiner}fkByQuestId.rid},
            </when>
            <otherwise>
                t.QUEST_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}optionValue != null and ${joiner}optionValue != ''">
                t.OPTION_VALUE = #{${joiner}optionValue},
            </when>
            <otherwise>
                t.OPTION_VALUE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}scoreValue != null and ${joiner}scoreValue != ''">
                t.SCORE_VALUE = #{${joiner}scoreValue},
            </when>
            <otherwise>
                t.SCORE_VALUE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fillValue != null and ${joiner}fillValue != ''">
                t.FILL_VALUE = #{${joiner}fillValue},
            </when>
            <otherwise>
                t.FILL_VALUE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}multiNum != null and ${joiner}multiNum != ''">
                t.MULTI_NUM = #{${joiner}multiNum},
            </when>
            <otherwise>
                t.MULTI_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByQueTypeId != null and ${joiner}fkByQueTypeId.rid != null">
                t.QUE_TYPE_ID = #{${joiner}fkByQueTypeId.rid},
            </when>
            <otherwise>
                t.QUE_TYPE_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByExampoolId != null and ${joiner}fkByExampoolId.rid != null">
                t.EXAMPOOL_ID = #{${joiner}fkByExampoolId.rid},
            </when>
            <otherwise>
                t.EXAMPOOL_ID = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_PROB_ANS_DETAIL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_PROB_ANS_DETAIL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TD_PROB_ANS_DETAIL t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertEntity" parameterType="TdProbAnsDetail">
        <selectKey resultType="Integer" order="BEFORE" keyProperty="rid">
            SELECT TD_PROB_ANS_DETAIL_SEQ.Nextval AS rid FROM DUAL
        </selectKey>
        insert into TD_PROB_ANS_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MAIN_TYPE,
            MAIN_ID,
            QUEST_ID,
            QUE_TYPE_ID,
            OPTION_VALUE,
            SCORE_VALUE,
            FILL_VALUE,
            MULTI_NUM,
            EXAMPOOL_ID,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{rid},
            #{createDate},
            #{createManid},
            #{mainType},
            #{mainId},
            #{fkByQuestId.rid},
            #{fkByQueTypeId.rid},
            #{optionValue},
            #{scoreValue},
            #{fillValue},
            #{multiNum},
            #{fkByExampoolId.rid},
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TD_PROB_ANS_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MAIN_TYPE,
            MAIN_ID,
            QUEST_ID,
            QUE_TYPE_ID,
            OPTION_VALUE,
            SCORE_VALUE,
            FILL_VALUE,
            MULTI_NUM,
            EXAMPOOL_ID,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TD_PROB_ANS_DETAIL_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.mainType != null">
                            #{item.mainType} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.mainId != null">
                            #{item.mainId} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByQuestId != null and item.fkByQuestId.rid != null">
                            #{item.fkByQuestId.rid} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByQueTypeId != null and item.fkByQueTypeId.rid != null">
                            #{item.fkByQueTypeId.rid} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.optionValue != null">
                            #{item.optionValue} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.scoreValue != null">
                            #{item.scoreValue} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fillValue != null">
                            #{item.fillValue} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.multiNum != null">
                            #{item.multiNum} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.fkByExampoolId != null and item.fkByExampoolId.rid != null">
                            #{item.fkByExampoolId.rid} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TdProbAnsDetail" >
        update TD_PROB_ANS_DETAIL t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TD_PROB_ANS_DETAIL t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TdProbAnsDetail">
        delete from TD_PROB_ANS_DETAIL
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
    <delete id="deleteTdProbAnsDetail">
        delete from TD_PROB_ANS_DETAIL WHERE QUE_TYPE_ID = #{busType} AND MAIN_ID = #{mainId}
    </delete>

    <delete id="deletePsnCheckTdProbAnsDetail">
        delete from TD_PROB_ANS_DETAIL WHERE MAIN_TYPE = #{mainType} AND MAIN_ID = #{mainId}
    </delete>

    <select id="findAnsDetailListByQueTypeIdAndMainIds" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TD_PROB_ANS_DETAIL t
        where 1=1
        <if test="null != queTypeId">
            AND t.QUE_TYPE_ID = #{queTypeId}
        </if>
        <if test="mainIdList != null and mainIdList.size > 0">
            AND t.MAIN_ID IN
            <foreach collection="mainIdList" item="item" index="index" separator="," open="(" close=")" >
                <trim  suffixOverrides=",">
                    #{item}
                </trim>
            </foreach>
        </if>
    </select>

</mapper>

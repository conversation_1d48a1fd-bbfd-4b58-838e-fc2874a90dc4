<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TsUserInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TsUserInfo">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="USER_TYPE" property="userType" />
        <result column="EMP_ID" property="fkByEmpId.rid" />
        <result column="USER_NO" property="userNo" />
        <result column="USERNAME" property="username" />
        <result column="PASSWORD" property="password" />
        <result column="IF_MODPSW" property="ifModpsw" />
        <result column="RMK" property="rmk" />
        <result column="IF_REVEAL" property="ifReveal" />
        <result column="UNIT_RID" property="fkByUnitRid.rid" />
        <result column="USERADMIN" property="useradmin" />
        <result column="MB_NUM" property="mbNum" />
        <result column="DISP_KJMENU" property="dispKjmenu" />
        <result column="EMAIL" property="email" />
        <result column="IS_ADD" property="isAdd" />
        <result column="UPLOAD_TAG" property="uploadTag" />
        <result column="ERR_MSG" property="errMsg" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.USER_TYPE,t.EMP_ID,t.USER_NO,t.USERNAME,t.PASSWORD,t.IF_MODPSW,t.RMK,t.IF_REVEAL,t.UNIT_RID,t.USERADMIN,t.MB_NUM,t.DISP_KJMENU,t.EMAIL,t.IS_ADD,t.UPLOAD_TAG,t.ERR_MSG,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="rid != null">
            and ${mAlias}RID = #{rid}
        </if>
        <if test="createDate != null">
            and ${mAlias}CREATE_DATE = #{createDate}
        </if>
        <if test="createManid != null">
            and ${mAlias}CREATE_MANID = #{createManid}
        </if>
        <if test="modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{modifyDate}
        </if>
        <if test="modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{modifyManid}
        </if>
        <if test="userType != null and userType != ''">
            and ${mAlias}USER_TYPE = #{userType}
        </if>
        <if test="fkByEmpId != null and fkByEmpId.rid != null">
            and ${mAlias}EMP_ID = #{fkByEmpId.rid}
        </if>
        <if test="userNo != null and userNo != ''">
            and ${mAlias}USER_NO = #{userNo}
        </if>
        <if test="username != null and username != ''">
            and ${mAlias}USERNAME = #{username}
        </if>
        <if test="password != null and password != ''">
            and ${mAlias}PASSWORD = #{password}
        </if>
        <if test="ifModpsw != null and ifModpsw != ''">
            and ${mAlias}IF_MODPSW = #{ifModpsw}
        </if>
        <if test="rmk != null and rmk != ''">
            and ${mAlias}RMK = #{rmk}
        </if>
        <if test="ifReveal != null and ifReveal != ''">
            and ${mAlias}IF_REVEAL = #{ifReveal}
        </if>
        <if test="fkByUnitRid != null and fkByUnitRid.rid != null">
            and ${mAlias}UNIT_RID = #{fkByUnitRid.rid}
        </if>
        <if test="useradmin != null and useradmin != ''">
            and ${mAlias}USERADMIN = #{useradmin}
        </if>
        <if test="mbNum != null and mbNum != ''">
            and ${mAlias}MB_NUM = #{mbNum}
        </if>
        <if test="dispKjmenu != null and dispKjmenu != ''">
            and ${mAlias}DISP_KJMENU = #{dispKjmenu}
        </if>
        <if test="email != null and email != ''">
            and ${mAlias}EMAIL = #{email}
        </if>
        <if test="isAdd != null and isAdd != ''">
            and ${mAlias}IS_ADD = #{isAdd}
        </if>
        <if test="uploadTag != null and uploadTag != ''">
            and ${mAlias}UPLOAD_TAG = #{uploadTag}
        </if>
        <if test="errMsg != null and errMsg != ''">
            and ${mAlias}ERR_MSG = #{errMsg}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}userType != null and ${joiner}userType != ''">
                t.USER_TYPE = #{${joiner}userType},
            </when>
            <otherwise>
                t.USER_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByEmpId != null and ${joiner}fkByEmpId.rid != null">
                t.EMP_ID = #{${joiner}fkByEmpId.rid},
            </when>
            <otherwise>
                t.EMP_ID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}userNo != null and ${joiner}userNo != ''">
                t.USER_NO = #{${joiner}userNo},
            </when>
            <otherwise>
                t.USER_NO = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}username != null and ${joiner}username != ''">
                t.USERNAME = #{${joiner}username},
            </when>
            <otherwise>
                t.USERNAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}password != null and ${joiner}password != ''">
                t.PASSWORD = #{${joiner}password},
            </when>
            <otherwise>
                t.PASSWORD = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifModpsw != null and ${joiner}ifModpsw != ''">
                t.IF_MODPSW = #{${joiner}ifModpsw},
            </when>
            <otherwise>
                t.IF_MODPSW = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}rmk != null and ${joiner}rmk != ''">
                t.RMK = #{${joiner}rmk},
            </when>
            <otherwise>
                t.RMK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifReveal != null and ${joiner}ifReveal != ''">
                t.IF_REVEAL = #{${joiner}ifReveal},
            </when>
            <otherwise>
                t.IF_REVEAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fkByUnitRid != null and ${joiner}fkByUnitRid.rid != null">
                t.UNIT_RID = #{${joiner}fkByUnitRid.rid},
            </when>
            <otherwise>
                t.UNIT_RID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}useradmin != null and ${joiner}useradmin != ''">
                t.USERADMIN = #{${joiner}useradmin},
            </when>
            <otherwise>
                t.USERADMIN = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}mbNum != null and ${joiner}mbNum != ''">
                t.MB_NUM = #{${joiner}mbNum},
            </when>
            <otherwise>
                t.MB_NUM = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dispKjmenu != null and ${joiner}dispKjmenu != ''">
                t.DISP_KJMENU = #{${joiner}dispKjmenu},
            </when>
            <otherwise>
                t.DISP_KJMENU = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}email != null and ${joiner}email != ''">
                t.EMAIL = #{${joiner}email},
            </when>
            <otherwise>
                t.EMAIL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}isAdd != null and ${joiner}isAdd != ''">
                t.IS_ADD = #{${joiner}isAdd},
            </when>
            <otherwise>
                t.IS_ADD = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}uploadTag != null and ${joiner}uploadTag != ''">
                t.UPLOAD_TAG = #{${joiner}uploadTag},
            </when>
            <otherwise>
                t.UPLOAD_TAG = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}errMsg != null and ${joiner}errMsg != ''">
                t.ERR_MSG = #{${joiner}errMsg},
            </when>
            <otherwise>
                t.ERR_MSG = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_USER_INFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_USER_INFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_USER_INFO t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TsUserInfo" >
        update TS_USER_INFO t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TS_USER_INFO t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TsUserInfo">
        delete from TS_USER_INFO
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
	<select id="selectMenuAuthUser" resultType="Integer">
        select
        <trim suffixOverrides=",">
           t.rid,
        </trim>
        from TS_USER_INFO t
        left join TS_USER_ROLE t1 on t.rid = t1.USER_INFO_ID  
        left join TS_ROLE t2 on t1.role_id = t2.rid
        left join TS_ROLE_POWER t3 on t3.ROLE_ID = t2.rid
        left join ts_simple_code t4 on t3.ROLE_TYPE_ID = t4.rid
        left join ts_unit t5 on t5.rid = t.unit_rid
        left join ts_zone t6 on t6.rid = t5.MANAGE_ZONE_ID
        left join TS_USER_MENU t7 on t.rid = t7.USER_INFO_ID
        left join TS_ROLE_MENU t8 on t8.ROLE_ID = t2.rid
        left join ts_menu t9 on t8.MENU_ID = t9.rid or t7.menu_template_id = t9.rid
        <where>
			t4.EXTENDS1 = 1
			and t9.isfunc = 1 
			<if test="menuEn != null and menuEn != ''">
	            and t9.menu_en = #{menuEn}
	        </if>
	        <if test="zoneGb != null and zoneGb != ''">
	            and t6.zone_gb like concat(#{zoneGb},'%')
	        </if>
	        <if test="zoneType != null and zoneType != ''">
	            and t6.zone_type = #{zoneType}
	        </if>
        </where>
        group by t.rid
    </select>
    <select id="findTsUserAll" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_USER_INFO t
    </select>


</mapper>

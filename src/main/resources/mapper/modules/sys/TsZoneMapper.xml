<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TsZoneMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TsZone">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="ZONE_GB" property="zoneGb" />
        <result column="ZONE_NAME" property="zoneName" />
        <result column="ZONE_TYPE" property="zoneType" />
        <result column="IF_REVEAL" property="ifReveal" />
        <result column="STOP_DATE" property="stopDate" />
        <result column="ZONE_CODE" property="zoneCode" />
        <result column="FULL_NAME" property="fullName" />
        <result column="DSF_CODE" property="dsfCode" />
        <result column="REAL_ZONE_TYPE" property="realZoneType" />
        <result column="IF_CITY_DIRECT" property="ifCityDirect" />
        <result column="IF_PROV_DIRECT" property="ifProvDirect" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.ZONE_GB,t.ZONE_NAME,t.ZONE_TYPE,t.IF_REVEAL,t.STOP_DATE,t.ZONE_CODE,t.FULL_NAME,t.DSF_CODE,t.REAL_ZONE_TYPE,t.IF_CITY_DIRECT,t.IF_PROV_DIRECT,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="rid != null">
            and ${mAlias}RID = #{rid}
        </if>
        <if test="createDate != null">
            and ${mAlias}CREATE_DATE = #{createDate}
        </if>
        <if test="createManid != null">
            and ${mAlias}CREATE_MANID = #{createManid}
        </if>
        <if test="modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{modifyDate}
        </if>
        <if test="modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{modifyManid}
        </if>
        <if test="zoneGb != null and zoneGb != ''">
            and ${mAlias}ZONE_GB = #{zoneGb}
        </if>
        <if test="zoneName != null and zoneName != ''">
            and ${mAlias}ZONE_NAME = #{zoneName}
        </if>
        <if test="zoneType != null and zoneType != ''">
            and ${mAlias}ZONE_TYPE = #{zoneType}
        </if>
        <if test="ifReveal != null and ifReveal != ''">
            and ${mAlias}IF_REVEAL = #{ifReveal}
        </if>
        <if test="stopDate != null">
            and ${mAlias}STOP_DATE = #{stopDate}
        </if>
        <if test="zoneCode != null and zoneCode != ''">
            and ${mAlias}ZONE_CODE = #{zoneCode}
        </if>
        <if test="fullName != null and fullName != ''">
            and ${mAlias}FULL_NAME = #{fullName}
        </if>
        <if test="dsfCode != null and dsfCode != ''">
            and ${mAlias}DSF_CODE = #{dsfCode}
        </if>
        <if test="realZoneType != null and realZoneType != ''">
            and ${mAlias}REAL_ZONE_TYPE = #{realZoneType}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.CREATE_DATE = #{${joiner}createDate},
        <choose>
            <when test="${joiner}createManid != null and ${joiner}createManid != ''">
                t.CREATE_MANID = #{${joiner}createManid},
            </when>
            <otherwise>
                t.CREATE_MANID = null,
            </otherwise>
        </choose>
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}zoneGb != null and ${joiner}zoneGb != ''">
                t.ZONE_GB = #{${joiner}zoneGb},
            </when>
            <otherwise>
                t.ZONE_GB = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}zoneName != null and ${joiner}zoneName != ''">
                t.ZONE_NAME = #{${joiner}zoneName},
            </when>
            <otherwise>
                t.ZONE_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}zoneType != null and ${joiner}zoneType != ''">
                t.ZONE_TYPE = #{${joiner}zoneType},
            </when>
            <otherwise>
                t.ZONE_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifReveal != null and ${joiner}ifReveal != ''">
                t.IF_REVEAL = #{${joiner}ifReveal},
            </when>
            <otherwise>
                t.IF_REVEAL = null,
            </otherwise>
        </choose>
            t.STOP_DATE = #{${joiner}stopDate},
        <choose>
            <when test="${joiner}zoneCode != null and ${joiner}zoneCode != ''">
                t.ZONE_CODE = #{${joiner}zoneCode},
            </when>
            <otherwise>
                t.ZONE_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}fullName != null and ${joiner}fullName != ''">
                t.FULL_NAME = #{${joiner}fullName},
            </when>
            <otherwise>
                t.FULL_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dsfCode != null and ${joiner}dsfCode != ''">
                t.DSF_CODE = #{${joiner}dsfCode},
            </when>
            <otherwise>
                t.DSF_CODE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}realZoneType != null and ${joiner}realZoneType != ''">
                t.REAL_ZONE_TYPE = #{${joiner}realZoneType},
            </when>
            <otherwise>
                t.REAL_ZONE_TYPE = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_ZONE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_ZONE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_ZONE t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
            </include>
        </where>
    </select>


    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TsZone" >
        update TS_ZONE t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update TS_ZONE t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TsZone">
        delete from TS_ZONE
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->
	<select id="findZoneList" resultType="TsZone" parameterType="TsZone">
		SELECT
		<trim suffixOverrides=",">
			T.RID AS rid,
			T.ZONE_GB AS zoneGb,
			T.ZONE_NAME AS zoneName,
			T.ZONE_TYPE AS zoneLevel,
		</trim>
		    FROM TS_ZONE T
		    WHERE T.IF_REVEAL = '1'
		<if test="zoneGb != null">
			AND T.ZONE_GB LIKE ''||#{zoneGb}||'%'
		</if>
		<if test="zoneTypeMin != null">
			AND T.ZONE_TYPE &gt;= #{zoneTypeMin}
		</if>
		<if test="zoneTypeMax != null">
			AND T.ZONE_TYPE &lt;= #{zoneTypeMax}
		</if>
		<if test="ifContainsProv != null and !ifContainsProv">
            and T.ZONE_TYPE > 2
        </if>
		ORDER BY T.ZONE_GB
	</select>
    <select id="selectTsZones" resultType="com.chis.modules.sys.entity.TsZone">
        SELECT
        <trim suffixOverrides=",">
            T.RID AS rid,
            T.ZONE_GB AS zoneGb,
            T.ZONE_NAME AS zoneName,
            T.ZONE_TYPE AS zoneLevel,
        </trim>
        FROM TS_ZONE T
        ORDER BY T.ZONE_GB,T.IF_REVEAL DESC
    </select>

    <select id="findTsZoneList" resultType="com.chis.modules.sys.entity.TsZone">
        SELECT
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        FROM TS_ZONE T
        WHERE T.ZONE_GB LIKE #{zoneGb}
        <if test="zoneTypeMin != null">
            AND T.ZONE_TYPE &gt;= #{zoneTypeMin}
        </if>
        <if test="zoneTypeMax != null">
            AND T.ZONE_TYPE &lt;= #{zoneTypeMax}
        </if>
        <if test="ifReveal != null">
            AND T.IF_REVEAL = #{ifReveal}
        </if>
        ORDER BY T.ZONE_GB, T.IF_REVEAL DESC
    </select>


</mapper>

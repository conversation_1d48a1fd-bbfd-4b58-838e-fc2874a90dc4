<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.modules.sys.mapper.TsSystemParamMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="TsSystemParam">
        <result column="RID" property="rid" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_MANID" property="createManid" />
        <result column="MODIFY_DATE" property="modifyDate" />
        <result column="MODIFY_MANID" property="modifyManid" />
        <result column="PARAM_TYPE" property="paramType" />
        <result column="PARAM_NAME" property="paramName" />
        <result column="PARAM_VALUE" property="paramValue" />
        <result column="PARAM_RMK" property="paramRmk" />
        <result column="IF_REVEAL" property="ifReveal" />
        <result column="SPLSHT" property="splsht" />
        <result column="DATA_TYPE" property="dataType" />
        <result column="DICT_TYPE" property="dictType" />
        <result column="DICT_VALUE" property="dictValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        t.RID,t.CREATE_DATE,t.CREATE_MANID,t.MODIFY_DATE,t.MODIFY_MANID,
        t.PARAM_TYPE,t.PARAM_NAME,t.PARAM_VALUE,t.PARAM_RMK,t.IF_REVEAL,t.SPLSHT,t.DATA_TYPE,t.DICT_TYPE,t.DICT_VALUE,
    </sql>


    <!-- 通用方法列：-->
    <!--mAlias：主表别名，删除操作时为空字符串，否则为“t.” -->
    <sql id="BaseFieldList">
        <if test="${joiner}rid != null">
            and ${mAlias}RID = #{${joiner}rid}
        </if>
        <if test="${joiner}createDate != null">
            and ${mAlias}CREATE_DATE = #{${joiner}createDate}
        </if>
        <if test="${joiner}createManid != null">
            and ${mAlias}CREATE_MANID = #{${joiner}createManid}
        </if>
        <if test="${joiner}modifyDate != null">
            and ${mAlias}MODIFY_DATE = #{${joiner}modifyDate}
        </if>
        <if test="${joiner}modifyManid != null">
            and ${mAlias}MODIFY_MANID = #{${joiner}modifyManid}
        </if>
        <if test="${joiner}paramType != null and ${joiner}paramType != ''">
            and ${mAlias}PARAM_TYPE = #{${joiner}paramType}
        </if>
        <if test="${joiner}paramName != null and ${joiner}paramName != ''">
            and ${mAlias}PARAM_NAME = #{${joiner}paramName}
        </if>
        <if test="${joiner}paramValue != null and ${joiner}paramValue != ''">
            and ${mAlias}PARAM_VALUE = #{${joiner}paramValue}
        </if>
        <if test="${joiner}paramRmk != null and ${joiner}paramRmk != ''">
            and ${mAlias}PARAM_RMK = #{${joiner}paramRmk}
        </if>
        <if test="${joiner}ifReveal != null and ${joiner}ifReveal != ''">
            and ${mAlias}IF_REVEAL = #{${joiner}ifReveal}
        </if>
        <if test="${joiner}splsht != null and ${joiner}splsht != ''">
            and ${mAlias}SPLSHT = #{${joiner}splsht}
        </if>
        <if test="${joiner}dataType != null and ${joiner}dataType != ''">
            and ${mAlias}DATA_TYPE = #{${joiner}dataType}
        </if>
        <if test="${joiner}dictType != null and ${joiner}dictType != ''">
            and ${mAlias}DICT_TYPE = #{${joiner}dictType}
        </if>
        <if test="${joiner}dictValue != null and ${joiner}dictValue != ''">
            and ${mAlias}DICT_VALUE = #{${joiner}dictValue}
        </if>
    </sql>

    <!-- 更新全部字段列-->
    <!-- 单个更新：joiner 为空字符 -->
    <!-- 批量更新：joiner 为“item.” -->
    <sql id="BaseUpdateFullFieldList">
            t.MODIFY_DATE = #{${joiner}modifyDate},
        <choose>
            <when test="${joiner}modifyManid != null and ${joiner}modifyManid != ''">
                t.MODIFY_MANID = #{${joiner}modifyManid},
            </when>
            <otherwise>
                t.MODIFY_MANID = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}paramType != null and ${joiner}paramType != ''">
                t.PARAM_TYPE = #{${joiner}paramType},
            </when>
            <otherwise>
                t.PARAM_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}paramName != null and ${joiner}paramName != ''">
                t.PARAM_NAME = #{${joiner}paramName},
            </when>
            <otherwise>
                t.PARAM_NAME = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}paramValue != null and ${joiner}paramValue != ''">
                t.PARAM_VALUE = #{${joiner}paramValue},
            </when>
            <otherwise>
                t.PARAM_VALUE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}paramRmk != null and ${joiner}paramRmk != ''">
                t.PARAM_RMK = #{${joiner}paramRmk},
            </when>
            <otherwise>
                t.PARAM_RMK = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}ifReveal != null and ${joiner}ifReveal != ''">
                t.IF_REVEAL = #{${joiner}ifReveal},
            </when>
            <otherwise>
                t.IF_REVEAL = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}splsht != null and ${joiner}splsht != ''">
                t.SPLSHT = #{${joiner}splsht},
            </when>
            <otherwise>
                t.SPLSHT = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dataType != null and ${joiner}dataType != ''">
                t.DATA_TYPE = #{${joiner}dataType},
            </when>
            <otherwise>
                t.DATA_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dictType != null and ${joiner}dictType != ''">
                t.DICT_TYPE = #{${joiner}dictType},
            </when>
            <otherwise>
                t.DICT_TYPE = null,
            </otherwise>
        </choose>
        <choose>
            <when test="${joiner}dictValue != null and ${joiner}dictValue != ''">
                t.DICT_VALUE = #{${joiner}dictValue},
            </when>
            <otherwise>
                t.DICT_VALUE = null,
            </otherwise>
        </choose>
    </sql>


<!-- ========================自动生成的公共方法====================================== -->

    <!-- 列表查询：用于分页 -->
    <select id="pageList" resultMap="BaseResultMap">
    	SELECT * FROM (
		SELECT ZWX.*, ROWNUM AS RN FROM (
        select
        <trim suffixOverrides=",">
            <include refid="BaseColumnList"/>
        </trim>
        from  TS_SYSTEM_PARAM t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value="property."/>
            </include>
        </where>
        ) ZWX 
		) WHERE 1=1 
		<if test="first != null and pageSize != null">
			AND RN BETWEEN #{first} AND #{pageSize}
		</if>
    </select>


    <!-- 单个查询 -->
    <select id="selectByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_SYSTEM_PARAM t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <!-- 批量查询 -->
    <select id="selectListByEntity" resultMap="BaseResultMap">
        select
        <trim suffixOverrides=",">
           <include refid="BaseColumnList"/>
        </trim>
        from  TS_SYSTEM_PARAM t
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value="t."/>
                <property name="joiner" value=""/>
            </include>
        </where>
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into TS_SYSTEM_PARAM
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RID,
            CREATE_DATE,
            CREATE_MANID,
            MODIFY_DATE,
            MODIFY_MANID,
            PARAM_TYPE,
            PARAM_NAME,
            PARAM_VALUE,
            PARAM_RMK,
            IF_REVEAL,
            SPLSHT,
            DATA_TYPE,
            DICT_TYPE,
            DICT_VALUE,
        </trim>
        <foreach collection="list" item="item" index="index"
                 separator=" UNION ALL " open="SELECT TS_SYSTEM_PARAM_SEQ.Nextval, A.* FROM ("
                 close=") A">
            <trim suffixOverrides=",">
                SELECT
                    <choose>
                        <when test="item.createDate != null">
                            #{item.createDate} AS C1,
                        </when>
                        <otherwise>
                            NULL AS C1,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.createManid != null">
                            #{item.createManid} AS C2,
                        </when>
                        <otherwise>
                            NULL AS C2,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyDate != null">
                            #{item.modifyDate} AS C3,
                        </when>
                        <otherwise>
                            NULL AS C3,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.modifyManid != null">
                            #{item.modifyManid} AS C4,
                        </when>
                        <otherwise>
                            NULL AS C4,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.paramType != null">
                            #{item.paramType} AS C5,
                        </when>
                        <otherwise>
                            NULL AS C5,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.paramName != null">
                            #{item.paramName} AS C6,
                        </when>
                        <otherwise>
                            NULL AS C6,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.paramValue != null">
                            #{item.paramValue} AS C7,
                        </when>
                        <otherwise>
                            NULL AS C7,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.paramRmk != null">
                            #{item.paramRmk} AS C8,
                        </when>
                        <otherwise>
                            NULL AS C8,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.ifReveal != null">
                            #{item.ifReveal} AS C9,
                        </when>
                        <otherwise>
                            NULL AS C9,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.splsht != null">
                            #{item.splsht} AS C10,
                        </when>
                        <otherwise>
                            NULL AS C10,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.dataType != null">
                            #{item.dataType} AS C11,
                        </when>
                        <otherwise>
                            NULL AS C11,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.dictType != null">
                            #{item.dictType} AS C12,
                        </when>
                        <otherwise>
                            NULL AS C12,
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="item.dictValue != null">
                            #{item.dictValue} AS C13,
                        </when>
                        <otherwise>
                            NULL AS C13,
                        </otherwise>
                    </choose>
			</trim>
			FROM DUAL
	    </foreach>
	</insert>

    <!-- 单个更新：更新所有字段 -->
    <update id="updateFullById" parameterType="TsSystemParam" >
        update TS_SYSTEM_PARAM t
        <set>
            <include refid="BaseUpdateFullFieldList">
                <property name="joiner" value=""/>
            </include>
        </set>
        where t.rid = #{rid}
    </update>

    <!-- 批量更新：更新所有字段 -->
    <update id="updateFullBatchById" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update TS_SYSTEM_PARAM t
            <set>
                <include refid="BaseUpdateFullFieldList">
                    <property name="joiner" value="item."/>
                </include>
            </set>
            where t.rid = #{item.rid}
        </foreach>
    </update>

    <!-- 删除：根据对象赋值字段进行删除 -->
    <delete id="removeByEntity" parameterType="TsSystemParam">
        delete from TS_SYSTEM_PARAM
        <where>
            <include refid="BaseFieldList">
                <property name="mAlias" value=""/>
            </include>
        </where>
    </delete>

<!-- ========================自定义方法====================================== -->




</mapper>

heth-timer:
  sche-cron:
    #调用陕西尘肺康复管理系统接口定时规则(sxcfCron1: 定期重置所有状态为1、2的数据为0; sxcfCron2: 同步康复信息)(其他平台配置不启用 "-" )
    # "0 0 2 8/10 * ?" -每月从8日凌晨2点开始,每10天执行一次
    sxcfCron1: "-"
    # "0 0 1 * * ?" -每天凌晨1点执行一次
    sxcfCron2: "-"
  # 陕西尘肺康复管理系统接口相关参数
  jl:
    # 接口地址（正式地址：http://123.206.65.233:7000/clientApi）
    apiRoot:
    url:
      # 获取token接口
      getToken: /auth
      # 获取患者档案
      getPatient: /sxcf/getPatient
      # 获取患者评估处方
      getPatientPd: /sxcf/getPatientPd
      # 获取患者康复处方
      getPatientKffa: /sxcf/getPatientKffa
      # 患者尘肺病康复记录
      getCfbRecoveryRecord: /sxcf/getCfbRecoveryRecord
    # 请求token需要的key与secret
    token:
      key: sxcf
      secret: wdfjqiw147owhs12
    # 加密
    encrypt:
      # AES密钥
      key: wdfjqiw147owhs12
      # 为true则不加密，正式配置 false
      debug: false
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--<property name="LOG_HOME" value="${catalina.base}/zwx-logs" />-->
    <!--
    定义日志文件的根目录
        property：用来定义变量值
        属性：name的值是变量的名称，value的值时变量定义的值。通过定义的值会被插入到logger上下文中。定义变量后，可以使“${}”来使用变量
        1）不指定路径在当前项目下生成文件
        2）可以指定完整的路径
     -->
    <property name="log.filepath" value="./zwx-logs" />
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n" />
    <property name="log.maxFileSize" value="10MB" />
    <property name="log.maxHistory" value="10" />

    <!-- 输出到控制台 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--
            日志输出格式
                %d表示日期时间，
                %thread表示线程名，
                %-4level：级别从左显示4个字符宽度，
                %logger{36}表示logger名字最长36字符，否则按照局点分割，
                %msg：日志消息，
                %n是换行符
            -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%-4level] - %msg \(%logger{36}:%line\) [%thread] %X{REQID}%n</pattern>
            <!-- 设置字符集 -->
            <charset>UTF-8</charset>
        </encoder>
        <!--此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <!--
    输出到文件
        时间滚动输出 level为 ERROR 日志
        滚动记录日志，先将日志记录到指纹文件，当符合某个条件时，将日志记录到其他文件
    -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>${log.filepath}/log_error.log</file>
        <encoder>
            <!--
           日志输出格式
               %d表示日期时间，
               %thread表示线程名，
               %-5level：级别从左显示5个字符宽度，
               %logger{50}表示logger名字最长50字符，否则按照局点分割，
               %msg：日志消息，
               %n是换行符
           -->
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset> <!-- 设置字符集 -->
        </encoder>
        <!--
        日志记录器的滚动策略，按日期，按大小记录
            当发生滚动时，决定RollingFileAppender的行为，涉及文件夹移动和重命名
            TimeBasedRollingPolicy：最常用的滚动策略，它根据时间来制定滚动策略，既负责滚动也负责触发滚动。
        -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--
            日志归档,设置每个月滚动
                滚动时产生的文件的存放位置及文件名称
                %d{yyyy-MM}：按月进行日志滚动
                %i：当文件大小超过maxFileSize时，按照i进行文件滚动
            -->
            <fileNamePattern>${log.filepath}/error/log-error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- 活动文件的大小 -->
                <maxFileSize>${log.maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--
            只保存最近6个月的文件
                控制保留的归档文件的最大数量，超出数量就删除旧文件。假设设置每月滚动，
                且maxHistory是6，则只保存最近6个月的文件，删除之前的旧文件。
                注意，删除旧文件是，那些为了归档而创建的目录也会被删除。
            -->
            <maxHistory>${log.maxHistory}</maxHistory>
        </rollingPolicy>
        <!-- 此日志文件只记录error级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--
    输出到文件
        时间滚动输出 level为 DEBUG 日志
    -->
    <appender name="DEBUG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.filepath}/log_debug.log</file>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.filepath}/debug/log-debug-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${log.maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${log.maxHistory}</maxHistory>
        </rollingPolicy>
        <!-- 此日志文件只记录debug级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--
    输出到文件
        时间滚动输出 level为 INFO 日志
    -->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.filepath}/log_info.log</file>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset> <!-- 设置字符集 -->
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.filepath}/info/log-info-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${log.maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${log.maxHistory}</maxHistory>
        </rollingPolicy>
        <!-- 此日志文件只记录info级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="IMPORTANT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.filepath}/log_important.log</file>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset> <!-- 设置字符集 -->
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.filepath}/important/log-important-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${log.maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${log.maxHistory}</maxHistory>
        </rollingPolicy>
        <!-- 此日志文件只记录info级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <!--
    root节点是必选节点，用来指定最基础的日志输出级别，只有一个level属性
       level:用来设置打印级别，大小写无关
       LEVEL 从高到底： OFF、FATAL、ERROR、WARN、INFO、DEBUG、TRACE、 ALL
       可以包含零个或多个元素，标识这个appender将会添加到这个logger。
       开发调试时，可把级别调成DEBUG,并把控制台输出STDOUT级别调成DEBUG
    -->
    <root level="INFO">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="ERROR_FILE" />
        <appender-ref ref="DEBUG_FILE" />
        <appender-ref ref="INFO_FILE" />
    </root>

    <!--
        <logger>用来设置某一个包或者具体的某一个类的日志打印级别、
        以及指定<appender>。<logger>仅有一个name属性，
        一个可选的level和一个可选的addtivity属性。
        name:用来指定受此logger约束的某一个包或者具体的某一个类。
        level:用来设置打印级别，大小写无关
        LEVEL 从高到底： OFF、FATAL、ERROR、WARN、INFO、DEBUG、TRACE、 ALL
        addtivity:是否向上级logger传递打印信息。默认是true。
    -->
    <logger name="important" additivity="false" level="INFO">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="IMPORTANT_FILE" />
    </logger>
</configuration>
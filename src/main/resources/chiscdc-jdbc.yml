spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: oracle.jdbc.OracleDriver
    druid:
      #12c url: *******************************************
      #11g url: ****************************************
      url: ****************************************
      username: cqzw
      password: cybercdc
      initial-size: 1
      #数据库连接池中的最大连接数，用于限制程序获取数据库连接的数量
      max-active: 100
      min-idle: 1
      #获取数据库连接等待超时的时间，设置60000，即60秒
      max-wait: 60000
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
          enabled: true
          url-pattern: /druid/*
          #login-username: admin
          #login-password: admin
      filter:
          stat:
              #是否开启慢查询日志输出
              log-slow-sql: true
              #判断是否慢查询的时间 单位是毫秒 超过这个时间就会输出慢查询日志
              slow-sql-millis: 1000
              merge-sql: false
          wall:
              config:
                  multi-statement-allow: true
      
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.chis.modules.**.entity
#  typeEnumsPackage: com.baomidou.springboot.property.enums
  global-config:
    db-config:
      id-type: INPUT
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: NOT_EMPTY
      #数据库大写下划线转换
      capital-mode: false
  configuration:
      #配置JdbcTypeForNull, oracle数据库必须配置
      jdbc-type-for-null: 'null'
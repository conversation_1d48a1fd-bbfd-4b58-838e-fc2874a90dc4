package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>类描述：用人单位预警-职业卫生技术服务信息报送卡信息查询返回对象 </p>
 * pw 2023/9/28
 **/
@Data
public class CrptWarnOcchethCardInfoVo implements Serializable {
    private static final long serialVersionUID = 8296332140300663203L;
    private Integer crptId;
    /** 企业职业风险分类 1 严重 3 一般 */
    private String codeDesc;
    /** 最新出具技术报告日期 */
    private Date rptDate;
}

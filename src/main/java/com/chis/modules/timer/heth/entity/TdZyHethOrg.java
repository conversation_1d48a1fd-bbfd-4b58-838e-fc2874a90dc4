package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdZyHethOrg
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZY_HETH_ORG")
@KeySequence(value = "TD_ZY_HETH_ORG_SEQ",clazz = Integer.class)
public class TdZyHethOrg extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZyUnitbasicinfo fkByMainId;

    @TableField("UNIT_NAME")
    private String unitName;

    @TableField("REPORT_NO")
    private String reportNo;

    @TableField("CREDIT_CODE")
    private String creditCode;

    @TableField("CONTACT_INFO")
    private String contactInfo;


    public TdZyHethOrg(Integer rid) {
        super(rid);
    }


}

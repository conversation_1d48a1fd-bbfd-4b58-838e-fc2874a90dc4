package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人, 2022-09-02,TdZwcfKfPg
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWCF_KF_PG")
public class TdZwcfKfPg extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID", el = "fkByMainId.rid")
    private TdZwcfPatientInfo fkByMainId;

    @TableField("PG_TIME")
    private Date pgTime;

    @TableField("PG_PDF_PATH")
    private String pgPdfPath;


    public TdZwcfKfPg(Integer rid) {
        super(rid);
    }


}

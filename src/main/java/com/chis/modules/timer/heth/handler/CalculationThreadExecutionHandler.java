package com.chis.modules.timer.heth.handler;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Description: 线程池的拒绝策略
 *
 * @ClassAuthor pw,2021年12月18日,CalculationThreadExecutionHandler
 */
@Slf4j
public class CalculationThreadExecutionHandler implements RejectedExecutionHandler {
    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        if(null != executor){
            log.warn("线程池拒绝策略被调用，当前线程池线程数：{} 执行任务线程大致数量： {} 建议增加最大线程数或者队列容量",
                    executor.getPoolSize(), executor.getActiveCount());
        }else{
            log.warn("线程池拒绝策略被调用，ThreadPoolExecutor 空");
        }
        if(!executor.isShutdown()){
            r.run();// 让调用线程执行
        }
    }
}

package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;


/**
 * @Description: 异步导出用于传递体检数据上传项目规范体检项目rid与对应的危害因素rid
 *
 * @MethodAuthor pw,2021年05月12日
 */
@Data
public class BadrsnItemVo implements Serializable {
    private static final long serialVersionUID = -7856858254094921442L;
    private Integer stdId;//体检数据上传控制有害因素rid
    private Integer deterWay;//项目判定方式  1：所有条件都符合 2：任一条件符合
    private Integer badrsnId;//危害因素rid
    private Integer itemId;//体检项目rid
    private Integer badRsnItemRid;//项目规范rid
    private Integer onguardStateId;//在岗状态rid
}

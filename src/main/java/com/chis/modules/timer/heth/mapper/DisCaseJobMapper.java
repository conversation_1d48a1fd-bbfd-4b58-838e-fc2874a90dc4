package com.chis.modules.timer.heth.mapper;

import com.chis.modules.timer.heth.pojo.bo.warn.DisCaseBusDataBO;
import com.chis.modules.timer.heth.pojo.bo.warn.DisCaseNotInTime;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface DisCaseJobMapper {
    List<DisCaseBusDataBO> findOccDisCaseDataList(@Param("zoneGbMax") String zoneGbMax,
                                                  @Param("beginDate") Date beginDate);
    /**
     * <p>方法描述： 查询疑似职业病例数数据 </p>
     * pw 2023/11/11
     **/
    List<DisCaseBusDataBO> findSuspectedOccDisCaseDataList(@Param("beginDate") Date beginDate,@Param("warnLevel")  Integer warnLevel,
                                                           @Param("zoneGb") String zoneGb);

    List<DisCaseNotInTime> findNotTimeBhkDatas(@Param("limitDate") Date limitDate,@Param("beginDate")  Date beginDate,@Param("zoneCode") String zoneCode,@Param("warnUnit")  Integer warnUnit);

    List<DisCaseNotInTime> findSupoccdiseList(@Param("bhkRids") List<Integer> bhkRids);

    List<DisCaseNotInTime> findYszybRptList(@Param("bhkRids")  List<Integer> bhkRids);

    /**
     * <p>方法描述：查询职业病报告不及时数据 </p>
     * pw 2023/11/12
     **/
    List<DisCaseNotInTime> findDisCaseNotInTimeDataList(@Param("limitDate") Date limitDate,@Param("beginDate")  Date beginDate
            ,@Param("warnLevel")  Integer warnLevel,@Param("warnDays") Integer warnDays, @Param("zoneGb") String zoneGb);
}

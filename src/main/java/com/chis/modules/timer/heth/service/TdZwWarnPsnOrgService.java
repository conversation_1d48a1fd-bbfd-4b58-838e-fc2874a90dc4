package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwWarnPsnOrg;
import com.chis.modules.timer.heth.mapper.TdZwWarnPsnOrgMapper;
import org.springframework.stereotype.Service;

/**
 * <p> 服务实现类：  </p>
 *
 * <AUTHOR> 2024-07-12,TdZwWarnPsnOrgService
 */
@Service
public class TdZwWarnPsnOrgService extends ZwxBaseServiceImpl<TdZwWarnPsnOrgMapper, TdZwWarnPsnOrg> {

    public void deleteByOrgWarnMainId(Integer orgWarnId) {
        baseMapper.deleteByOrgWarnMainId(orgWarnId);
    }
}

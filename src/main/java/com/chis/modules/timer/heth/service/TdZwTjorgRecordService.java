package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwTjorgRecord;
import com.chis.modules.timer.heth.mapper.TdZwTjorgRecordMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-11-13,TdZwTjorgRecordService
 */
@Service
public class TdZwTjorgRecordService extends ZwxBaseServiceImpl<TdZwTjorgRecordMapper, TdZwTjorgRecord> {

    public List<TdZwTjorgRecord> selectTdZwTjorgRecord(Integer mainId){
        return baseMapper.selectTdZwTjorgRecord(mainId);
    }
}

package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TbZwWarnModel;
import com.chis.modules.timer.heth.mapper.TbZwWarnModelMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人, 2023-11-10,TbZwWarnModelService
 */
@Service
public class TbZwWarnModelService extends ZwxBaseServiceImpl<TbZwWarnModelMapper, TbZwWarnModel> {
    public List<TbZwWarnModel> selectListByWarnType(Integer warnType) {
        return this.baseMapper.selectListByWarnType(warnType);
    }
}

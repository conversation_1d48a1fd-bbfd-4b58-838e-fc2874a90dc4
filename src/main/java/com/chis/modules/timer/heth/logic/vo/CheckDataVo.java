package com.chis.modules.timer.heth.logic.vo;

import com.chis.modules.timer.heth.entity.TdTjBhk;
import com.chis.modules.timer.heth.pojo.BhkCheckPojo;
import lombok.Data;
import com.alibaba.excel.annotation.ExcelProperty;

@Data
public class CheckDataVo {
    @ExcelProperty("姓名")
    private String name;
    
    @ExcelProperty("体检编号")
    private String checkNo;
    
    @ExcelProperty("体检机构名称")
    private String checkOrg;
    
    @ExcelProperty("退回原因")
    private String rejectReason;
    
    private String errorMsg;

    private TdTjBhk bhk;

    private BhkCheckPojo bhkCheckPojo;

} 
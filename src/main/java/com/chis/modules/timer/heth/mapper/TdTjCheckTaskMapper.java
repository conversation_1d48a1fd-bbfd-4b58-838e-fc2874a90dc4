package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdTjCheckTask;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2024-10-14,TdTjCheckTaskMapper
 */
@Repository
public interface TdTjCheckTaskMapper extends ZwxBaseMapper<TdTjCheckTask> {

    List<TdTjCheckTask> queryTasks();

    void updateTaskByRid(@Param("totalNum") BigDecimal totalNum, @Param("statue") Integer statue,
                         @Param("errorMsg") String errorMsg, @Param("psnId") Integer psnId, @Param("rid") Integer rid);

    /**
     * 查询待处理的批量退回任务
     * @return 待处理任务列表
     */
    List<TdTjCheckTask> selectPendingTasks();
    /**
     * 更新任务信息
     * @param task 待更新的任务对象
     * @return 更新结果
     */
    int updateTask(@Param("task") TdTjCheckTask task);
}

package com.chis.modules.timer.heth.mapper;


import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZwyjOtrBhk;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2020-11-12,TdZwyjOtrBhkMapper
 */
@Repository
public interface TdZwyjOtrBhkMapper extends ZwxBaseMapper<TdZwyjOtrBhk> {


    /**
     * <p>
     *     方法描述：查询不在超范围服务预警统计日志中的超范围服务体检记录列表
     *     数据返回条数有限制 若未传递数据长度 则默认一百条
     * </p>
     *
     * @MethodAuthor pw,2020年11月19日,
     */
    List<TdZwyjOtrBhk> findTdZwyjOtrBhkExcludeSmyRCD(@Param("dataSize") Integer dataSize);
}

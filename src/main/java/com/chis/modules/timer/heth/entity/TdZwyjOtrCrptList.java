package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-19,TdZwyjOtrCrptList
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_OTR_CRPT_LIST")
@KeySequence(value = "TD_ZWYJ_OTR_CRPT_LIST_SEQ",clazz = Integer.class)
public class TdZwyjOtrCrptList extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwyjOtrWarn fkByMainId;

    @TableField(value = "CRPT_ID" , el = "fkByCrptId.rid")
    private TbTjCrpt fkByCrptId;

    @TableField(value = "CRPT_ZONE_ID" , el = "fkByCrptZoneId.rid")
    private TsZone fkByCrptZoneId;

    @TableField("CRPT_NAME")
    private String crptName;

    @TableField("CREDIT_CODE")
    private String creditCode;

    @TableField("BEGIN_BHK_DATE")
    private Date beginBhkDate;

    @TableField("END_BHK_DATE")
    private Date endBhkDate;

    @TableField("OTR_PSNS")
    private String otrPsns;


    public TdZwyjOtrCrptList(Integer rid) {
        super(rid);
    }


}

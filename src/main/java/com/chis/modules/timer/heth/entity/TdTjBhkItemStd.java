package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-05-12,TdTjBhkItemStd
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_TJ_BHK_ITEM_STD")
@KeySequence(value = "TD_TJ_BHK_ITEM_STD_SEQ",clazz = Integer.class)
public class TdTjBhkItemStd extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "BHK_ID" , el = "fkByBhkId.rid")
    private TdTjBhk fkByBhkId;

    @TableField(value = "ITEM_STD_ID" , el = "fkByItemStdId.rid")
    private TdZwBadrsnItem fkByItemStdId;

    @TableField("NOSTD_FLAG")
    private String nostdFlag;


    public TdTjBhkItemStd(Integer rid) {
        super(rid);
    }


}

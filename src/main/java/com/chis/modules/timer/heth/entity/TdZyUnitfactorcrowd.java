package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdZyUnitfactorcrowd
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZY_UNITFACTORCROWD")
@KeySequence(value = "TD_ZY_UNITFACTORCROWD_SEQ",clazz = Integer.class)
public class TdZyUnitfactorcrowd extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZyUnitbasicinfo fkByMainId;

    @TableField("IFHF_DUST")
    private Integer ifhfDust;

    @TableField("HF_DUST_PEOPLES")
    private Integer hfDustPeoples;

    @TableField("HF_DUST_SILICON_PEOPLES")
    private Integer hfDustSiliconPeoples;

    @TableField("HF_DUST_COAL_PEOPLES")
    private Integer hfDustCoalPeoples;

    @TableField("HF_DUST_ASBESTOS_NUM")
    private Integer hfDustAsbestosNum;

    @TableField("IFHF_CHEMISTRY")
    private Integer ifhfChemistry;

    @TableField("HF_CHEMISTRY_PEOPLES")
    private Integer hfChemistryPeoples;

    @TableField("HF_CHEMISTRY_LEAD_PEOPLES")
    private Integer hfChemistryLeadPeoples;

    @TableField("HF_CHEMISTRY_BENZENE_PEOPLES")
    private Integer hfChemistryBenzenePeoples;

    @TableField("IFHF_PHYSICS")
    private Integer ifhfPhysics;

    @TableField("HF_PHYSICS_PEOPLES")
    private Integer hfPhysicsPeoples;

    @TableField("HF_PHYSICS_NOISE_PEOPLES")
    private Integer hfPhysicsNoisePeoples;

    @TableField("IFHF_RADIOACTIVITY")
    private Integer ifhfRadioactivity;

    @TableField("HF_RADIOACTIVITY_PEOPLES")
    private Integer hfRadioactivityPeoples;

    @TableField("IFHF_BIOLOGY")
    private Integer ifhfBiology;

    @TableField("HF_BIOLOGY_PEOPLES")
    private Integer hfBiologyPeoples;

    @TableField("IFHF_OTHER")
    private Integer ifhfOther;

    @TableField("HF_OTHER_PEOPLES")
    private Integer hfOtherPeoples;


    public TdZyUnitfactorcrowd(Integer rid) {
        super(rid);
    }


}

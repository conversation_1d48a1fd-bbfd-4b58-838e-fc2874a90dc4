package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-05-13,TdZwBadrsnStd
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_BADRSN_STD")
public class TdZwBadrsnStd extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "BADRSN_ID" , el = "fkByBadrsnId.rid")
    private TsSimpleCode fkByBadrsnId;

    @TableField("STOP_TAG")
    private String stopTag;

    @TableField("ITEM_DESC")
    private String itemDesc;

    @TableField("DETER_WAY")
    private String deterWay;


    public TdZwBadrsnStd(Integer rid) {
        super(rid);
    }


}

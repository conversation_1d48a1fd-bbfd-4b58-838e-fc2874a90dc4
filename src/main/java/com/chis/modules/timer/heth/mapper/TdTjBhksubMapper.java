package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdTjBhksub;
import com.chis.modules.timer.heth.logic.ChestRstPO;
import com.chis.modules.timer.heth.logic.HearingRstPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdTjBhksubMapper
 */
@Repository
public interface TdTjBhksubMapper extends ZwxBaseMapper<TdTjBhksub> {


    /**
     * <p>
     *     方法描述：依据体检主表rid集合批量查询 未缺项 定量项目 且危急值项目维护中存在数据的体检子表集合
     * </p>
     *
     * @MethodAuthor pw,2020年11月23日,
     */
    List<TdTjBhksub> selectDangerValSubBhkList(@Param("bhkIdList")List<Integer> bhkIdList);


    /**
     * @Description: 依据体检主表rid集合 获取 体检子表集合
     *               注意体检主表rid集合不允许为空
     *
     * @MethodAuthor pw,2021年05月12日
     */
    List<TdTjBhksub> selectSubBhkListByBhkRidList(@Param("bhkIdList")List<Integer> bhkIdList);

    /**
     * <p>方法描述： 依据体检主表rid集合以及特定项目标记 获取 体检子表集合 </p>
     * @MethodAuthor： pw 2022/10/20
     **/
    List<TdTjBhksub> selectSubBhkListByItemTagAndBhkRidList(@Param("bhkIdList")List<Integer> bhkIdList,
                                                            @Param("itemTag")Integer itemTag);

    List<ChestRstPO> selectChestRstList(@Param("bhkStartDate") String bhkStartDate,
                                        @Param("bhkEndDate") String bhkEndDate,
                                        @Param("rptPrintStartDate") String rptPrintStartDate,
                                        @Param("rptPrintEndDate") String rptPrintEndDate,
                                        @Param("size") Integer size);

    List<HearingRstPO> selectHearingRstList(@Param("itemCodeList") List<Integer> itemCodeList,
                                            @Param("bhkStartDate") String bhkStartDate,
                                            @Param("bhkEndDate") String bhkEndDate,
                                            @Param("rptPrintStartDate") String rptPrintStartDate,
                                            @Param("rptPrintEndDate") String rptPrintEndDate,
                                            @Param("size") Integer size,
                                            @Param("useRgltagMode") boolean useRgltagMode);

    List<Integer> findItemRidListByCodeList(@Param("itemCodeList") List<String> itemCodeList);
}

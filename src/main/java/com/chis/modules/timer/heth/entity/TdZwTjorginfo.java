package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsUnit;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-13,TdZwTjorginfo
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_TJORGINFO")
public class TdZwTjorginfo extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "ORG_ID" , el = "fkByOrgId.rid")
    private TsUnit fkByOrgId;

    @TableField("ORG_NAME")
    private String orgName;

    @TableField("ORG_ADDR")
    private String orgAddr;

    @TableField("ORG_FZ")
    private String orgFz;

    @TableField("ORG_FZZW")
    private String orgFzzw;

    @TableField("LINK_MAN")
    private String linkMan;

    @TableField("LINK_MB")
    private String linkMb;

    @TableField("LINK_TEL")
    private String linkTel;

    @TableField("FAX")
    private String fax;

    @TableField("ZIPCODE")
    private String zipcode;

    @TableField("EMAIL")
    private String email;

    @TableField("CERT_NO")
    private String certNo;

    @TableField("FIRST_GETDAY")
    private Date firstGetday;

    @TableField("STATE")
    private String state;

    @TableField("CANCEL_STATE")
    private String cancelState;

    @TableField("CANCEL_DATE")
    private Date cancelDate;

    @TableField("VALID_DATE")
    private Date validDate;

    @TableField("CREDIT_CODE")
    private String creditCode;

    @TableField("OUT_WORK_POWER")
    private String outWorkPower;

    private Integer srvId;//资质服务机构Id

    private List<TdZwTjorgRecord> tdZwTjorgRecordList;

    public TdZwTjorginfo(Integer rid) {
        super(rid);
    }


}

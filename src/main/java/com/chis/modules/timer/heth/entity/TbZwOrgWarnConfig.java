package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * <AUTHOR> 2024-07-11,TbZwOrgWarnConfig
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TB_ZW_ORG_WARN_CONFIG")
public class TbZwOrgWarnConfig extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("BUS_TYPE")
    private String busType;

    @TableField("WARN_TYPE")
    private String warnType;

    @TableField(value = "ITEM_ID", el = "fkByItemId.rid")
    private TsSimpleCode fkByItemId;

    @TableField("WARN_TYPE_DESC")
    private String warnTypeDesc;

    @TableField("XH")
    private String xh;

    @TableField(value = "POST_ID", el = "fkByPostId.rid")
    private TsSimpleCode fkByPostId;

    @TableField(value = "INST_TYPE_ID", el = "fkByInstTypeId.rid")
    private TsSimpleCode fkByInstTypeId;

    @TableField("MIN_NUMS")
    private Integer minNums;

    @TableField("TRAIN_YEAR")
    private Integer trainYear;

    @TableField(value = "TITLE_ID", el = "fkByTitleId.rid")
    private TsSimpleCode fkByTitleId;

    @TableField("ORG_NUMS")
    private Integer orgNums;

    @TableField("IF_TEST_INST")
    private String ifTestInst;

    @TableField("IF_DEL")
    private String ifDel;

    private Integer warnTypeId;

    public TbZwOrgWarnConfig(Integer rid) {
        super(rid);
    }


}

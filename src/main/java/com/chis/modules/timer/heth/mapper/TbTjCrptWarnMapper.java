package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TbTjCrptWarn;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2023-09-27,TbTjCrptWarnMapper
 */
@Repository
public interface TbTjCrptWarnMapper extends ZwxBaseMapper<TbTjCrptWarn> {
    /**
     * <p>方法描述：通过企业rid获取最新的预警信息 </p>
     * pw 2023/9/28
     **/
    List<TbTjCrptWarn> selectLastestWarnByCrptIds(@Param("crptRidList")List<Integer> crptRidList);
}

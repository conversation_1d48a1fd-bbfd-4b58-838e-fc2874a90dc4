package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnCsionPdjl
 */
@Data
@Builder
@NoArgsConstructor
@Accessors(chain = true)
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_BSN_CSION_PDJL")
public class TdZwyjBsnCsionPdjl extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwyjBsnCsionItemSub fkByMainId;

    @TableField(value = "PD_CLUSION_ID" , el = "fkByPdClusionId.rid")
    private TdZdzybAnalyItmType fkByPdClusionId;


    public TdZwyjBsnCsionPdjl(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.mapper;

import com.chis.modules.timer.heth.logic.vo.ItemsVo;
import com.chis.modules.timer.heth.logic.vo.OcchethInfoDataVo;
import com.chis.modules.timer.heth.logic.vo.PsnNumVo;
import com.chis.modules.timer.heth.logic.vo.SrvorgInfoDataVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TechnicalServiceMapper {

    List<OcchethInfoDataVo> getOcchethInfoVo(@Param("type") Integer type,@Param("dataSize") Integer dataSize);

    List<ItemsVo> getOcchethItems(@Param("ridList") List<Integer> ridList);

    List<SrvorgInfoDataVo> getSrvorgInfoVo(@Param("type") Integer type,@Param("dataSize") Integer dataSize);

    List<ItemsVo> getSrvorgItems(@Param("ridList") List<Integer> ridList);

    List<PsnNumVo> getPsnNums(@Param("ridList") List<Integer> ridList);
}

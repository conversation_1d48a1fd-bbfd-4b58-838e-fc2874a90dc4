package com.chis.modules.timer.heth.logic.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>类描述： 职业/放射卫生技术服务信息报送服务地址传输类 </p>
 * @ClassAuthor： pw 2022/12/24
 **/
@Data
public class TdZwUploadAddressVo implements Serializable {
    private static final long serialVersionUID = 4656598864995028640L;
    @JSONField(serialize = false)
    private Integer rid;
    @JSONField(serialize = false)
    private Integer rcdRid;
    private String securityKey;
    private Integer reportId;
    private String ocode;
    private String assxbm;
}

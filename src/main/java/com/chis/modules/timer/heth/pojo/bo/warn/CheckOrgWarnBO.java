package com.chis.modules.timer.heth.pojo.bo.warn;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 检查机构预警-检查机构信息
 */
@Data
public class CheckOrgWarnBO {
    /**
     * 机构ID
     */
    private Integer rid;
    /**
     * 是否首次备案
     */
    private Integer ifFilingFirst;
    /**
     * 备案有效期（年）
     */
    private Integer filingYear;
    /**
     * 备案日期
     */
    private Date firstGetday;
    /**
     * 备案类别ID
     */
    private List<Integer> itemList;
    /**
     * 预警主表ID
     */
    private Integer warnId;
    /**
     * 预警日期
     */
    private Date warnDate;
}

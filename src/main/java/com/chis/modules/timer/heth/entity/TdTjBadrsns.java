package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.TsSimpleCode;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigInteger;
import java.util.Date;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdTjBadrsns
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("TD_TJ_BADRSNS")
public class TdTjBadrsns {

    private static final long serialVersionUID = 1L;

    /**
     * 主键：默认自增
     */
    @TableId
    private BigInteger rid;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createManid;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    @TableField(value = "BHK_ID" , el = "fkByBhkId.rid")
    private TdTjBhk fkByBhkId;

    @TableField(value = "BADRSN_ID" , el = "fkByBadrsnId.rid")
    private TsSimpleCode fkByBadrsnId;

    @TableField(value = "EXAM_CONCLUSION_ID" , el = "fkByExamConclusionId.rid")
    private TsSimpleCode fkByExamConclusionId;

    @TableField("QTJB_NAME")
    private String qtjbName;

    //危害因素扩展字段1
    private String badExtends2;

    //危害因素
    private String badName;


    public TdTjBadrsns(BigInteger rid) {
        this.rid=rid;
    }


}

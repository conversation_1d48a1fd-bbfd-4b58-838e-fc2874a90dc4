package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZwBadrsnItem;
import com.chis.modules.timer.heth.logic.vo.BadrsnItemVo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2021-05-12,TdZwBadrsnItemMapper
 */
@Repository
public interface TdZwBadrsnItemMapper extends ZwxBaseMapper<TdZwBadrsnItem> {

    /**
     * @Description:  获取体检数据上传项目规范集合
     * 注意 获取的数据排除了TD_ZW_BADRSN_STD中STOP_TAG为1的数据
     *
     * @MethodAuthor pw,2021年05月12日
     */
    List<BadrsnItemVo> findPartialTdZwBadrsnItemListByBhkRids();
}

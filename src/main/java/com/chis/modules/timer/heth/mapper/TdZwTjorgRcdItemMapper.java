package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZwTjorgRcdItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2020-11-13,TdZwTjorgRcdItemMapper
 */
@Repository
public interface TdZwTjorgRcdItemMapper extends ZwxBaseMapper<TdZwTjorgRcdItem> {
    public List<TdZwTjorgRcdItem> selectTdZwTjorgRcdItem(@Param("mainId") Integer mainId);
}

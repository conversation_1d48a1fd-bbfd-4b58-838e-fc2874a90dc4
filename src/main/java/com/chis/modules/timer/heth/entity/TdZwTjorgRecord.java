package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-13,TdZwTjorgRecord
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_TJORG_RECORD")
public class TdZwTjorgRecord extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwTjorginfo fkByMainId;

    @TableField(value = "ZONE_ID" , el = "fkByZoneId.rid")
    private TsZone fkByZoneId;

    @TableField("RCD_NO")
    private String rcdNo;

    @TableField("CERT_DATE")
    private Date certDate;

    @TableField("STATE_MARK")
    private String stateMark;

    @TableField("LOGOUT_DATE")
    private Date logoutDate;

    private String zoneGb;

    private List<TdZwTjorgRcdItem> tdZwTjorgRcdItemList;

    public TdZwTjorgRecord(Integer rid) {
        super(rid);
    }


}

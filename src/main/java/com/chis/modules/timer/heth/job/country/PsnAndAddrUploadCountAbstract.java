package com.chis.modules.timer.heth.job.country;

import com.alibaba.fastjson.JSON;
import com.chis.comm.utils.DesEncryptUtil;
import com.chis.comm.utils.HttpRequestUtil;
import com.chis.comm.utils.ObjectCopyUtil;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsContraSub;
import com.chis.modules.sys.service.TsContraSubService;
import com.chis.modules.timer.heth.entity.TdZywsCardRcd;
import com.chis.modules.timer.heth.enums.CardUploadCountryBusTypeEnum;
import com.chis.modules.timer.heth.logic.vo.CardResponsAddVo;
import com.chis.modules.timer.heth.logic.vo.TdZwOcchethCardPsnVo;
import com.chis.modules.timer.heth.logic.vo.TdZwSrvorgCardPsnVo;
import com.chis.modules.timer.heth.logic.vo.TdZwUploadAddressVo;
import com.chis.modules.timer.heth.service.TdZywsCardRcdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>类描述： 职业/放射卫生技术服务信息报送参与人员以及服务地址-基类 </p>
 * @ClassAuthor： pw 2022/12/23
 **/
@Slf4j
@Component
public abstract class PsnAndAddrUploadCountAbstract {
    @Value("${heth-timer.country.security-key}")
    private String securityKey;

    @Value("${heth-timer.country.dataSize}")
    private Integer dataSize;

    @Value("${heth-timer.country.encrypt-key}")
    private String encryptKey;

    @Value("${heth-timer.country.occhethPsnAdd_url}")
    private String occhethPsnAddUrl;

    @Value("${heth-timer.country.srvorgPsnAdd_url}")
    private String srvorgPsnAddUrl;

    @Value("${heth-timer.country.occhethAddrAdd_url}")
    private String occhethAddrAddUrl;

    @Value("${heth-timer.country.srvorgAddrAdd_url}")
    private String srvorgAddrAddUrl;

    @Resource
    private TdZywsCardRcdService cardRcdService;
    @Resource
    private TsContraSubService tsContraSubService;

    /** 业务类型 职业卫生报送卡-参与人员13 放射卫生报送卡-参与人员23 */
    protected Integer busPsnType;
    /** 业务类型 职业卫生报送卡-服务地址14 放射卫生报送卡-服务地址24 */
    protected Integer busAddrType;

    /**
     * <p>方法描述：参与人员上传 </p>
     * @MethodAuthor： pw 2022/12/23
     **/
    public void uploadPsnData(){
        this.fillPsnBusType();
        boolean flag = null == this.busPsnType || (CardUploadCountryBusTypeEnum.OCCPSN.getCode().compareTo(this.busPsnType) != 0 &&
                CardUploadCountryBusTypeEnum.SRVORGPSN.getCode().compareTo(this.busPsnType) != 0);
        if(flag){
            return;
        }
        //失败状态的 改成待上传
        this.cardRcdService.updateCardRcdByType(this.busPsnType, "2");
        List<TdZwOcchethCardPsnVo> dataList = this.queryPsnDataList(this.dataSize);
        while(!CollectionUtils.isEmpty(dataList)){
            this.executePsnUploadData(dataList);
            dataList = this.queryPsnDataList(this.dataSize);
        }
    }

    /**
     * <p>方法描述： 服务地址上传 </p>
     * @MethodAuthor： pw 2022/12/24
     **/
    public void uploadAddrData(){
        this.fillAddrBusType();
        boolean flag = null == this.busAddrType || (CardUploadCountryBusTypeEnum.OCCADDR.getCode().compareTo(this.busAddrType) != 0 &&
                CardUploadCountryBusTypeEnum.SRVORGADDR.getCode().compareTo(this.busAddrType) != 0);
        if(flag){
            return;
        }
        //失败状态的 改成待上传
        this.cardRcdService.updateCardRcdByType(this.busAddrType, "2");
        List<TdZwUploadAddressVo> dataList = this.queryAddrDataList(this.dataSize);
        while(!CollectionUtils.isEmpty(dataList)){
            this.executeAddrUploadData(dataList);
            dataList = this.queryAddrDataList(this.dataSize);
        }
    }

    /**
     * <p>方法描述： 执行上传服务地址到国家 </p>
     * @MethodAuthor： pw 2022/12/24
     **/
    public void executeAddrUploadData(List<TdZwUploadAddressVo> dataList){
        for(TdZwUploadAddressVo addressVo : dataList){
            addressVo.setSecurityKey(this.securityKey);
            TdZywsCardRcd cardRcd=new TdZywsCardRcd();
            cardRcd.setBusType(this.busAddrType);
            cardRcd.setBusId(addressVo.getRid());
            cardRcd.setUploadDate(new Date());
            //默认失败
            cardRcd.setState(2);
            cardRcd.setCreateDate(new Date());
            cardRcd.setCreateManid(1);
            String errMsg = null;
            String assxbm = addressVo.getAssxbm();
            if(StringUtils.isBlank(assxbm)){
                errMsg = "行政区划码不允许为空";
            }else{
                assxbm = this.contraSubExchange("15","1", assxbm);
                if(StringUtils.isBlank(assxbm)){
                    errMsg = "行政区划码："+addressVo.getAssxbm()+"对照不上";
                }else{
                    addressVo.setAssxbm(assxbm);
                }
            }

            if(StringUtils.isBlank(errMsg)){
                try{
                    // 加密后传输
                    Map<String, String> addMap = new HashMap<>();
                    String apiData = JSON.toJSONString(addressVo);
                    log.info("{}新增接口，封装参数：{}",CardUploadCountryBusTypeEnum.OCCADDR.getCode().compareTo(this.busAddrType) == 0 ? "职业卫生报送卡-服务地址" : "放射卫生报送卡-服务地址", apiData);
                    addMap.put("apiData",  DesEncryptUtil.encryptByKey(apiData,this.encryptKey));
                    String result = HttpRequestUtil.httpRequestByFormData(CardUploadCountryBusTypeEnum.OCCADDR.getCode()
                            .compareTo(this.busAddrType) == 0 ? this.occhethAddrAddUrl : this.srvorgAddrAddUrl, addMap);
                    log.info("{}新增接口，返回：{}",CardUploadCountryBusTypeEnum.OCCADDR.getCode().compareTo(this.busAddrType) == 0 ? "职业卫生报送卡-服务地址" : "放射卫生报送卡-服务地址", result);
                    CardResponsAddVo addVo = JSON.toJavaObject(JSON.parseObject(result), CardResponsAddVo.class);
                    String msg =  "接口返回信息：" + result;
                    if(null != addVo){
                        if(StringUtils.isNotBlank(addVo.getCode()) && "1".equals(addVo.getCode())){
                            //成功
                            cardRcd.setState(1);
                            if(null != addVo.getData() && null != addVo.getData().getAddId()){
                                cardRcd.setReportId(addVo.getData().getAddId());
                            }
                        }else{
                            errMsg = msg;
                        }
                    }else{
                        errMsg = msg;
                    }
                }catch(Exception e){
                    errMsg = e.toString();
                    log.error(e.getMessage(), new Throwable(e));
                }
            }

            if(StringUtils.isNotBlank(errMsg)){
                cardRcd.setErrMsg(errMsg.length() > 1000 ? errMsg.substring(0, 1000) : errMsg);
            }
            if(addressVo.getRcdRid()==null){
                this.cardRcdService.insertEntity(cardRcd);
            }else{
                cardRcd.setRid(addressVo.getRcdRid());
                this.cardRcdService.updateFullById(cardRcd);
            }
        }
    }

    /**
     * <p>方法描述： 执行上传参与人员到国家 </p>
     * @MethodAuthor： pw 2022/12/23
     **/
    public void executePsnUploadData(List<TdZwOcchethCardPsnVo> dataList){
        for(TdZwOcchethCardPsnVo cardPsnVo : dataList){
            cardPsnVo.setSecurityKey(this.securityKey);
            String name = cardPsnVo.getName();
            if(StringUtils.isNotBlank(name) && name.length() > 16){
                cardPsnVo.setName(name.substring(0,16));
            }

            TdZywsCardRcd cardRcd=new TdZywsCardRcd();
            cardRcd.setBusType(this.busPsnType);
            cardRcd.setBusId(cardPsnVo.getRid());
            cardRcd.setUploadDate(new Date());
            //默认失败
            cardRcd.setState(2);
            cardRcd.setCreateDate(new Date());
            cardRcd.setCreateManid(1);
            String errMsg = null;
            try{
                // 加密后传输
                Map<String, String> addMap = new HashMap<>();
                String apiData = "";
                //如果是放射卫生报送卡-参与人员 重新封装对象
                if (CardUploadCountryBusTypeEnum.SRVORGPSN.getCode().compareTo(this.busPsnType) == 0) {
                    TdZwSrvorgCardPsnVo srvorgCardPsnVo = new TdZwSrvorgCardPsnVo();
                    ObjectCopyUtil.copyProperties(cardPsnVo, srvorgCardPsnVo);
                    apiData = JSON.toJSONString(srvorgCardPsnVo);
                } else {
                    apiData = JSON.toJSONString(cardPsnVo);
                }
                log.info("{}新增接口，封装参数：{}",CardUploadCountryBusTypeEnum.OCCPSN.getCode().compareTo(this.busPsnType) == 0 ? "职业卫生报送卡-参与人员" : "放射卫生报送卡-参与人员", apiData);
                addMap.put("apiData",  DesEncryptUtil.encryptByKey(apiData,this.encryptKey));
                String result = HttpRequestUtil.httpRequestByFormData(CardUploadCountryBusTypeEnum.OCCPSN.getCode()
                        .compareTo(this.busPsnType) == 0 ? this.occhethPsnAddUrl : this.srvorgPsnAddUrl, addMap);
                log.info("{}新增接口，返回：{}",CardUploadCountryBusTypeEnum.OCCPSN.getCode().compareTo(this.busPsnType) == 0 ? "职业卫生报送卡-参与人员" : "放射卫生报送卡-参与人员", result);
                CardResponsAddVo addVo = JSON.toJavaObject(JSON.parseObject(result), CardResponsAddVo.class);
                String msg =  "接口返回信息：" + result;
                if(null != addVo){
                    if(StringUtils.isNotBlank(addVo.getCode()) && "1".equals(addVo.getCode())){
                        //成功
                        cardRcd.setState(1);
                        if(null != addVo.getData() && null != addVo.getData().getStaffId()){
                            cardRcd.setReportId(addVo.getData().getStaffId());
                        }
                    }else{
                        errMsg = msg;
                    }
                }else{
                    errMsg = msg;
                }
            }catch(Exception e){
                errMsg = e.toString();
                log.error(e.getMessage(), new Throwable(e));
            }
            if(StringUtils.isNotBlank(errMsg)){
                cardRcd.setErrMsg(errMsg.length() > 1000 ? errMsg.substring(0, 1000) : errMsg);
            }
            if(cardPsnVo.getRcdRid()==null){
                this.cardRcdService.insertEntity(cardRcd);
            }else{
                cardRcd.setRid(cardPsnVo.getRcdRid());
                this.cardRcdService.updateFullById(cardRcd);
            }
        }
    }

    /**
     * <p>方法描述： 获取对照国家的编码 </p>
     * @MethodAuthor： pw 2022/12/24
     **/
    public String contraSubExchange(String contraCode,String busType, String leftCode){
        Map<String, TsContraSub> contraSubMap = this.tsContraSubService.findTsContraSub(contraCode, busType);
        if(CollectionUtils.isEmpty(contraSubMap)){
            return null;
        }
        if(StringUtils.isNotBlank(leftCode)){
            TsContraSub contraSub = contraSubMap.get(leftCode);
            if(null != contraSub){
                return contraSub.getRightCode();
            }
        }
        return null;
    }

    /**
     * <p>方法描述： 给人员信息busType 赋值 </p>
     * @MethodAuthor： pw 2022/12/23
     **/
    public abstract void fillPsnBusType();

    /**
     * <p>方法描述： 查询需要上传的人员数据 </p>
     * @MethodAuthor： pw 2022/12/23
     **/
    public abstract List<TdZwOcchethCardPsnVo> queryPsnDataList(Integer dataSize);

    /**
     * <p>方法描述： 给服务地址信息busType 赋值 </p>
     * @MethodAuthor： pw 2022/12/24
     **/
    public abstract void fillAddrBusType();
    /**
     * <p>方法描述： 查询需要上传的服务地址数据 </p>
     * @MethodAuthor： pw 2022/12/24
     **/
    public abstract List<TdZwUploadAddressVo> queryAddrDataList(Integer dataSize);
}

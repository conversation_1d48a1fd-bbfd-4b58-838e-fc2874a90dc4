package com.chis.modules.timer.heth.pojo.json;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 同步用人单位信息参数
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CrptValueJson {
    /**
     * 地区ID
     */
    private Integer zoneId;
    /**
     * 单位名称
     */
    private String crptName;
    /**
     * 是否分支机构
     */
    private Integer ifSubPrg;
    /**
     * 社会信用代码
     */
    private String institutionCode;
    /**
     * 上级单位名称
     */
    private String upperCrptName;
    /**
     * 上级单位社会信用代码
     */
    private String upperInstitutionCode;
    /**
     * 上级单位地区ID
     */
    private Integer upperZoneId;
    /**
     * 单位注册地址
     */
    private String address;
    /**
     * 工作场所地址
     */
    private String enrolAddress;
    /**
     * 企业规模ID
     */
    private Integer crptSizeId;
    /**
     * 经济类型ID
     */
    private Integer economyId;
    /**
     * 行业类别ID
     */
    private Integer indusTypeId;
    /**
     * 职业卫生管理联系人
     */
    private String occManaOffice;
    /**
     * 联系电话
     */
    private String linkPhone2;
    /**
     * 职工人数
     */
    private Integer workForce;
    /**
     * 外委人数
     */
    private Integer outsourceNum;
    /**
     * 接害总人数
     */
    private Integer holdCardMan;
    /**
     * 职业病累计人数
     */
    private Integer occupationalDiseasesNum;
    /**
     * 营业状态
     */
    private Integer operationStatus;
    /**
     * 信息来源编码
     */
    private String sourceCode;
    /**
     * 唯一标识
     */
    private String uuid;
    /**
     * 危害因素ID集合
     */
    private List<Integer> badRsnList;
}

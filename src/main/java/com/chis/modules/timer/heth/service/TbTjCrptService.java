package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TbTjCrpt;
import com.chis.modules.timer.heth.mapper.TbTjCrptMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-11-19,TbTjCrptService
 */
@Service
public class TbTjCrptService extends ZwxBaseServiceImpl<TbTjCrptMapper, TbTjCrpt> {

    /**
     * <p>方法描述： 通过企业rid获取企业信息，包含地区名称 </p>
     * pw 2023/11/11
     **/
    public List<TbTjCrpt> findCrptWithZoneInfoByRids (List<Integer> ridList) {
        if (CollectionUtils.isEmpty(ridList)) {
            return Collections.emptyList();
        }
        ridList = ridList.stream().distinct().collect(Collectors.toList());
        List<TbTjCrpt> allCrptList = new ArrayList<>();
        List<List<Integer>> groupList = StringUtils.splitListProxy(ridList, 1000);
        for (List<Integer> curRidList : groupList) {
            List<TbTjCrpt> tmpList = this.baseMapper.findCrptWithZoneInfoByRids(curRidList);
            if (CollectionUtils.isEmpty(tmpList)) {
                continue;
            }
            allCrptList.addAll(tmpList);
        }
        return allCrptList;
    }
}

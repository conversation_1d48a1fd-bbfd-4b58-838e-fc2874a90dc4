package com.chis.modules.timer.heth.logic.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 曼荼罗返回的数据对象
 *
 * @ClassAuthor pw,2022年12月29日,MtlDataVo
 */
@Data
public class MtlDataVo implements Serializable {
    private static final long serialVersionUID = 3082798873706158815L;
    //患者姓名
    private String psnName;
    //患者唯一标识
    private String psnNo;
    //身份证号
    private String idc;
    //患者所属区县
    private String county;
    //患者所属站点的名称
    private String siteName;
    //当年首次康复时间
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    @DateTimeFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstRehabilitaionDateThisYear;
    //当年末次康复日期
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    @DateTimeFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastRehabilitaionDateThisYear;
    //有无器械康复
    private Boolean hasInstrumentRehabilitation;
    //有无中医康复
    private Boolean hasRehabilitationOfTraditionalChineseMedicine;
    //有无营养康复
    private Boolean hasNutritionalRehabilitation;
    //有无心理康复
    private Boolean hasPsychologicalRehabilitation;
    //康复训练总次数
    private Integer rehabilitationNum;
    //康复训练总时间
    private Double totalTime;
    //康复训练平均时间
    private Double averageDuration;
    //血氧饱和度
    private MtlDateVo bloodOxygenSaturation;
    //ADL评分
    private MtlDateVo ADLScore;
    //心理量表测量（焦虑症）评分
    private MtlDateVo AnxietyScore;
    //心理量表测量（抑郁症）评分
    private MtlDateVo DepressionScore;
}

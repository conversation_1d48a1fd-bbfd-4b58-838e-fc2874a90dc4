package com.chis.modules.timer.heth.job.country;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.enums.CardUploadCountryBusTypeEnum;
import com.chis.modules.timer.heth.logic.vo.TdZwOcchethCardVo;
import com.chis.modules.timer.heth.service.TdZwOcchethCardVoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p>类描述： </p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年12月21日
 **/
@Slf4j
@Component
public class OcchethCardReportJob<T> extends OcchethCardAbstract<TdZwOcchethCardVo> {


    @Autowired
    private TdZwOcchethCardVoService occhethCardVoService;


    @Scheduled(cron = "${heth-timer.country.sche-cron.occhethCardCron}")
    @Override
    public void start() {
        super.uploadReportInfo();
    }

    @Override
    public List<TdZwOcchethCardVo> findDealData(Integer dataSize, String startDate) {
        return occhethCardVoService.findOcchethCard(dataSize, startDate);
    }


    @Override
    public List<TdZwOcchethCardVo> dealDate(List<TdZwOcchethCardVo> list, Map<String, String> backMap) {
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        List<TdZwOcchethCardVo> newList=new ArrayList<>();
        for (TdZwOcchethCardVo cardVo : list) {
            StringBuffer errorMsg=new StringBuffer();
             //行政区划代码对照
            String sssxbm = cardVo.getSssxbm();
            if(StringUtils.isNotBlank(sssxbm)){
                sssxbm = this.contraSubExchange("15","1", sssxbm);
                if(StringUtils.isBlank(sssxbm)){
                    //地区编码对照不上
                    errorMsg.append(";服务的用人单位注册地址-行政区划码：【").append(cardVo.getSssxbm()).append("】对照失败");
                }else{
                    cardVo.setSssxbm(sssxbm);
                }
            }
            //企业规模对照
            String ssize = cardVo.getSsize();
            if(StringUtils.isNotBlank(ssize)){
                ssize = this.contraSubExchange("15","2", ssize);
                if(StringUtils.isBlank(ssize)){
                    //地区编码对照不上
                    errorMsg.append(";服务的用人单位企业规模：【").append(cardVo.getSsize()).append("】对照失败");
                }else{
                    cardVo.setSsize(ssize);
                }
            }
            //格式校验，长度处理逻辑
            //职业卫生资质认可机构项目负责人  长度处理
            cardVo.setCname(executeCheckStr(cardVo.getCname(),16));
            //服务的用人单位名称
            cardVo.setSoname(executeCheckStr(cardVo.getSoname(),64));
            //服务的用人单位统一社会信用代码或其他编码
            cardVo.setSocode(executeCheckStr(cardVo.getSocode(),32));
            //服务的用人单位注册地址-行政区划码
            cardVo.setSssxbm(executeCheckStr(cardVo.getSssxbm(),6));
            //服务的用人单位注册地址-详细地址
            cardVo.setSaddress(executeCheckStr(cardVo.getSaddress(),128));
            //服务的用人单位联系人
            cardVo.setCname(executeCheckStr(cardVo.getCname(),16));
            //服务的用人单位职业病危害因素检测岗位或工种数
            if("1".equals(cardVo.getIfBadrsnJc()) && !this.validateInteger(cardVo.getSysnums(), 1, 99999)){
                errorMsg.append(";服务的用人单位职业病危害因素检测岗位或工种数【").append(cardVo.getSysnums()).append("】长度异常");
            }
            //服务的用人单位职业病危害因素检测岗位或工种超标数
            if(!this.validateInteger(cardVo.getSysexnums(), 0, 99999)){
                errorMsg.append(";服务的用人单位职业病危害因素检测岗位或工种超标数【").append(cardVo.getSysexnums()).append("】长度异常");
            }
            //服务的用人单位职业病危害现状评价检测岗位或工种数
            if("1".equals(cardVo.getIfStatusPj()) &&  !this.validateInteger(cardVo.getSxznums(), 1, 99999)){
                errorMsg.append(";服务的用人单位职业病危害现状评价检测岗位或工种数【").append(cardVo.getSxznums()).append("】长度异常");
            }
            //服务的用人单位职业病危害现状评价检测
            if(!this.validateInteger(cardVo.getSxzexnums(), 0, 99999)){
                errorMsg.append(";服务的用人单位职业病危害现状评价检测岗位或工种超标数【").append(cardVo.getSxzexnums()).append("】长度异常");
            }
            //服务的用人单位职业病防护设备设施与防护用品的效果评价-开展职业病防护设备设施防护效果检测设备设施数
            if(!this.validateInteger(cardVo.getSissfsseq(), 1, 99999)){
                errorMsg.append(";服务的用人单位职业病防护设备设施与防护用品的效果评价-开展职业病防护设备设施防护效果检测设备设施数【").append(cardVo.getSissfsseq()).append("】长度异常");
            }
            //服务的用人单位职业病防护设备设施与防护用品的效果评价-开展职业病防护设备设施防护效果检测不合格的设备设施数
            if(!this.validateInteger(cardVo.getSissfssbq(), 0, 99999)){
                errorMsg.append(";服务的用人单位职业病防护设备设施与防护用品的效果评价-开展职业病防护设备设施防护效果检测不合格的设备设施数【").append(cardVo.getSissfssbq()).append("】长度异常");
            }
            //服务的用人单位职业病防护设备设施与防护用品的效果评价-开展职业病防护设备设施防护效果检测不合格的设备设施名称
            cardVo.setSissfssco(executeCheckStr(cardVo.getSissfssco(),128));
            //服务的用人单位职业病防护设备设施与防护用品的效果评价-开展职业病防护用品防护效果检测设备设施数
            if(!this.validateInteger(cardVo.getSissfyfeq(), 1, 99999)){
                errorMsg.append(";服务的用人单位职业病防护设备设施与防护用品的效果评价-开展职业病防护用品防护效果检测设备设施数【").append(cardVo.getSissfyfeq()).append("】长度异常");
            }
            //服务的用人单位职业病防护设备设施与防护用品的效果评价-开展职业病防护用品防护效果检测不合格的设备设施数
            if(!this.validateInteger(cardVo.getSissfyfbq(), 0, 99999)){
                errorMsg.append(";服务的用人单位职业病防护设备设施与防护用品的效果评价-开展职业病防护用品防护效果检测不合格的设备设施数【").append(cardVo.getSissfyfbq()).append("】长度异常");
            }
            //服务的用人单位职业病防护设备设施与防护用品的效果评价-开展职业病防护设备设施防护效果检测不合格的设备设施名称
            cardVo.setSissfyfco(executeCheckStr(cardVo.getSissfyfco(),128));
            String phone = cardVo.getCphone();
            if(StringUtils.isNotBlank(phone)){
                phone = this.fixTelphone(phone);
                cardVo.setCphone(phone);
            }
            phone = cardVo.getScphone();
            if(StringUtils.isNotBlank(phone)){
                phone = this.fixTelphone(phone);
                cardVo.setScphone(phone);
            }
            if(errorMsg.length()>0){
                if(cardVo.getRcdRid()!=null){
                    backMap.put(cardVo.getRid()+"&"+cardVo.getRcdRid(),errorMsg.substring(1));
                }else{
                    backMap.put(cardVo.getRid().toString(),errorMsg.substring(1));
                }
            }else{
                newList.add(cardVo);
            }
        }
        return newList;
    }

    @Override
    public Integer getBusType() {
        return CardUploadCountryBusTypeEnum.OCCCARD.getCode();
    }

}

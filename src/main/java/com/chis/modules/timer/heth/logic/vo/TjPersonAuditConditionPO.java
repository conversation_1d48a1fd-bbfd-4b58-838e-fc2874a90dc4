package com.chis.modules.timer.heth.logic.vo;


import lombok.Data;

/**
 *  <p>方法描述：个案审核导出条件</p>
 * @MethodAuthor hsj 2024-08-22 18:02
 */
@Data
public class TjPersonAuditConditionPO {

	private String searchEntrustCrptZoneCode;
	private String searchEntrustCrptName;
	private String searchEntrustCreditCode;
	private String searchZoneCode;
	private String searchCrptName;
	private String searchCreditCode;
	private String searchBhkCode;
	private String searchIdc;
	private String searchPersonName;
	private String searchBhkBdate;
	private String searchBhkEdate;
	private String selectOnGuardIds;
	private String selectBadRsnIds;
	private String startRptPrintDate;
	private String endRptPrintDate;
	private String jcTypes;
	private String ifRhks;
	private String ifAbnormals;
	private String searchAbnomalInfo;
	private String searchBackUpRsn;
	private String states;
	private String searchUnitId;
	private String returnedStates;
	private Integer checkLevel;
	private Integer startRow;
	private Integer endRow;
	private Integer zoneType;
	/**
	 * 质控编号
	 */
	private String zkBhkCode;
}

package com.chis.modules.timer.heth.config;

import com.chis.modules.timer.heth.handler.CalculationThreadExecutionHandler;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * 线程池配置
 * */
@Data
@Configuration
@ConfigurationProperties(prefix = "heth-timer.calculat-export.thread-pool")
public class ThreadPoolConfig {
    /** 核心线程数 */
    private int corePoolSize;
    /** 最大线程数 */
    private int maximumPoolSize;
    /** 线程存活时间 */
    private Long keepAliveTime;
    /** 队列容量 */
    private int queueCapacity;

    @Bean("calculationExecutorService")
    public ExecutorService buildCalculatQueueThreadPool(){
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("calculation-thread-%d").build();
        // 实例化线程池
        ExecutorService pool = new ThreadPoolExecutor(this.getCorePoolSize(), this.getMaximumPoolSize(), this.getKeepAliveTime(), TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(this.getQueueCapacity()),namedThreadFactory,new CalculationThreadExecutionHandler());
        return pool;
    }
}

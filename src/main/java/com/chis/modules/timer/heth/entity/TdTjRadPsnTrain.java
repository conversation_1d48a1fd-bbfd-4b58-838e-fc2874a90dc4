package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-07,TdTjRadPsnTrain
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_TJ_RAD_PSN_TRAIN")
@KeySequence(value = "TD_TJ_RAD_PSN_TRAIN_SEQ",clazz = Integer.class)
public class TdTjRadPsnTrain extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdTjRadhethPsn fkByMainId;

    @TableField("TRAIN_ORG_NAME")
    private String trainOrgName;

    @TableField("TRAIN_RESULT")
    private Integer trainResult;

    @TableField("ANNEX_PATH")
    private String annexPath;

    @TableField("TRAIN_DATE")
    private Date trainDate;

    @TableField("CENT_NO")
    private String centNo;

    @TableField("UUID")
    private String uuid;

    @TableField("ADD_UP_TIME")
    private Integer addUpTime;

    @TableField("SCORE")
    private Double score;

    @TableField("DATA_SOURCE")
    private Integer dataSource;


    public TdTjRadPsnTrain(Integer rid) {
        super(rid);
    }


}

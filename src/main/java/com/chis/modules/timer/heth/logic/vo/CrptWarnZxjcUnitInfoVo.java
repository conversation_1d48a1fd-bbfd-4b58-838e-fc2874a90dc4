package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>类描述：用人单位预警-在线监测数据查询返回对象 </p>
 * pw 2023/9/28
 **/
@Data
public class CrptWarnZxjcUnitInfoVo implements Serializable {
    private static final long serialVersionUID = -9057888983362074911L;
    /** 在线监测基本信息rid */
    private Integer rid;
    private Integer crptId;
    /** 主要负责人培训 */
    private Integer ifLeadersTrain;
    /** 职业卫生管理人员培训 */
    private Integer ifManagersTrain;
    /** 上一年度检测情况 */
    private Integer ifat;
    /** 上一年度职业健康检查开展情况 */
    private Integer ifhea;
    /** 接触职业病危害因素总人数 */
    private Integer contactTotalPeoples;
    /** 体检总人数 */
    private Integer checkTotalPeoples;
    /** 创建日期 */
    private Date createDate;
}

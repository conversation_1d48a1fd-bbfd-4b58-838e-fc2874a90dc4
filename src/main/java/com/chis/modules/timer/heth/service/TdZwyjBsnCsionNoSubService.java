package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwyjBsnCsionNo;
import com.chis.modules.timer.heth.entity.TdZwyjBsnCsionNoSub;
import com.chis.modules.timer.heth.enums.BadRsnCaffeineKeyEnum;
import com.chis.modules.timer.heth.mapper.TdZwyjBsnCsionNoSubMapper;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnCsionNoSubService
 */
@Service
public class TdZwyjBsnCsionNoSubService extends ZwxBaseServiceImpl<TdZwyjBsnCsionNoSubMapper, TdZwyjBsnCsionNoSub> {
    @Autowired
    private TdZwyjBsnCsionNoService csionNoService;
    /**
     * <p>方法描述：查询配置重点职业病判定结论</p>
     * @MethodAuthor qrr,2020-10-29,findCsionPdjlMap
     * */
    public Map<Integer,List<TdZwyjBsnCsionNoSub>> findCsionNoRstMap(){
        Map<Integer, List<TdZwyjBsnCsionNoSub>> noRstMap = (Map<Integer,List<TdZwyjBsnCsionNoSub>>) CaffeineUtil.getIfPresent(BadRsnCaffeineKeyEnum.NO_RSN_CAFFEINE_KEY, null);
        if(null==noRstMap){
            List<TdZwyjBsnCsionNo> csionNoList = csionNoService.selectListByEntity(null);
            Map<Integer,List<TdZwyjBsnCsionNoSub>> noSubMap = this.findBsnCsionNoSubMap();
            //key:在岗状态Id,value:TdZwyjBsnCsionNoSub
            noRstMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(csionNoList)){
                for(TdZwyjBsnCsionNo csionNo:csionNoList){
                    if(null==csionNo.getFkByOnguardStateid()){
                        continue;
                    }
                    Integer onguardStateid = csionNo.getFkByOnguardStateid().getRid();
                    List<TdZwyjBsnCsionNoSub> noSubList = noSubMap.get(csionNo.getRid());
                    if(!CollectionUtils.isEmpty(noSubList)){
                        for(TdZwyjBsnCsionNoSub t:noSubList){
                            if(null==noRstMap.get(onguardStateid)){
                                List<TdZwyjBsnCsionNoSub> list = new ArrayList<>();
                                list.add(t);
                                noRstMap.put(onguardStateid,list);
                            }else {
                                List<TdZwyjBsnCsionNoSub> list = noRstMap.get(onguardStateid);
                                list.add(t);
                            }
                        }
                    }
                }
            }
        }
        return noRstMap;
    }
    /**
     * <p>方法描述：</p>
     * @MethodAuthor qrr,2020-11-04,findBsnCsionNoSubMap
     * */
    public Map<Integer,List<TdZwyjBsnCsionNoSub>> findBsnCsionNoSubMap(){
        List<TdZwyjBsnCsionNoSub> noSubList = this.selectListByEntity(null);
        //key:配置主表Id,value:TdZwyjBsnCsionNoSub
        Map<Integer,List<TdZwyjBsnCsionNoSub>> noSubMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(noSubList)){
            for(TdZwyjBsnCsionNoSub t:noSubList){
                if (null==t.getFkByMainId()){
                    continue;
                }
                if(null==noSubMap.get(t.getFkByMainId().getRid())){
                    List<TdZwyjBsnCsionNoSub> list = new ArrayList<>();
                    list.add(t);
                    noSubMap.put(t.getFkByMainId().getRid(),list);
                }else {
                    List<TdZwyjBsnCsionNoSub> list = noSubMap.get(t.getFkByMainId().getRid());
                    list.add(t);
                }
            }
        }
        return noSubMap;
    }
}

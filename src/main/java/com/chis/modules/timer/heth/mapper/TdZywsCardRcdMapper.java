package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZywsCardRcd;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2022-12-21,TdZywsCardRcdMapper
 */
@Repository
public interface TdZywsCardRcdMapper extends ZwxBaseMapper<TdZywsCardRcd> {

    void updateCardRcdByType(@Param("type") Integer type,@Param("states") String states);
    /**
     * <p>方法描述：updateCardRcdState</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-20
     **/
    int updateCardRcdState(@Param("busType") Integer busType, @Param("updateState")String updateState, @Param("reportId")Integer reportId, @Param("errMsg")String errMsg, @Param("uploadDate")String uploadDate, @Param("rid") Integer rid);

    void updateCardRcdByState(@Param("type") Integer type,@Param("state") Integer state,@Param("msg") String msg,@Param("cardId") Integer cardId);

    int updateCardRcdReportId(@Param("busType") Integer busType,@Param("rid") Integer rid);

    void updateCardRcdStateForFailed(Integer busType);

    void updateCardRcdStateByReportId(@Param("busType") int busType,
                                      @Param("reportId") int reportId,
                                      @Param("state") int state,
                                      @Param("errMsg") String errMsg,
                                      @Param("uploadDate") Date uploadDate);
}

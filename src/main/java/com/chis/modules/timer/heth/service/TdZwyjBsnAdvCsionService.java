package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwyjBsnAdvCsion;
import com.chis.modules.timer.heth.entity.TdZwyjBsnBhk;
import com.chis.modules.timer.heth.mapper.TdZwyjBsnAdvCsionMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnAdvCsionService
 */
@Service
public class TdZwyjBsnAdvCsionService extends ZwxBaseServiceImpl<TdZwyjBsnAdvCsionMapper, TdZwyjBsnAdvCsion> {
    public List<TdZwyjBsnAdvCsion> findBsnAdvCsions(Integer mainId){
        TdZwyjBsnAdvCsion advCsion = new TdZwyjBsnAdvCsion();
        advCsion.setFkByBhkId(new TdZwyjBsnBhk(mainId));
        return this.baseMapper.selectListByEntity(advCsion);
    }
    /**
     * <p>方法描述：根据主表删除子表</p>
     * @MethodAuthor qrr,2020-10-30,deleteByMainId
     * */
    public void deleteByMainId(Integer mainId){
        this.baseMapper.deleteByMainId(mainId);
    }
}

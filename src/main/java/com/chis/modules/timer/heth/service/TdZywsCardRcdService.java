package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZywsCardRcd;
import com.chis.modules.timer.heth.mapper.TdZywsCardRcdMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2022-12-21,TdZywsCardRcdService
 */
@Service
public class TdZywsCardRcdService extends ZwxBaseServiceImpl<TdZywsCardRcdMapper, TdZywsCardRcd> {

    @Autowired
    private TdZywsCardRcdMapper tdZywsCardRcdMapper;

    /**
     *  <p>方法描述：日志表更新为0
     *  type类型
     *  states：需要更新的状态</p>
     * @MethodAuthor hsj 2022-12-21 14:59
     */
    public void updateCardRcdByType(Integer type, String states) {
        this.baseMapper.updateCardRcdByType(type,states);
    }


    /**
     * <p>方法描述：更新日志表状态</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-12-20
     **/
    public int updateCardRcdState(Integer busType, String updateState, Integer reportId,String errMsg,String uploadDate,Integer rid) {
        return this.baseMapper.updateCardRcdState(busType,updateState,reportId,errMsg,uploadDate,rid);
    }
    /**
     *  <p>方法描述：日志表更新 成功时清空错误信息</p>
     * @MethodAuthor hsj 2022-12-22 17:49
     */
    public void updateCardRcdByState(Integer type, Integer state, String msg, Integer cardId) {
        this.baseMapper.updateCardRcdByState(type,state,msg,cardId);
    }

    /**
     * <p>方法描述：更新第三方编码为空</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-22
     **/
    public int updateCardRcdReportId(Integer busType, Integer rid) {
        return this.baseMapper.updateCardRcdReportId(busType,rid);
    }

    /**
     * 将主表日志状态为4或5的更新为1
     *
     * @param busType 业务类型 <pre>12：职业卫生报送卡</pre><pre>22：放射卫生报送卡</pre>
     */
    public void updateCardRcdStateForFailed(Integer busType) {
        this.tdZywsCardRcdMapper.updateCardRcdStateForFailed(busType);
    }

    public void updateCardRcdStateByReportId(int busType, int reportId, int state, String errMsg) {
        this.tdZywsCardRcdMapper.updateCardRcdStateByReportId(busType, reportId, state, errMsg, new Date());
    }
}

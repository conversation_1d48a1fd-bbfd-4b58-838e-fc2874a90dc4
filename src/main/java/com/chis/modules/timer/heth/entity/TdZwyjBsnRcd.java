package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-31,TdZwyjBsnRcd
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_BSN_RCD")
@KeySequence(value = "TD_ZWYJ_BSN_RCD_SEQ",clazz = Integer.class)
public class TdZwyjBsnRcd extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "BHK_ID" , el = "fkByBhkId.rid")
    private TdTjBhk fkByBhkId;

    @TableField("DATA_MARK")
    private String dataMark;

    @TableField("ERR_MSG")
    private String errMsg;


    public TdZwyjBsnRcd(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.mapper;

import com.chis.modules.timer.heth.logic.vo.TdZwOcchethCardPsnVo;
import com.chis.modules.timer.heth.logic.vo.TdZwOcchethCardVo;
import com.chis.modules.timer.heth.logic.vo.TdZwUploadAddressVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2022-12-21,TdZwOcchethCardMapper
 */
@Repository
public interface TdZwOcchethCardVoMapper {

    List<TdZwOcchethCardVo> findOcchethCard(@Param("dataSize") Integer dataSize,@Param("startDate") String startDate);

    List<TdZwOcchethCardPsnVo> findOcchethCardPsn(@Param("dataSize")Integer dataSize);

    List<TdZwUploadAddressVo> findOcchethCardAddress(@Param("dataSize")Integer dataSize);

    List<Map<String, Object>> selectNeedUpdateFileAndSubmitDataForOccHealth(
            @Param("busTypeMain") Integer busTypeMain,
            @Param("busTypePsn") Integer busTypePsn,
            @Param("busTypeZone") Integer busTypeZone,
            @Param("dataSize") Integer dataSize,
            @Param("lastDate") Date lastDate);
}

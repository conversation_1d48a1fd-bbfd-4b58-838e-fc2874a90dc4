package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdTjRadhethPsn;
import com.chis.modules.timer.heth.mapper.TdTjRadhethPsnMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdTjRadhethPsnService
 */
@Service
public class TdTjRadhethPsnService extends ZwxBaseServiceImpl<TdTjRadhethPsnMapper, TdTjRadhethPsn> {

    public List<TdTjRadhethPsn> selectRadhethPsn(Integer dataSize) {
        return this.baseMapper.selectRadhethPsn(dataSize);
    }

    public void updateRadhethPsn(List<Integer> psnsIds) {
        this.baseMapper.updateRadhethPsn(psnsIds);
    }

    public List<TdTjRadhethPsn> selectRadhethPsnByState(Integer dataSize) {
        return this.baseMapper.selectRadhethPsnByState(dataSize);
    }

    public void updateRadhethPsns(List<String> idcList, String state, String msg) {
        this.baseMapper.updateRadhethPsns(idcList,state,msg);
    }
}

package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwTjorginfo;
import com.chis.modules.timer.heth.mapper.TdZwTjorginfoMapper;
import com.chis.modules.timer.heth.pojo.bo.warn.CheckOrgWarnBO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人, 2020-11-13,TdZwTjorginfoService
 */
@Service
public class TdZwTjorginfoService extends ZwxBaseServiceImpl<TdZwTjorginfoMapper, TdZwTjorginfo> {
    public List<TdZwTjorginfo> selectAllOrgInfoList() {
        return baseMapper.selectAllOrgInfoList();
    }

    /**
     * 查询所有已提交、修改日期大于预警时间或（存在新备案预警、备案时间超过90天）或还未预警的检查机构
     *
     * @param date            90天前的日期
     * @param newSimpleCodeId 新备案预警异常码表ID
     * @return 预警信息列表，如果查询结果为空，则返回一个空列表。
     */
    public List<CheckOrgWarnBO> selectOrgWarnInfoList(Date date, Integer newSimpleCodeId) {
        List<CheckOrgWarnBO> orgWarnBOList = baseMapper.selectOrgWarnInfoList(date, newSimpleCodeId);
        if (CollectionUtils.isEmpty(orgWarnBOList)) {
            return new ArrayList<>();
        }
        return orgWarnBOList;
    }
}

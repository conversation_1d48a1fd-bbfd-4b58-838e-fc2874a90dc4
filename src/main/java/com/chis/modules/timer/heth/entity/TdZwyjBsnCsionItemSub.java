package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.beans.Transient;
import java.util.List;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnCsionItemSub
 */
@Data
@Builder
@NoArgsConstructor
@Accessors(chain = true)
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_BSN_CSION_ITEM_SUB")
public class TdZwyjBsnCsionItemSub extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwyjBsnClusion fkByMainId;

    @TableField("ITEM_DESC")
    private String itemDesc;

    @TableField("PD_CSION_DESC")
    private String pdCsionDesc;

    @TableField("XH")
    private String xh;

    /**项目规则*/
    private List<TdZwyjBsnCsionItems> itemsList;
    /**判定结论*/
    List<TdZwyjBsnCsionPdjl> pdjlList;

    @Transient
    public List<TdZwyjBsnCsionItems> getItemsList() {
        return itemsList;
    }

    public void setItemsList(List<TdZwyjBsnCsionItems> itemsList) {
        this.itemsList = itemsList;
    }
    @Transient
    public List<TdZwyjBsnCsionPdjl> getPdjlList() {
        return pdjlList;
    }

    public void setPdjlList(List<TdZwyjBsnCsionPdjl> pdjlList) {
        this.pdjlList = pdjlList;
    }

    public TdZwyjBsnCsionItemSub(Integer rid) {
        super(rid);
    }


}

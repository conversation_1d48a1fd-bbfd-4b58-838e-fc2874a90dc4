package com.chis.modules.timer.heth.pojo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 */
@Data
public class BhkCheckCondition implements java.io.Serializable {

    private String checkLevel;
    private Integer zoneType;
    private String username;
    private String searchZoneCodeEmp;
    private String searchCrptNameEmp;
    private String searchCreditCodeEmp;
    private String searchZoneCode;
    private String searchCrptName;
    private String searchCreditCode;
    private String searchIdc;
    private String searchPersonName;
    private String searchBhkType;
    private Date searchBhkBdate;
    private Date searchBhkEdate;
    private Date startCreateDate;
    private Date endCreateDate;
    private Date startRptPrintDate;
    private Date endRptPrintDate;
    private String selectOnGuardIds;
    private String selectBadRsnIds;
    private Date searchRcvBdate;
    private Date searchRcvEdate;
    private String jcTypes;
    private String ifRhks;
    private String ifAbnormals;
    private String searchUnitId;
    private Date limitDate;

    /**吉林特性字段 异常情况*/
    private String searchAbnormals;
    /**云南特性字段 是否迟报*/
    private String ifLate;
    /**云南特性配置 职业健康检查数据审核最大迟报天数*/
    private Integer maxDaysLateByCaseReview;
    /**广东特性字段 质控编号*/
    private String searchZkBhkCode;

    private String statueStr;
    /**选择的单危害因素结论*/
    private String searchSelBhkrstIds;
}

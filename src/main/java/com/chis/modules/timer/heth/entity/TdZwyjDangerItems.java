package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-21,TdZwyjDangerItems
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_DANGER_ITEMS")
public class TdZwyjDangerItems extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "ITEM_ID" , el = "fkByItemId.rid")
    private TbTjItems fkByItemId;

    @TableField("DANGER_NAME")
    private String dangerName;

    @TableField(value = "MSRUNT_ID" , el = "fkByMsruntId.rid")
    private TsSimpleCode fkByMsruntId;

    @TableField("GE_VAL")
    private String geVal;

    @TableField("GT_VAL")
    private String gtVal;

    @TableField("LE_VAL")
    private String leVal;

    @TableField("LT_VAL")
    private String ltVal;

    @TableField("IF_REVEAL")
    private String ifReveal;


    public TdZwyjDangerItems(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.ObjectUtils;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.mapper.CheckOrgWarnMapper;
import com.chis.modules.timer.heth.pojo.bo.warn.InstInfoWarnBO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>类描述：定时预警资质机构工具（检查机构）服务</p>
 *
 * @ClassAuthor hsj 2024-07-13 8:02
 */
@Service
public class CheckOrgWarnService {
    @Autowired
    private CheckOrgWarnMapper mapper;
    @Resource
    private TdZwOrgWarnMainService orgWarnMainService;
    @Resource
    private TdZwOrgWarnExceptService orgWarnExceptService;
    @Resource
    private TdZwOrgWarnDetailService orgWarnDetailService;
    @Resource
    private TdZwOrgWarnInstService orgWarnInstService;
    @Resource
    private TdZwOrgWarnPsnsService orgWarnPsnsService;
    @Resource
    private TdZwWarnPsnOrgService warnPsnOrgService;

    /**
     * <p>方法描述：根据机构id查询设备信息</p>
     *
     * @MethodAuthor hsj 2024-07-13 8:04
     */
    public Map<Integer, Map<Integer, List<InstInfoWarnBO>>> findInstInfoWarnBOByOrgId(List<Integer> orgIdList) {
        Map<Integer, Map<Integer, List<InstInfoWarnBO>>> map = new HashMap<>();
        if (CollectionUtils.isEmpty(orgIdList)) {
            return map;
        }
        List<InstInfoWarnBO> instInfoWarnList = this.mapper.findInstInfoWarnBOByOrgId(orgIdList);
        if (CollectionUtils.isEmpty(instInfoWarnList)) {
            return map;
        }
        return instInfoWarnList.stream()
                .filter(instInfo -> ObjectUtils.isNotNull(instInfo.getOrgRid()))
                .collect(Collectors.groupingBy(InstInfoWarnBO::getOrgRid, Collectors.groupingBy(InstInfoWarnBO::getInstKindId)));
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(TdZwOrgWarnMain orgWarnMain) {
        orgWarnMain.setIfExcept(CollectionUtils.isEmpty(orgWarnMain.getOrgWarnExceptList()) ? 0 : 1);
        Integer rid = orgWarnMain.getRid();
        if (ObjectUtils.isNotNull(rid)) {
            //刪除相关的子表信息
            //人员执业机构表 TD_ZW_WARN_PSN_ORG
            this.warnPsnOrgService.deleteByOrgWarnMainId(rid);
            //预警人员表 TD_ZW_ORG_WARN_PSNS
            this.orgWarnPsnsService.deleteByOrgWarnMainId(rid);
            //预警仪器表 TD_ZW_ORG_WARN_INST
            this.orgWarnInstService.deleteByOrgWarnMainId(rid);
            //预警明细表 TD_ZW_ORG_WARN_DETAIL
            this.orgWarnDetailService.deleteByOrgWarnMainId(rid);
            //异常信息表 TD_ZW_ORG_WARN_EXCEPT
            TdZwOrgWarnExcept except = new TdZwOrgWarnExcept();
            except.setFkByMainId(orgWarnMain);
            this.orgWarnExceptService.removeByEntity(except);
        }
        this.orgWarnMainService.saveOrUpdate(orgWarnMain);
        List<TdZwOrgWarnExcept> orgWarnExceptList = orgWarnMain.getOrgWarnExceptList();
        if (CollectionUtils.isEmpty(orgWarnExceptList)) {
            return;
        }
        this.orgWarnExceptService.saveBatch(orgWarnExceptList);
        List<TdZwOrgWarnDetail> details = new LinkedList<>();
        List<TdZwOrgWarnInst> orgWarnInsts = new LinkedList<>();
        List<TdZwOrgWarnPsns> orgWarnPsns = new LinkedList<>();
        List<TdZwWarnPsnOrg> warnPsnOrgs = new LinkedList<>();
        orgWarnExceptList.forEach(o -> {
            List<TdZwOrgWarnDetail> detailList = o.getDetailList();
            if (CollectionUtils.isEmpty(detailList)) {
                return;
            }
            details.addAll(detailList);
            detailList.forEach(detail -> {
                List<TdZwOrgWarnPsns> orgWarnIpnstLis = detail.getOrgWarnPsnsList();
                if (!CollectionUtils.isEmpty(orgWarnIpnstLis)) {
                    orgWarnPsns.addAll(orgWarnIpnstLis);
                    orgWarnIpnstLis.forEach(psn -> {
                        List<TdZwWarnPsnOrg> warnPsnOrgList = psn.getWarnPsnOrgList();
                        if (!CollectionUtils.isEmpty(warnPsnOrgList)) {
                            warnPsnOrgs.addAll(warnPsnOrgList);
                        }
                    });
                }
                List<TdZwOrgWarnInst> orgWarnInstList = detail.getOrgWarnInstList();
                if (!CollectionUtils.isEmpty(orgWarnInstList)) {
                    orgWarnInsts.addAll(orgWarnInstList);
                }
            });
        });
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        this.orgWarnDetailService.saveBatch(details);
        if (!CollectionUtils.isEmpty(orgWarnInsts)) {
            this.orgWarnInstService.saveBatch(orgWarnInsts);
        }
        if (!CollectionUtils.isEmpty(orgWarnPsns)) {
            this.orgWarnPsnsService.saveBatch(orgWarnPsns);
        }
        if (!CollectionUtils.isEmpty(warnPsnOrgs)) {
            this.warnPsnOrgService.saveBatch(warnPsnOrgs);
        }
    }
}

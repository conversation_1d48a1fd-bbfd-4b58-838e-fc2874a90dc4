package com.chis.modules.timer.heth.job.zw.warn;

import com.chis.modules.timer.heth.pojo.bo.warn.DisCaseBusDataBO;
import com.chis.modules.timer.heth.pojo.bo.warn.DisCaseNotInTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>类描述：疑似职业病例数预警 </p>
 * pw 2023/11/11
 **/
@Slf4j
@Component
public class SuspectedOccDisCaseJob extends DisCaseJob{

    @Scheduled(cron = "${warn-timer.zwWarn.cron.suspected-occ-discase}")
    public void start() {
        log.info("{} 工具启动，当前版本：{} 当前时间：{}", "疑似职业病例数预警", 20231113,new Date());
        super.warnStart();
    }

    @Override
    void setWarnTypeAndBusType() {
        this.setWarnType(1);
        this.setBusType(1);
    }

    @Override
    List<DisCaseBusDataBO> findBusDataList() {
        List<DisCaseBusDataBO> queryResultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(this.warnModelBOMap)) {
            return queryResultList;
        }
        Set<String> busIdSet = new HashSet<>();
        for (Integer warnLevel : this.warnModelBOMap.keySet()) {
            List<DisCaseBusDataBO> curQueryList = this.disCaseJobService.findSuspectedOccDisCaseDataList(this.beginDate, warnLevel, this.zoneGbMax);
            if (CollectionUtils.isEmpty(curQueryList)) {
                continue;
            }
            for (DisCaseBusDataBO dataBO : curQueryList) {
                Integer id = dataBO.getId();
                Integer disId = dataBO.getDisId();
                String key = id+"&"+disId;
                if (null == id || null == disId || busIdSet.contains(key)) {
                    continue;
                }
                busIdSet.add(key);
                queryResultList.add(dataBO);
            }
        }
        queryResultList = queryResultList.stream().sorted(Comparator.comparing(DisCaseBusDataBO::getDate)) .collect(Collectors.toList());
        return queryResultList;
    }
}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdZyUnithealthcustody
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZY_UNITHEALTHCUSTODY")
@KeySequence(value = "TD_ZY_UNITHEALTHCUSTODY_SEQ",clazz = Integer.class)
public class TdZyUnithealthcustody extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZyUnitbasicinfo fkByMainId;

    @TableField("IFHEA")
    private Integer ifhea;

    @TableField("CHECK_UNIT_NAMES")
    private String checkUnitNames;

    @TableField("CHECK_REPORT_NOS")
    private String checkReportNos;

    @TableField("IFHEA_DUST")
    private Integer ifheaDust;

    @TableField("HEA_DUST_PEOPLES")
    private Integer heaDustPeoples;

    @TableField("IFHEA_CHEMISTRY")
    private Integer ifheaChemistry;

    @TableField("HEA_CHEMISTRY_PEOPLES")
    private Integer heaChemistryPeoples;

    @TableField("HEA_CHEMISTRY_LEAD_PEOPLES")
    private Integer heaChemistryLeadPeoples;

    @TableField("HEA_TCHEMISTRY_BENZENE_PEOPLES")
    private Integer heaTchemistryBenzenePeoples;

    @TableField("IFHEA_PHYSICS")
    private Integer ifheaPhysics;

    @TableField("HEA_PHYSICS_PEOPLES")
    private Integer heaPhysicsPeoples;

    @TableField("HEA_PHYSICS_NOISE_PEOPLES")
    private Integer heaPhysicsNoisePeoples;

    @TableField("IFHEA_RADIOACTIVITY")
    private Integer ifheaRadioactivity;

    @TableField("HEA_RADIOACTIVITY_PEOPLES")
    private Integer heaRadioactivityPeoples;


    public TdZyUnithealthcustody(Integer rid) {
        super(rid);
    }


}

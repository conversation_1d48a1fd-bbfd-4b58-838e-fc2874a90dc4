package com.chis.modules.timer.heth.job;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.chis.comm.utils.*;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.service.TsContraSubService;
import com.chis.modules.sys.service.TsSimpleCodeService;
import com.chis.modules.timer.heth.entity.TbTjItems;
import com.chis.modules.timer.heth.entity.TdTjBhkAbnomal;
import com.chis.modules.timer.heth.entity.TdTjExport;
import com.chis.modules.timer.heth.handler.CalculationThreadExecutionHandler;
import com.chis.modules.timer.heth.logic.BhkAuditQueryPO;
import com.chis.modules.timer.heth.logic.BhkCalculationAuditQueryPO;
import com.chis.modules.timer.heth.logic.vo.*;
import com.chis.modules.timer.heth.service.*;
import com.chis.modules.webmvc.utils.JacksonUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @Description: 个案异步导出文件定时任务
 *
 * @ClassAuthor pw,2021年12月18日,BhkCalculationFileExportJob
 */
@Slf4j
@Component
public class BhkCalculationFileExportJob {

    @Autowired
    private TdTjExportService exportService;
    @Autowired
    private ExecutorService calculationExecutorService;
    @Autowired
    private TsSimpleCodeService simpleCodeService;
    @Autowired
    private BhkCalculationFileExportService calculationFileExportService;
    @Autowired
    private TdTjBhkService bhkService;
    @Autowired
    private TdTjItemsService tjItemsService;
    @Autowired
    private TdTjBhkAbnomalService bhkAbnomalService;

    @Autowired
    private TsContraSubService tsContraSubService;

    @Value("${heth-timer.calculat-export.dataSize}")
    private Integer dataSize;
    @Value("${heth-timer.virtual.directory}")
    private String virtualDirectory;

    /** 码表缓存 key rid value 码表实体 */
    private Map<Integer,TsSimpleCode> simpleCodeMap;
    /** 导出文件的文件夹 */
    private String exportRootFolder;
    /** 导出基础信息的码表缓存 key rid value 码表实体 */
    private Map<Integer,TsSimpleCode> exportSimpleCodeMap;
    /** 体检项目缓存 */
    private Map<Integer, String> itemCachMap = new HashMap<Integer, String>();
    private Map<Integer, TbTjItems> itemCachMaps = new HashMap<Integer, TbTjItems>();
    /** Excel 单个文件数据最大数量*/
    @Value("${heth-timer.calculat-export.maxDataNum}")
    private Integer MAX_DATA_NUM;
    /** 子线程数 */
    @Value("${heth-timer.calculat-export.thread-pool.childPoolSize}")
    private Integer childPoolSize;
    @Value("${heth-timer.calculat-export.executeDataSizeLimit}")
    private Integer executeDataSizeLimit;
    @Value("${heth-timer.calculat-export.ifExportZkBhkCode:false}")
    private Boolean ifExportZkBhkCode;
    private boolean folderExistFlag;
    private boolean ifQueryExecute;

    /**职业健康检查数据审核 导出是否显示国家退回原因 */
    private boolean ifShowErrMsg;

    // 样式缓存
    private CellStyle defaultStyle;
    private CellStyle centerStyle;
    private CellStyle leftStyle;

    @Scheduled(cron = "${heth-timer.sche-cron.calculatExportCron}")
    //@Scheduled(initialDelay = 3000,fixedDelay = 60000)
    public void start(){
        ifQueryExecute = true;
        Date date = new Date();
        log.info("个案异步导出文件定时任务启动，当前程序版本：{} 启动时间：{} {}","20230221",
                DateUtils.formatDateTime(date),"注意：当前程序依赖TB_TJ_CRPT_INDEPEND表 如果这个表没有查询会报错");
        initTsSimpleCodeMap();
        initTjItem();
        initParam();
        if(CollectionUtils.isEmpty(this.simpleCodeMap)){
            log.error("个案异步导出文件，5550码表无数据！");
            return;
        }
        if(StringUtils.isBlank(this.virtualDirectory) || !(new File(this.virtualDirectory)).exists()){
            log.error("个案异步导出文件，虚拟路径异常！");
            return;
        }
        this.exportRootFolder = "aynscExport"+File.separator;
        //若没有文件分隔符 加上
        if(!this.virtualDirectory.endsWith(File.separator) && !this.virtualDirectory.endsWith("/")){
            this.virtualDirectory += File.separator;
        }
        if(!folderExistFlag){
            File folder = new File(this.virtualDirectory+this.exportRootFolder);
            if(!folder.exists()){
                folder.mkdirs();
            }
            folderExistFlag = true;
            folder = null;
        }
        long topStartTime = System.currentTimeMillis();
        int total = 0;
        if(null == dataSize){
            dataSize = 100;
        }
        if(null == executeDataSizeLimit){
            executeDataSizeLimit = dataSize*5;
        }
        if(executeDataSizeLimit > 10000){
            executeDataSizeLimit = 10000;
        }
        date = new Date();
        String beginTime = DateUtils.formatDateTime(date);
        date = null;
        List<TdTjExport> tjExportList = queryExecuteTdExportList(dataSize);
        while(!CollectionUtils.isEmpty(tjExportList)){
            total += threadExcuteFunction(tjExportList);
            tjExportList = queryExecuteTdExportList(dataSize);
        }
        date = new Date();
        log.info("个案异步导出文件定时任务执行完成，共执行{}条生成文件任务，用时：{}毫秒 准备执行时间：{} 当前时间：{}",total,
                (System.currentTimeMillis() - topStartTime), beginTime,
                DateUtils.formatDateTime(date));
    }

    /**
     * @Description: 抽取查询正在导出状态的数据方法
     *
     * @MethodAuthor pw,2022年01月15日
     */
    private List<TdTjExport> queryExecuteTdExportList(Integer size){
        TdTjExport queryExport = new TdTjExport();
        //仅查询正在导出状态的数据
        queryExport.setState(0);
        if(null == size){
            size = 100;
        }
        return this.exportService.pageListOrderByCreateTime(1, size, queryExport);
    }

    /**
     * @Description: 准备提供给线程执行的任务
     *
     * @MethodAuthor pw,2021年12月18日
     */
    private int threadExcuteFunction(List<TdTjExport> tjExportList){
        int preNum = tjExportList.size();
        log.info("个案异步导出文件，准备处理{}个任务",preNum);
        long startTime = System.currentTimeMillis();
        CompletionService completionService = new ExecutorCompletionService(calculationExecutorService);
        for(TdTjExport tdTjExport : tjExportList){
            completionService.submit(() -> executeExport(tdTjExport));
        }
        //已经完成的任务rid集合
        List<Integer> ridAlreadyExecuteList = new ArrayList<>();
        int errorNum = 0;
        int successNum = 0;
        while(tjExportList.size() - errorNum > 0){
            try{
                Future<TdTjExport> future = completionService.take();
                TdTjExport editTjExport = future.get();
                if(null != editTjExport && null != editTjExport.getRid()){
                    log.info(editTjExport.getExportFilePath());
                    if(StringUtils.isNotBlank(editTjExport.getExportFilePath()) &&
                            editTjExport.getExportFilePath().endsWith(".zip")){
                        // 压缩文件
                        zipExportFile(editTjExport);
                    }
                    updateState(editTjExport);
                    successNum++;
                    ridAlreadyExecuteList.add(editTjExport.getRid());
                    tjExportList = tjExportList.stream().filter(v -> v.getRid() != editTjExport.getRid().intValue())
                            .collect(Collectors.toList());
                    //判断 正在处理和待处理的任务数 小于配置数量的一半 那么就查询出新的一组数据加入
                    // 这里如果无限制加入 如果错误的太多 会占用内存 这里会加限制
                    if(ifQueryExecute && (tjExportList.size() - errorNum < dataSize/2) && tjExportList.size() < executeDataSizeLimit){
                        List<TdTjExport> tmpExportList = queryExecuteTdExportList(dataSize-tjExportList.size()+errorNum);
                        if(!CollectionUtils.isEmpty(tmpExportList)){
                            List<Integer> alreadExistRidList = tjExportList.stream()
                                    .mapToInt(v -> v.getRid()).boxed().collect(Collectors.toList());
                            //去除已经存在的
                            tmpExportList = tmpExportList.stream()
                                    .filter(v -> !alreadExistRidList.contains(v.getRid())).collect(Collectors.toList());
                        }else{
                            ifQueryExecute = false;
                        }
                        if(!CollectionUtils.isEmpty(tmpExportList)){
                            log.info("个案异步导出文件，{}个任务处理已过半，现在加入{} 个任务",preNum,
                                    tmpExportList.size());
                            //将新查询到的任务加入到线程中
                            for(TdTjExport addTdTjExport : tmpExportList){
                                completionService.submit(() -> executeExport(addTdTjExport));
                            }
                            tjExportList.addAll(tmpExportList);
                        }
                    }
                }
            }catch(Exception e){
                e.printStackTrace();
                errorNum++;
                String message = null == e.getMessage() ? "个案异步导出文件，线程返回异常" :
                        (e.getMessage().length()>500 ? e.getMessage().substring(0,500) : e.getMessage());
                List<TdTjExport> exceptList = new ArrayList<>();
                exceptList.addAll(tjExportList);
                if(!CollectionUtils.isEmpty(ridAlreadyExecuteList)){
                    exceptList = exceptList.stream()
                            .filter(v -> !ridAlreadyExecuteList.contains(v.getRid())).collect(Collectors.toList());
                }
                if(!CollectionUtils.isEmpty(exceptList)){
                    for(TdTjExport editTjExport : exceptList){
                        editTjExport.setState(2);
                        editTjExport.setExportFilePath(null);
                        editTjExport.setExportFileDate(null);
                        editTjExport.setErrorMsg(null == message ? "个案异步导出文件，线程返回异常" : message);
                        updateState(editTjExport);
                    }
                }
                log.error("个案异步导出文件，线程返回异常",e);
            }
        }
        log.info("个案异步导出文件，{}个任务处理成功，{}个任务处理失败，用时：{}毫秒",successNum,errorNum,(System.currentTimeMillis()-startTime));
        return errorNum+successNum;
    }

    /**
     * @Description: 压缩文件夹
     *
     * @MethodAuthor pw,2022年12月31日
     */
    private void zipExportFile(TdTjExport tjExport) throws Exception{
        long startTime = System.currentTimeMillis();
        String filePath = this.virtualDirectory + File.separator + tjExport.getExportFilePath();
        String dirPath = filePath.substring(0,filePath.indexOf(".zip")) + File.separator;
        log.info(dirPath);
        File dirFile = new File(dirPath);
        if(!dirFile.exists()){
            throw new RuntimeException("生成压缩文件失败，未找到文件夹！");
        }
        OutputStream outputStream = null;
        File tmpFile = new File(filePath);
        try{
            log.info("异步任务Rid：{} 准备生成压缩文件", tjExport.getRid());
            outputStream = new FileOutputStream(tmpFile);
            //需考虑文件夹内文件太多的情况
            FileUtils.toZip(dirPath, outputStream, true);
            log.info("异步任务Rid：{} 生成压缩文件用时：{}", tjExport.getRid(), (System.currentTimeMillis() - startTime));
        }catch(Exception e){
            e.printStackTrace();
            tmpFile.deleteOnExit();
            log.info("异步任务Rid：{} 生成压缩异常：{}", tjExport.getRid(), e.getMessage());
            throw new RuntimeException("生成压缩文件失败，未成功生成文件！");
        }finally {
            if(null != outputStream){
                outputStream.close();
            }
        }
        if(!tmpFile.exists()){
            throw new RuntimeException("生成压缩文件失败，未成功生成文件！");
        }
        //删除文件
        try{
            startTime = System.currentTimeMillis();
            FileUtils.deleteDirectory(dirFile);
            log.info("异步任务Rid：{} 压缩完删除文件夹用时：{}", tjExport.getRid(), (System.currentTimeMillis() - startTime));
        }catch(Exception e){
            log.error("异步任务Rid：{} 压缩完删除异常：{} 需手动删除文件夹：{}", tjExport.getRid(),
                    (System.currentTimeMillis() - startTime), dirPath, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * @Description: 执行修改导出任务实体
     *
     * @MethodAuthor pw,2021年12月18日
     */
    private void updateState(TdTjExport tjExport){
        try{
            String filePath = tjExport.getExportFilePath();
            //处理windows系统生成的文件夹 在linux中访问不到的情况
            if(StringUtils.isNotBlank(filePath) && filePath.indexOf(File.separator) != -1){
                filePath = filePath.replace(File.separator, "/");
                tjExport.setExportFilePath(filePath);
            }
            tjExport.setModifyDate(new Date());
            tjExport.setModifyManid(0);
            this.exportService.updateFullById(tjExport);
        }catch(Exception e){
            e.printStackTrace();
            log.error("个案异步导出文件修改导出任务【异步导出】异常，修改rid[{}]对象：{}",tjExport.getRid(), tjExport);
        }
    }

    /**
     * @Description: 线程调用的生成文件以及修改任务实体信息
     *
     * @MethodAuthor pw,2021年12月18日
     */
    private TdTjExport executeExport(TdTjExport tjExport) throws Exception{
        long startTime = System.currentTimeMillis();
        try{
            //分发生成文件
            distributionGenerateFileTask(tjExport);
            tjExport.setState(1);
            tjExport.setErrorMsg(null);
        }catch(Exception e){
            e.printStackTrace();
            log.error("个案异步导出文件，任务rid：{} 生成文件异常",tjExport.getRid(), e);
            tjExport.setState(2);
            String message = null == e.getMessage() ? "个案异步导出文件，生成文件异常" :
                    (e.getMessage().length()>500 ? e.getMessage().substring(0,500) : e.getMessage());
            tjExport.setErrorMsg(message);
            tjExport.setExportFilePath(null);
            tjExport.setExportFileDate(null);
            throw e;
        }
        log.info("处理导出任务【异步导出】 rid：{} 用时：{}ms", tjExport.getRid(), (System.currentTimeMillis()-startTime));
        return tjExport;
    }

    /**
     * @Description: 生成文件 赋值导出文件名称、地址、时间
     *
     * @MethodAuthor pw,2021年12月18日
     */
    private void distributionGenerateFileTask(TdTjExport tjExport) throws Exception{
        TsSimpleCode simpleCode = null == tjExport.getFkByBusTypeId() || null == tjExport.getFkByBusTypeId().getRid() ?
                null : simpleCodeMap.get(tjExport.getFkByBusTypeId().getRid());
        if(null == simpleCode){
            log.error("导出任务rid：{} 业务类型未找到",tjExport.getRid());
            throw new RuntimeException("业务类型未匹配到码表");
        }
        tjExport.setFkByBusTypeId(simpleCode);
        String extends1 = simpleCode.getExtends1();
        if(StringUtils.isBlank(simpleCode.getExtends3())){
            throw new RuntimeException("码表缺少导出文件名称");
        }
        if ("1".equals(extends1)) {
            //个案查询
            this.auditGeneratePersonCalExportVo(tjExport);
        } else if ("3".equals(extends1)) {
            //个案审核
            this.auditGenerateCalExportVo(tjExport);
        } else if ("2".equals(extends1)) {
            //体检最新档案导出
            this.auditGenerateTjNewestRecExportVo(tjExport);
        } else if ("4".equals(extends1)) {
            //个案审核查询
            this.auditGenerateQueryCalExportVo(tjExport);
        } else {
            throw new RuntimeException("该业务类型不支持导出，业务类型扩展字段1" + (StringUtils.isBlank(extends1) ? "为空" : "为" + extends1));
        }
        //执行完 未赋值的 抛出去
        if(null == tjExport.getExportFileName() || null == tjExport.getExportFilePath() ||
                null == tjExport.getExportFileDate()){
            throw new RuntimeException("生成文件失败，可能缺少导出文件名称、导出文件地址或者导出文件时间导致");
        }
        //excel文件
        if(tjExport.getExportFilePath().endsWith(".xlsx")){
            File file = new File(this.virtualDirectory+tjExport.getExportFilePath());
            if(!file.exists()){
                throw new RuntimeException("生成文件失败，未成功生成文件！");
            }
        }
    }

    /**
     * <p>方法描述：个案审核查询导出</p>
     *
     * @MethodAuthor hsj 2024-08-17 16:34
     */
    private void auditGenerateQueryCalExportVo(TdTjExport tjExport) throws Exception{
        String jsonString = tjExport.getExportCondition();
        if (StringUtils.isBlank(jsonString)) {
          return;
        }
        TjPersonAuditConditionPO conditionPo = JSONObject.parseObject(jsonString, TjPersonAuditConditionPO.class);
        int count = this.calculationFileExportService.findAuditGenerateQueryCount(conditionPo);
        log.info("个案审核查询文件，导出任务rid：{} 准备查询{}条数据", tjExport.getRid(), count);
        this.generateSplitFile(tjExport, conditionPo, count, true, Convert.toStr(conditionPo.getCheckLevel()));
    }

    /**
     * @Description: 个案审核查询生成Excel文件的数据
     *
     * @MethodAuthor pw,2022年12月27日
     */
    private void auditGenerateCalExportVo(TdTjExport tjExport) throws Exception{
        String exportCondition = tjExport.getExportCondition();
        if(StringUtils.isBlank(exportCondition)){
            throw new RuntimeException("导出条件不允许为空");
        }
        BhkCalculationAuditQueryPO auditQueryPO = JSONObject.parseObject(exportCondition, BhkCalculationAuditQueryPO.class);
        if(null == auditQueryPO){
            throw new RuntimeException("导出条件解析异常");
        }
        if(StringUtils.isBlank(auditQueryPO.getCheckLevel()) || null == auditQueryPO.getZoneType()){
            throw new RuntimeException("缺少关键参数checkLevel或者zoneType");
        }
        BhkAuditQueryPO queryPO = new BhkAuditQueryPO();
        try{
            queryPO.setIfNeedEnctryInfo(auditQueryPO.getIfNeedEnctryInfo());
            queryPO.setCheckLevel(Integer.parseInt(auditQueryPO.getCheckLevel()));
            queryPO.setZoneType(auditQueryPO.getZoneType());
            if(StringUtils.isNotBlank(auditQueryPO.getSearchUnitId())){
                List<Integer> unitRidList = Arrays.stream(auditQueryPO.getSearchUnitId().split(","))
                        .map(v -> Integer.parseInt(v)).distinct().collect(Collectors.toList());
                queryPO.setSearchUnitIdList(unitRidList);
            }
            queryPO.setSearchZoneCode(null == auditQueryPO.getZonecode() ? null :
                    ZoneUtil.zoneSelect(auditQueryPO.getZonecode()));
            queryPO.setSearchCrptName(auditQueryPO.getCrptName());
            queryPO.setSearchCreditCode(auditQueryPO.getCreditCode());
            queryPO.setSearchPersonName(auditQueryPO.getPersonName());
            queryPO.setSearchIdc(auditQueryPO.getPersonIdc());
            queryPO.setZkBhkCode(auditQueryPO.getZkBhkCode());
            String searchBhkBdate = null;
            if(StringUtils.isNotBlank(auditQueryPO.getSearchBhkBdate())){
                Date date = DateUtils.parseDate(auditQueryPO.getSearchBhkBdate());
                if(null == date){
                    throw new RuntimeException("参数searchBhkBdate日期转换异常："+auditQueryPO.getSearchBhkBdate());
                }
                searchBhkBdate = DateUtils.formatDate(date, null)+" 00:00:00";
            }
            String searchBhkEdate = null;
            if(StringUtils.isNotBlank(auditQueryPO.getSearchBhkEdate())){
                Date date = DateUtils.parseDate(auditQueryPO.getSearchBhkEdate());
                if(null == date){
                    throw new RuntimeException("参数searchBhkEdate日期转换异常："+auditQueryPO.getSearchBhkEdate());
                }
                searchBhkEdate = DateUtils.formatDate(date, null)+" 23:59:59";
            }
            queryPO.setSearchBhkBdate(searchBhkBdate);
            queryPO.setSearchBhkEdate(searchBhkEdate);
            if(StringUtils.isNotBlank(auditQueryPO.getSelectOnGuardIds())){
                List<Integer> onGuardRidList = Arrays.stream(auditQueryPO.getSelectOnGuardIds().split(","))
                        .map(v -> Integer.parseInt(v)).distinct().collect(Collectors.toList());
                queryPO.setSelectOnGuardIds(onGuardRidList);
            }
            if(StringUtils.isNotBlank(auditQueryPO.getSelectBadRsnIds())){
                List<Integer> badRsnRidList = Arrays.stream(auditQueryPO.getSelectBadRsnIds().split(","))
                        .map(v -> Integer.parseInt(v)).distinct().collect(Collectors.toList());
                queryPO.setSelectBadRsnIds(badRsnRidList);
            }
            if(StringUtils.isNotBlank(auditQueryPO.getJcTypes())){
                List<Integer> jcTypeList = Arrays.stream(auditQueryPO.getJcTypes().split(","))
                        .map(v -> Integer.parseInt(v)).distinct().collect(Collectors.toList());
                queryPO.setJcTypeList(jcTypeList);
            }
            if(StringUtils.isNotBlank(auditQueryPO.getIfRhks()) &&
                    auditQueryPO.getIfRhks().split(",").length == 1){
                queryPO.setIfRhk(Integer.parseInt(auditQueryPO.getIfRhks().split(",")[0]));
            }
            if(StringUtils.isNotBlank(auditQueryPO.getIfAbnormals()) &&
                    auditQueryPO.getIfAbnormals().split(",").length == 1){
                queryPO.setIfAbnormal(Integer.parseInt(auditQueryPO.getIfAbnormals().split(",")[0]));
            }
            List<Integer> searchStateList = new ArrayList<>();
            if(StringUtils.isNotBlank(auditQueryPO.getStates())){
                List<Integer> stateList = Arrays.stream(auditQueryPO.getStates().split(","))
                        .map(v -> Integer.parseInt(v)).distinct().collect(Collectors.toList());
                queryPO.setStateList(stateList);
                for (Integer state : stateList) {
                    boolean checkLevel3 = new Integer(3).equals(queryPO.getCheckLevel());
                    boolean state0 = new Integer(0).equals(state);
                    boolean state1 = new Integer(1).equals(state);
                    if ((state0 || state1) && checkLevel3) {
                        continue;
                    }
                    searchStateList.add(state);
                }
            }

            if (CollectionUtils.isEmpty(searchStateList)) {
                searchStateList.add(10086);
            }
            queryPO.setSearchStateList(searchStateList);

            String searchRcvBdate = null;
            if(StringUtils.isNotBlank(auditQueryPO.getSearchRcvBdate())){
                Date date = DateUtils.parseDate(auditQueryPO.getSearchRcvBdate());
                if(null == date){
                    throw new RuntimeException("参数searchRcvBdate日期转换异常："+auditQueryPO.getSearchRcvBdate());
                }
                searchRcvBdate = DateUtils.formatDate(date, null)+" 00:00:00";
            }
            String searchRcvEdate = null;
            if(StringUtils.isNotBlank(auditQueryPO.getSearchRcvEdate())){
                Date date = DateUtils.parseDate(auditQueryPO.getSearchRcvEdate());
                if(null == date){
                    throw new RuntimeException("参数searchRcvEdate日期转换异常："+auditQueryPO.getSearchRcvEdate());
                }
                searchRcvEdate = DateUtils.formatDate(date, null)+" 23:59:59";
            }
            queryPO.setSearchRcvBdate(searchRcvBdate);
            queryPO.setSearchRcvEdate(searchRcvEdate);
            //体检类型
            if(StringUtils.isNotBlank(auditQueryPO.getBhkTypes())){
                List<Integer> bhkTypeList = Arrays.stream(auditQueryPO.getBhkTypes().split(","))
                        .map(Integer::parseInt).distinct().collect(Collectors.toList());
                queryPO.setBhkTypeList(bhkTypeList);
            }
            //报告日期
            String startCreateDate = null;
            if(StringUtils.isNotBlank(auditQueryPO.getStartCreateDate())){
                Date date = DateUtils.parseDate(auditQueryPO.getStartCreateDate());
                if(null == date){
                    throw new RuntimeException("参数startCreateDate日期转换异常："+auditQueryPO.getStartCreateDate());
                }
                startCreateDate = DateUtils.formatDate(date, null)+" 00:00:00";
            }
            String endCreateDate = null;
            if(StringUtils.isNotBlank(auditQueryPO.getEndCreateDate())){
                Date date = DateUtils.parseDate(auditQueryPO.getEndCreateDate());
                if(null == date){
                    throw new RuntimeException("参数endCreateDate日期转换异常："+auditQueryPO.getEndCreateDate());
                }
                endCreateDate = DateUtils.formatDate(date, null)+" 23:59:59";
            }
            queryPO.setStartCreateDate(startCreateDate);
            queryPO.setEndCreateDate(endCreateDate);
            //报告出具日期
            String startRptPrintDate = null;
            if(StringUtils.isNotBlank(auditQueryPO.getStartRptPrintDate())){
                Date date = DateUtils.parseDate(auditQueryPO.getStartRptPrintDate());
                if(null == date){
                    throw new RuntimeException("参数startRptPrintDate日期转换异常："+auditQueryPO.getStartRptPrintDate());
                }
                startRptPrintDate = DateUtils.formatDate(date, null)+" 00:00:00";
            }
            String endRptPrintDate = null;
            if(StringUtils.isNotBlank(auditQueryPO.getEndRptPrintDate())){
                Date date = DateUtils.parseDate(auditQueryPO.getEndRptPrintDate());
                if(null == date){
                    throw new RuntimeException("参数endRptPrintDate日期转换异常："+auditQueryPO.getEndRptPrintDate());
                }
                endRptPrintDate = DateUtils.formatDate(date, null)+" 23:59:59";
            }
            queryPO.setStartRptPrintDate(startRptPrintDate);
            queryPO.setEndRptPrintDate(endRptPrintDate);
            queryPO.setSearchZoneCodeEmp(auditQueryPO.getSearchZoneCodeEmp());
            queryPO.setSearchCrptNameEmp(auditQueryPO.getSearchCrptNameEmp());
            queryPO.setSearchCreditCodeEmp(auditQueryPO.getSearchCreditCodeEmp());
            //异常情况
            queryPO.setSearchAbnormals(auditQueryPO.getSearchAbnormals());
            //选择的单危害因素结论
            if(StringUtils.isNotBlank(auditQueryPO.getSearchSelBhkrstIds())){
                List<Integer> searchSelBhkrstIds = Arrays.stream(auditQueryPO.getSearchSelBhkrstIds().split(","))
                        .map(v -> Integer.parseInt(v)).distinct().collect(Collectors.toList());
                queryPO.setSearchSelBhkrstIds(searchSelBhkrstIds);
            }
            
        }catch(Exception e){
            e.printStackTrace();
            throw new RuntimeException("解析导出条件参数异常",e);
        }
        int count = this.calculationFileExportService.findBhkRidCountByQueryPO(queryPO);
        log.info("个案审核导出文件，导出任务rid：{} 准备查询{}条数据", tjExport.getRid(),count);
        generateSplitFile(tjExport,queryPO,count,true,auditQueryPO.getCheckLevel());
    }

    /**
     * @Description: 子文件生成
     *
     * @MethodAuthor pw,2022年12月31日
     */
    private void generateSubFile(SXSSFWorkbook wBook,  Integer rid,int subFileName,String fileName,String path) throws Exception{
        log.info("子文件生成中。。。。。。");
        long startTime = System.currentTimeMillis();
        String dirPath = this.virtualDirectory+path+File.separator;
        String filePath = dirPath+fileName+subFileName+".xlsx";
        OutputStream outputStream = null;
        try{
            File file = new File(dirPath);
            file.mkdirs();
            outputStream = new FileOutputStream(new File(filePath));
            wBook.write(outputStream);
            outputStream.flush();
            log.info("导出文件，导出任务rid：{} 子文件生成成功 地址:{} 用时：{}", rid,
                    filePath,
                    (System.currentTimeMillis() - startTime));
        }catch(Exception e){
            e.printStackTrace();
            log.error("导出文件，导出任务rid：{} 子文件生成异常", rid,e);
            throw new RuntimeException(e);
        }finally {
            if(null != outputStream){
                outputStream.close();
            }
            if(null != wBook){
                wBook.close();
            }
        }
    }

    /**
     * @Description: 线程调用的个案审核导出文件
     *
     * @MethodAuthor pw,2022年12月27日
     */
    private Map<Integer, List<CalExportVo>> auditGenerateCalExportVoHelper(BhkAuditQueryPO queryPO, Integer exportRid,
                                                                           int count) throws Exception{
        long startTime = System.currentTimeMillis();
        Integer pageNum = null != queryPO.getEndRow() ? queryPO.getEndRow()/1000 : null;
        if(null != queryPO.getEndRow() && queryPO.getEndRow() > count){
            queryPO.setEndRow(count);
        }
        List<Integer> bhkRidList = this.calculationFileExportService.findBhkRidListByQueryPO(queryPO);
        log.info("个案审核导出文件，导出任务rid：{} {} 并发查询主表rid集合用时：{}ms",exportRid,
                (null == pageNum ? "" : "第"+pageNum+"轮"),
                (System.currentTimeMillis()-startTime));
        startTime = System.currentTimeMillis();
        List<CalExportVo> queryList = mixResultVOByBhkRidList(bhkRidList, queryPO.getIfNeedEnctryInfo(),true, queryPO.getCheckLevel(), queryPO.getZoneType(), false,false);
        log.info("个案审核导出文件，导出任务rid：{} {} 并发合成{}条数据用时：{}ms",exportRid,
                (null == pageNum ? "" : "第"+pageNum+"轮"),
                (null == queryList ? 0 : queryList.size()),
                (System.currentTimeMillis()-startTime));
        Map<Integer, List<CalExportVo>> resultMap = new HashMap<>(1);
        resultMap.put(null == pageNum ? -1 : pageNum,
                null == queryList ? new ArrayList<>() : queryList);
        return resultMap;
    }


    /**
     * <p>描述：处理数据并生成文件，若超过最大数量，则生成子文件</p>
     * @param tjExport
     * @param queryPO 查询条件PO
     * @param count 数据总量
     * @param ifAudit true个案审核
     *
     * @return
     * @Author: 龚哲,2022/1/4 11:43,generateSplitFile
     */
    private void generateSplitFile(TdTjExport tjExport,Object queryPO,int count,boolean ifAudit,String checkLevel) throws Exception{
        long startTime = System.currentTimeMillis();
        String extends1 = tjExport.getFkByBusTypeId().getExtends1();
        String title = ifAudit?"个案审核":"个案查询";
        if("2".equals(extends1)){
            title = "体检最新档案";
        }
        String uuid = StringUtils.genUUID();
        String path = this.exportRootFolder+uuid;
        OutputStream outputStream = null;
        SXSSFWorkbook wBook = null;
        SXSSFSheet sheet = null;
        int rowAccess=1000;
        //sheet总列数
        Integer colCount = null;
        String exportItems = null;
        //是否主动检测版本
        boolean ifActive=false;
        try{
            if(null == exportItems){
                JSONObject json = JSONObject.parseObject(tjExport.getExportCondition());
                if(json.containsKey("exportItems")){
                    exportItems = json.getString("exportItems");
                }
                if(json.containsKey("ifActiveMonitor") && "1".equals(json.getString("ifActiveMonitor"))){
                    ifActive=true;
                }
            }
            wBook = new SXSSFWorkbook(rowAccess);
            initCellStyles(wBook); // 初始化样式
            sheet = wBook.createSheet();
            //固定头部两行
            sheet.createFreezePane(0,1);
            colCount = initAuditXlsxHead(wBook, sheet,exportItems,ifAudit,checkLevel,ifActive,extends1,this.ifExportZkBhkCode);
        }catch(Exception e){
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        int size = 0;
        int totalNum = 0;
        if(count > 0){
            size = count/1000;
            if(count%1000 > 0){
                size += 1;
            }
            ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                    .setNameFormat("generateSplitFile-thread-%d").build();
            // 实例化线程池
            ExecutorService executorPool = new ThreadPoolExecutor(childPoolSize, 200, 0,
                    TimeUnit.MILLISECONDS,
                    new ArrayBlockingQueue<>(10240),namedThreadFactory,new CalculationThreadExecutionHandler());
            CompletionService completionService = new ExecutorCompletionService(executorPool);
            List<Future<Map<Integer, List<CalExportVo>>>> futureList = new ArrayList<>(1000);
            if ("1".equals(extends1)){
                //个案查询
                for(int i = 1;i <= size;i++){
                    TjPersonSearchConditionQueryPO bhkAuditQueryPO = new TjPersonSearchConditionQueryPO();
                    ObjectCopyUtil.copyProperties(queryPO, bhkAuditQueryPO);
                    //查询条件“体检类型”调整
                    // 同时勾选“职业健康检查”、“放射卫生健康检查”或都不勾选则查询体检类型为3、4的数据(无需特殊处理)
                    // 仅勾选“放射卫生健康检查”则查询体检类型为4的数据(无需特殊处理)
                    // 仅勾选“职业健康检查”时，调整为查询 “体检类型 BHK_TYPE”为 3和4 ，并且“是否纯放射”为“0：否”或空的数据
                    if (!CollectionUtils.isEmpty(bhkAuditQueryPO.getSearchBhkType())
                            && bhkAuditQueryPO.getSearchBhkType().size() == 1
                            && (new Integer(3).equals(bhkAuditQueryPO.getSearchBhkType().get(0)))) {
                            bhkAuditQueryPO.setSearchBhkTypeHas3(true);
                    }
                    bhkAuditQueryPO.setStartRow(1+(i-1)*1000);
                    bhkAuditQueryPO.setEndRow(i*1000);
                    final TjPersonSearchConditionQueryPO finalQueryPO = bhkAuditQueryPO;
                    Future<Map<Integer, List<CalExportVo>>> future = completionService.submit(() -> auditGeneratePersonCalExportVoHelper(finalQueryPO, tjExport.getRid(), count));
                    futureList.add(future);
                }
            }else if("2".equals(extends1)){
                //体检最新档案
                for(int i = 1;i <= size;i++){
                    TjPersonSearchConditionPO bhkAuditQueryPO = new TjPersonSearchConditionPO();
                    ObjectCopyUtil.copyProperties(queryPO, bhkAuditQueryPO);
                    bhkAuditQueryPO.setStartRow(1+(i-1)*1000);
                    bhkAuditQueryPO.setEndRow(i*1000);
                    final TjPersonSearchConditionPO finalQueryPO = bhkAuditQueryPO;
                    Future<Map<Integer, List<CalExportVo>>> future = completionService.submit(() -> auditGenerateTjNewestRecExportVoHelper(finalQueryPO, tjExport.getRid(), count));
                    futureList.add(future);
                }
            }else if("4".equals(extends1)){
                //个案审核查询
                for(int i = 1;i <= size;i++){
                    TjPersonAuditConditionPO auditConditionPO = new TjPersonAuditConditionPO();
                    ObjectCopyUtil.copyProperties(queryPO, auditConditionPO);
                    auditConditionPO.setStartRow(1+(i-1)*1000);
                    auditConditionPO.setEndRow(i*1000);
                    final TjPersonAuditConditionPO finalQueryPO = auditConditionPO;
                    Future<Map<Integer, List<CalExportVo>>> future = completionService.submit(() -> auditGenerateQueryExportVoHelper(finalQueryPO, tjExport.getRid(), count));
                    futureList.add(future);
                }

            }else{
               //个案审核
               for(int i = 1;i <= size;i++){
                   BhkAuditQueryPO bhkAuditQueryPO = new BhkAuditQueryPO();
                   ObjectCopyUtil.copyProperties(queryPO, bhkAuditQueryPO);
                   bhkAuditQueryPO.setStartRow(1+(i-1)*1000);
                   bhkAuditQueryPO.setEndRow(i*1000);
                   final BhkAuditQueryPO finalQueryPO = bhkAuditQueryPO;
                   Future<Map<Integer, List<CalExportVo>>> future = completionService.submit(() -> auditGenerateCalExportVoHelper(finalQueryPO, tjExport.getRid(), count));
                   futureList.add(future);
               }
           }
            queryPO = null;
            boolean threadErrFlag = false;
            try{
                if(!CollectionUtils.isEmpty(futureList)){
                    for(int i=0; i<futureList.size(); i++){
                        if(threadErrFlag){
                            log.error("导出任务rid：{} 处理数据并生成文件的线程异常，退出遍历线程取值", tjExport.getRid());
                            break;
                        }
                        Future<Map<Integer, List<CalExportVo>>> future = futureList.get(i);
                        long waitTime = System.currentTimeMillis();
                        log.info("导出任务rid：{} 准备获取当前下标数据结果，当前下标：{} ",tjExport.getRid(), i);
                        Map<Integer, List<CalExportVo>> tmpMap = future.get();
                        long useTime = System.currentTimeMillis() - waitTime;
                        if(useTime > 5000){
                            log.info("导出任务rid：{} 获取当前下标数据结果，当前下标：{} 等待时间：{}ms",tjExport.getRid(),
                                    i ,useTime);
                        }
                        if(!CollectionUtils.isEmpty(tmpMap)){
                            for(Map.Entry<Integer, List<CalExportVo>> mapEntity : tmpMap.entrySet()){
                                if(null == mapEntity.getKey() || CollectionUtils.isEmpty(mapEntity.getValue())){
                                    continue;
                                }
                                //由于线程按先进先获取 当前线程未返回 一直等待到线程返回 所以这里获取的时候是按载入顺序获取的 这里的page 就是按顺序获取的页码
                                int page = mapEntity.getKey();
                                List<CalExportVo> dataList = mapEntity.getValue();
                                totalNum += dataList.size();
                                log.info("导出任务rid：{} 分段page：{} ", tjExport.getRid(), page);
                                initAuditXlsxData(wBook,sheet,dataList,exportItems,ifAudit,colCount,rowAccess,checkLevel,ifActive,extends1,tjExport);
                                if(count> MAX_DATA_NUM){
                                    int fileIndex = totalNum%MAX_DATA_NUM;
                                    if(fileIndex == 0){
                                        //写文件 并重新赋值outputStream  wBook  sheet
                                        generateSubFile(wBook,tjExport.getRid(), totalNum/MAX_DATA_NUM,
                                                tjExport.getExportFileName(), path);
                                        if(i == futureList.size()-1){
                                            wBook = null;
                                        }else{
                                            //创建新的Excel
                                            wBook = new SXSSFWorkbook(rowAccess);
                                            sheet = wBook.createSheet();
                                            //固定头部两行
                                            sheet.createFreezePane(0,1);
                                            colCount = initAuditXlsxHead(wBook, sheet,exportItems,ifAudit,checkLevel,ifActive,extends1,this.ifExportZkBhkCode);
                                        }
                                    }else if(i == futureList.size()-1){
                                        //写文件 写完设置outputStream  wBook  sheet null
                                        generateSubFile(wBook, tjExport.getRid(), totalNum/MAX_DATA_NUM+1,
                                                tjExport.getExportFileName(), path);
                                        wBook = null;
                                    }
                                }
                                //处理完清空list 便于回收
                                dataList.clear();
                            }
                        }
                    }
                }
            }catch(Exception e){
                threadErrFlag = true;
                e.printStackTrace();
                throw new RuntimeException(e);
            }finally {
                log.info(title + "，导出任务rid：{} 线程池关闭", tjExport.getRid());
                executorPool.shutdownNow();
            }
        }
        try{
            if(null != wBook && count <= MAX_DATA_NUM){
                log.info("生成文件中。。。。。。");
                path += ".xlsx";
                outputStream = new FileOutputStream(new File(this.virtualDirectory+path));
                wBook.write(outputStream);
                outputStream.flush();
            }
            if(count > MAX_DATA_NUM){
                //多文件最终会生成一个zip文件
                path += ".zip";
            }
            log.info( "{}导出文件，导出任务rid：{} 经过{}轮查询 导出数据{}条 用时：{} {}",title, tjExport.getRid(),
                    size, totalNum,(System.currentTimeMillis() - startTime), count > MAX_DATA_NUM ? "稍后生成zip文件" : "");
            tjExport.setExportFilePath(path);
            tjExport.setExportFileDate(new Date());
        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException(e);
        }finally {
            if(null != outputStream){
                outputStream.close();
            }
        }
    }

    /**
     *  <p>方法描述：个案审核查询</p>
     * @MethodAuthor hsj 2024-08-23 17:03
     */
    private Object auditGenerateQueryExportVoHelper(TjPersonAuditConditionPO queryPO, Integer exportRid, int count) throws Exception{
        long startTime = System.currentTimeMillis();
        Integer pageNum = null != queryPO.getEndRow() ? queryPO.getEndRow()/1000 : null;
        if(null != queryPO.getEndRow() && queryPO.getEndRow() > count){
            queryPO.setEndRow(count);
        }
        List<Integer> bhkRidList = this.calculationFileExportService.findAuditGenerateQueryList(queryPO);
        log.info("个案审核查询导出文件，导出任务rid：{} {} 并发查询主表rid集合用时：{}ms",exportRid,
                (null == pageNum ? "" : "第"+pageNum+"轮"),
                (System.currentTimeMillis()-startTime));
        startTime = System.currentTimeMillis();
        List<CalExportVo> queryList = mixResultVOByBhkRidList(bhkRidList,true,true, queryPO.getCheckLevel(), queryPO.getZoneType(), false,true);
        log.info("个案审核查询导出文件，导出任务rid：{} {} 并发合成{}条数据用时：{}ms",exportRid,
                (null == pageNum ? "" : "第"+pageNum+"轮"),
                (null == queryList ? 0 : queryList.size()),
                (System.currentTimeMillis()-startTime));
        Map<Integer, List<CalExportVo>> resultMap = new HashMap<>(1);
        resultMap.put(null == pageNum ? -1 : pageNum,
                null == queryList ? new ArrayList<>() : queryList);
        return resultMap;
    }



    /**
     * <p>描述：根据导出条件Json获取到主表rid集合</p>
     *
     * @param tjExport
     * @Author: 龚哲, 2021/12/21 8:35,findBhkRidsByExportConditionJson
     */
    private void auditGeneratePersonCalExportVo(TdTjExport tjExport) throws Exception {
        String jsonString = tjExport.getExportCondition();
        if (StringUtils.isNotBlank(jsonString)) {
            TjPersonSearchConditionPO conditionPo = JSONObject.parseObject(jsonString,TjPersonSearchConditionPO.class);
            TjPersonSearchConditionQueryPO queryPO = new TjPersonSearchConditionQueryPO();
            if(1 == conditionPo.getType()) {
                queryPO.setIfNeedEnctryInfo(true);
            }else if(2 == conditionPo.getType()) {
                queryPO.setIfNeedEnctryInfo(false);
            }
            queryPO.setSearchPersonName(conditionPo.getSearchPersonName());
            queryPO.setSearchIDC(conditionPo.getSearchIDC());
            queryPO.setZkBhkCode(conditionPo.getZkBhkCode());
            if(StringUtils.isNotBlank(conditionPo.getSearchUnitId())){
                List<Integer> list = Arrays.stream(conditionPo.getSearchUnitId().split(","))
                        .map(v -> Integer.parseInt(v)).distinct().collect(Collectors.toList());
                queryPO.setSearchUnitId(list);
            }
            queryPO.setSelectAgeAnalyDetails(conditionPo.getSelectAgeAnalyDetails());
            queryPO.setSelectWorkAnalyDetails(conditionPo.getSelectWorkAnalyDetails());
            queryPO.setSearchZoneCode(conditionPo.getSearchZoneCode());
            queryPO.setSearchCrptName(conditionPo.getSearchCrptName());
            if(conditionPo.getSearchBhkType()!=null && conditionPo.getSearchBhkType().length>0){
                List<Integer> list = Arrays.stream(conditionPo.getSearchBhkType())
                        .map(v -> Integer.parseInt(v)).distinct().collect(Collectors.toList());
                queryPO.setSearchBhkType(list);
            }
            queryPO.setSearchStartTime(conditionPo.getSearchStartTime());
            queryPO.setSearchEndTime(conditionPo.getSearchEndTime());
            queryPO.setStartRptPrintDate(conditionPo.getStartRptPrintDate());
            queryPO.setEndRptPrintDate(conditionPo.getEndRptPrintDate());
            queryPO.setStartCreateDate(conditionPo.getStartCreateDate());
            queryPO.setEndCreateDate(conditionPo.getEndCreateDate());
            if(StringUtils.isNotBlank(conditionPo.getSelectOnGuardIds())){
                List<Integer> list = Arrays.stream(conditionPo.getSelectOnGuardIds().split(","))
                        .map(v -> Integer.parseInt(v)).distinct().collect(Collectors.toList());
                queryPO.setSelectOnGuardIds(list);
            }
            if(StringUtils.isNotBlank(conditionPo.getSelectBadRsnIds())){
                List<Integer> list = Arrays.stream(conditionPo.getSelectBadRsnIds().split(","))
                        .map(v -> Integer.parseInt(v)).distinct().collect(Collectors.toList());
                queryPO.setSelectBadRsnIds(list);
            }
            if(StringUtils.isNotBlank(conditionPo.getSearchSelBhkrstIds())){
                List<Integer> list = Arrays.stream(conditionPo.getSearchSelBhkrstIds().split(","))
                        .map(v -> Integer.parseInt(v)).distinct().collect(Collectors.toList());
                queryPO.setSearchSelBhkrstIds(list);
            }
            queryPO.setSearchItemIds(conditionPo.getSearchItemIds());
            queryPO.setSearchBhkNum(conditionPo.getSearchBhkNum()==null?null:Integer.parseInt(conditionPo.getSearchBhkNum()));
            queryPO.setSearchPsnType(conditionPo.getSearchPsnType()==null?null:Integer.parseInt(conditionPo.getSearchPsnType()));
            if(conditionPo.getSearchJcType()!=null && conditionPo.getSearchJcType().length>0){
                List<Integer> list = Arrays.stream(conditionPo.getSearchJcType())
                        .map(v -> Integer.parseInt(v)).distinct().collect(Collectors.toList());
                queryPO.setSearchJcType(list);
            }
            queryPO.setIfAdmin(conditionPo.getIfAdmin());
            //非质控员 资质未注册
            if(!conditionPo.getIfAdmin() && StringUtils.isBlank(conditionPo.getSearchUnitId())){
                return ;
            }
            //用工单位地区
            queryPO.setSearchEntrustCrptZoneGb(conditionPo.getSearchEntrustCrptZoneGb());
            //用工单位名称
            queryPO.setSearchEntrustCrptName(conditionPo.getSearchEntrustCrptName());
            //选择的主检结论
            if(StringUtils.isNotBlank(conditionPo.getSearchSelMhkrstIds())){
                List<Integer> list = Arrays.stream(conditionPo.getSearchSelMhkrstIds().split(","))
                        .map(v -> Integer.parseInt(v)).distinct().collect(Collectors.toList());
                queryPO.setSearchSelMhkrstIds(list);
            }
            //查询条件“体检类型”调整
            // 同时勾选“职业健康检查”、“放射卫生健康检查”或都不勾选则查询体检类型为3、4的数据(无需特殊处理)
            // 仅勾选“放射卫生健康检查”则查询体检类型为4的数据(无需特殊处理)
            // 仅勾选“职业健康检查”时，调整为查询 “体检类型 BHK_TYPE”为 3和4 ，并且“是否纯放射”为“0：否”或空的数据
            if (!CollectionUtils.isEmpty(queryPO.getSearchBhkType())
                    && queryPO.getSearchBhkType().size() == 1
                    && (new Integer(3).equals(queryPO.getSearchBhkType().get(0)))) {
                queryPO.setSearchBhkTypeHas3(true);
            }
            int count = this.bhkService.findTjPersonSearchCounts(queryPO);
            log.info("个案查询导出文件，导出任务rid：{} 准备查询{}条数据", tjExport.getRid(),count);
            generateSplitFile(tjExport,queryPO,count,false,null);
        }
    }

    /**
     * <p>描述：线程调用的个案查询导出文件</p>
     * @param queryPO
     * @param exportRid
     * @param count
     *
     * @return {@link Map< Integer, List< CalExportVo>>}
     * @Author: 龚哲,2021/12/28 9:37,auditGeneratePersonCalExportVoHelper
     */
    private Map<Integer, List<CalExportVo>> auditGeneratePersonCalExportVoHelper(TjPersonSearchConditionQueryPO queryPO, Integer exportRid,
                                                                           int count) throws Exception{
        long startTime = System.currentTimeMillis();
        Integer pageNum = null != queryPO.getEndRow() ? queryPO.getEndRow()/1000 : null;
        if(null != queryPO.getEndRow() && queryPO.getEndRow() > count){
            queryPO.setEndRow(count);
        }
        List<Integer> searchRids = this.bhkService.findTjPersonSearch(queryPO);
        log.info("个案查询导出文件，导出任务rid：{} {} 并发查询主表rid集合用时：{}ms",exportRid,
                (null == pageNum ? "" : "第"+pageNum+"轮"),
                (System.currentTimeMillis()-startTime));
        startTime = System.currentTimeMillis();
        List<CalExportVo> searchCalExports = mixResultVOByBhkRidList(searchRids,queryPO.getIfNeedEnctryInfo(),false,null,null, false,false);
        log.info("个案查询导出文件，导出任务rid：{} {} 并发合成{}条数据用时：{}ms",exportRid,
                (null == pageNum ? "" : "第"+pageNum+"轮"),
                (null == searchCalExports ? 0 : searchCalExports.size()),
                (System.currentTimeMillis()-startTime));
        Map<Integer, List<CalExportVo>> resultMap = new HashMap<>(1);
        resultMap.put(null == pageNum ? -1 : pageNum,
                null == searchCalExports ? new ArrayList<>() : searchCalExports);
        return resultMap;
    }

    /**
     * <p>方法描述：体检最新档案生成Excel文件的数据 </p>
     * @MethodAuthor： pw 2022/7/2
     **/
    private void auditGenerateTjNewestRecExportVo(TdTjExport tjExport) throws Exception{
        String jsonString = tjExport.getExportCondition();
        if (StringUtils.isNotBlank(jsonString)) {
            TjPersonSearchConditionPO conditionPo = JSONObject.parseObject(jsonString,TjPersonSearchConditionPO.class);
            int count = this.calculationFileExportService.findTjNewestRecCount(conditionPo);
            log.info("体检最新档案导出文件，导出任务rid：{} 准备查询{}条数据", tjExport.getRid(),count);
            generateSplitFile(tjExport,conditionPo,count,false,null);
        }
    }

    /**
     * @Description: 初始化个案审核表头
     * @param wb
     * @param sheet
     * @param exportItems 体检项目id
     * @param ifAudit 是否个案审核的数据 true是
     *
     * @return int 返回最大列数下标
     * @MethodAuthor pw,2021年12月20日
     */
    private int initAuditXlsxHead(SXSSFWorkbook wb, SXSSFSheet sheet, String exportItems, boolean ifAudit, String checkLevel, boolean ifActive,String extends1) {
        return initAuditXlsxHead(wb, sheet, exportItems, ifAudit, checkLevel, ifActive, extends1, this.ifExportZkBhkCode != null && this.ifExportZkBhkCode);
    }
    
    private int initAuditXlsxHead(SXSSFWorkbook wb, SXSSFSheet sheet, String exportItems, boolean ifAudit, String checkLevel, boolean ifActive, String extends1, boolean ifExportZkBhkCode) {
        // 初始化样式
        CellStyle style = createHeadCellStyle(wb);
        Row headerRow = sheet.createRow(0);
		// 创建第二行以及数据
        List<String> cellValueList = new ArrayList<>();
        cellValueList.add("姓名");
        if(ifExportZkBhkCode) {
            cellValueList.add("质控编号");
        }
        cellValueList.addAll(Arrays.asList("体检编号", "性别", "年龄", "证件类型", "证件号码", "联系电话", "用人单位名称", "用人单位联系人"
                , "用人单位联系电话", "用人单位所属地区", "用人单位通讯地址", "用人单位社会信用代码", "用人单位规模", "用人单位经济类型", "用人单位行业分类"
                , "用工单位名称", "用工单位联系人", "用工单位联系电话", "用工单位所属地区", "用工单位通讯地址", "用工单位社会信用代码", "用工单位规模", "用工单位经济类型", "用工单位行业分类"
                , "监测类型", "在岗状态", "所属部门", "工种代码","工种","防护用品佩戴情况","总工龄", "接害工龄", "体检类型","是否复检","体检危害因素"
                , "体检危害因素明细结论", "接触危害因素" ));
        if (ifActive) {
            cellValueList.add("主动监测危害因素");
        }
        addSection(cellValueList, new String[]{"人员职业史-起止日期", "人员职业史-工作单位名称", "人员职业史-部门车间", "人员职业史-防护措施"
                , "吸烟史-目前吸烟情况", "吸烟史-吸烟史（年）", "吸烟史-吸烟史（月）", "吸烟史-平均每天吸烟量（支）"
                , "饮酒史-饮酒情况", "饮酒史-酒量（ml/天） ", "饮酒史-酒龄（年）"
                ,"婚姻史-结婚日期","婚姻史-配偶接触放射线情况","婚姻史-配偶职业及健康情况"
                , "既往病史-既往病史疾病名称", "既往病史-诊断日期", "既往病史-诊断单位", "既往病史-治疗经过", "既往病史-转归"
                , "放射史-起止日期", "放射史-工作单位名称", "放射史-每日工作时数或工作量", "放射史-职业史累积受照剂量", "放射史-职业史过量照射史", "放射史-职业照射种类", "放射史-放射线种类"
                , "其他-家族史", "其他-个人史", "其他-其他", "症状-自觉症状", "症状-其他症状"});

        //体检项目标题
        this.processTjItems(cellValueList,exportItems);
        addSection(cellValueList, new String[]{
                "主检结论", "主检建议", "职业禁忌证名称", "疑似职业病名称","体检机构", "体检日期", "报告出具日期", "报告日期"
        });
        //如果是个案审核，添加以下字段 【接收日期】、【状态】、【是否异常】、【异常原因】
        if(ifAudit){
            if(!"4".equals(extends1)){
                cellValueList.add("接收日期");
            }
            cellValueList.add("审核状态");
            //3级审核
            if("3".equals(checkLevel)){
                addSection(cellValueList,new String[]{"区县级审核日期", "区县级审核意见", "市级审核日期", "市级审核意见", "省级审核日期", "省级审核意见"});
            }else if("2".equals(checkLevel)){
                addSection(cellValueList,new String[]{"区县级审核日期", "区县级审核意见",  "省级审核日期", "省级审核意见"});
            }
            addSection(cellValueList,new String[]{"是否异常", "异常原因"});
            if(this.ifShowErrMsg){
                cellValueList.add("国家退回原因");
            }

        }
        this.createCellsWithStyle(headerRow, cellValueList, style);
        int colInd = cellValueList.size();
        this.setCellWidth(sheet,colInd,ifAudit,ifExportZkBhkCode);
        return colInd;
    }

    /**
     * <p>方法描述：线程调用的体检最新档案</p>
     * @MethodAuthor： pw 2022/7/2
     **/
    private Map<Integer, List<CalExportVo>> auditGenerateTjNewestRecExportVoHelper(TjPersonSearchConditionPO queryPO, Integer exportRid,
                                                                                   int count) throws Exception{
        long startTime = System.currentTimeMillis();
        Integer pageNum = null != queryPO.getEndRow() ? queryPO.getEndRow()/1000 : null;
        if(null != queryPO.getEndRow() && queryPO.getEndRow() > count){
            queryPO.setEndRow(count);
        }
        List<Integer> searchRids = this.calculationFileExportService.findTjNewestRecRidList(queryPO);
        log.info("体检最新档案导出文件，导出任务rid：{} {} 并发查询主表rid集合用时：{}ms",exportRid,
                (null == pageNum ? "" : "第"+pageNum+"轮"),
                (System.currentTimeMillis()-startTime));
        startTime = System.currentTimeMillis();
        List<CalExportVo> searchCalExports = mixResultVOByBhkRidList(searchRids,true,false,null,null, true,false);
        log.info("体检最新档案导出文件，导出任务rid：{} {} 并发合成{}条数据用时：{}ms",exportRid,
                (null == pageNum ? "" : "第"+pageNum+"轮"),
                (null == searchCalExports ? 0 : searchCalExports.size()),
                (System.currentTimeMillis()-startTime));
        Map<Integer, List<CalExportVo>> resultMap = new HashMap<>(1);
        resultMap.put(null == pageNum ? -1 : pageNum,
                null == searchCalExports ? new ArrayList<>() : searchCalExports);
        return resultMap;
    }

    /**
     *  <p>方法描述：表个宽度设置</p>
     * @MethodAuthor hsj 2025-03-05 14:06
     */
    private void setCellWidth(SXSSFSheet sheet, int colInd,Boolean ifAudit) {
        setCellWidth(sheet, colInd, ifAudit, this.ifExportZkBhkCode != null && this.ifExportZkBhkCode);
    }
    
    private void setCellWidth(SXSSFSheet sheet, int colInd,Boolean ifAudit, boolean ifExportZkBhkCode) {
        for(int i = 0 ; i < colInd ; i++)	{
            sheet.setColumnWidth(i, 5000);
            // 如果导出质控编号，设置质控编号列（第2列，索引为1）的宽度
            if(ifExportZkBhkCode && i == 1) {
                sheet.setColumnWidth(i, 6000); // 质控编号列稍微宽一些
            }
            if(ifAudit){
                //个案审核才需要处理最后一列异常原因的宽度
                if(this.ifShowErrMsg) {
                    sheet.setColumnWidth(colInd - 2, 5000 * 5);
                }
                sheet.setColumnWidth(colInd-1, 5000*5);
            }
        }
    }

    /**
     *  <p>方法描述：文件表头导出样式</p>
     * @MethodAuthor hsj 2025-03-03 14:19
     */
    private CellStyle createHeadCellStyle(SXSSFWorkbook wb) {
        //设置表头字体
        Font headfont = wb.createFont();
        headfont.setFontName("宋体");
        headfont.setFontHeightInPoints((short) 10);// 字体大小
        headfont.setBold(true);// 加粗
        // 设置表头样式
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);// 设置水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 上下居中
        style.setFont(headfont);
        return style;
    }
    // 表头封装
    private void addSection(List<String> list, String[] values) {
        list.addAll(Arrays.asList(values));
    }
    /**
     *  <p>方法描述：体检项目标题封装</p>
     * @MethodAuthor hsj 2025-03-03 14:26
     */
    private void processTjItems(List<String> cellValueList,String exportItems) {
        if(StringUtils.isBlank(exportItems))	{
            return;
        }
        String[] split = exportItems.split(",");
        for(String itemId : split)	{
            if(StringUtils.isBlank(itemId))	{
                continue;
            }
            String itemName =this.itemCachMap.get(new Integer(itemId));
            TbTjItems tbTjItems = this.itemCachMaps.get(new Integer(itemId));
            String[] secValue =  new String[]{ itemName+"-检查结果", itemName+"-计量单位", itemName+"-合格标记"};
            addSection(cellValueList,secValue);
            //判断是否为胸片
            if(null != tbTjItems.getItemTag() && "30".equals(tbTjItems.getItemTag()) ){
                cellValueList.add(itemName+"-结果判定");
            }
        }
    }

    /**
     *  <p>方法描述：表头列生成</p>
     * @MethodAuthor hsj 2025-03-03 14:23
     */
    private void createCellsWithStyle(Row row, List<String> cellValueList, CellStyle style) {
        int colInd = 0;
        if(CollectionUtils.isEmpty(cellValueList)){
            return;
        }
        for( String colName : cellValueList){
            Cell c1 = row.createCell(colInd++);
            c1.setCellValue(colName);
            c1.setCellStyle(style);
        }
    }
    /**
     * <p>描述：填充xlsx内容</p>
     * @param wb
     * @param sheet
     * @param list
     * @param exportItems
     * @param ifAudit
     * @param colCount 列数（下标）
     * @param rowAccess
     *
     * @return
     * @Author: 龚哲,2021/12/23 14:35,initAuditXlsxData
     */
    private void initAuditXlsxData(SXSSFWorkbook wb,SXSSFSheet sheet,List<CalExportVo> list,String exportItems,
                                   boolean ifAudit,int colCount,int rowAccess,String checkLevel,boolean ifActive,String extends1) throws Exception {
        initAuditXlsxData(wb, sheet, list, exportItems, ifAudit, colCount, rowAccess, checkLevel, ifActive, extends1, null);
    }
    
    private void initAuditXlsxData(SXSSFWorkbook wb,SXSSFSheet sheet,List<CalExportVo> list,String exportItems,
                                   boolean ifAudit,int colCount,int rowAccess,String checkLevel,boolean ifActive,String extends1, TdTjExport tjExport) throws Exception {
        int rowInd = sheet.getLastRowNum()+1;
        CellStyle style = defaultStyle;
        CellStyle style1 = centerStyle;
        CellStyle style2 = leftStyle;
        style.setWrapText(true);//自动换行
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中

        style1.setAlignment(HorizontalAlignment.CENTER);// 设置水平居中
        style1.setVerticalAlignment(VerticalAlignment.CENTER);// 上下居中

        style2.setWrapText(true);//自动换行
        style2.setAlignment(HorizontalAlignment.LEFT);// 设置居左
        style2.setVerticalAlignment(VerticalAlignment.CENTER);

        for(CalExportVo vo:list){
            Row row = sheet.createRow(rowInd);
            for (int i = 0; i <= colCount; i++) {
                row.createCell(i);//创建单元格
            }
            //使用动态下标 避免每次插入列 都要调整下标
            int cellIndex = 0;
            row.getCell(cellIndex++).setCellValue(vo.getName());//姓名
            // 根据配置文件判断是否填充质控编号
            if(this.ifExportZkBhkCode != null && this.ifExportZkBhkCode) {
                row.getCell(cellIndex++).setCellValue(vo.getZkBhkCode());//质控编号
            }
            row.getCell(cellIndex++).setCellValue(vo.getBhkCode());//体检编号
            row.getCell(cellIndex++).setCellValue(vo.getSex());//性别
            row.getCell(cellIndex++).setCellValue(vo.getAge());//年龄
            row.getCell(cellIndex++).setCellValue(vo.getCardType());//证件类型
            row.getCell(cellIndex++).setCellValue(vo.getIdc());//证件号码
            row.getCell(cellIndex++).setCellValue(vo.getLinkTel());//联系电话
            row.getCell(cellIndex++).setCellValue(vo.getWorkUnitName());//用人单位名称
            row.getCell(cellIndex++).setCellValue(vo.getWorkUnitLinkMan());//用人单位联系人
            row.getCell(cellIndex++).setCellValue(vo.getWorkUnitLinkTel());//用人单位联系电话
            row.getCell(cellIndex++).setCellValue(vo.getWorkUnitZoneArea());//用人单位所属地区
            row.getCell(cellIndex++).setCellValue(vo.getWorkUnitAddress());//用人单位通讯地址
            row.getCell(cellIndex++).setCellValue(vo.getWorkUnitCreditCode());//用人单位社会信用代码
            row.getCell(cellIndex++).setCellValue(vo.getWorkUnitCrptSize());//用人单位规模
            row.getCell(cellIndex++).setCellValue(vo.getWorkUnitEconomy());//用人单位经济类型
            row.getCell(cellIndex++).setCellValue(vo.getWorkUnitIndusType());//用人单位行业分类
            row.getCell(cellIndex++).setCellValue(vo.getEmpCrptName());//用工单位名称
            row.getCell(cellIndex++).setCellValue(vo.getEmpLinkMan());//用工单位联系人
            row.getCell(cellIndex++).setCellValue(vo.getEmpLinkPhone());//用工单位联系电话
            row.getCell(cellIndex++).setCellValue(vo.getEmpZone());//用工单位所属地区
            row.getCell(cellIndex++).setCellValue(vo.getEmpAddress());//用工单位通讯地址
            row.getCell(cellIndex++).setCellValue(vo.getEmpInstItutionCode());//用工单位社会信用代码
            row.getCell(cellIndex++).setCellValue(vo.getEmpCrptSize());//用工单位规模
            row.getCell(cellIndex++).setCellValue(vo.getEmpEconomy());//用工单位经济类型
            row.getCell(cellIndex++).setCellValue(vo.getEmpIndusType());//用工单位行业分类
            row.getCell(cellIndex++).setCellValue(vo.getJcType());//监测类型
            row.getCell(cellIndex++).setCellValue(vo.getOnguardState());//在岗状态
            row.getCell(cellIndex++).setCellValue(vo.getDpt());//岗位
            row.getCell(cellIndex++).setCellValue(vo.getWorkNo());//工种代码
            row.getCell(cellIndex++).setCellValue(vo.getWorkName());//工种
            row.getCell(cellIndex++).setCellValue(vo.getProtectEquName());//防护用品佩戴情况
            row.getCell(cellIndex++).setCellValue(vo.getWrklnt());//总工龄
            row.getCell(cellIndex++).setCellValue(vo.getTchbadTotalTime());//接害工龄
            row.getCell(cellIndex++).setCellValue(vo.getBhkType());//体检类型
            row.getCell(cellIndex++).setCellValue(vo.getIfRhk());//是否复检
            row.getCell(cellIndex++).setCellValue(vo.getBadRsnName());//体检危害因素
            row.getCell(cellIndex++).setCellValue(vo.getBadRsnRstName());//体检危害因素明细结论
            row.getCell(cellIndex++).setCellValue(vo.getTouchBadName());//接触危害因素
            if(ifActive){
                row.getCell(cellIndex++).setCellValue(vo.getActiveBadName());//主动检测危害因素
            }
            int tmpCellIndex = cellIndex;
            //人员职业史
            EmhistoryExportVo psnEmhistory = vo.getPsnEmhistory();
            if(psnEmhistory!=null){
                row.getCell(tmpCellIndex++).setCellValue(psnEmhistory.getStastpDate());//起止日期
                row.getCell(tmpCellIndex++).setCellValue(psnEmhistory.getUnitName());//工作单位名称
                row.getCell(tmpCellIndex++).setCellValue(psnEmhistory.getDepartment());//部门车间
                row.getCell(tmpCellIndex++).setCellValue(psnEmhistory.getDefendStep());//防护措施
            }
            cellIndex++;
            cellIndex++;
            cellIndex++;
            cellIndex++;
            tmpCellIndex = cellIndex;
            //吸烟史
            ExmsAndSymptomExportVo exmSym = vo.getExmSym();
            if(exmSym!=null){
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getSmksta());//目前吸烟情况
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getSmkyerqty());//吸烟史（年）
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getSmkmthqty());//吸烟史（月）
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getSmkdayble());//平均每天吸烟量（支）
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getWinsta());//饮酒情况
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getWindaymlx());//烟酒史酒量
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getWinyerqty());//烟酒史酒龄
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getMrydat());//婚姻史结婚日期
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getCplrdtcnd());//配偶接触放射线情况
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getCplprfhthcnd());//配偶职业及健康状况

            }
            cellIndex+=10;
            tmpCellIndex = cellIndex;
            //既往病史
            AnamnesisExportVo anamnesis = vo.getAnamnesis();
            if(anamnesis!=null){
                row.getCell(tmpCellIndex++).setCellValue(anamnesis.getHstnam());//既往病史疾病名称
                row.getCell(tmpCellIndex++).setCellValue(anamnesis.getHstdat());//诊断日期
                row.getCell(tmpCellIndex++).setCellValue(anamnesis.getHstunt());//诊断单位
                row.getCell(tmpCellIndex++).setCellValue(anamnesis.getHstcruprc());//治疗经过
                row.getCell(tmpCellIndex++).setCellValue(anamnesis.getHstlps());//转归
            }
            cellIndex++;
            cellIndex++;
            cellIndex++;
            cellIndex++;
            cellIndex++;
            tmpCellIndex = cellIndex;
            //放射史
            EmhistoryExportVo fsEmhistory = vo.getFsEmhistory();
            if(fsEmhistory!=null){
                row.getCell(tmpCellIndex++).setCellValue(fsEmhistory.getStastpDate());//起止日期
                row.getCell(tmpCellIndex++).setCellValue(fsEmhistory.getUnitName());//工作单位名称
                row.getCell(tmpCellIndex++).setCellValue(fsEmhistory.getPrfwrklod());//每日工作时数或工作量
                row.getCell(tmpCellIndex++).setCellValue(fsEmhistory.getPrfshnvlu());//职业史累积受照剂量
                row.getCell(tmpCellIndex++).setCellValue(fsEmhistory.getPrfexcshn());//职业史过量照射史
                row.getCell(tmpCellIndex++).setCellValue(fsEmhistory.getPrfraysrt2());//职业照射种类
                row.getCell(tmpCellIndex++).setCellValue(fsEmhistory.getFsszl());//放射线种类
            }
            cellIndex++;
            cellIndex++;
            cellIndex++;
            cellIndex++;
            cellIndex++;
            cellIndex++;
            cellIndex++;
            tmpCellIndex = cellIndex;
            //其他、症状
            if(exmSym!=null){
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getJzs());//家族史
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getGrs());//个人史
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getOther());//其他
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getSym());//自觉症状
                row.getCell(tmpCellIndex++).setCellValue(exmSym.getOthsym());//其他症状
            }
            cellIndex++;
            cellIndex++;
            cellIndex++;
            cellIndex++;
            cellIndex++;
            tmpCellIndex = cellIndex;
            //体检项目
            int exportItemLength = 0;
            if(StringUtils.isNotBlank(exportItems))	{
                String[] split = exportItems.split(",");
                List<BhkSubExportVo> itemList = vo.getItemList();
                for(int i = 0; i < split.length;i++){
                    String itemId = split[i];
                    List<BhkSubExportVo> itemIdList = itemList.stream().filter(V->V.getItemRid().toString().equals(itemId)).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(itemIdList)){
                        BhkSubExportVo subExportVo = itemIdList.get(0);
                        if(subExportVo!=null){
                            //结果-2
                            row.getCell(tmpCellIndex + exportItemLength).setCellValue(subExportVo.getItemRst());
                            //计量单位-3
                            row.getCell(tmpCellIndex+1 + exportItemLength).setCellValue(subExportVo.getMsrunt());
                            //合格标记-4
                            row.getCell(tmpCellIndex+2 + exportItemLength).setCellValue(subExportVo.getRglTag());
                            //胸片：结果判定 -5
                            if(Integer.valueOf(30).equals(subExportVo.getItemTag())){
                                row.getCell(tmpCellIndex+3 + exportItemLength).setCellValue(subExportVo.getRstFlag());
                            }
                        }
                    }
                    TbTjItems tbTjItems = this.itemCachMaps.get(new Integer(itemId));
                    //判断是否为胸片
                    if(null != tbTjItems.getItemTag() && "30".equals(tbTjItems.getItemTag()) ){
                        exportItemLength += 4;
                    }else{
                        exportItemLength += 3;
                    }
                }
            }
            row.getCell((cellIndex++)+exportItemLength).setCellValue(vo.getMhkRst());//主检结论
            //主检建议
            row.getCell((cellIndex++)+exportItemLength).setCellValue(vo.getMhkAdv());
            //职业禁忌证名称
            String contraindication = vo.getContraindication();
            List<String> cList = removeDupByContains(StringUtils.string2list(contraindication, "#@，@#"));
            row.getCell((cellIndex++) + exportItemLength).setCellValue(StringUtils.list2string(cList, "，"));
            //疑似职业病名称
            String suOccDisease = vo.getSuOccDisease();
            List<String> soDiseaseList = removeDupByContains(StringUtils.string2list(suOccDisease, "#@，@#"));
            row.getCell((cellIndex++) + exportItemLength).setCellValue(StringUtils.list2string(soDiseaseList, "，"));
            row.getCell((cellIndex++)+exportItemLength).setCellValue(vo.getOrgName());//体检机构
            //因为需要设置值和样式 设置样式的时候+1就行
            row.getCell(cellIndex+exportItemLength).setCellValue(vo.getBhkDate());//体检日期
            row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);//体检日期
            row.getCell(cellIndex+exportItemLength).setCellValue(vo.getRptPrintDate());//报告打印日期
            row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
            row.getCell(cellIndex+exportItemLength).setCellValue(vo.getFillDate());//填报日期
            row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
            if(ifAudit){//个案审核新增字段
                if(!"4".equals(extends1)){
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getReciveDate());//接收日期
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                }
                row.getCell(cellIndex+exportItemLength).setCellValue(vo.getState());//状态
                row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                //审核信息赋值
                initAuditAdv(wb,row,checkLevel,vo,exportItemLength, cellIndex);
            }
            rowInd++;
            if(rowInd % rowAccess == 0) {
                sheet.flushRows();//到临界值刷新缓存
            }
        }
    }

    /**
     *  <p>方法描述：饮酒情况</p>
     * @MethodAuthor hsj 2025-03-05 16:36
     */
    public String getDrinkingDescription(String drinkingStatus) {
        if (StringUtils.isBlank(drinkingStatus)) {
            return "";
        }
        switch (drinkingStatus) {
            case "0":
                return "不饮酒";
            case "1":
                return "偶尔饮";
            case "2":
                return "经常饮";
            default:
                return "";
        }
    }

    /**
     * <p>方法描述：初始化审核日期和审核状态</p>
     * @MethodAuthor： yzz
     * @Date：2022-09-21
     **/
    private void initAuditAdv(SXSSFWorkbook wb,Row row, String checkLevel, CalExportVo vo,int exportItemLength, int cellIndex) {
        if(StringUtils.isBlank(checkLevel)||vo.getChkState()==null){
            return;
        }
        CellStyle style = defaultStyle;
        CellStyle style1 = centerStyle;
        CellStyle style2 = leftStyle;
        style.setWrapText(true);//自动换行
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中

        style1.setAlignment(HorizontalAlignment.CENTER);// 设置水平居中
        style1.setVerticalAlignment(VerticalAlignment.CENTER);// 上下居中

        style2.setWrapText(true);//自动换行
        style2.setAlignment(HorizontalAlignment.LEFT);// 设置居左
        style2.setVerticalAlignment(VerticalAlignment.CENTER);

        if("3".equals(checkLevel)){
            if(vo.getIfCityDirect()!=null&&vo.getIfCityDirect()==1){
                if(vo.getChkState()==0||vo.getChkState()==5){
                    row.getCell(cellIndex+exportItemLength).setCellValue("");
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountySmtDate());
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountyAuditAdv());
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                }else if(vo.getChkState()==4||vo.getChkState()==6||vo.getChkState()==7){
                    row.getCell(cellIndex+exportItemLength).setCellValue("");
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountySmtDate());
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountyAuditAdv());
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getProSmtDate());
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getProAuditAdv());
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                }else{
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                }
            }else{
                if(vo.getChkState()==0||vo.getChkState()==3){
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountySmtDate());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountyAuditAdv());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                }else  if(vo.getChkState()==2||vo.getChkState()==5){
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountySmtDate());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountyAuditAdv());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCitySmtDate());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCityAuditAdv());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                }else if(vo.getChkState()==4||vo.getChkState()==6||vo.getChkState()==7){
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountySmtDate());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountyAuditAdv());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCitySmtDate());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCityAuditAdv());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getProSmtDate());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getProAuditAdv());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                }else{
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                }
            }
            row.getCell(cellIndex+exportItemLength).setCellValue(vo.getIfAbnomal());//是否异常
            row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
            row.getCell(cellIndex+exportItemLength).setCellValue(vo.getErrorMsg());//异常原因
            row.getCell((cellIndex++)+exportItemLength).setCellStyle(style2);
            if(this.ifShowErrMsg){
                row.getCell(cellIndex+exportItemLength).setCellValue(vo.getChkState()==7?vo.getErrMsg():"");//国家失败原因
                row.getCell((cellIndex++)+exportItemLength).setCellStyle(style2);//国家失败原因单元格样式
            }
        }else if("2".equals(checkLevel)) {
            if (vo.getIfProvDirect()!=null&&vo.getIfProvDirect() == 1) {
              if(vo.getChkState()==4||vo.getChkState()==6||vo.getChkState()==7){
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getProSmtDate());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getProAuditAdv());//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                }else{
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");//
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                }
            }else{
                if(vo.getChkState()==0||vo.getChkState()==5){
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountySmtDate());
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountyAuditAdv());
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                }else if(vo.getChkState()==4||vo.getChkState()==6||vo.getChkState()==7){
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountySmtDate());
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getCountyAuditAdv());
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getProSmtDate());
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue(vo.getProAuditAdv());
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                }else{
                    row.getCell(cellIndex+exportItemLength).setCellValue("");
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);
                    row.getCell(cellIndex+exportItemLength).setCellValue("");
                    row.getCell((cellIndex++)+exportItemLength).setCellStyle(style);
                }
            }
            row.getCell(cellIndex+exportItemLength).setCellValue(vo.getIfAbnomal());//是否异常
            row.getCell((cellIndex++)+exportItemLength).setCellStyle(style1);//异常原因单元格样式
            row.getCell(cellIndex+exportItemLength).setCellValue(vo.getErrorMsg());//异常原因
            row.getCell((cellIndex++)+exportItemLength).setCellStyle(style2);
            if(this.ifShowErrMsg){
                row.getCell(cellIndex+exportItemLength).setCellValue(vo.getChkState()==7?vo.getErrMsg():"");//国家失败原因
                row.getCell((cellIndex++)+exportItemLength).setCellStyle(style2);//国家失败原因单元格样式
            }
        }
    }

    /**
     * @Description: 通过体检主表rid集合获取返回信息集合
     * @param ridList
     * @param ifNeedEnctryInfo 是否需要脱敏 true是
     * @param ifAudit 是否个案审核的数据 true是
     * @param checkLevel  个案审核导出条件中的checkLevel 非个案审核导出传递null
     * @param zoneType  个案审核导出条件中的zoneType 非个案审核导出传递null
     * @param ifLatestExport 是否体检最新档案导出 导出的是初检数据 但取最新的复检的主检结论、主检建议、职业禁忌证名称、疑似职业病名称
     *                       并且【XX项目】结果、【XX项目】计量单位、【XX项目】合格标记如果初检有则覆盖初检的结果，初检没有会增加
     *                       检测结果逻辑 初检有a、b、c三个项目 对应复检有c、d两个项目 最终取初检的a、b和复检的c、d
     * @MethodAuthor pw,2021年12月21日
     */
    private List<CalExportVo> mixResultVOByBhkRidList(List<Integer> ridList,boolean ifNeedEnctryInfo,boolean ifAudit,
                                                      Integer checkLevel,
                                                      Integer zoneType, boolean ifLatestExport,boolean ifAuditQuery)
            throws Exception{
        if(ifAudit && (null == checkLevel || null == zoneType)){
            throw new RuntimeException("个案审核导出条件缺少checkLevel与zoneType");
        }
        List<CalExportVo> list = new ArrayList<>();
        if(CollectionUtils.isEmpty(ridList)){
            return list;
        }
        //去重
        ridList = ridList.stream().distinct().collect(Collectors.toList());
        //key 初检记录的rid value 最新的复检记录rid
        Map<Integer,Integer> mainRidWithRhkRidMap = new HashMap<>();
        if(ifLatestExport && !CollectionUtils.isEmpty(ridList)){
            mainRidWithRhkRidMap = this.bhkService.findLastestRhkRidByFstRid(ridList);
            log.error("体检最新档案导出 ridList：{} 对应的复检Map:{}", JacksonUtils.toJson(ridList), JacksonUtils.toJson(mainRidWithRhkRidMap));
        }
        //ifLatestExport true 体检子表 结果 最新的复检体检项目会覆盖补充初检的体检子表
        final List<BhkSubExportVo> subExportVoList = ifAuditQuery ? new ArrayList<>() : querySubInfoByBhkRidList(ridList, mainRidWithRhkRidMap);
        final List<EmhistoryExportVo> emhistoryExportVoList = queryEmHistoryByBhkRidList(ridList);
        final List<AnamnesisExportVo> anamnesisExportVoList = queryAnamnesisByBhkRidList(ridList);
        final List<ExmsAndSymptomExportVo> exmsAndSymptomExportVoList = queryExmsDataAndSymptomByBhkRidList(ridList);
        final List<AbnormalBadrsnExportVo> abnormalBadrsnExportVoList = ifAudit ? queryAbnormalInfoByBhkRidList(ridList) : null;
        final Map<Integer,Integer> mainRidWithRhkRidMapForThread = mainRidWithRhkRidMap;
        //获取  主动监测异常信息
        List<TdTjBhkAbnomal> tdTjBhkAbnomalList=queryBhkAbnomals(ridList);

        List<List<Integer>> ridForThreadList = null;
        if(ridList.size() < 90000){
            ridForThreadList = StringUtils.splitListProxy(ridList,1000);
        }else if(ridList.size() < 900000){
            ridForThreadList = StringUtils.splitListProxy(ridList,9000);
        }else{
            ridForThreadList = StringUtils.splitListProxy(ridList,90000);
        }
        ridList.clear();
        if(ridForThreadList.size() > 1){
            ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                    .setNameFormat("mixResult-thread-%d").build();
            // 实例化线程池 主线程不超过10个
            ExecutorService executorService = new ThreadPoolExecutor(ridForThreadList.size() < 10 ? ridForThreadList.size() : 10,
                    200, 0,
                    TimeUnit.MILLISECONDS,
                    new ArrayBlockingQueue<>(10240),namedThreadFactory,new CalculationThreadExecutionHandler());
            CompletionService completionService = new ExecutorCompletionService(executorService);
            for(List<Integer> bhkRidList : ridForThreadList){
                final List<BhkSubExportVo> tmpSubExportVoList = CollectionUtils.isEmpty(subExportVoList) ? null :
                        subExportVoList.stream().filter(v -> bhkRidList.contains(v.getBhkRid()))
                                .collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(tmpSubExportVoList)){
                    subExportVoList.removeAll(tmpSubExportVoList);
                }
                final List<EmhistoryExportVo> tmpEmhistoryExportVoList = CollectionUtils.isEmpty(emhistoryExportVoList) ? null :
                        emhistoryExportVoList.stream().filter(v -> bhkRidList.contains(v.getBhkRId()))
                                .collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(tmpEmhistoryExportVoList)){
                    emhistoryExportVoList.removeAll(tmpEmhistoryExportVoList);
                }
                final List<AnamnesisExportVo> tmpAnamnesisExportVoList = CollectionUtils.isEmpty(anamnesisExportVoList) ? null :
                        anamnesisExportVoList.stream().filter(v -> bhkRidList.contains(v.getBhkRid()))
                                .collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(tmpAnamnesisExportVoList)){
                    anamnesisExportVoList.removeAll(tmpAnamnesisExportVoList);
                }
                final List<ExmsAndSymptomExportVo> tmpExmsAndSymptomExportVoList = CollectionUtils.isEmpty(exmsAndSymptomExportVoList) ? null :
                        exmsAndSymptomExportVoList.stream().filter(v -> bhkRidList.contains(v.getBhkRid()))
                                .collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(tmpExmsAndSymptomExportVoList)){
                    exmsAndSymptomExportVoList.removeAll(tmpExmsAndSymptomExportVoList);
                }
                final List<AbnormalBadrsnExportVo> tmpAbnormalBadrsnExportVoList = ifAudit ? (CollectionUtils.isEmpty(abnormalBadrsnExportVoList) ? null :
                        abnormalBadrsnExportVoList.stream().filter(v -> bhkRidList.contains(v.getBhkRid()))
                                .collect(Collectors.toList())) : null;
                if(!CollectionUtils.isEmpty(tmpAbnormalBadrsnExportVoList)){
                    abnormalBadrsnExportVoList.removeAll(tmpAbnormalBadrsnExportVoList);
                }
                completionService.submit(() -> fillResultVO(bhkRidList, ifNeedEnctryInfo, ifAudit, tmpSubExportVoList,
                        tmpEmhistoryExportVoList, tmpAnamnesisExportVoList, tmpExmsAndSymptomExportVoList,
                        tmpAbnormalBadrsnExportVoList, checkLevel, zoneType, mainRidWithRhkRidMapForThread,tdTjBhkAbnomalList,ifAuditQuery));
            }
            boolean threadErrFlag = false;
            try{
                for(int i=0; i<ridForThreadList.size(); i++){
                    if(threadErrFlag){
                        break;
                    }
                    Future<List<CalExportVo>> future = completionService.take();
                    List<CalExportVo> tmpList = future.get();
                    if(!CollectionUtils.isEmpty(tmpList)){
                        list.addAll(tmpList);
                    }
                }
            }catch(Exception e){
                threadErrFlag = true;
                e.printStackTrace();
                throw new RuntimeException(e);
            }finally {
                executorService.shutdownNow();
            }
        }else{
            List<CalExportVo> tmpList = fillResultVO(ridForThreadList.get(0), ifNeedEnctryInfo, ifAudit, subExportVoList,
                    emhistoryExportVoList, anamnesisExportVoList, exmsAndSymptomExportVoList, abnormalBadrsnExportVoList,
                    checkLevel, zoneType, mainRidWithRhkRidMapForThread,tdTjBhkAbnomalList,ifAuditQuery);
            if(!CollectionUtils.isEmpty(tmpList)){
                list.addAll(tmpList);
            }
        }
        return list;
    }

    /**
     * @Description: 填充数据
     *
     * @MethodAuthor pw,2021年12月22日
     */
    private List<CalExportVo> fillResultVO(List<Integer> ridList, boolean ifNeedEnctryInfo, boolean ifAudit,
                                           List<BhkSubExportVo> subExportVoList,
                                           List<EmhistoryExportVo> emhistoryExportVoList,
                                           List<AnamnesisExportVo> anamnesisExportVoList,
                                           List<ExmsAndSymptomExportVo> exmsAndSymptomExportVoList,
                                           List<AbnormalBadrsnExportVo> abnormalBadrsnExportVoList,Integer checkLevel,
                                           Integer zoneType, Map<Integer,Integer> mainRidWithRhkRidMap,
                                           List<TdTjBhkAbnomal> tdTjBhkAbnomalList,boolean ifAuditQuery){
        if(CollectionUtils.isEmpty(ridList)){
            return null;
        }
        List<CalExportVo> list = new ArrayList<>(ridList.size());
        List<BaseInfoExportVo> baseInfoExportVoList = queryBhkInfoByRidList(ridList);
        if(CollectionUtils.isEmpty(baseInfoExportVoList)){
            return list;
        }
        //key 最新复检rid value 对应的基本信息
        Map<Integer, BaseInfoExportVo> rhkBaseInfoMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(mainRidWithRhkRidMap)){
            //获取复检对应的基本信息
            List<BaseInfoExportVo> rhkBaseInfoExportVoList = queryBhkInfoByRidList(new ArrayList<>(mainRidWithRhkRidMap.values()));
            if(!CollectionUtils.isEmpty(rhkBaseInfoExportVoList)){
                rhkBaseInfoExportVoList.stream().forEach(v -> rhkBaseInfoMap.put(v.getBhkRid(), v));
            }
        }
        for(Integer bhkRid : ridList){
            Optional<BaseInfoExportVo> optional = baseInfoExportVoList.stream()
                    .filter(v -> null != v.getBhkRid() && v.getBhkRid() == bhkRid.intValue()).findFirst();
            if(optional.isPresent()){
                BaseInfoExportVo baseInfo = optional.get();
                baseInfo.setCheckLevel(checkLevel);
                baseInfo.setIfAuditQuery(ifAuditQuery);
                baseInfo.setZoneType(zoneType);
                CalExportVo exportVo = new CalExportVo();
                exportVo.setIfNeedEnctryInfo(ifNeedEnctryInfo);
                exportVo.setName(baseInfo.getPersonName());
                exportVo.setZkBhkCode(baseInfo.getZkBhkCode());
                exportVo.setBhkCode(baseInfo.getBhkCode());
                exportVo.setSex(baseInfo.getSex());
                exportVo.setAge(null == baseInfo.getAge() ? "" : baseInfo.getAge().toString());
                TsSimpleCode simpleCode = null == baseInfo.getCardTypeId() ? null :
                        exportSimpleCodeMap.get(baseInfo.getCardTypeId());
                exportVo.setCardType(null == simpleCode ? "" : simpleCode.getCodeName());
                exportVo.setIdc(baseInfo.getIdc());
                exportVo.setLinkTel(baseInfo.getLinkTel());
                exportVo.setWorkUnitName(baseInfo.getCrptName());
                exportVo.setWorkUnitLinkMan(baseInfo.getLinkMan2());
                exportVo.setWorkUnitLinkTel(baseInfo.getLinkPhone2());
                exportVo.setWorkUnitZoneArea(baseInfo.getFullName());
                exportVo.setWorkUnitAddress(baseInfo.getAddress());
                exportVo.setWorkUnitCreditCode(baseInfo.getInstitutionCode());
                simpleCode = null == baseInfo.getCrptSizeId() ? null :
                        exportSimpleCodeMap.get(baseInfo.getCrptSizeId());
                exportVo.setWorkUnitCrptSize(null == simpleCode ? "" : simpleCode.getCodeName());
                simpleCode = null == baseInfo.getEconomyId() ? null :
                        exportSimpleCodeMap.get(baseInfo.getEconomyId());
                exportVo.setWorkUnitEconomy(null == simpleCode ? "" : simpleCode.getCodeName());
                simpleCode = null == baseInfo.getIndusTypeId() ? null :
                        exportSimpleCodeMap.get(baseInfo.getIndusTypeId());
                exportVo.setWorkUnitIndusType(null == simpleCode ? "" : simpleCode.getCodePath());
                exportVo.setJcType(null == baseInfo.getJcType() ? "" : (1 == baseInfo.getJcType() ? "常规监测" :
                        2 == baseInfo.getJcType() ? "主动监测" : ""));
                simpleCode = null == baseInfo.getOnguardStateId() ? null :
                        exportSimpleCodeMap.get(baseInfo.getOnguardStateId());
                exportVo.setOnguardState(null == simpleCode ? "" : simpleCode.getCodeName());
                exportVo.setDpt(baseInfo.getDpt());
                exportVo.setWorkNo(baseInfo.getWorkNo());
                exportVo.setWorkName(baseInfo.getWorkName());
                exportVo.setWrklnt((null == baseInfo.getWrklnt() ? "" : baseInfo.getWrklnt().intValue()+"年")+
                        (null == baseInfo.getWrklntMonth() ? "" : baseInfo.getWrklntMonth()+"月"));
                exportVo.setTchbadTotalTime((null == baseInfo.getTchBadRsntim() ? "" : baseInfo.getTchBadRsntim()+"年")+
                        (null == baseInfo.getTchBadRsnMonth() ? "" : baseInfo.getTchBadRsnMonth()+"月"));
                //体检类型
                String bhkType = baseInfo.getBhkType();
                //3：职业健康检查
                //4：放射卫生健康检查
                if("3".equals(bhkType)){
                    exportVo.setBhkType("职业健康检查");
                }else if("4".equals(bhkType)){
                    exportVo.setBhkType("放射卫生健康检查");
                }
                exportVo.setBadRsnName(baseInfo.getBadName());
                //体检危害因素明细结论
                exportVo.setBadRsnRstName(baseInfo.getBadRsnRstName());
                Integer rhkRid = null == mainRidWithRhkRidMap ? null : mainRidWithRhkRidMap.get(bhkRid);
                BaseInfoExportVo rhkBaseInfoExportVo = null == rhkRid ? null : rhkBaseInfoMap.get(rhkRid);
                //条件判断是否需要最新复检覆盖初检的主检结论
                Integer bhkRstId = null == rhkBaseInfoExportVo ? baseInfo.getBhkRstId() : 
                        rhkBaseInfoExportVo.getBhkRstId();
                simpleCode = null == bhkRstId ? null : exportSimpleCodeMap.get(bhkRstId);
                exportVo.setMhkRst(null == simpleCode ? "" : simpleCode.getCodeName());
                //条件判断是否需要最新复检覆盖初检的主检建议
                exportVo.setMhkAdv(null == rhkBaseInfoExportVo ? baseInfo.getMhkAdv() : rhkBaseInfoExportVo.getMhkAdv());
                //条件判断是否需要最新复检覆盖初检的职业禁忌证名称
                exportVo.setContraindication(null == rhkBaseInfoExportVo ? baseInfo.getContraindication() :
                        rhkBaseInfoExportVo.getContraindication());
                //条件判断是否需要最新复检覆盖初检的疑似职业病名称
                exportVo.setSuOccDisease(null == rhkBaseInfoExportVo ? baseInfo.getSuOccDisease() : 
                        rhkBaseInfoExportVo.getSuOccDisease());
                exportVo.setOrgName(baseInfo.getUnitName());
                exportVo.setBhkDate(null == baseInfo.getBhkDate() ? "" : DateUtils.formatDate(baseInfo.getBhkDate()));
                exportVo.setRptPrintDate(null == baseInfo.getRptPrintDate() ? "" :
                        DateUtils.formatDate(baseInfo.getRptPrintDate()));
                exportVo.setFillDate(null == baseInfo.getFillDate() ? "" :
                        DateUtils.formatDate(baseInfo.getFillDate()));
                exportVo.setIfRhk(null != baseInfo.getIfRhk() && 1 == baseInfo.getIfRhk() ? "是" : "否");
                if(ifAudit){
                    exportVo.setReciveDate(null == baseInfo.getDealCompleteDate() ? "" :
                            DateUtils.formatDate(baseInfo.getDealCompleteDate()));
                    exportVo.setState(baseInfo.getChkStateStr());
                    exportVo.setIfAbnomal(null != baseInfo.getIfAbnomal() && 1 == baseInfo.getIfAbnomal() ? "是" : "否");
                    exportVo.setIfCityDirect(baseInfo.getIfCityDirect());
                    exportVo.setIfProvDirect(baseInfo.getIfProvDirect());
                    exportVo.setChkState(baseInfo.getChkState());
                    //初始化审核信息
                    //initAuditAdv(exportVo,baseInfo);
                    exportVo.setCountySmtDate(null == baseInfo.getCountySmtDate() ? "" :
                            DateUtils.formatDate(baseInfo.getCountySmtDate()));
                    exportVo.setCountyAuditAdv(baseInfo.getCountyAuditAdv());
                    exportVo.setCitySmtDate(null == baseInfo.getCitySmtDate() ? "" :
                            DateUtils.formatDate(baseInfo.getCitySmtDate()));
                    exportVo.setCityAuditAdv(baseInfo.getCityAuditAdv());
                    exportVo.setProSmtDate(null == baseInfo.getProSmtDate() ? "" :
                            DateUtils.formatDate(baseInfo.getProSmtDate()));
                    exportVo.setProAuditAdv(baseInfo.getProAuditAdv());
                    String errorMsg = "";
                    if(null != baseInfo.getIfAbnomal() && 1 == baseInfo.getIfAbnomal()){
                        String lackMsg = null != baseInfo.getIfInteitmLack() && 1 == baseInfo.getIfInteitmLack() ?
                                baseInfo.getLackMsg() : null;
                        //获取当前体检的异常记录
                        List<TdTjBhkAbnomal> newTdTjBhkAbnomalList= tdTjBhkAbnomalList.stream()
                                .filter(v->v.getBhkId()!=null&&v.getBhkId().intValue()==bhkRid)
                                .collect(Collectors.toList());
                        errorMsg = errorMsgJoinner(lackMsg,newTdTjBhkAbnomalList);
                    }
                    exportVo.setErrorMsg(errorMsg);
                    //国家失败原因
                    exportVo.setErrMsg(baseInfo.getErrMsg());
                }
                exportVo.setBhkRid(baseInfo.getBhkRid());

                //人员职业史
                //放射史
                if(!CollectionUtils.isEmpty(emhistoryExportVoList)){
                    Optional<EmhistoryExportVo> optionalPsnEm = emhistoryExportVoList.stream()
                            .filter(v -> null != v.getBhkRId() &&
                                    v.getBhkRId().intValue() == bhkRid &&
                                    null != v.getHisType() &&
                                    2 == v.getHisType())
                            .findFirst();
                    if(optionalPsnEm.isPresent()){
                        exportVo.setPsnEmhistory(optionalPsnEm.get());
                    }else{
                        exportVo.setPsnEmhistory(new EmhistoryExportVo());
                    }
                    Optional<EmhistoryExportVo> optionalFsEm = emhistoryExportVoList.stream()
                            .filter(v -> null != v.getBhkRId() &&
                                    v.getBhkRId().intValue() == bhkRid &&
                                    null != v.getHisType() &&
                                    1 == v.getHisType())
                            .findFirst();
                    if(optionalFsEm.isPresent()){
                        exportVo.setFsEmhistory(optionalFsEm.get());
                    }else{
                        exportVo.setFsEmhistory(new EmhistoryExportVo());
                    }
                }else{
                    exportVo.setPsnEmhistory(new EmhistoryExportVo());
                    exportVo.setFsEmhistory(new EmhistoryExportVo());
                }
                //既往病史
                if(!CollectionUtils.isEmpty(anamnesisExportVoList)){
                    Optional<AnamnesisExportVo> anamnesisOptional = anamnesisExportVoList.stream()
                            .filter(v -> null != v.getBhkRid() && v.getBhkRid().intValue() == bhkRid).findFirst();
                    if(anamnesisOptional.isPresent()){
                        exportVo.setAnamnesis(anamnesisOptional.get());
                    }else{
                        exportVo.setAnamnesis(new AnamnesisExportVo());
                    }
                }else{
                    exportVo.setAnamnesis(new AnamnesisExportVo());
                }

                //其他和症状
                ExmsAndSymptomExportVo exmSym = new ExmsAndSymptomExportVo();
                exmSym.setBhkRid(bhkRid);
                List<ExmsAndSymptomExportVo> curExmAndSymList = null;
                if(!CollectionUtils.isEmpty(exmsAndSymptomExportVoList)){
                    curExmAndSymList = exmsAndSymptomExportVoList.stream()
                            .filter(v -> null != v.getBhkRid() && v.getBhkRid().intValue() == bhkRid)
                            .collect(Collectors.toList());
                }
                if(!CollectionUtils.isEmpty(curExmAndSymList)){
                    StringJoiner grsJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getGrs()))
                            .map(v -> v.getGrs()).distinct().forEach(v -> grsJoiner.add(v));
                    exmSym.setGrs(grsJoiner.toString());

                    StringJoiner jzsJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getJzs()))
                            .map(v -> v.getJzs()).distinct().forEach(v -> jzsJoiner.add(v));
                    exmSym.setJzs(jzsJoiner.toString());

                    StringJoiner otherJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getOther()))
                            .map(v -> v.getOther()).distinct().forEach(v -> otherJoiner.add(v));
                    exmSym.setOther(otherJoiner.toString());

                    StringJoiner symJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getSym()))
                            .map(v -> v.getSym()).distinct().forEach(v -> symJoiner.add(v));
                    exmSym.setSym(symJoiner.toString());

                    StringJoiner othsymJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getOthsym()))
                            .map(v -> v.getOthsym()).distinct().forEach(v -> othsymJoiner.add(v));
                    exmSym.setOthsym(othsymJoiner.toString());
                    //吸烟史-目前吸烟情况
                    StringJoiner smkstaJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getSmksta()))
                            .map(ExmsAndSymptomExportVo::getSmksta).distinct().forEach(smkstaJoiner::add);
                    exmSym.setSmksta(smkstaJoiner.toString());
                    //吸烟史-吸烟史（年）
                    StringJoiner smkyerqtyJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getSmkyerqty()))
                            .map(ExmsAndSymptomExportVo::getSmkyerqty).distinct().forEach(smkyerqtyJoiner::add);
                    exmSym.setSmkyerqty(smkyerqtyJoiner.toString());
                    //吸烟史-吸烟史（月）
                    StringJoiner smkmthqtyJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getSmkmthqty()))
                            .map(ExmsAndSymptomExportVo::getSmkmthqty).distinct().forEach(smkmthqtyJoiner::add);
                    exmSym.setSmkmthqty(smkmthqtyJoiner.toString());
                    //吸烟史-平均每天吸烟量（支）
                    StringJoiner smkdaybleJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getSmkdayble()))
                            .map(ExmsAndSymptomExportVo::getSmkdayble).distinct().forEach(smkdaybleJoiner::add);
                    exmSym.setSmkdayble(smkdaybleJoiner.toString());
                    //饮酒情况
                    StringJoiner winstaJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getWinsta()))
                            .map(v -> getDrinkingDescription(v.getWinsta())).distinct().forEach(winstaJoiner::add);
                    exmSym.setWinsta(winstaJoiner.toString());
                    //烟酒史酒量
                    StringJoiner windaymlxJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getWindaymlx()))
                            .map(ExmsAndSymptomExportVo::getWindaymlx).distinct().forEach(windaymlxJoiner::add);
                    exmSym.setWindaymlx(windaymlxJoiner.toString());
                    //烟酒史酒龄
                    StringJoiner winyerqtyJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getWinyerqty()))
                            .map(ExmsAndSymptomExportVo::getWinyerqty).distinct().forEach(winyerqtyJoiner::add);
                    exmSym.setWinyerqty(winyerqtyJoiner.toString());
                    //婚姻史结婚日期
                    StringJoiner mrydatJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getMrydat()))
                            .map(ExmsAndSymptomExportVo::getMrydat).distinct().forEach(mrydatJoiner::add);
                    exmSym.setMrydat(mrydatJoiner.toString());
                    //配偶接触放射线情况
                    StringJoiner cplrdtcndJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getCplrdtcnd()))
                            .map(ExmsAndSymptomExportVo::getCplrdtcnd).distinct().forEach(cplrdtcndJoiner::add);
                    exmSym.setCplrdtcnd(cplrdtcndJoiner.toString());
                    //配偶职业及健康状况
                    StringJoiner cplprfhthcndJoiner = new StringJoiner("，");
                    curExmAndSymList.stream().filter(v -> StringUtils.isNotBlank(v.getCplprfhthcnd()))
                            .map(ExmsAndSymptomExportVo::getCplprfhthcnd).distinct().forEach(cplprfhthcndJoiner::add);
                    exmSym.setCplprfhthcnd(cplprfhthcndJoiner.toString());
                    exmsAndSymptomExportVoList.removeAll(curExmAndSymList);//用完移除 避免因ExmsAndSymptomExportVo太多导致内存溢出
                }
                exportVo.setExmSym(exmSym);

                //体检项目
                List<BhkSubExportVo> fillBhkSubExportVoList = new ArrayList<>();//subExportVoList
                if(!CollectionUtils.isEmpty(subExportVoList) && subExportVoList.stream()
                        .anyMatch(v -> null != v.getBhkRid() && v.getBhkRid().intValue() == bhkRid)){
                    fillBhkSubExportVoList = subExportVoList.stream()
                            .filter(v -> null != v.getBhkRid() && v.getBhkRid().intValue() == bhkRid)
                            .collect(Collectors.toList());
                    subExportVoList.removeAll(fillBhkSubExportVoList);//用完移除 避免subExportVoList中BhkSubExportVo太多导致内存溢出
                }
                //若mainRidWithRhkRidMap不为空 体检项目已经在前置查询的时候 将最新复检的体检项目覆盖补充到初检的体检项目中
                exportVo.setItemList(fillBhkSubExportVoList);

                //用工单位相关赋值
                exportVo.setEmpCrptName(baseInfo.getEmpCrptName());
                exportVo.setEmpLinkMan(baseInfo.getEmpLinkMan());
                exportVo.setEmpLinkPhone(baseInfo.getEmpLinkPhone());
                exportVo.setEmpZone(baseInfo.getEmpZone());
                exportVo.setEmpAddress(baseInfo.getEmpAddress());
                exportVo.setEmpInstItutionCode(baseInfo.getEmpInstItutionCode());
                simpleCode = null == baseInfo.getEmpCrptSizeId() ? null :
                        exportSimpleCodeMap.get(baseInfo.getEmpCrptSizeId());
                exportVo.setEmpCrptSize(null == simpleCode ? "" : simpleCode.getCodeName());
                simpleCode = null == baseInfo.getEmpEconomyId() ? null :
                        exportSimpleCodeMap.get(baseInfo.getEmpEconomyId());
                exportVo.setEmpEconomy(null == simpleCode ? "" : simpleCode.getCodeName());
                simpleCode = null == baseInfo.getEmpIndusTypeId() ? null :
                        exportSimpleCodeMap.get(baseInfo.getEmpIndusTypeId());
                exportVo.setEmpIndusType(null == simpleCode ? "" : simpleCode.getCodePath());
                exportVo.setTouchBadName(baseInfo.getTouchBadName());
                //主动检测危害因素
                exportVo.setActiveBadName(baseInfo.getActiveBadName());
                //防护用品佩戴情况
                exportVo.setProtectEquName(baseInfo.getProtectEquName());
                list.add(exportVo);
            }else{
                continue;
            }
        }
        //用完清空 避免对象过多导致内存溢出
        if(!CollectionUtils.isEmpty(subExportVoList)){
            subExportVoList.clear();
        }
        if(!CollectionUtils.isEmpty(emhistoryExportVoList)){
            emhistoryExportVoList.clear();
        }
        if(!CollectionUtils.isEmpty(anamnesisExportVoList)){
            anamnesisExportVoList.clear();
        }
        if(!CollectionUtils.isEmpty(exmsAndSymptomExportVoList)){
            exmsAndSymptomExportVoList.clear();
        }
        if(!CollectionUtils.isEmpty(abnormalBadrsnExportVoList)){
            abnormalBadrsnExportVoList.clear();
        }
        return list;
    }




    /**
     * @Description: 拼接异常原因
     *
     * @MethodAuthor pw,2021年12月22日
     */
    private String errorMsgJoinner(String lackMsg,List<TdTjBhkAbnomal> tdTjBhkAbnomalList){
        StringBuffer buffer = new StringBuffer();
        int num = 1;
        if(!CollectionUtils.isEmpty(tdTjBhkAbnomalList)){
            for (TdTjBhkAbnomal tdTjBhkAbnomal : tdTjBhkAbnomalList) {
                buffer.append(num++).append("、").append(tdTjBhkAbnomal.getAbnomalInfo())
                        .append((char)10);
            }
        }
        if(StringUtils.isNotBlank(lackMsg)){
            buffer.append(num).append("、").append(lackMsg).append((char)10);
        }
        if(buffer.length()>0){
            return buffer.substring(0,buffer.length()-1);
        }
        return buffer.toString();
    }

    /**
     * @Description: 通过体检主表rid集合获取基本信息集合
     *
     * @MethodAuthor pw,2021年12月21日
     */
    private List<BaseInfoExportVo> queryBhkInfoByRidList(List<Integer> ridList){
        return this.calculationFileExportService.findBaseInfoExportVoListByRidList(ridList);
    }


    /**
     * @Description: 通过体检主表rid集合获取 个案审核危害因素异常信息
     *
     * @MethodAuthor pw,2021年12月22日
     */
    private List<AbnormalBadrsnExportVo> queryAbnormalInfoByBhkRidList(List<Integer> ridList){
        return this.calculationFileExportService.findAbnormalBadrsnExportVoListByBhkRidList(ridList);
    }

    /**
     * <p>方法描述：通过体检主表rid集合获取 主动监测异常信息</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-26
     **/
    private List<TdTjBhkAbnomal> queryBhkAbnomals(List<Integer> ridList){
        return bhkAbnomalService.findBhkAbnomals(ridList);
    }

    /**
     * @Description: 通过体检主表rid集合获取体检子表信息
     * mainRidWithRhkRidMap key 初检rid value对应的最新复检rid
     * mainRidWithRhkRidMap 不为空 则复检与初检重叠的项目 会被复检的项目覆盖 复检有但初检没有的项目 会被加入
     * @MethodAuthor pw,2021年12月20日
     */
    private List<BhkSubExportVo> querySubInfoByBhkRidList(List<Integer> ridList,
                                                          Map<Integer,Integer> mainRidWithRhkRidMap){
        List<BhkSubExportVo> queryResultList = this.calculationFileExportService.findSubExportVoListByBhkRidList(ridList);
        if(CollectionUtils.isEmpty(mainRidWithRhkRidMap)){
            return queryResultList;
        }
        List<Integer> rhkRidList = new ArrayList<>(mainRidWithRhkRidMap.values());
        List<BhkSubExportVo> queryRhkResultList = this.calculationFileExportService.findSubExportVoListByBhkRidList(rhkRidList);
        List<BhkSubExportVo> resultList = new ArrayList<>();
        Map<Integer,List<BhkSubExportVo>> mainRidResultMap = CollectionUtils.isEmpty(queryResultList) ?
                Collections.EMPTY_MAP : queryResultList.stream()
                .collect(Collectors.groupingBy(BhkSubExportVo::getBhkRid));
        Map<Integer,List<BhkSubExportVo>> rhkRidResultMap = CollectionUtils.isEmpty(queryRhkResultList) ?
                Collections.EMPTY_MAP : queryRhkResultList.stream()
                .collect(Collectors.groupingBy(BhkSubExportVo::getBhkRid));
        for(Integer rid : ridList){
            List<BhkSubExportVo> fillList = new ArrayList<>();
            List<BhkSubExportVo> tmpList = mainRidResultMap.get(rid);
            if(!CollectionUtils.isEmpty(tmpList)){
                fillList.addAll(tmpList);
            }
            Integer rhkRid = mainRidWithRhkRidMap.get(rid);
            tmpList = null == rhkRid ? null : rhkRidResultMap.get(rhkRid);
            if(!CollectionUtils.isEmpty(tmpList)){
                List<Integer> rhkItemRidList = tmpList.stream().mapToInt(BhkSubExportVo::getItemRid)
                        .boxed().collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(fillList)){
                    fillList = fillList.stream().filter(v -> !rhkItemRidList.contains(v.getItemRid()))
                            .collect(Collectors.toList());
                }
                //转换rid 复检的体检项目 覆盖和补充初检体检项目
                tmpList = tmpList.stream().map(v -> {
                    v.setBhkRid(rid);
                    return v;
                }).collect(Collectors.toList());
                fillList.addAll(tmpList);
            }
            if(!CollectionUtils.isEmpty(fillList)){
                resultList.addAll(fillList);
            }
        }
        return resultList;
    }

    /**
     * @Description: 通过体检主表rid集合获取问诊(非)放射职业史信息（放射与非放射的）
     *
     * @MethodAuthor pw,2021年12月20日
     */
    private List<EmhistoryExportVo> queryEmHistoryByBhkRidList(List<Integer> ridList){
        return this.calculationFileExportService.findEmhistoryExportVoListByBhkRidList(ridList);
    }

    /**
     * @Description: 通过体检主表rid集合获取问诊既往病史
     *
     * @MethodAuthor pw,2021年12月20日
     */
    private List<AnamnesisExportVo> queryAnamnesisByBhkRidList(List<Integer> ridList){
        return this.calculationFileExportService.findAnamnesisExportVoListByBhkRidList(ridList);
    }

    /**
     * @Description: 通过体检主表rid集合获取问诊项目与问诊症状信息
     *
     * @MethodAuthor pw,2021年12月20日
     */
    private List<ExmsAndSymptomExportVo> queryExmsDataAndSymptomByBhkRidList(List<Integer> ridList){
        return this.calculationFileExportService.findExmsAndSymExportVoListByBhkRidList(ridList);
    }

    /********************************************** 以下初始化方法 ↓ *************************************************/

    /**
    * <p>Description：初始化系统参数 </p>
    * <p>Author： yzz 2024-05-14 </p>
    */
    public void initParam(){
        this.ifShowErrMsg = "1".equals(tsContraSubService.selectParam(0, "IF_HAVE_GS_UPLOAD"));
    }

    /**
     * @Description: 初始化码表 放入缓存
     *
     * @MethodAuthor pw,2021年12月18日
     */
    private void initTsSimpleCodeMap(){
        simpleCodeMap = new HashMap<>(2);
        initTsSimpleCodeMapHelper("5550", simpleCodeMap);
        exportSimpleCodeMap = new HashMap(1000);
        //证件类型 5503
        initTsSimpleCodeMapHelper("5503", exportSimpleCodeMap);
        //企业规模 5004
        initTsSimpleCodeMapHelper("5004", exportSimpleCodeMap);
        //经济类型 5003
        initTsSimpleCodeMapHelper("5003", exportSimpleCodeMap);
        //行业类别 5002
        initTsSimpleCodeMapHelper("5002", exportSimpleCodeMap);
        //在岗状态 5009
        initTsSimpleCodeMapHelper("5009", exportSimpleCodeMap);
        //主检结论 5005
        initTsSimpleCodeMapHelper("5005", exportSimpleCodeMap);
    }

    /**
     * @Description: 辅助初始化码表
     *
     * @MethodAuthor pw,2021年12月22日
     */
    private void initTsSimpleCodeMapHelper(String typeCode, Map<Integer,TsSimpleCode> map){
        List<TsSimpleCode> list = this.simpleCodeService.findTsSimpleCodeAllList(typeCode);
        if(!CollectionUtils.isEmpty(list)){
            for(TsSimpleCode simpleCode : list){
                map.put(simpleCode.getRid(), simpleCode);
            }
            list.clear();
        }
    }

    /**
     * <p>描述：初始化体检项目</p>
     * @param
     *
     * @return
     * @Author: 龚哲,2021/12/23 17:38,initTjItem
     */
    private void initTjItem(){
        List<TbTjItems> list = this.tjItemsService.selectListByEntity(new TbTjItems());
        if( null != list && list.size() > 0 ) {
            for (TbTjItems tbTjItems : list) {
                itemCachMap.put(tbTjItems.getRid(), tbTjItems.getItemName());
                itemCachMaps.put(tbTjItems.getRid(), tbTjItems);
            }
            list.clear();
        }
    }
    /********************************************** 以上初始化方法 ↑ *************************************************/

    /**
     * 使用List集合contains实现List去重(保持原List顺序)
     *
     * @param list 原始List
     * @return 去重后的List(保持原List顺序)
     */
    public static List<String> removeDupByContains(List<String> list) {
        List<String> newList = new ArrayList<>();
        for (String s : list) {
            boolean isContains = newList.contains(s);
            if (!isContains) {
                newList.add(s);
            }
        }
        list.clear();
        list.addAll(newList);
        return list;
    }

    private void initCellStyles(SXSSFWorkbook wb) {
        // 默认样式
        defaultStyle = wb.createCellStyle();
        defaultStyle.setWrapText(true);
        defaultStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 居中样式
        centerStyle = wb.createCellStyle();
        centerStyle.setAlignment(HorizontalAlignment.CENTER);
        centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 居左样式
        leftStyle = wb.createCellStyle();
        leftStyle.setWrapText(true);
        leftStyle.setAlignment(HorizontalAlignment.LEFT);
        leftStyle.setVerticalAlignment(VerticalAlignment.CENTER);
    }
}

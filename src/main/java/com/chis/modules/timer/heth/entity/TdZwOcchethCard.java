package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsUnit;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-12-21,TdZwOcchethCard
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_OCCHETH_CARD")
public class TdZwOcchethCard extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("CARD_NO")
    private String cardNo;

    @TableField("ORG_NAME")
    private String orgName;

    @TableField("ORG_FZ")
    private String orgFz;

    @TableField("ORG_ADDR")
    private String orgAddr;

    @TableField("CERT_NO")
    private String certNo;

    @TableField("PRO_FZ")
    private String proFz;

    @TableField("PRO_LINKTEL")
    private String proLinktel;

    @TableField(value = "CRPT_ID" , el = "fkByCrptId.rid")
    private TbTjCrpt fkByCrptId;

    @TableField(value = "ZONE_ID" , el = "fkByZoneId.rid")
    private TsZone fkByZoneId;

    @TableField("CRPT_NAME")
    private String crptName;

    @TableField("ADDRESS")
    private String address;

    @TableField("SAFEPOSITION")
    private String safeposition;

    @TableField("SAFEPHONE")
    private String safephone;

    @TableField(value = "CRPT_SIZE_ID" , el = "fkByCrptSizeId.rid")
    private TsSimpleCode fkByCrptSizeId;

    @TableField("INVEST_START_DATE")
    private Date investStartDate;

    @TableField("INVEST_END_DATE")
    private Date investEndDate;

    @TableField("JC_START_DATE")
    private Date jcStartDate;

    @TableField("JC_END_DATE")
    private Date jcEndDate;

    @TableField("RPT_DATE")
    private Date rptDate;

    @TableField("RPT_NO")
    private String rptNo;

    @TableField("IF_BADRSN_JC")
    private String ifBadrsnJc;

    @TableField("IF_STATUS_PJ")
    private String ifStatusPj;

    @TableField("IF_INST_USE_PJ")
    private String ifInstUsePj;

    @TableField("IF_JC_INST")
    private String ifJcInst;

    @TableField("JC_INST_NUM")
    private String jcInstNum;

    @TableField("JC_NOT_HG_INST_NUM")
    private String jcNotHgInstNum;

    @TableField("NOT_HG_INST_NAME")
    private String notHgInstName;

    @TableField("IF_JC_USE")
    private String ifJcUse;

    @TableField("JC_USE_NUM")
    private String jcUseNum;

    @TableField("JC_NOT_HG_USE_NUM")
    private String jcNotHgUseNum;

    @TableField("NOT_HG_USE_NAME")
    private String notHgUseName;

    @TableField("ORG_FZ_MAN")
    private String orgFzMan;

    @TableField("FILL_FORM_PSN")
    private String fillFormPsn;

    @TableField("FILL_LINK")
    private String fillLink;

    @TableField("FILL_DATE")
    private Date fillDate;

    @TableField("FILL_UNIT_NAME")
    private String fillUnitName;

    @TableField("ANNEX_PATH")
    private String annexPath;

    @TableField(value = "FILL_UNIT_ID" , el = "fkByFillUnitId.rid")
    private TsUnit fkByFillUnitId;

    @TableField("STATE")
    private String state;

    @TableField("DEL_MARK")
    private String delMark;


    public TdZwOcchethCard(Integer rid) {
        super(rid);
    }


}

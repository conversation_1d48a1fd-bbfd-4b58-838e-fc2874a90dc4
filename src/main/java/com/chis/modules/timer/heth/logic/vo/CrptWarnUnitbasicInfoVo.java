package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>类描述：用人单位预警-申报单位信息[在线申报]查询返回对象 </p>
 * pw 2023/9/27
 **/
@Data
public class CrptWarnUnitbasicInfoVo implements Serializable {
    private static final long serialVersionUID = 7254637470996620477L;
    /** 申报基本信息rid */
    private Integer rid;
    private Integer crptId;
    /** 最新申报日期 */
    private Date declareDate;
    /** 有误主要负责人培训 */
    private Integer ifLeadersTrain;
    /** 职业卫生管理人员培训 */
    private Integer ifManagersTrain;
    /** 本年度检测情况 */
    private Integer ifat;
    /** 本年度岗中职业健康检查开展情况 */
    private Integer ifhea;
    /** 接害总人数（含外委） */
    private Integer victimsNum;
    /** 粉尘体检人数 */
    private Integer dustNum;
    /** 化学物质体检人数 */
    private Integer chemistryNum;
    /** 物理因素体检人数 */
    private Integer physicsNum;
    /** 放射性因素体检人数 */
    private Integer radioNum;
    /** 申报基本信息创建日期 */
    private Date createDate;
}

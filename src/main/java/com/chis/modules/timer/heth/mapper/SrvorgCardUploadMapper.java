package com.chis.modules.timer.heth.mapper;

import com.chis.modules.timer.heth.logic.CountSrvorgCardJsonPO;
import com.chis.modules.timer.heth.logic.vo.TdZwOcchethCardPsnVo;
import com.chis.modules.timer.heth.logic.vo.TdZwUploadAddressVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>类描述： 放射卫生技术服务信息报告卡Mapper </p>
 * @ClassAuthor： pw 2022/12/22
 **/
@Repository
public interface SrvorgCardUploadMapper {
    /**
     * <p>方法描述： 查询待处理的放射卫生技术服务信息报告卡主表数据 </p>
     * @MethodAuthor： pw 2022/12/22
     **/
    List<CountSrvorgCardJsonPO> findExecuteCardDataList(@Param("pageSize") Integer pageSize,@Param("startDate") String startDate);

    /**
     * <p>方法描述： 查询待处理的放射卫生技术服务信息报告卡参与人员数据 </p>
     * @MethodAuthor： pw 2022/12/24
     **/
    List<TdZwOcchethCardPsnVo> findExecutePsnDataList(@Param("pageSize") Integer pageSize);

    /**
     * <p>方法描述： 查询待处理的放射卫生技术服务信息报告卡服务地址数据 </p>
     * @MethodAuthor： pw 2022/12/24
     **/
    List<TdZwUploadAddressVo> findExecuteAddrDataList(@Param("pageSize") Integer pageSize);

    List<Map<String, Object>> selectNeedUpdateFileAndSubmitDataForSrvOrg(
            @Param("busTypeMain") Integer busTypeMain,
            @Param("busTypePsn") Integer busTypePsn,
            @Param("busTypeZone") Integer busTypeZone,
            @Param("dataSize") Integer dataSize,
            @Param("lastDate") Date lastDate
    );
}

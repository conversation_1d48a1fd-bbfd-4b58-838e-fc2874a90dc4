package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>类描述：专项治理数据下载 检测结果明细 </p>
 * pw 2023/9/21
 **/
@Data
public class SpecialRectficationResultVo implements Serializable {
    private static final long serialVersionUID = -5820062223562022317L;
    private String factorCode;
    private String factorName;
    private String itemCode;
    private String itemName;
    private String station;
    private Integer compositiveResult;
    private Integer judgementResult;
    private BigDecimal previousTwa;
    private BigDecimal currentTwa;
    private BigDecimal finalTwa;
    private BigDecimal previousStel;
    private BigDecimal currentStel;
    private BigDecimal finalStel;
    private BigDecimal previousMac;
    private BigDecimal currentMac;
    private BigDecimal finalMac;
    private BigDecimal previousPe;
    private BigDecimal currentPe;
    private BigDecimal finalPe;
    private BigDecimal previousLex;
    private BigDecimal currentLex;
    private BigDecimal finalLex;
}

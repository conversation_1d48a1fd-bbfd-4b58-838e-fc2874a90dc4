package com.chis.modules.timer.heth.service;

import com.chis.modules.timer.heth.logic.BhkAuditQueryPO;
import com.chis.modules.timer.heth.logic.vo.*;
import com.chis.modules.timer.heth.mapper.BhkCalulationFileExportMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 异步导出Service
 *
 * @ClassAuthor pw,2021年12月21日,BhkCalculationFileExportService
 */
@Service
public class BhkCalculationFileExportService {
    @Autowired
    private BhkCalulationFileExportMapper fileExportMapper;

    /**
     * @Description: 通过体检主表rid集合获取异步导出数据的基础信息
     *
     * @MethodAuthor pw,2021年12月22日
     */
    public List<BaseInfoExportVo> findBaseInfoExportVoListByRidList(List<Integer> bhkRidList){
        if(CollectionUtils.isEmpty(bhkRidList)){
            return null;
        }
        List<BaseInfoExportVo> resultList = new ArrayList<>(bhkRidList.size());
        List<Integer> tmpRidList = new ArrayList<>(1000);
        for(Integer rid : bhkRidList){
            tmpRidList.add(rid);
            if(tmpRidList.size() >= 1000){
                List<BaseInfoExportVo> tmpResultList = this.fileExportMapper
                        .findBaseInfoExportVoListByRidList(tmpRidList);
                if(!CollectionUtils.isEmpty(tmpResultList)){
                    resultList.addAll(tmpResultList);
                }
                tmpRidList.clear();
            }
        }
        if(!CollectionUtils.isEmpty(tmpRidList)){
            List<BaseInfoExportVo> tmpResultList = this.fileExportMapper
                    .findBaseInfoExportVoListByRidList(tmpRidList);
            if(!CollectionUtils.isEmpty(tmpResultList)){
                resultList.addAll(tmpResultList);
            }
        }
        return resultList;
    }

    /**
     * @Description: 通过体检主表rid集合获取个案审核部分的危害因素异常
     *
     * @MethodAuthor pw,2021年12月22日
     */
    public List<AbnormalBadrsnExportVo> findAbnormalBadrsnExportVoListByBhkRidList(List<Integer> bhkRidList){
        if(CollectionUtils.isEmpty(bhkRidList)){
            return null;
        }
        List<AbnormalBadrsnExportVo> resultList = new ArrayList<>(bhkRidList.size());
        List<Integer> tmpRidList = new ArrayList<>(1000);
        for(Integer rid : bhkRidList){
            tmpRidList.add(rid);
            if(tmpRidList.size() >= 1000){
                List<AbnormalBadrsnExportVo> tmpResultList = this.fileExportMapper
                        .findAbnormalBadrsnExportVoListByBhkRidList(tmpRidList);
                if(!CollectionUtils.isEmpty(tmpResultList)){
                    resultList.addAll(tmpResultList);
                }
                tmpRidList.clear();
            }
        }
        if(!CollectionUtils.isEmpty(tmpRidList)){
            List<AbnormalBadrsnExportVo> tmpResultList = this.fileExportMapper
                    .findAbnormalBadrsnExportVoListByBhkRidList(tmpRidList);
            if(!CollectionUtils.isEmpty(tmpResultList)){
                resultList.addAll(tmpResultList);
            }
        }
        return resultList;
    }

    /**
     * @Description: 通过体检主表rid集合获取体检子表信息
     *
     * @MethodAuthor pw,2021年12月21日
     */
    public List<BhkSubExportVo> findSubExportVoListByBhkRidList(List<Integer> bhkRidList){
        if(CollectionUtils.isEmpty(bhkRidList)){
            return null;
        }
        List<BhkSubExportVo> resultList = new ArrayList<>(bhkRidList.size());
        List<Integer> tmpRidList = new ArrayList<>(1000);
        for(Integer rid : bhkRidList){
            tmpRidList.add(rid);
            if(tmpRidList.size() >= 1000){
                List<BhkSubExportVo> tmpResultList = this.fileExportMapper.findSubExportVoListByBhkRidList(tmpRidList);
                if(!CollectionUtils.isEmpty(tmpResultList)){
                    resultList.addAll(tmpResultList);
                }
                tmpRidList.clear();
            }
        }
        if(!CollectionUtils.isEmpty(tmpRidList)){
            List<BhkSubExportVo> tmpResultList = this.fileExportMapper.findSubExportVoListByBhkRidList(tmpRidList);
            if(!CollectionUtils.isEmpty(tmpResultList)){
                resultList.addAll(tmpResultList);
            }
        }
        return resultList;
    }

    /**
     * @Description: 通过体检主表rid集合获取问诊(非)放射职业史信息（放射与非放射的）
     *
     * @MethodAuthor pw,2021年12月21日
     */
    public List<EmhistoryExportVo> findEmhistoryExportVoListByBhkRidList(List<Integer> bhkRidList){
        if(CollectionUtils.isEmpty(bhkRidList)){
            return null;
        }
        List<EmhistoryExportVo> resultList = new ArrayList<>(bhkRidList.size());
        List<Integer> tmpRidList = new ArrayList<>(1000);
        for(Integer rid : bhkRidList){
            tmpRidList.add(rid);
            if(tmpRidList.size() >= 1000){
                List<EmhistoryExportVo> tmpResultList = this.fileExportMapper.findEmhistoryExportVoListByBhkRidList(tmpRidList);
                if(!CollectionUtils.isEmpty(tmpResultList)){
                    resultList.addAll(tmpResultList);
                }
                tmpRidList.clear();
            }
        }
        if(!CollectionUtils.isEmpty(tmpRidList)){
            List<EmhistoryExportVo> tmpResultList = this.fileExportMapper.findEmhistoryExportVoListByBhkRidList(tmpRidList);
            if(!CollectionUtils.isEmpty(tmpResultList)){
                resultList.addAll(tmpResultList);
            }
        }
        return resultList;
    }

    /**
     * @Description: 通过体检主表rid集合获取问诊既往病史
     *
     * @MethodAuthor pw,2021年12月21日
     */
    public List<AnamnesisExportVo> findAnamnesisExportVoListByBhkRidList(List<Integer> bhkRidList){
        if(CollectionUtils.isEmpty(bhkRidList)){
            return null;
        }
        List<AnamnesisExportVo> resultList = new ArrayList<>(bhkRidList.size());
        List<Integer> tmpRidList = new ArrayList<>(1000);
        for(Integer rid : bhkRidList){
            tmpRidList.add(rid);
            if(tmpRidList.size() >= 1000){
                List<AnamnesisExportVo> tmpResultList = this.fileExportMapper.findAnamnesisExportVoListByBhkRidList(tmpRidList);
                if(!CollectionUtils.isEmpty(tmpResultList)){
                    resultList.addAll(tmpResultList);
                }
                tmpRidList.clear();
            }
        }
        if(!CollectionUtils.isEmpty(tmpRidList)){
            List<AnamnesisExportVo> tmpResultList = this.fileExportMapper.findAnamnesisExportVoListByBhkRidList(tmpRidList);
            if(!CollectionUtils.isEmpty(tmpResultList)){
                resultList.addAll(tmpResultList);
            }
        }
        return resultList;
    }

    /**
     * @Description: 通过体检主表rid集合获取问诊项目与问诊症状信息
     *
     * @MethodAuthor pw,2021年12月21日
     */
    public List<ExmsAndSymptomExportVo> findExmsAndSymExportVoListByBhkRidList(List<Integer> bhkRidList){
        if(CollectionUtils.isEmpty(bhkRidList)){
            return null;
        }
        List<ExmsAndSymptomExportVo> resultList = new ArrayList<>(bhkRidList.size());
        List<Integer> tmpRidList = new ArrayList<>(1000);
        for(Integer rid : bhkRidList){
            tmpRidList.add(rid);
            if(tmpRidList.size() >= 1000){
                List<ExmsAndSymptomExportVo> tmpResultList = this.fileExportMapper.findExmsAndSymExportVoListByBhkRidList(tmpRidList);
                if(!CollectionUtils.isEmpty(tmpResultList)){
                    resultList.addAll(tmpResultList);
                }
                tmpRidList.clear();
            }
        }
        if(!CollectionUtils.isEmpty(tmpRidList)){
            List<ExmsAndSymptomExportVo> tmpResultList = this.fileExportMapper.findExmsAndSymExportVoListByBhkRidList(tmpRidList);
            if(!CollectionUtils.isEmpty(tmpResultList)){
                resultList.addAll(tmpResultList);
            }
        }
        return resultList;
    }

    public List<Integer> findBhkRidListByQueryPO(BhkAuditQueryPO queryPO){
        return this.fileExportMapper.findBhkRidListByQueryPO(queryPO);
    }

    public int findBhkRidCountByQueryPO(BhkAuditQueryPO queryPO){
        Integer count = this.fileExportMapper.findBhkRidCountByQueryPO(queryPO);
        return null == count ? 0 : count.intValue();
    }

    public List<Integer> findTjNewestRecRidList(TjPersonSearchConditionPO queryPO){
        return this.fileExportMapper.findTjNewestRecRidList(queryPO);
    }

    public Integer findTjNewestRecCount(TjPersonSearchConditionPO queryPO){
        return this.fileExportMapper.findTjNewestRecCount(queryPO);
    }

    public Integer findAuditGenerateQueryCount(TjPersonAuditConditionPO  queryPO) {
        return this.fileExportMapper.findAuditGenerateQueryCount(queryPO);
    }

    public List<Integer> findAuditGenerateQueryList(TjPersonAuditConditionPO  queryPO) {
        return this.fileExportMapper.findAuditGenerateQueryList(queryPO);
    }
}

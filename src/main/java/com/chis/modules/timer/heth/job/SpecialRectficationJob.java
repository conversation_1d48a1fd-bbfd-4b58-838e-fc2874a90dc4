package com.chis.modules.timer.heth.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chis.comm.utils.*;
import com.chis.modules.sys.entity.TsContraSub;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.service.TsContraSubService;
import com.chis.modules.sys.service.TsSimpleCodeService;
import com.chis.modules.sys.service.TsZoneService;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.enums.BadRsnCaffeineKeyEnum;
import com.chis.modules.timer.heth.enums.RespCodeEnum;
import com.chis.modules.timer.heth.logic.vo.*;
import com.chis.modules.timer.heth.pojo.SpecialRectficationPojo;
import com.chis.modules.timer.heth.pojo.json.CrptUploadRepDTO;
import com.chis.modules.timer.heth.pojo.json.CrptUploadRepSingleDTO;
import com.chis.modules.timer.heth.pojo.json.CrptValueJson;
import com.chis.modules.timer.heth.service.SpecialRectficationService;
import com.chis.modules.webmvc.api.enums.ReturnType;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.message.BasicHeader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>类描述：专项治理数据下载 </p>
 * pw 2023/9/20
 **/
@Slf4j
@Component
public class SpecialRectficationJob {
    @Value("${heth-timer.special-rectfi.token-url}")
    private String tokenUrl;
    @Value("${heth-timer.special-rectfi.rectfi-url}")
    private String rectfiUrl;
    @Value("${heth-timer.special-rectfi.secret-key}")
    private String secretKey;
    @Value("${heth-timer.special-rectfi.limit-time}")
    private String limitDateStr;
    @Value("${spring.encrypt.key}")
    private String key;

    @Value("${spring.encrypt.debug}")
    private String debug;
    /**
     * 同步用人单位信息接口
     */
    @Value("${heth-timer.del.url.saveOrUpdateCrpt}")
    private String delSaveOrUpdateCrptUrl;
    @Resource
    private TsContraSubService contraSubService;
    @Resource
    private TsSimpleCodeService codeService;
    @Resource
    private TsZoneService zoneService;
    @Resource
    private SpecialRectficationService rectficationService;

    /** 是否https请求 */
    private boolean ifSSL;
    /** 缓存对照 key 对照类型 value-key leftCode(检测项目的dsfSpecialDesc+"@"+leftCode) */
    private Map<Integer,Map<String, TsContraSub>> contraSubTypeMap;
    private Map<String,Map<String, TsSimpleCode>> simpleCodeTypeMap;
    private Map<String, TsZone> tsZoneMap;

    /** 缓存对照成功并且可以找到的地区 避免重复匹配同一编码地区 key 第三方地区编码 */
    private Map<String, TsZone> zoneCacheMap;
    /** 缓存对照成功并且可以找到的行业分类  key 第三方行业分类编码 */
    private Map<String, TsSimpleCode> industryCacheMap;
    /** 缓存对照成功并且可以找到的经济类型  key 第三方经济类型编码 */
    private Map<String, TsSimpleCode> economicCacheMap;
    /** 缓存对照成功并且可以找到的企业规模  key 第三方企业规模编码 */
    private Map<String, TsSimpleCode> crptSizeCacheMap;
    /** 缓存对照成功并且可以找到的危害因素  key 第三方危害因素编码 */
    private Map<String, TsSimpleCode> badRsnCacheMap;
    /** 缓存对照成功并且可以找到的检测项目  key 第三方危害因素编码+@+第三方检测项目编码 */
    private Map<String, TsSimpleCode> itemCacheMap;

    @Scheduled(cron = "${heth-timer.sche-cron.rectfiCron}")
    //@Scheduled(initialDelay = 3000,fixedDelay = 60000)
    public void start(){
        boolean ifBreak = StringUtils.isBlank(this.tokenUrl) ||
                StringUtils.isBlank(this.rectfiUrl) ||
                StringUtils.isBlank(this.secretKey);
        if(ifBreak){
            log.error("专项治理数据下载参数配置异常，Job退出！");
            return;
        }
        ifBreak = this.ifCacheMapErr();
        if(ifBreak){
            log.error("专项治理数据下载存在对照校验失败，需调整后重启程序，当前Job退出！");
            return;
        }
        this.ifSSL = this.tokenUrl.trim().startsWith("https");
        long start = System.currentTimeMillis();
        this.initMap();
        log.info("专项治理数据下载初始化码表与对照用时：{}ms",(System.currentTimeMillis() - start));
        try{
            TdZxzlBeginDate beginDate = this.rectficationService.findTdZxzlBeginDate();
            while(true){
                start = System.currentTimeMillis();
                String returnJson = this.requestRectfi(beginDate);
                ifBreak = StringUtils.isBlank(returnJson);
                log.info("专项治理数据下载用时 {}ms,返回：{}",(System.currentTimeMillis() - start), returnJson);
                start = System.currentTimeMillis();
                if(ifBreak){
                    return;
                }
                //去除长度为1的数组里的空对象
                returnJson = returnJson.replaceAll("\\[\\{\\}\\]","[]");
                SpecialRectficationReturnVo rectficationReturnVo = JSON.parseObject(returnJson, SpecialRectficationReturnVo.class);
                ifBreak = null == rectficationReturnVo || !"00".equals(rectficationReturnVo.getType()) ||
                        0 == rectficationReturnVo.getTotal();
                if(ifBreak){
                    return;
                }
                SpecialRectficationPojo dataPo = new SpecialRectficationPojo();
                dataPo.setZxzlBeginDate(beginDate);
                //校验 封装
                this.validateAndFillDataPo(rectficationReturnVo.getRectificationListExpList(), dataPo);
                ifBreak = CollectionUtils.isEmpty(dataPo.getRectificationLists());
                if(ifBreak){
                    break;
                }
                this.rectficationService.saveOrUpdateSpecialRectficationInfo(dataPo);
                beginDate = dataPo.getZxzlBeginDate();
                log.info("专项治理数据下载本次完成校验存储总耗时：{} ms",(System.currentTimeMillis()-start));
            }
        }catch(Exception e){
            e.printStackTrace();
        }
        log.info("专项治理数据下载这一轮定时任务完成");
    }

    /**
     * <p>方法描述：校验以及封装 </p>
     * pw 2023/9/21
     **/
    private void validateAndFillDataPo(List<SpecialRectficationBaseInfoVo> infoList, SpecialRectficationPojo dataPo){
        if(CollectionUtils.isEmpty(infoList)){
            return;
        }
        infoList = infoList.stream().filter(v -> StringUtils.isNotBlank(v.getUid())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(infoList)){
            return;
        }
        List<String> uuidList = infoList.stream().map(SpecialRectficationBaseInfoVo::getUid)
                .distinct().collect(Collectors.toList());
        dataPo.setBaseInfoMap(this.rectficationService.findMainInfoByUuidResultMap(uuidList));
        for(SpecialRectficationBaseInfoVo baseInfoVo : infoList){
            this.validateAndFillMainData(baseInfoVo, dataPo);
        }
        this.showErrorTip(dataPo);
        this.fillCrptId(dataPo);
        dataPo.setBaseInfoMap(null);
    }

    /**
     * <p>方法描述： 请求数据下载接口 </p>
     * pw 2023/9/20
     **/
    private String requestRectfi(TdZxzlBeginDate beginDate){
        String tokenId = this.requestToken();
        if(null == tokenId){
            return null;
        }
        log.info("专项治理数据下载tokenId:{}"+tokenId);
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put("modifyTime", null == beginDate || null == beginDate.getBeginDate() ? this.limitDateStr : DateUtils.formatDateTime(beginDate.getBeginDate()));
        paramMap.put("uid", null == beginDate ? null : beginDate.getUuid());
        String jsonStr = JSON.toJSONString(paramMap);
        log.info("专项治理数据下载 paramMap:{}"+jsonStr);
        try{
            String returnJson;
            if(this.ifSSL){
                Map<String,String> headerMap = new HashMap<>();
                headerMap.put("Content-type","application/json; charset=UTF-8");
                headerMap.put("tokenId",tokenId);
                returnJson = HttpRequestUtil.httpSSLRequest(this.rectfiUrl, "POST",jsonStr, headerMap);
            }else{
                BasicHeader[] header = new BasicHeader[1];
                header[0] = new BasicHeader("tokenId", tokenId);
                returnJson = HttpRequestUtil.httpRequestByRawNew(this.rectfiUrl,jsonStr, header);
            }
            return returnJson;
        }catch(Exception e){
            log.error(e.getMessage(),new Throwable(e));
        }
        return null;
    }

    /**
     * <p>方法描述：获取token </p>
     * pw 2023/9/20
     **/
    private String requestToken(){
        try {
            String tokenKey = "token";
            CrptOnLineTokenVo token = (CrptOnLineTokenVo) CaffeineUtil.getIfPresent(BadRsnCaffeineKeyEnum.SPECIAL_RECTIFICATION, tokenKey);
            //有效期
            if(null!=token){
                if(token.getEndDate().after(new Date())){//有效
                    return token.getTokenId();
                }
            }
            String requestUrl = this.tokenUrl+"?secretKey="+this.secretKey;
            String returnJson;
            if(ifSSL){
                returnJson = HttpRequestUtil.httpSSLRequest(requestUrl, "GET",null, null);
            }else{
                returnJson = HttpRequestUtil.httpRequest(requestUrl, "GET",null);
            }
            if(StringUtils.isBlank(returnJson)){
                return null;
            }
            log.info("专项治理数据下载请求tokenId返回：{}", returnJson);
            token = JSON.parseObject(returnJson,CrptOnLineTokenVo.class);
            if(null!=token){
                if(!RespCodeEnum.SUCCESS.getCode().equals(token.getType())){
                    throw new Exception("请求token失败！");
                }
                int hour = 23;
                if(null != token.getValidateTime()){
                    hour = token.getValidateTime()-1;
                }
                if(null == token.getMs()){
                    token.setMs(new Date());
                }
                Date endTime = DateUtils.addHours(token.getMs(),hour);
                token.setEndDate(endTime);
            }
            CaffeineUtil.put(BadRsnCaffeineKeyEnum.SPECIAL_RECTIFICATION,tokenKey,token);
            return token.getTokenId();
        } catch (Exception e) {
            log.error(e.getMessage(),new Throwable(e));
        }
        return null;
    }

    /**
     * <p>方法描述：填充企业rid </p>
     * pw 2023/9/21
     **/
    private void fillCrptId(SpecialRectficationPojo dataPo){
        List<TdRectificationList> rectificationLists = dataPo.getRectificationLists();
        if(CollectionUtils.isEmpty(rectificationLists)){
            return;
        }
        List<String> creditCodeList = rectificationLists.stream().map(TdRectificationList::getCreditCode)
                .distinct().collect(Collectors.toList());
        Map<String,Integer> creditCodeCrptIdMap = this.rectficationService.findCrptIdByInstitutionCodeList(creditCodeList);
        Set<String> creditCodeSet = new HashSet<>();
        List<TdRectificationList> waitFillCrptList = new ArrayList<>();
        List<CrptValueJson> crptValueJsonList = new ArrayList<>();
        for(TdRectificationList baseInfo : rectificationLists){
            String creditCode = baseInfo.getCreditCode();
            if(creditCodeSet.contains(creditCode)){
                continue;
            }
            Integer crptId = creditCodeCrptIdMap.get(creditCode);
            if(null != crptId){
                baseInfo.setFkByCrptId(new TbTjCrpt(crptId));
                continue;
            }
            waitFillCrptList.add(baseInfo);

            CrptValueJson crptValueJson = new CrptValueJson();
            crptValueJson.setZoneId(null == baseInfo.getFkByZoneId() ? null : baseInfo.getFkByZoneId().getRid());
            crptValueJson.setCrptName(baseInfo.getUnitName());
            crptValueJson.setIfSubPrg(0);
            crptValueJson.setInstitutionCode(baseInfo.getCreditCode());
            crptValueJson.setAddress(baseInfo.getAddress());
            crptValueJson.setEnrolAddress(baseInfo.getEnrolAddress());
            crptValueJson.setCrptSizeId(null == baseInfo.getFkByEnterpriseScaleId() ? null : baseInfo.getFkByEnterpriseScaleId().getRid());
            crptValueJson.setEconomyId(null == baseInfo.getFkByEconomicId() ? null : baseInfo.getFkByEconomicId().getRid());
            crptValueJson.setIndusTypeId(null == baseInfo.getFkByIndustryId() ? null : baseInfo.getFkByIndustryId().getRid());
            crptValueJson.setOccManaOffice(baseInfo.getLinkMan());
            crptValueJson.setLinkPhone2(baseInfo.getLinkPhone());
            crptValueJson.setSourceCode("1007");
            crptValueJson.setUuid(creditCode);
            crptValueJsonList.add(crptValueJson);
            creditCodeSet.add(creditCode);
        }
        if(CollectionUtils.isEmpty(crptValueJsonList)){
            return;
        }
        try {
            creditCodeCrptIdMap = syncCrptInfo(crptValueJsonList);
        }catch(Exception e){
            log.error("调用同步企业信息接口异常",e);
            creditCodeCrptIdMap = null;
        }
        if(CollectionUtils.isEmpty(creditCodeCrptIdMap) || creditCodeCrptIdMap.size() != crptValueJsonList.size()){
            rectificationLists.clear();
            return;
        }
        for(TdRectificationList baseInfo : waitFillCrptList){
            Integer crptId = creditCodeCrptIdMap.get(baseInfo.getCreditCode());
            if(null == crptId){
                rectificationLists.clear();
                return;
            }
            baseInfo.setFkByCrptId(new TbTjCrpt(crptId));
        }
    }

    /**
     * <p>方法描述：调用同步用人单位信息接口 </p>
     * pw 2023/9/21
     **/
    private Map<String, Integer> syncCrptInfo(List<CrptValueJson> crptValueJsonList) throws Exception{
        Map<String, Integer> resultMap = new HashMap<>();
        if(CollectionUtils.isEmpty(crptValueJsonList)){
            return resultMap;
        }
        Map<String, List<CrptValueJson>> crptValueListMap = new HashMap<>();
        crptValueListMap.put("crptList", crptValueJsonList);
        String crptValueJsonStr = JSONObject.toJSONString(crptValueListMap);
        String url = this.delSaveOrUpdateCrptUrl;
        log.info("同步用人单位信息接口请求信息：{}", crptValueJsonStr);
        if (!"true".equals(debug)) {
            //加密
            crptValueJsonStr = AesEncryptUtils.aesEncrypt(crptValueJsonStr, key);
        }
        String responseBody = OkHttpUtils.postJsonParams(url, crptValueJsonStr, null);
        if (StringUtils.isBlank(responseBody)) {
            return resultMap;
        }
        //解密
        if (!"true".equals(debug)) {
            responseBody = AesEncryptUtils.aesDecrypt(responseBody, key);
        }
        log.info("同步用人单位信息接口返回信息：{}", responseBody);
        CrptUploadRepDTO repDTO = JSONObject.parseObject(responseBody, CrptUploadRepDTO.class);
        if (repDTO == null) {
            throw new Exception("同步用人单位信息接口失败！返回信息为空！");
        }
        if (!ReturnType.SUCCESS_PROCESS.getTypeNo().equals(repDTO.getType())
                || CollectionUtils.isEmpty(repDTO.getRidList())) {
            if (repDTO.getMess() == null) {
                throw new Exception("同步用人单位信息接口失败！失败返回信息mess为空！");
            }
            throw new Exception("同步用人单位信息接口失败！" + repDTO.getMess());
        }
        List<CrptUploadRepSingleDTO> singleDTOList = repDTO.getRidList();
        for (CrptUploadRepSingleDTO singleDTO : singleDTOList) {
            if (singleDTO == null) {
                throw new Exception("同步用人单位信息接口失败！部分返回信息为空！");
            }
            if (!ReturnType.SUCCESS_PROCESS.getTypeNo().equals(singleDTO.getType())
                    || singleDTO.getRid() == null) {
                if (singleDTO.getMess() == null) {
                    throw new Exception("同步用人单位信息接口失败！部分失败返回信息mess为空！");
                }
                throw new Exception("同步用人单位信息接口失败！" + singleDTO.getMess());
            }
            resultMap.put(singleDTO.getUuid(), singleDTO.getRid());
        }
        return resultMap;
    }

    /**
     * <p>方法描述：单数据校验填充 </p>
     * pw 2023/9/21
     **/
    private void validateAndFillMainData(SpecialRectficationBaseInfoVo baseInfoVo, SpecialRectficationPojo dataPo){
        String uuid = baseInfoVo.getUid();
        TdRectificationList mainInfo = dataPo.getBaseInfoMap().get(uuid);
        if(null == mainInfo){
            mainInfo = new TdRectificationList();
        }
        mainInfo.setUuid(baseInfoVo.getUid());
        mainInfo.setUnitName(baseInfoVo.getEmployerName());
        //地区
        mainInfo.setFkByZoneId(this.findZoneForFill(baseInfoVo.getBizZone(), dataPo));
        mainInfo.setCreditCode(baseInfoVo.getCreditCode());
        if(StringUtils.isBlank(mainInfo.getCreditCode())){
            log.error("uid {} 社会信用代码空！", mainInfo.getUuid());
            return;
        }
        //行业类别
        mainInfo.setFkByIndustryId(this.findSimpleCodeForFill(baseInfoVo.getIndustryCategory(),
                this.industryCacheMap,
                this.contraSubTypeMap.get(3),
                this.simpleCodeTypeMap.get("5002"),
                dataPo.getCodeIndustryContraSet(),
                dataPo.getCodeIndustrySimpleSet()));
        //经济类型
        mainInfo.setFkByEconomicId(this.findSimpleCodeForFill(baseInfoVo.getEconomicCode(),
                this.economicCacheMap,
                this.contraSubTypeMap.get(4),
                this.simpleCodeTypeMap.get("5003"),
                dataPo.getCodeEconomicContraSet(),
                dataPo.getCodeEconomicSimpleSet()));
        //企业规模
        mainInfo.setFkByEnterpriseScaleId(this.findSimpleCodeForFill(baseInfoVo.getEnterpriseScale(),
                this.crptSizeCacheMap,
                this.contraSubTypeMap.get(2),
                this.simpleCodeTypeMap.get("5004"),
                dataPo.getCodeCrptSizeContraSet(),
                dataPo.getCodeCrptSizeSimpleSet()));
        String managementPerson = baseInfoVo.getManagementPerson();
        //超出截取
        mainInfo.setLinkMan(StringUtils.isNotBlank(managementPerson) && managementPerson.length() > 50 ?
                managementPerson.substring(0,50) : managementPerson);
        String managementPersonContact = baseInfoVo.getManagementPersonContact();
        //非电话或者手机号码格式的不存储
        if(StringUtils.isNotBlank(managementPersonContact) && !StringUtils.vertyPhone(managementPersonContact)){
            managementPersonContact = null;
        }
        mainInfo.setLinkPhone(managementPersonContact);
        mainInfo.setAddress(baseInfoVo.getBizAddr());
        mainInfo.setEnrolAddress(baseInfoVo.getZoneFullName());
        mainInfo.setIfAbove10Staff(this.exchangeFillFlag(baseInfoVo.getExistsAbove10Staff()));
        mainInfo.setIfMainFactorOver(this.exchangeFillFlag(baseInfoVo.getExistsMainFactorOverproof()));
        mainInfo.setIfWorkPlaceDetection(this.exchangeFillFlag(baseInfoVo.getExistsWorkplaceDetection()));
        mainInfo.setIfOtherFactorOver(this.exchangeFillFlag(baseInfoVo.getExistsOtherFactorOverproof()));
        mainInfo.setDataSource(baseInfoVo.getDataSource());
        mainInfo.setRegulationRsn(baseInfoVo.getRegulationReason());
        mainInfo.setConfirmDate(baseInfoVo.getConfirmDate());
        mainInfo.setReportDate(baseInfoVo.getReportDate());
        mainInfo.setCompleteDate(baseInfoVo.getCompleteDateName());
        mainInfo.setInProgress(baseInfoVo.getRectificationProgress());
        mainInfo.setPreTotalHg(this.exchangeFillFlag(baseInfoVo.getPreviousTotalQualified()));
        mainInfo.setResultAbnormal(baseInfoVo.getResultAbnormal());
        mainInfo.setRectificationResult(baseInfoVo.getRectificationResult());
        mainInfo.setReportState(baseInfoVo.getReportState());
        mainInfo.setCheckUnit(baseInfoVo.getDismissUnit());
        mainInfo.setCheckPsn(baseInfoVo.getDismissPsn());
        mainInfo.setCheckDate(baseInfoVo.getDismissDate());
        mainInfo.setCheckAdv(baseInfoVo.getDismissReason());
        mainInfo.setServiceOrgName(baseInfoVo.getServiceOrganName());
        mainInfo.setUpdateDate(baseInfoVo.getModifyDate());

        this.detailData(baseInfoVo.getTdPreviousSituationExpList(), mainInfo, dataPo);
        this.sehemeData(baseInfoVo.getRectificationSolutionExp(), mainInfo, dataPo);
        this.resultData(baseInfoVo.getRectificationDetectionDetailExpList(), mainInfo, dataPo);
        dataPo.getRectificationLists().add(mainInfo);
    }

    /**
     * <p>方法描述：整改前情况明细校验封装 </p>
     * pw 2023/9/21
     **/
    private void detailData(List<SpecialRectficationDetailVo> detailVoList,
                            TdRectificationList mainInfo,
                            SpecialRectficationPojo dataPo){
        if(CollectionUtils.isEmpty(detailVoList)){
            return;
        }
        for(SpecialRectficationDetailVo detailVo : detailVoList){
            this.validateAndFillDetailData(detailVo, mainInfo, dataPo);
        }
    }
    /**
     * <p>方法描述：整改方案校验封装 </p>
     * pw 2023/9/21
     **/
    private void sehemeData(List<SpecialRectficationSchemeVo> schemeVoList,
                            TdRectificationList mainInfo,
                            SpecialRectficationPojo dataPo){
        if(CollectionUtils.isEmpty(schemeVoList)){
            return;
        }
        for(SpecialRectficationSchemeVo schemeVo : schemeVoList){
            this.validateAndFillSehemeData(schemeVo, mainInfo, dataPo);
        }
    }
    /**
     * <p>方法描述：检测结果明细校验封装 </p>
     * pw 2023/9/21
     **/
    private void resultData(List<SpecialRectficationResultVo> resultVoList,
                            TdRectificationList mainInfo,
                            SpecialRectficationPojo dataPo){
        if(CollectionUtils.isEmpty(resultVoList)){
            return;
        }
        for(SpecialRectficationResultVo resultVo : resultVoList){
            this.validateAndFillResultData(resultVo, mainInfo, dataPo);
        }
    }

    /**
     * <p>方法描述：单个整改前情况明细校验封装 </p>
     * pw 2023/9/21
     **/
    private void validateAndFillDetailData(SpecialRectficationDetailVo detailVo,
                                           TdRectificationList mainInfo,
                                           SpecialRectficationPojo dataPo){
        TdPreviousDetail detail = new TdPreviousDetail();
        detail.setFkByMainId(mainInfo);
        detail.setFkByFactorId(this.findSimpleCodeForFill(detailVo.getHazardCode(),
                this.badRsnCacheMap,
                this.contraSubTypeMap.get(7),
                this.simpleCodeTypeMap.get("5007"),
                dataPo.getCodeBadRsnContraSet(),
                dataPo.getCodeBadRsnSimpleSet()));
        detail.setDetectionStation(detailVo.getDetectionStation());
        detail.setOverweightStation(detailVo.getOverweightStation());
        detail.setDetectionPoint(detailVo.getDetectionPoint());
        detail.setOverweightPoint(detailVo.getOverweightPoint());
        dataPo.getPreviousDetailList().add(detail);
    }
    /**
     * <p>方法描述：单个整改方案校验封装 </p>
     * pw 2023/9/21
     **/
    private void validateAndFillSehemeData(SpecialRectficationSchemeVo schemeVo,
                                           TdRectificationList mainInfo,
                                           SpecialRectficationPojo dataPo){
        TdRectificationScheme scheme = new TdRectificationScheme();
        scheme.setFkByMainId(mainInfo);
        scheme.setRectificationMeasure(schemeVo.getRectificationMeasure());
        scheme.setCapitalInvestment(schemeVo.getCapitalInvestment());
        scheme.setStartDate(schemeVo.getStartDate());
        scheme.setEndDate(schemeVo.getEndDate());
        scheme.setPrincipal(schemeVo.getPrincipal());
        scheme.setContact(schemeVo.getContact());
        dataPo.getRectificationSchemeList().add(scheme);
    }
    /**
     * <p>方法描述：单个检测结果明细校验封装 </p>
     * pw 2023/9/21
     **/
    private void validateAndFillResultData(SpecialRectficationResultVo resultVo,
                                           TdRectificationList mainInfo,
                                           SpecialRectficationPojo dataPo){
        TdRectificationResult result = new TdRectificationResult();
        result.setFkByMainId(mainInfo);
        result.setJobName(resultVo.getStation());
        result.setJudgementResult(resultVo.getJudgementResult());
        result.setCompositiveResult(resultVo.getCompositiveResult());
        result.setPreviousTwa(resultVo.getPreviousTwa());
        result.setCurrentTwa(resultVo.getCurrentTwa());
        result.setFinalTwa(resultVo.getFinalTwa());
        result.setPreviousStel(resultVo.getPreviousStel());
        result.setCurrentStel(resultVo.getCurrentStel());
        result.setFinalStel(resultVo.getFinalStel());
        result.setPreviousMac(resultVo.getPreviousMac());
        result.setCurrentMac(resultVo.getCurrentMac());
        result.setFinalMac(resultVo.getFinalMac());
        result.setPreviousPe(resultVo.getPreviousPe());
        result.setCurrentPe(resultVo.getCurrentPe());
        result.setFinalPe(resultVo.getFinalPe());
        result.setPreviousLex(resultVo.getPreviousLex());
        result.setCurrentLex(resultVo.getCurrentLex());
        result.setFinalLex(resultVo.getFinalLex());
        result.setFkByFactorId(this.findSimpleCodeForFill(resultVo.getFactorCode(),
                this.badRsnCacheMap,
                this.contraSubTypeMap.get(7),
                this.simpleCodeTypeMap.get("5007"),
                dataPo.getCodeBadRsnContraSet(),
                dataPo.getCodeBadRsnSimpleSet()));
        result.setFkByItemId(this.findItemForFill(resultVo.getFactorCode()+"@"+resultVo.getItemCode(), dataPo));
        dataPo.getRectificationResultList().add(result);
    }

    /**
     * <p>方法描述：检测项目对照校验 </p>
     * pw 2023/9/21
     **/
    private TsSimpleCode findItemForFill(String key, SpecialRectficationPojo dataPo){
        TsSimpleCode simpleCode = StringUtils.isBlank(key) ? null : this.itemCacheMap.get(key);
        if(null == simpleCode){
            TsContraSub contraSub = this.contraSubTypeMap.get(8).get(key);
            if(null == contraSub){
                dataPo.getCodeItemContraSet().add(key);
            }else{
                simpleCode = this.simpleCodeTypeMap.get("5581").get(contraSub.getRightCode());
                if(null == simpleCode){
                    dataPo.getCodeItemSimpleSet().add(contraSub.getRightCode());
                }
            }
            if(null == simpleCode){
                simpleCode = new TsSimpleCode();
            }
        }
        if(null != simpleCode){
            this.itemCacheMap.put(key, simpleCode);
        }
        return simpleCode;
    }

    /**
     * <p>方法描述：布尔类型的值转换成是否 </p>
     * pw 2023/9/21
     **/
    private Integer exchangeFillFlag(Boolean fillFlag){
        return null == fillFlag ? null : (fillFlag ? 1 : 0);
    }

    /**
     * <p>方法描述：地区对照校验 </p>
     * pw 2023/9/21
     **/
    private TsZone findZoneForFill(String dsfCode, SpecialRectficationPojo dataPo){
        TsZone tsZone = StringUtils.isBlank(dsfCode) ? null : this.zoneCacheMap.get(dsfCode);
        if(StringUtils.isNotBlank(dsfCode) && null == tsZone){
            Map<String, TsContraSub> contraSubMap = this.contraSubTypeMap.get(1);
            TsContraSub contraSub = contraSubMap.get(dsfCode);
            if(null == contraSub){
                dataPo.getZoneContraSet().add(dsfCode);
            }else {
                tsZone = this.tsZoneMap.get(contraSub.getRightCode());
                if(null == tsZone){
                    dataPo.getZoneSimpleSet().add(contraSub.getRightCode());
                }
            }
            if(null == tsZone){
                tsZone = new TsZone();
            }
        }
        if(null != tsZone){
            this.zoneCacheMap.put(dsfCode, tsZone);
        }
        return tsZone;
    }

    /**
     * <p>方法描述：码表对照校验 </p>
     * pw 2023/9/21
     **/
    private TsSimpleCode findSimpleCodeForFill(String dsfCode,
                                               Map<String, TsSimpleCode> curCacheMap,
                                               Map<String, TsContraSub> contraSubMap,
                                               Map<String, TsSimpleCode> simpleCodeMap,
                                               Set<String> conreaSet,
                                               Set<String> simpleSet){
        TsSimpleCode simpleCode = StringUtils.isBlank(dsfCode) ? null : curCacheMap.get(dsfCode);
        if(StringUtils.isNotBlank(dsfCode) && null == simpleCode){
            TsContraSub contraSub = contraSubMap.get(dsfCode);
            if(null == contraSub){
                conreaSet.add(dsfCode);
            }else{
                simpleCode = simpleCodeMap.get(contraSub.getRightCode());
                if(null == simpleCode){
                    simpleSet.add(contraSub.getRightCode());
                }
            }
            if(null == simpleCode){
                simpleCode = new TsSimpleCode();
            }
        }
        if(null != simpleCode){
            curCacheMap.put(dsfCode, simpleCode);
        }
        return simpleCode;
    }

    /**
     * <p>方法描述：对照失败提示 </p>
     * pw 2023/9/21
     **/
    private void showErrorTip(SpecialRectficationPojo dataPo){
        StringBuilder builder = new StringBuilder();
        List<Object> objList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(dataPo.getZoneContraSet())){
            builder.append("，").append("地区对照失败的第三方编码：{} ");
            objList.add(dataPo.getZoneContraSet());
        }
        if(!CollectionUtils.isEmpty(dataPo.getZoneSimpleSet())){
            builder.append("，").append("找不到地区的zoneGb：{} ");
            objList.add(dataPo.getZoneSimpleSet());
        }
        if(!CollectionUtils.isEmpty(dataPo.getCodeIndustryContraSet())){
            builder.append("，").append("行业类别对照失败的第三方编码：{} ");
            objList.add(dataPo.getCodeIndustryContraSet());
        }
        if(!CollectionUtils.isEmpty(dataPo.getCodeIndustrySimpleSet())){
            builder.append("，").append("找不到行业类别的codeNo：{} ");
            objList.add(dataPo.getCodeIndustrySimpleSet());
        }
        if(!CollectionUtils.isEmpty(dataPo.getCodeEconomicContraSet())){
            builder.append("，").append("经济类型对照失败的第三方编码：{} ");
            objList.add(dataPo.getCodeEconomicContraSet());
        }
        if(!CollectionUtils.isEmpty(dataPo.getCodeEconomicSimpleSet())){
            builder.append("，").append("找不到经济类型的codeNo：{} ");
            objList.add(dataPo.getCodeEconomicSimpleSet());
        }
        if(!CollectionUtils.isEmpty(dataPo.getCodeCrptSizeContraSet())){
            builder.append("，").append("企业规模对照失败的第三方编码：{} ");
            objList.add(dataPo.getCodeCrptSizeContraSet());
        }
        if(!CollectionUtils.isEmpty(dataPo.getCodeCrptSizeSimpleSet())){
            builder.append("，").append("找不到企业规模的codeNo：{} ");
            objList.add(dataPo.getCodeCrptSizeSimpleSet());
        }
        if(!CollectionUtils.isEmpty(dataPo.getCodeBadRsnContraSet())){
            builder.append("，").append("危害因素对照失败的第三方编码：{} ");
            objList.add(dataPo.getCodeBadRsnContraSet());
        }
        if(!CollectionUtils.isEmpty(dataPo.getCodeBadRsnSimpleSet())){
            builder.append("，").append("找不到危害因素的codeNo：{} ");
            objList.add(dataPo.getCodeBadRsnSimpleSet());
        }
        if(!CollectionUtils.isEmpty(dataPo.getCodeItemContraSet())){
            builder.append("，").append("检测项目对照失败的第三方危害因素编码@第三方检测项目编码：{} ");
            objList.add(dataPo.getCodeItemContraSet());
        }
        if(!CollectionUtils.isEmpty(dataPo.getCodeItemSimpleSet())){
            builder.append("，").append("找不到检测项目的codeNo：{} ");
            objList.add(dataPo.getCodeItemSimpleSet());
        }
        if(builder.length() > 0){
            dataPo.setRectificationLists(null);
            log.error(builder.substring(1), objList.toArray());
        }
    }

    /**
     * <p>方法描述： 校验缓存 </p>
     * pw 2023/9/21
     **/
    private boolean ifCacheMapErr(){
        if(!CollectionUtils.isEmpty(this.zoneCacheMap) && this.zoneCacheMap.entrySet().stream().anyMatch(v -> null == v.getValue().getRid())){
            return true;
        }
        if(!CollectionUtils.isEmpty(this.industryCacheMap) && this.industryCacheMap.entrySet().stream().anyMatch(v -> null == v.getValue().getRid())){
            return true;
        }
        if(!CollectionUtils.isEmpty(this.economicCacheMap) && this.economicCacheMap.entrySet().stream().anyMatch(v -> null == v.getValue().getRid())){
            return true;
        }
        if(!CollectionUtils.isEmpty(this.crptSizeCacheMap) && this.crptSizeCacheMap.entrySet().stream().anyMatch(v -> null == v.getValue().getRid())){
            return true;
        }
        if(!CollectionUtils.isEmpty(this.badRsnCacheMap) && this.badRsnCacheMap.entrySet().stream().anyMatch(v -> null == v.getValue().getRid())){
            return true;
        }
        if(!CollectionUtils.isEmpty(this.itemCacheMap) && this.itemCacheMap.entrySet().stream().anyMatch(v -> null == v.getValue().getRid())){
            return true;
        }
        return false;
    }

    /**
     * <p>方法描述：初始化对照及码表 </p>
     * pw 2023/9/20
     **/
    private void initMap(){
        //查询都有缓存 如果已经缓存 不需要重新设置缓存
        if(null != this.contraSubTypeMap){
            return;
        }
        this.contraSubTypeMap = new HashMap<>();
        this.contraSubTypeMap.put(1, this.contraSubService.findTsContraSub("9", "1"));
        this.contraSubTypeMap.put(2, this.contraSubService.findTsContraSub("9", "2"));
        this.contraSubTypeMap.put(3, this.contraSubService.findTsContraSub("9", "3"));
        this.contraSubTypeMap.put(4, this.contraSubService.findTsContraSub("9", "4"));
        this.contraSubTypeMap.put(7, this.contraSubService.findTsContraSub("9", "7"));
        Map<String, List<TsContraSub>> tmpMap = this.contraSubService.findSimpBySpecialDescr("9", "8");
        //key DSF_SPECIAL_DESC+“@”+LEFT_CODE
        Map<String, TsContraSub> fillMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(tmpMap)){
            List<TsContraSub> tmpList;
            for(Map.Entry<String, List<TsContraSub>> mapEntity : tmpMap.entrySet()){
                String dsfSpecial = mapEntity.getKey();
                tmpList = mapEntity.getValue();
                if(CollectionUtils.isEmpty(tmpList) || StringUtils.isBlank(dsfSpecial)){
                    continue;
                }
                for(TsContraSub contraSub : mapEntity.getValue()){
                    String leftCode = contraSub.getLeftCode();
                    if(StringUtils.isBlank(leftCode)){
                        continue;
                    }
                    fillMap.put(dsfSpecial+"@"+leftCode, contraSub);
                }
            }
        }
        this.contraSubTypeMap.put(8, fillMap);
        this.simpleCodeTypeMap = new HashMap<>();
        this.simpleCodeTypeMap.put("5004",this.codeService.findTsSimpleCodeMap("5004"));
        this.simpleCodeTypeMap.put("5002",this.codeService.findTsSimpleCodeMap("5002"));
        this.simpleCodeTypeMap.put("5003",this.codeService.findTsSimpleCodeMap("5003"));
        this.simpleCodeTypeMap.put("5007",this.codeService.findTsSimpleCodeMap("5007"));
        this.simpleCodeTypeMap.put("5581",this.codeService.findTsSimpleCodeMap("5581"));
        this.tsZoneMap = zoneService.findTsZoneMap();

        this.zoneCacheMap = new HashMap<>();
        this.industryCacheMap = new HashMap<>();
        this.economicCacheMap = new HashMap<>();
        this.crptSizeCacheMap = new HashMap<>();
        this.badRsnCacheMap = new HashMap<>();
        this.itemCacheMap = new HashMap<>();
    }
}

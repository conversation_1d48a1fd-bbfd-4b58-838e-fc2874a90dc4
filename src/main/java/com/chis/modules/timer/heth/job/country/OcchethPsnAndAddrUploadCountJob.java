package com.chis.modules.timer.heth.job.country;

import com.chis.modules.timer.heth.enums.CardUploadCountryBusTypeEnum;
import com.chis.modules.timer.heth.logic.vo.TdZwOcchethCardPsnVo;
import com.chis.modules.timer.heth.logic.vo.TdZwUploadAddressVo;
import com.chis.modules.timer.heth.service.TdZwOcchethCardVoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>类描述：职业卫生技术服务信息报送参与人员以及服务地址上传任务 </p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年12月24日
 **/
@Slf4j
@Component
public class OcchethPsnAndAddrUploadCountJob extends PsnAndAddrUploadCountAbstract{

    @Autowired
    private TdZwOcchethCardVoService occhethCardVoService;


    @Scheduled(cron = "${heth-timer.country.sche-cron.occhethPsnCron}")
    public void startPsn() {
        this.uploadPsnData();
    }

    @Scheduled(cron = "${heth-timer.country.sche-cron.occhethAddrCron}")
    public void startAddr(){
        this.uploadAddrData();
    }


    @Override
    public void fillPsnBusType() {
        this.busPsnType = CardUploadCountryBusTypeEnum.OCCPSN.getCode();
    }

    @Override
    public List<TdZwOcchethCardPsnVo> queryPsnDataList(Integer dataSize) {
        return occhethCardVoService.findOcchethCardPsn(dataSize);
    }

    @Override
    public void fillAddrBusType() {
        this.busAddrType = CardUploadCountryBusTypeEnum.OCCADDR.getCode();
    }

    @Override
    public List<TdZwUploadAddressVo> queryAddrDataList(Integer dataSize) {
        return occhethCardVoService.findOcchethCardAddress(dataSize);
    }
}

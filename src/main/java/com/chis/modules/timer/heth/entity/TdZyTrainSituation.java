package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdZyTrainSituation
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZY_TRAIN_SITUATION")
@KeySequence(value = "TD_ZY_TRAIN_SITUATION_SEQ",clazz = Integer.class)
public class TdZyTrainSituation extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZyUnitbasicinfo fkByMainId;

    @TableField("IF_LEADERS_TRAIN")
    private Integer ifLeadersTrain;

    @TableField("IF_MANAGERS_TRAIN")
    private Integer ifManagersTrain;

    @TableField("TRAIN_SUM")
    private Integer trainSum;

    @TableField("TRAIN_SITUATION")
    private Integer trainSituation;


    public TdZyTrainSituation(Integer rid) {
        super(rid);
    }


}

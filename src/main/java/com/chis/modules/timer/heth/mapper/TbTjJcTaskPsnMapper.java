package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TbTjJcTaskPsn;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2024-10-15,TbTjJcTaskPsnMapper
 */
@Repository
public interface TbTjJcTaskPsnMapper extends ZwxBaseMapper<TbTjJcTaskPsn> {

    void updateBatchByBhkRid(@Param("list") List<Integer> rids);

}

package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.entity.TbTjCrpt;
import com.chis.modules.timer.heth.logic.vo.*;
import com.chis.modules.timer.heth.mapper.CrptMergeMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CrptMergeService {
    @Resource
    private CrptMergeMapper crptMergeMapper;
    @Resource
    private TbTjCrptService crptService;

    /**
     * <p>方法描述：修订 </p>
     * pw 2023/8/11
     **/
    @Transactional(rollbackFor = Exception.class)
    public void executeMergeCrpt(TdTjCrptTaskVo taskVo){
        Integer newestCrptId = taskVo.getNewestCrptId();
        //审核记录
        if(0 == this.crptMergeMapper.checkIfExistBgkLastSta(newestCrptId)){
            this.crptMergeMapper.insertBgkLastSta(newestCrptId, taskVo.getOpePsnId());
        }
        //职业病危害因素明细
        List<CrptBadRsnVo> crptBadRsnList = this.generateCrptBadRsn(taskVo);
        if(!CollectionUtils.isEmpty(crptBadRsnList)){
            this.crptMergeMapper.insertCrptBadRsnBatch(crptBadRsnList, taskVo.getOpePsnId(), newestCrptId);
        }
        List<CrptMergeDynamicUpdatePO> updateList = new ArrayList<>();
        this.fillCrptUpdateData(taskVo, updateList);
        //先更新业务表原ID到SOURCE_CRPT_ID以及SOURCE_EMP_CRPT_ID，再更新用人单位ID以及用工单位ID
        this.generateBusinessSourceIdUpdateData(taskVo.getStopCrptIdList(), updateList);
        this.crptMergeMapper.updateDynamicBatch(updateList);
        updateList.clear();
        this.generateBusinessUpdateData(taskVo.getStopCrptIdList(), newestCrptId, updateList);
        //停用的企业表更新
        this.crptMergeMapper.updateStopCrptInfo(taskVo.getStopCrptIdList());
        //执行顺序必须在updateStopCrptInfo后
        this.crptMergeMapper.updateDynamicBatch(updateList);
        this.crptMergeMapper.updateCrptTask(taskVo.getRid(), 1, null);
    }

    /**
     * <p>方法描述：修订失败 执行的数据库操作 </p>
     * pw 2023/8/11
     **/
    @Transactional(rollbackFor = Exception.class)
    public void mergeCrptFail(TdTjCrptTaskVo taskVo, String errMsg){
        if(StringUtils.isNotBlank(errMsg) && errMsg.length() > 1000){
            errMsg = errMsg.substring(0, 1000);
        }
        List<CrptMergeDynamicUpdatePO> list = new ArrayList<>();
        List<Integer> crptIdList = new ArrayList<>();
        crptIdList.add(taskVo.getNewestCrptId());
        crptIdList.addAll(taskVo.getStopCrptIdList());
        this.fillFailAndSuccessDynamicUpdatePOList(crptIdList, list, "3");
        this.crptMergeMapper.updateCrptTask(taskVo.getRid(), 2, errMsg);
        this.crptMergeMapper.updateDynamicBatch(list);
    }

    /**
     * <p>方法描述：获取待执行的同步业务数据异步任务 </p>
     * pw 2023/8/9
     **/
    public List<TdTjCrptTaskVo> findTdTjCrptTaskVoListBySize(Integer dataSize){
        return this.crptMergeMapper.findTdTjCrptTaskVoListBySize(null == dataSize ? 100 : dataSize);
    }

    /**
     * <p>方法描述：通过异步任务rid集合获取子表信息点集合 </p>
     * pw 2023/8/9
     **/
    public List<TdTjCrptTaskSubVo> findTdTjCrptTaskSubVoListByMainIdList(List<Integer> mainRidList){
        if(CollectionUtils.isEmpty(mainRidList)){
            return Collections.emptyList();
        }
        mainRidList = mainRidList.stream().distinct().collect(Collectors.toList());
        List<List<Integer>> groupList = StringUtils.splitListProxy(mainRidList, 1000);
        List<TdTjCrptTaskSubVo> resultList = new ArrayList<>();
        for(List<Integer> curMainRidList : groupList){
            List<TdTjCrptTaskSubVo> queryResultList = this.crptMergeMapper.findTdTjCrptTaskSubVoListByMainIdList(curMainRidList);
            if(CollectionUtils.isEmpty(queryResultList)){
                continue;
            }
            resultList.addAll(queryResultList);
        }
        return resultList;
    }

    /**
     * <p>方法描述：通过需要修订的主体机构rid集合获取分支机构的单位名称与rid </p>
     * pw 2023/8/10
     **/
    public Map<String,List<String>> findSubCrptNameByFatherIdList(List<Integer> mainRidList){
        if(CollectionUtils.isEmpty(mainRidList)){
            return Collections.emptyMap();
        }
        mainRidList = mainRidList.stream().distinct().collect(Collectors.toList());
        List<List<Integer>> groupList = StringUtils.splitListProxy(mainRidList, 1000);
        List<Map<String,Object>> queryResultList = new ArrayList<>();
        for(List<Integer> tmpRidList : groupList){
            List<Map<String,Object>> resultList = this.crptMergeMapper.findSubCrptNameByFatherIdList(tmpRidList);
            if(!CollectionUtils.isEmpty(resultList)){
                queryResultList.addAll(resultList);
            }
        }
        if(CollectionUtils.isEmpty(queryResultList)){
            return Collections.emptyMap();
        }
        Map<String,List<String>> resultMap = new HashMap<>();
        for(Map<String,Object> map : queryResultList){
            String crptName = null == map.get("crptName") ? null : map.get("crptName").toString();
            String rid = null == map.get("rid") ? null : map.get("rid").toString();
            if(StringUtils.isBlank(crptName) || StringUtils.isBlank(rid)){
                continue;
            }
            List<String> tmpList = resultMap.get(crptName);
            if(null == tmpList){
                tmpList = new ArrayList<>();
            }
            tmpList.add(rid);
            resultMap.put(crptName, tmpList);
        }
        return resultMap;
    }

    /**
     * <p>方法描述：通过社会信用代码获取主体机构企业rid集合 </p>
     * pw 2023/8/11
     **/
    public List<Integer> findMainCrptRidListByInstitutionCode(String institutionCode){
        if(StringUtils.isBlank(institutionCode)){
            return Collections.emptyList();
        }
        return this.crptMergeMapper.findMainCrptRidListByInstitutionCode(institutionCode);
    }

    /**
     * <p>方法描述：通过单位名称与社会信用代码 获取企业rid集合 </p>
     * pw 2023/8/11
     **/
    public List<Integer> findRidListByCrptNameAndInstitutionCode(String crptName, String institutionCode){
        if(StringUtils.isBlank(crptName) || StringUtils.isBlank(institutionCode)){
            return Collections.emptyList();
        }
        return this.crptMergeMapper.findRidListByCrptNameAndInstitutionCode(crptName, institutionCode);
    }

    /**
     * <p>方法描述：获取分支机构对应的主体机构 </p>
     * pw 2023/8/12
     **/
    public Integer findSingleMainCrptRidByInstitutionCode(String institutionCode){
        if(StringUtils.isBlank(institutionCode)){
            return null;
        }
        List<Integer> resultList = this.crptMergeMapper.findMainCrptRidListByInstitutionCode(institutionCode);
        return CollectionUtils.isEmpty(resultList) ? null : resultList.get(0);
    }

    /**
     * <p>方法描述：生成差异的企业信息职业病危害因素明细 </p>
     * pw 2023/8/12
     **/
    private List<CrptBadRsnVo> generateCrptBadRsn(TdTjCrptTaskVo taskVo){
        Integer newestCrptId = taskVo.getNewestCrptId();
        List<Integer> stopCrptIdList = taskVo.getStopCrptIdList();
        List<CrptBadRsnVo> stopList = this.crptMergeMapper.findDistinctCrptBadRsnVoList(stopCrptIdList);
        if(!CollectionUtils.isEmpty(stopList)){
            stopList = stopList.stream().filter(v -> null != v.getSourceId() && null != v.getBadRsnId())
                    .collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(stopList)){
            return Collections.emptyList();
        }
        List<Integer> crptIdList = new ArrayList<>();
        crptIdList.add(newestCrptId);
        List<CrptBadRsnVo> curList = this.crptMergeMapper.findDistinctCrptBadRsnVoList(crptIdList);
        Map<Integer,List<Integer>> curBadRsnMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(curList)){
            curList = curList.stream().filter(v -> null != v.getSourceId() && null != v.getBadRsnId())
                    .collect(Collectors.toList());
        }
        if(!CollectionUtils.isEmpty(curList)){
            for(CrptBadRsnVo badRsnVo : curList){
                Integer sourceId = badRsnVo.getSourceId();
                Integer badRsnId = badRsnVo.getBadRsnId();
                List<Integer> tmpList = curBadRsnMap.get(sourceId);
                if(null == tmpList){
                    tmpList = new ArrayList<>();
                }
                tmpList.add(badRsnId);
                curBadRsnMap.put(sourceId, tmpList);
            }
        }
        stopList = stopList.stream().filter(v -> !curBadRsnMap.containsKey(v.getSourceId()) ||
                !curBadRsnMap.get(v.getSourceId()).contains(v.getBadRsnId())).collect(Collectors.toList());
        return stopList;
    }

    /**
     * <p>方法描述：修订失败 组装 更新停用企业记录和最新企业记录的修订状态为“3：修订失败” 数据传输对象 </p>
     * pw 2023/8/12
     **/
    private void fillFailAndSuccessDynamicUpdatePOList(List<Integer> crptIdList, List<CrptMergeDynamicUpdatePO> list, String updateState){
        if(CollectionUtils.isEmpty(crptIdList)){
            return;
        }
        crptIdList = crptIdList.stream().distinct().collect(Collectors.toList());
        for(Integer crptId : crptIdList){
            CrptMergeDynamicUpdatePO updatePO = new CrptMergeDynamicUpdatePO("TB_TJ_CRPT");
            updatePO.setColumnPOList(new ArrayList<>());
            updatePO.setParamList(new ArrayList<>());
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("UPDATE_STATE",updateState,null));
            updatePO.getParamList().add(new CrptMergeDynamicColumnPO("RID",String.valueOf(crptId),null));
            list.add(updatePO);
        }
    }

    /**
     * <p>方法描述：填充修改企业信息的对象 </p>
     * pw 2023/8/12
     **/
    private void fillCrptUpdateData(TdTjCrptTaskVo taskVo, List<CrptMergeDynamicUpdatePO> updateList){
        Integer newestCrptId = taskVo.getNewestCrptId();
        TbTjCrpt opeCrpt = this.crptService.selectByEntity(new TbTjCrpt(newestCrptId));
        Integer ifSubOrg = opeCrpt.getIfSubOrg();
        //最新企业表更新
        CrptMergeDynamicUpdatePO updatePO = new CrptMergeDynamicUpdatePO("TB_TJ_CRPT");
        updatePO.setColumnPOList(new ArrayList<>());
        updatePO.setParamList(new ArrayList<>());
        if(StringUtils.isNotBlank(taskVo.getCrptName())){
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("CRPT_NAME",taskVo.getCrptName(),null));
        }
        if(null != taskVo.getZoneId()){
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("ZONE_ID",String.valueOf(taskVo.getZoneId()),null));
        }
        String creditCode = taskVo.getCreditCode();
        if(StringUtils.isNotBlank(creditCode)){
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("INSTITUTION_CODE",creditCode,null));
        }
        Integer fatherCrptId = 1 == ifSubOrg && StringUtils.isNotBlank(creditCode) && !creditCode.equals(opeCrpt.getInstitutionCode()) ?
                this.findSingleMainCrptRidByInstitutionCode(creditCode) : null;
        if(null != fatherCrptId){
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("UPPER_UNIT_ID",String.valueOf(fatherCrptId),null));
        }
        if(StringUtils.isNotBlank(taskVo.getAddress())){
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("ADDRESS",taskVo.getAddress(),null));
        }
        if(StringUtils.isNotBlank(taskVo.getEnrolAddress())){
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("ENROL_ADDRESS",taskVo.getEnrolAddress(),null));
        }
        if(null != taskVo.getIndusTypeId()){
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("INDUS_TYPE_ID",String.valueOf(taskVo.getIndusTypeId()),null));
        }
        if(null != taskVo.getEconomyId()){
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("ECONOMY_ID",String.valueOf(taskVo.getEconomyId()),null));
        }
        if(null != taskVo.getCrptSizeId()){
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("CRPT_SIZE_ID",String.valueOf(taskVo.getCrptSizeId()),null));
        }
        if(StringUtils.isNotBlank(taskVo.getLinkMan())){
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("LINKMAN2",taskVo.getLinkMan(),null));
        }
        if(StringUtils.isNotBlank(taskVo.getLinkPhone())){
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("LINKPHONE2",taskVo.getLinkPhone(),null));
        }
        updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("DEL_MARK","0",null));
        updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("UPDATE_STATE","2",null));
        //+营业状态20230606
        updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("OPERATION_STATUS","1",null));
        updatePO.getParamList().add(new CrptMergeDynamicColumnPO("RID",String.valueOf(newestCrptId),null));
        updateList.add(updatePO);
        if(0 != opeCrpt.getIfSubOrg()){
            return ;
        }
        //主体机构社会信用代码改变了调整分支机构社会信用代码
        if(StringUtils.isNotBlank(creditCode) && !creditCode.equals(opeCrpt.getInstitutionCode())){
            updatePO = new CrptMergeDynamicUpdatePO("TB_TJ_CRPT");
            updatePO.setColumnPOList(new ArrayList<>());
            updatePO.setParamList(new ArrayList<>());
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("INSTITUTION_CODE",creditCode,null));
            updatePO.getParamList().add(new CrptMergeDynamicColumnPO("UPPER_UNIT_ID",String.valueOf(newestCrptId),null));
            updateList.add(updatePO);
        }
        if(StringUtils.isBlank(creditCode)){
            creditCode = opeCrpt.getInstitutionCode();
        }
        //停用企业的分支机构合并到最新企业
        for(Integer stopCrptId : taskVo.getStopCrptIdList()){
            updatePO = new CrptMergeDynamicUpdatePO("TB_TJ_CRPT");
            updatePO.setColumnPOList(new ArrayList<>());
            updatePO.setParamList(new ArrayList<>());
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("INSTITUTION_CODE",creditCode,null));
            updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO("UPPER_UNIT_ID",String.valueOf(newestCrptId),null));
            updatePO.getParamList().add(new CrptMergeDynamicColumnPO("UPPER_UNIT_ID",String.valueOf(stopCrptId),null));
            updateList.add(updatePO);
        }
    }

    /**
     * <p>方法描述：填充修改业务表sourceId的对象 </p>
     * pw 2023/8/12
     **/
    private void generateBusinessSourceIdUpdateData(List<Integer> stopCrptIdList, List<CrptMergeDynamicUpdatePO> updateList){
        List<String> tableNameList = this.genTableNameList();
        Map<String, String> empColumnMap = this.genEmpColumnMap();
        for(Integer paramCrptId : stopCrptIdList){
            for(String tableName : tableNameList){
                updateList.add(this.generateBusinessSourceIdUpdateDataHelper(tableName, "SOURCE_CRPT_ID", "CRPT_ID", String.valueOf(paramCrptId)));
                if(empColumnMap.containsKey(tableName)){
                    updateList.add(this.generateBusinessSourceIdUpdateDataHelper(tableName, "SOURCE_EMP_CRPT_ID", empColumnMap.get(tableName), String.valueOf(paramCrptId)));
                }
            }
        }
    }

    /**
     * <p>方法描述：辅助填充修改业务表sourceId的对象 </p>
     * pw 2023/8/12
     **/
    private CrptMergeDynamicUpdatePO generateBusinessSourceIdUpdateDataHelper(String tableName, String sourceColumnName,
                                                          String valColumnName, String paramCrptId){
        CrptMergeDynamicUpdatePO updatePO = new CrptMergeDynamicUpdatePO(tableName);
        updatePO.setColumnPOList(new ArrayList<>());
        updatePO.setParamList(new ArrayList<>());
        updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO(sourceColumnName,null,valColumnName));
        updatePO.getParamList().add(new CrptMergeDynamicColumnPO(valColumnName,paramCrptId,null));
        updatePO.getParamList().add(new CrptMergeDynamicColumnPO(sourceColumnName,null,null));
        return updatePO;
    }

    /**
     * <p>方法描述：填充修改业务表企业信息的对象 </p>
     * pw 2023/8/12
     **/
    private void generateBusinessUpdateData(List<Integer> stopCrptIdList, Integer newestCrptId ,
                                            List<CrptMergeDynamicUpdatePO> updateList){
        List<String> tableNameList = this.genTableNameList();
        Map<String, String> empColumnMap = this.genEmpColumnMap();
        for(Integer paramCrptId : stopCrptIdList){
            for(String tableName : tableNameList){
                updateList.add(this.generateBusinessUpdateDataHelper(tableName, "CRPT_ID", String.valueOf(newestCrptId), String.valueOf(paramCrptId)));
                if(empColumnMap.containsKey(tableName)){
                    updateList.add(this.generateBusinessUpdateDataHelper(tableName, empColumnMap.get(tableName), String.valueOf(newestCrptId), String.valueOf(paramCrptId)));
                }
            }
        }
    }

    /**
     * <p>方法描述：辅助填充修改业务表企业信息的对象 </p>
     * pw 2023/8/12
     **/
    private CrptMergeDynamicUpdatePO generateBusinessUpdateDataHelper(String tableName, String valColumnName,
                                                  String updateCrptId, String paramCrptId){
        CrptMergeDynamicUpdatePO updatePO = new CrptMergeDynamicUpdatePO(tableName);
        updatePO.setColumnPOList(new ArrayList<>());
        updatePO.setParamList(new ArrayList<>());
        updatePO.getColumnPOList().add(new CrptMergeDynamicColumnPO(valColumnName,updateCrptId,null));
        updatePO.getParamList().add(new CrptMergeDynamicColumnPO(valColumnName,paramCrptId,null));
        return updatePO;
    }

    /**
     * <p>方法描述： 操作的业务表名称 </p>
     * pw 2023/8/12
     **/
    private List<String> genTableNameList(){
        List<String> resultList = new ArrayList<>();
        resultList.add("TB_TJ_CRPT_MULTI");
        resultList.add("TD_TJ_BHK");
        resultList.add("TD_TJ_BHK_CLT");
        resultList.add("TD_YSJC_CHK_CONTRACT");
        resultList.add("TD_ZDZYB_BHK_TRANS");
        resultList.add("TD_ZWJD_ARCHIVES");
        resultList.add("TD_ZWJD_ARCHIVES_CARD");
        resultList.add("TD_ZW_OCCDISCASE");
        resultList.add("TD_ZW_OCCDIS_CARD_NEW");
        resultList.add("TD_ZW_YSZYB_RPT");
        resultList.add("TD_ZXJC_UNITBASICINFO");
        resultList.add("TD_ZY_UNITBASICINFO");
        return resultList;
    }

    /**
     * <p>方法描述：有用工单位的表-列 </p>
     * pw 2023/8/12
     **/
    private Map<String, String> genEmpColumnMap(){
        Map<String,String> resultMap = new HashMap<>();
        resultMap.put("TD_TJ_BHK","ENTRUST_CRPT_ID");
        resultMap.put("TD_TJ_BHK_CLT","EMP_CRPT_ID");
        resultMap.put("TD_ZWJD_ARCHIVES_CARD","EMP_CRPT_ID");
        resultMap.put("TD_ZW_OCCDIS_CARD_NEW","EMP_CRPT_ID");
        resultMap.put("TD_ZW_YSZYB_RPT","EMP_CRPT_ID");
        return resultMap;
    }
}

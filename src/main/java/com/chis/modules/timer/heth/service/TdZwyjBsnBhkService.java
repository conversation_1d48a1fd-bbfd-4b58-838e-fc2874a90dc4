package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.entity.TbTjSrvorg;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.mapper.TdZwyjBsnBhkMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnBhkService
 */
@Service
public class TdZwyjBsnBhkService extends ZwxBaseServiceImpl<TdZwyjBsnBhkMapper, TdZwyjBsnBhk> {
    @Autowired
    private TdZwyjBsnBadrsnsService badrsnsService;
    @Autowired
    private TdZwyjBsnAdvCsionService advCsionService;
    @Autowired
    private TdZwyjBsnAdvZdzybService advZdzybService;
    @Autowired
    private TdZwyjBsnCtrlCsionService ctrlCsionService;
    @Autowired
    private TdZwyjBsnCheckService checkService;
    /**
     * <p>方法描述：根据体检编号、体检机构Id查询重点危害因素体检数据</p>
     * @MethodAuthor qrr,2020-10-30,findTdZwyjBsnBhk
     * */
    public List<TdZwyjBsnBhk> findTdZwyjBsnBhk(String bhkCode, Integer orgId){
        TdZwyjBsnBhk bsnBhk = new TdZwyjBsnBhk();
        bsnBhk.setBhkCode(bhkCode);
        bsnBhk.setFkByBhkorgId(new TbTjSrvorg(orgId));
        return this.baseMapper.selectListByEntity(bsnBhk);
    }
    @Transactional(readOnly = false)
    public void saveOrUpdateBsnBhk(TdZwyjBsnBhk bsnBhk,
                                   List<TdZwyjBsnBadrsns> bsnBadrsnsList,
                                   List<TdZwyjBsnCtrlCsion> ctrlCsionList,
                                   List<TdZwyjBsnAdvCsion> advCsionList){
        if(null!=bsnBhk.getRid()){
            //删除接触危害因素
            badrsnsService.deleteByMainId(bsnBhk.getRid());
            //删除审核记录
            checkService.deleteByMainId(bsnBhk.getRid());
        }
        if(null==bsnBhk.getRid()){
            this.save(bsnBhk);
        }else {
            bsnBhk.setDataUpDate(new Date());
            this.updateFullById(bsnBhk);
        }
        if(!CollectionUtils.isEmpty(bsnBadrsnsList)){
            for(TdZwyjBsnBadrsns t:bsnBadrsnsList){
                t.setFkByBhkId(bsnBhk);
                badrsnsService.save(t);
            }
        }
        if(!CollectionUtils.isEmpty(ctrlCsionList)){
            for(TdZwyjBsnCtrlCsion t:ctrlCsionList){
                t.setFkByBhkId(bsnBhk);
                ctrlCsionService.save(t);
            }
        }
        if(!CollectionUtils.isEmpty(advCsionList)){
            for(TdZwyjBsnAdvCsion t:advCsionList){
                t.setFkByBhkId(bsnBhk);
                advCsionService.save(t);
            }
        }
    }
    /**
     * <p>方法描述：根据主表Id删除全部子表</p>
     * @MethodAuthor qrr,2020-11-03,deleteBsnBhk
     * */
    @Transactional(readOnly = false)
    public void deleteBsnBhk(Integer rid){
        if(null==rid){
            return;
        }
        //删除接触危害因素
        badrsnsService.deleteByMainId(rid);
        advCsionService.deleteByMainId(rid);
        advZdzybService.deleteByMainId(rid);
        ctrlCsionService.deleteByMainId(rid);
        checkService.deleteByMainId(rid);
        this.removeById(rid);
    }
}

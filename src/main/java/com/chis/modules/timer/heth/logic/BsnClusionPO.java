package com.chis.modules.timer.heth.logic;

import com.chis.modules.timer.heth.entity.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
/**
 * <p>类描述：危害因素结论配置规则</p>
 *
 * @ClassAuthor qrr,2020-10-29,BsnClusionPO
 * */
@Data
@NoArgsConstructor
public class BsnClusionPO {
    private TdZwyjBsnClusion clusion;
    /**建议结论*/
    private List<TdZwyjBsnClusionAdv> advList;
    /**判定项目*/
    private List<TdZwyjBsnCsionItemSub> itemSubList;
}

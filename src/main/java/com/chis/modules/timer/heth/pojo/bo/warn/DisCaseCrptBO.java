package com.chis.modules.timer.heth.pojo.bo.warn;

import com.chis.modules.timer.heth.entity.TdZwWarnInfo;
import lombok.Data;

import java.util.*;

/**
 * 单位BO
 */
@Data
public class DisCaseCrptBO {
    /**
     * 单位ID
     */
    private Integer crptId;
    /**
     * 单位名称
     */
    private String crptName;
    /**
     * 地区ID
     */
    private Integer zoneId;
    /**
     * 地区名称
     */
    private String zoneName;
    /**
     * 预警级别对应起始日期Map
     * <pre>key: 预警级别</pre>
     * <pre>value: 起始日期</pre>
     */
    private Map<Integer, Date> beginDateMap;
    /**
     * 预警级别对应业务数据Map
     * <pre>key: 预警级别</pre>
     * <pre>value: 业务数据</pre>
     */
    private Map<Integer, List<DisCaseBusDataBO>> busDataMap;
    /**
     * 未处置预警
     */
    private Map<String, TdZwWarnInfo> zwWarnInfoMap;

    public DisCaseCrptBO() {
        this.beginDateMap = new HashMap<>();
        this.busDataMap = new HashMap<>();
    }

    public DisCaseCrptBO(Integer crptId, String crptName, Integer zoneId, String zoneName) {
        this.crptId = crptId;
        this.crptName = crptName;
        this.zoneId = zoneId;
        this.zoneName = zoneName;
        this.beginDateMap = new HashMap<>();
        this.busDataMap = new HashMap<>();
    }
}

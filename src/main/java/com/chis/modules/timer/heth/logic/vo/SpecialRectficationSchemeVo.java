package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>类描述：专项治理数据下载 整改方案 </p>
 * pw 2023/9/21
 **/
@Data
public class SpecialRectficationSchemeVo implements Serializable {
    private static final long serialVersionUID = 1816847222435416297L;
    private BigDecimal capitalInvestment;
    private String contact;
    private Date startDate;
    private Date endDate;
    private String principal;
    private String rectificationMeasure;
}

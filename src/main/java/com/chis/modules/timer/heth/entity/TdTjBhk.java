package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.TbTjSrvorg;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdTjBhk
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_TJ_BHK")
public class TdTjBhk extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("BHK_CODE")
    private String bhkCode;

    @TableField(value = "BHKORG_ID" , el = "fkByBhkorgId.rid")
    private TbTjSrvorg fkByBhkorgId;

    @TableField(value = "CRPT_ID" , el = "fkByCrptId.rid")
    private TbTjCrpt fkByCrptId;

    @TableField(value = "PERSON_ID" , el = "fkByPersonId.rid")
    private TdTjPerson fkByPersonId;

    @TableField("PERSON_NAME")
    private String personName;

    @TableField("SEX")
    private String sex;

    @TableField("IDC")
    private String idc;

    @TableField("BRTH")
    private Date brth;

    @TableField("AGE")
    private String age;

    @TableField("ISXMRD")
    private String isxmrd;

    @TableField("LNKTEL")
    private String lnktel;

    @TableField("DPT")
    private String dpt;

    @TableField("WRKNUM")
    private String wrknum;

    @TableField("WRKLNT")
    private String wrklnt;

    @TableField("WRKLNTMONTH")
    private String wrklntmonth;

    @TableField("TCHBADRSNTIM")
    private String tchbadrsntim;

    @TableField("TCHBADRSNMONTH")
    private String tchbadrsnmonth;

    @TableField("WORK_NAME")
    private String workName;

    @TableField("BHK_TYPE")
    private String bhkType;

    @TableField(value = "ONGUARD_STATEID" , el = "fkByOnguardStateid.rid")
    private TsSimpleCode fkByOnguardStateid;

    @TableField("BHK_DATE")
    private Date bhkDate;

    @TableField("BHKRST")
    private String bhkrst;

    @TableField("MHKADV")
    private String mhkadv;

    @TableField("OCP_BHKRSTDES")
    private String ocpBhkrstdes;

    @TableField("MHKDCTNO")
    private String mhkdctno;

    @TableField("MHKDCT")
    private String mhkdct;

    @TableField("JDGDAT")
    private Date jdgdat;

    @TableField("BADRSN")
    private String badrsn;

    @TableField("IF_LACKITM")
    private String ifLackitm;

    @TableField("IF_TARGETDIS")
    private String ifTargetdis;

    @TableField("IF_WRKTABU")
    private String ifWrktabu;

    @TableField("IF_INTEITM_LACK")
    private String ifInteitmLack;

    @TableField("IF_RHK")
    private String ifRhk;

    @TableField("PROCESS_LACK")
    private String processLack;

    @TableField("LACK_MSG")
    private String lackMsg;

    @TableField("PSN_TYPE")
    private String psnType;

    @TableField("LAST_BHK_CODE")
    private String lastBhkCode;

    @TableField("LAST_FST_BHK_CODE")
    private String lastFstBhkCode;

    @TableField("CRPT_NAME")
    private String crptName;

    @TableField("UUID")
    private String uuid;

    @TableField("RPT_PRINT_DATE")
    private Date rptPrintDate;

    @TableField("IF_ITEM_SET_STD")
    private String ifItemSetStd;

    @TableField("SET_STD_MSG")
    private String setStdMsg;

    @TableField("IF_REPORT_INTIME")
    private String ifReportIntime;

    @TableField("IF_INTO_ZDZYB_ANALY")
    private String ifIntoZdzybAnaly;

    @TableField("CHECK_STATE")
    private String checkState;

    @TableField("BACK_RSN")
    private String backRsn;

    @TableField("NOT_CHECK_STATE")
    private String notCheckState;

    @TableField("NOT_CHECK_RSN")
    private String notCheckRsn;

    @TableField(value = "WORK_TYPE_ID" , el = "fkByWorkTypeId.rid")
    private TsSimpleCode fkByWorkTypeId;

    @TableField("WORK_OTHER")
    private String workOther;

    @TableField(value = "CARD_TYPE_ID" , el = "fkByCardTypeId.rid")
    private TsSimpleCode fkByCardTypeId;

    @TableField("HARM_START_DATE")
    private Date harmStartDate;

    @TableField("DEL_RSN")
    private String delRsn;

    @TableField("JC_TYPE")
    private String jcType;

    @TableField("OTHER_BADRSN")
    private String otherBadrsn;

    @TableField("IF_INTO_CHECK")
    private String ifIntoCheck;

    @TableField("IF_INDUS_TYPE_NOSTD")
    private String ifIndusTypeNostd;

    @TableField("IF_CRPT_SIZE_NOSTD")
    private String ifCrptSizeNostd;

    @TableField("IF_ABNOMAL")
    private String ifAbnomal;

    @TableField("STATE")
    private String state;

    @TableField("COUNTY_SMT_DATE")
    private Date countySmtDate;

    @TableField("COUNTY_RST")
    private String countyRst;

    @TableField("COUNTY_AUDIT_ADV")
    private String countyAuditAdv;

    @TableField("COUNTY_CHK_ORGID")
    private String countyChkOrgid;

    @TableField("CITY_SMT_DATE")
    private Date citySmtDate;

    @TableField("CITY_RST")
    private String cityRst;

    @TableField("CITY_AUDIT_ADV")
    private String cityAuditAdv;

    @TableField("CIYT_CHK_ORGID")
    private String ciytChkOrgid;

    @TableField("PRO_SMT_DATE")
    private Date proSmtDate;

    @TableField("CITY_RST2")
    private String cityRst2;

    @TableField("PRO_AUDIT_ADV")
    private String proAuditAdv;

    @TableField("PRO_CHK_ORGID")
    private String proChkOrgid;

    @TableField("DEAL_COMPLETE_DATE")
    private Date dealCompleteDate;

    @TableField("IF_WRK_AGE_NOSTD")
    private Integer ifWrkAgeNostd;

    //fkByEntrustCrptId替换成entrustId 因映射查询时标识符太长
    @TableField(value = "ENTRUST_CRPT_ID" , el = "entrustId.rid")
    private TbTjCrpt entrustId;
    //企业地区
    private Integer crptZoneId;
    private String crptZoneCode;
    private String frstIfIntoZdzybAnaly;//初检是否纳入重点职业病
    private String frstCheckState;//初检审核标记
    //查询接收的企业规模 非数据库字段 忽略
    @TableField(exist = false)
    private String crptSizeCodeName;
    //查询接收的行业类别
    @TableField(exist = false)
    private String indusTypeCodeName;
    /**用工单位名称*/
    @TableField(exist = false)
    private String entrustCrptName;

    @TableField(exist = false)
    private Integer ifCityDirect;

    @TableField(exist = false)
    private Integer ifProvDirect;

    @TableField(exist = false)
    private String zoneGb;

    @TableField(exist = false)
    private String bhkorgName;


    public TdTjBhk(Integer rid) {
        super(rid);
    }


}

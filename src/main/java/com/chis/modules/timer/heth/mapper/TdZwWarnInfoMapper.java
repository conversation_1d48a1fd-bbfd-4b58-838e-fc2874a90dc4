package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZwWarnInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人, 2023-11-10,TdZwWarnInfoMapper
 */
@Repository
public interface TdZwWarnInfoMapper extends ZwxBaseMapper<TdZwWarnInfo> {
    List<TdZwWarnInfo> selectNotDisposalList(@Param("modelId") Integer modelId,
                                             @Param("busType") Integer busType,
                                             @Param("busId") Integer busId);
}

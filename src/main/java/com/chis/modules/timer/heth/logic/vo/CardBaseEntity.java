package com.chis.modules.timer.heth.logic.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>类描述：职业/放射卫生技术服务信息报送卡 </p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年12月22日
 **/
@Data
public class CardBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @JSONField(serialize = false)
    private Integer rid;
    @JSONField(serialize = false)
    private Integer rcdRid;
    private String securityKey;
    private String ocode;
    @JSONField(serialize = false)
    private Integer reportId;
    @JSONField(serialize = false)
    private String ifBadrsnJc;
    @JSONField(serialize = false)
    private String ifStatusPj;

}

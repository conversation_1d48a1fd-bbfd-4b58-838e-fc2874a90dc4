package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdTjBhkAbnomal;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2022-08-25,TdTjBhkAbnomalMapper
 */
@Repository
public interface TdTjBhkAbnomalMapper extends ZwxBaseMapper<TdTjBhkAbnomal> {
    /**
     * <p>方法描述：通过BHK_ID删除异常信息 </p>
     * @MethodAuthor： pw 2022/8/25
     **/
    void deleteBhkAbnomalByBhkIdList(@Param("bhkIdList") List<Integer> bhkIdList);

     List<TdTjBhkAbnomal> findBhkAbnomals(@Param("ridList") List<Integer> ridList);
}

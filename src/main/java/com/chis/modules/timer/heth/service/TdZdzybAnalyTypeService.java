package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZdzybAnalyType;
import com.chis.modules.timer.heth.mapper.TdZdzybAnalyTypeMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2021-05-11,TdZdzybAnalyTypeService
 */
@Service
public class TdZdzybAnalyTypeService extends ZwxBaseServiceImpl<TdZdzybAnalyTypeMapper, TdZdzybAnalyType> {


    /**
     * @Description: 通过analyType 与 busType 获取重点职业病统计维度对应的统计项ID集合
     *
     * @MethodAuthor pw,2021年05月11日
     */
    public List<Integer> findAnalyItemIdListByAnalyTypeAndBusType(Integer busType, Integer analyType){
        if(null == busType || null == analyType){
            return null;
        }
        return this.baseMapper.findAnalyItemIdListByAnalyTypeAndBusType(analyType, busType);
    }

    /**
     * <p>方法描述：通过analyType 与 busType 获取重点职业病统计维度对应的社会信用代码/单位编码集合 </p>
     * @MethodAuthor： pw 2022/8/25
     **/
    public List<String> findAnalyUnitCodeListByAnalyTypeAndBusType(Integer busType, Integer analyType){
        if(null == busType || null == analyType){
            return null;
        }
        return this.baseMapper.findAnalyUnitCodeListByAnalyTypeAndBusType(analyType, busType);
    }

}

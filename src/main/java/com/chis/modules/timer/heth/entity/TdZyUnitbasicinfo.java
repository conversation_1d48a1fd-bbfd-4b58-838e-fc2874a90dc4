package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import java.util.Date;

import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdZyUnitbasicinfo
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZY_UNITBASICINFO")
@KeySequence(value = "TD_ZY_UNITBASICINFO_SEQ",clazz = Integer.class)
public class TdZyUnitbasicinfo extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("UUID")
    private String uuid;

    @TableField("RECORD_UUID")
    private String recordUuid;

    @TableField("DECLARE_YEAR")
    private Integer declareYear;

    @TableField("DECLARE_TYPE")
    private Integer declareType;

    @TableField("DECLARE_STATUS")
    private Integer declareStatus;

    @TableField("DECLARE_DATE")
    private Date declareDate;

    @TableField("APPROVE_DATE")
    private Date approveDate;

    @TableField(value = "REASON_ID" , el = "fkByReasonId.rid")
    private TsSimpleCode fkByReasonId;

    @TableField("REMARK")
    private String remark;

    @TableField(value = "ZONE_ID" , el = "fkByZoneId.rid")
    private TsZone fkByZoneId;

    @TableField("UNIT_NAME")
    private String unitName;

    @TableField("CREDIT_CODE")
    private String creditCode;

    @TableField("REG_ADDR")
    private String regAddr;

    @TableField("WORK_ADDR")
    private String workAddr;

    @TableField(value = "ENTERPRISE_SCALE_ID" , el = "fkByEnterpriseScaleId.rid")
    private TsSimpleCode fkByEnterpriseScaleId;

    @TableField(value = "INDUSTRY_CATE_ID" , el = "fkByIndustryCateId.rid")
    private TsSimpleCode fkByIndustryCateId;

    @TableField(value = "ECONOMIC_ID" , el = "fkByEconomicId.rid")
    private TsSimpleCode fkByEconomicId;

    @TableField("FILL_MAN")
    private String fillMan;

    @TableField("FILL_PHONE")
    private String fillPhone;

    @TableField("LEGAL_PERSON")
    private String legalPerson;

    @TableField("LEGAL_PERSON_PHONE")
    private String legalPersonPhone;

    @TableField("LINK_MANAGER")
    private String linkManager;

    @TableField("LINK_PHONE")
    private String linkPhone;

    @TableField("EMP_NUM")
    private Integer empNum;

    @TableField("EXTERNAL_NUM")
    private Integer externalNum;

    @TableField("VICTIMS_NUM")
    private Integer victimsNum;

    @TableField("OCCUPATIONAL_DISEASES_NUM")
    private Integer occupationalDiseasesNum;

    @TableField("IF_BRANCH")
    private Integer ifBranch;

    @TableField("PARENT_UNIT_UUID")
    private String parentUnitUuid;

    @TableField("IF_WAR_PRODUCT")
    private Integer ifWarProduct;

    @TableField("OPERATION_STATUS")
    private Integer operationStatus;

    @TableField("PARENT_UNIT_NAME")
    private String parentUnitName;

    @TableField("PARENT_UNIT_CREDIT")
    private String parentUnitCredit;

    @TableField(value = "PARENT_ZONE_ID" , el = "fkByParentZoneId.rid")
    private TsZone fkByParentZoneId;

    @TableField(value = "CRPT_ID")
    private Integer crptId;

    public TdZyUnitbasicinfo(Integer rid) {
        super(rid);
    }


}

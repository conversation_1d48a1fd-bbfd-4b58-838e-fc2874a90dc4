package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsUserInfo;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2023-09-27,TbTjCrptWarn
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TB_TJ_CRPT_WARN")
public class TbTjCrptWarn extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "CRPT_ID" , el = "fkByCrptId.rid")
    private TbTjCrpt fkByCrptId;

    @TableField(value = "WARN_ID" , el = "fkByWarnId.rid")
    private TsSimpleCode fkByWarnId;

    @TableField("WARN_DATE")
    private Date warnDate;

    @TableField(value = "RESULT_ID" , el = "fkByResultId.rid")
    private TsSimpleCode fkByResultId;

    @TableField("DEAL_DATE")
    private Date dealDate;

    @TableField(value = "DEAL_PSN_ID" , el = "fkByDealPsnId.rid")
    private TsUserInfo fkByDealPsnId;

    @TableField("RMK")
    private String rmk;

    @TableField("STATE")
    private Integer state;


    public TbTjCrptWarn(Integer rid) {
        super(rid);
    }


}

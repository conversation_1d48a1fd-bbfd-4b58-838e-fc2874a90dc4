package com.chis.modules.timer.heth.logic.vo;

import cn.hutool.core.convert.Convert;
import com.chis.comm.utils.StringUtils;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 异步导出基础信息Vo
 *
 * @MethodAuthor pw,2021年12月22日
 */
@Data
public class BaseInfoExportVo implements Serializable {
    private static final long serialVersionUID = 4792322986637808931L;
    /** 体检主表rid */
    private Integer bhkRid;
    /** 姓名 */
    private String personName;
    /** 质控编号 */
    private String zkBhkCode;
    /** 体检编号 */
    private String bhkCode;
    /** 性别 */
    private String sex;
    /** 年龄 */
    private Integer age;
    /** 证件类型ID */
    private Integer cardTypeId;
    /** 证件号码 */
    private String idc;
    /** 联系电话 */
    private String linkTel;
    /** 用人单位名称 */
    private String crptName;
    /** 用人单位联系人 */
    private String linkMan2;
    /** 用人单位联系电话 */
    private String linkPhone2;
    /** 用人单位所属地区 */
    private String fullName;
    /** 用人单位通讯地址 */
    private String address;
    /** 用人单位社会信用代码 */
    private String institutionCode;
    /** 用人单位企业规模ID */
    private Integer crptSizeId;
    /** 用人单位经济类型ID */
    private Integer economyId;
    /** 用人单位行业类型ID */
    private Integer indusTypeId;
    /** 监测类型 */
    private Integer jcType;
    /** 在岗状态ID */
    private Integer onguardStateId;
    /** 岗位 */
    private String dpt;
    /** 工种 */
    private String workName;
    /** 总工龄年 */
    private BigDecimal wrklnt;
    /** 总工龄月 */
    private Integer wrklntMonth;
    /** 接害工龄年 */
    private BigDecimal tchBadRsntim;
    /** 接害工龄月 */
    private Integer tchBadRsnMonth;
    /**
     * 体检类型
     */
    private String bhkType;
    /** 体检危害因素 */
    private String badName;
    /**
     * 体检危害因素明细结论
     * 显示格式“体检危害因素名称_体检结论”，多个中文逗号隔开
     */
    private String badRsnRstName;
    /** 主检结论ID */
    private Integer bhkRstId;
    /** 体检机构 */
    private String unitName;
    /** 体检日期 */
    private Date bhkDate;
    /** 报告打印日期 */
    private Date rptPrintDate;
    /** 填报日期 */
    private Date fillDate;
    /** 个案审核接收日期 */
    private Date dealCompleteDate;
    /** 个案审核状态 */
    private Integer chkState;
    /**是否复检*/
    private Integer ifRhk;
    /** 个案审核是否异常 */
    private Integer ifAbnomal;
    /** 个案审核企业规模是否不规范 */
    private Integer ifCrptSizeNostd;
    /** 个案审核行业类别是否不规范 */
    private Integer ifIndusTypeNostd;
    /** 个案审核是否有必检项目未检 */
    private Integer ifInteitmLack;
    /** 个案审核 必检项目未检处理信息 */
    private String lackMsg;
    /** 个案审核 省直属 */
    private Integer ifProvDirect;
    /** 个案审核 市直属 */
    private Integer ifCityDirect;
    /** 个案审核 初审提交日期 */
    private Date countySmtDate;
    /** 个案审核 初审意见 */
    private String countyAuditAdv;
    /** 个案审核 复审提交日期 */
    private Date citySmtDate;
    /** 个案审核 复审意见 */
    private String cityAuditAdv;
    /** 个案审核 终审提交日期 */
    private Date proSmtDate;
    /** 个案审核 终审意见 */
    private String proAuditAdv;
    private Integer checkLevel;
    private Integer zoneType;
    /** 个案审核 状态 */
    private String chkStateStr;

    /**
     * 主检建议
     */
    private String mhkAdv;
    /**
     * 职业禁忌证名称
     * <p>体检主表关联的禁忌证子表 TD_TJ_CONTRAINDLIST</p>
     */
    private String contraindication;
    /**
     * 疑似职业病名称
     * <p>检主表关联的疑似职业病子表 TD_TJ_SUPOCCDISELIST</p>
     */
    private String suOccDisease;
    /** 工龄是否不规范 */
    private Integer ifWrkAgeNostd;
    /** 用工单位名称 */
    private String empCrptName;
    /** 用工单位联系人 */
    private String empLinkMan;
    /** 用工单位联系电话 */
    private String empLinkPhone;
    /** 用工单位所属地区 */
    private String empZone;
    /** 用工单位通讯地址 */
    private String empAddress;
    /** 用工单位社会信用代码 */
    private String empInstItutionCode;
    /** 用工单位企业规模Id */
    private Integer empCrptSizeId;
    /** 用工单位经济类型Id */
    private Integer empEconomyId;
    /** 用工单位行业类别Id */
    private Integer empIndusTypeId;
    /** 接触危害因素 */
    private String touchBadName;
    /** 主动检测危害因素 */
    private String activeBadName;
    /** 国家失败原因*/
    private String errMsg;
    /**防护用品佩戴情况*/
    private String protectEquName;
    /**是否为审核查询*/
    private boolean ifAuditQuery;
    /** 工种代码 */
    private String workNo;

    public Date getDealCompleteDate() {
        if (chkState == null || checkLevel == null || zoneType == null) {
            return null;
        }
        if (2 == checkLevel) {
            if (zoneType <= 3) {
                if (chkState < 4) {
                    dealCompleteDate = null;
                } else if (!new Integer(1).equals(ifProvDirect)) {
                    dealCompleteDate = countySmtDate;
                }
            }
        } else if (3 == checkLevel) {
            if (2 == zoneType) {
                if (chkState < 4) {
                    dealCompleteDate = null;
                } else if (new Integer(1).equals(ifCityDirect)) {
                    dealCompleteDate = countySmtDate;
                } else {
                    dealCompleteDate = citySmtDate;
                }
            } else if (3 == zoneType) {
                if (!new Integer(1).equals(ifCityDirect)) {
                    if (chkState < 2) {
                        dealCompleteDate = null;
                    } else {
                        dealCompleteDate = countySmtDate;
                    }
                }
            }
        }
        return dealCompleteDate;
    }

    public String getChkStateStr() {
        chkStateStr = "";
        if(ifAuditQuery){
            dealAuditQueryState();
        }else{
            dealAuditState();
        }

        return chkStateStr;
    }

    /**
     *  <p>方法描述：审核查询状态封装</p>
     * @MethodAuthor hsj 2024-08-26 16:32
     */
    private void dealAuditQueryState() {
        if (null == chkState) {
            if ("2".equals(checkLevel) && null != ifProvDirect) {
                chkStateStr = "1".equals(Convert.toStr(ifProvDirect)) ? "待终审" : "待初审";
            } else if ("3".equals(checkLevel)) {
                chkStateStr = "待初审";
            }
            return;
        }

        switch (chkState){
            case 0:
                chkStateStr = "初审退回";
                break;
            case 1:
                chkStateStr = "待初审";
                break;
            case 2:
                chkStateStr = "复审退回";
                break;
            case 3:
                chkStateStr = "待复审";
                break;
            case 4:
                chkStateStr = "终审退回";
                break;
            case 5:
                chkStateStr = "待终审";
                break;
            case 6:
                chkStateStr= "终审通过";
                break;
            case 7:
                chkStateStr = "国家退回";
                break;
            default:
                break;
        }
    }


    /**
     *  <p>方法描述：审核状态封装</p>
     * @MethodAuthor hsj 2024-08-26 16:31
     */
    private void dealAuditState() {
        if (chkState == null) {
            return ;
        }
        switch (chkState) {
            case 0:
                if (new Integer(2).equals(checkLevel) || !new Integer(1).equals(ifCityDirect)) {
                    chkStateStr = "区县级退回";
                } else {
                    chkStateStr = "市级退回";
                }
                break;
            case 1:
                if (new Integer(2).equals(checkLevel) || !new Integer(1).equals(ifCityDirect)) {
                    chkStateStr = "区县级待审";
                } else {
                    chkStateStr = "市级待审";
                }
                break;
            case 2:
                chkStateStr = "市级退回";
                break;
            case 3:
                chkStateStr = "市级待审";
                break;
            case 4:
                chkStateStr = "省级退回";
                break;
            case 5:
                chkStateStr = "省级待审";
                break;
            case 6:
                chkStateStr = "省级通过";
                break;
            case 7:
                chkStateStr = "国家退回";
                break;
            default:
                break;
        }
    }
}

package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdTjExport;
import com.chis.modules.timer.heth.mapper.TdTjExportMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2021-12-18,TdTjExportService
 */
@Service
public class TdTjExportService extends ZwxBaseServiceImpl<TdTjExportMapper, TdTjExport> {

    /**
     * @Description: 按创建日期排序的分页查询
     *
     * @MethodAuthor pw,2021年12月18日
     */
    public List<TdTjExport> pageListOrderByCreateTime(Integer first,Integer end,TdTjExport entity){
        if(null == first || null == end || null == entity){
            return null;
        }
        return this.baseMapper.pageListOrderByCreateTime(first, end, entity);
    }

    /**
     *  <p>方法描述：查询创建时间为时间点前且状态为已下载且删除状态为空或者0,按照创建日期倒叙</p>
     * @MethodAuthor hsj 2025-03-06 8:30
     */
    public List<TdTjExport> queryCleanTdExportList(Integer dataSize, String cutoffTime) {
        if(StringUtils.isBlank(cutoffTime)){
            return null;
        }
        return this.baseMapper.queryCleanTdExportList(dataSize, cutoffTime);
    }

    /**
     *  <p>方法描述：导出任务更新</p>
     * @MethodAuthor hsj 2025-03-06 10:51
     */
    public void updateExport(TdTjExport tdTjExport) {
        this.baseMapper.updateFullById(tdTjExport);
    }
}

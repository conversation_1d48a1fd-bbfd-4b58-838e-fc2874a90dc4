package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.comm.utils.ZoneUtil;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.timer.heth.entity.TbTjCrptWarn;
import com.chis.modules.timer.heth.logic.vo.CrptWarnOcchethCardInfoVo;
import com.chis.modules.timer.heth.logic.vo.CrptWarnTjBhkVo;
import com.chis.modules.timer.heth.logic.vo.CrptWarnUnitbasicInfoVo;
import com.chis.modules.timer.heth.logic.vo.CrptWarnZxjcUnitInfoVo;
import com.chis.modules.timer.heth.mapper.EntrustCrptWarnMapper;
import com.chis.modules.timer.heth.mapper.TbTjCrptWarnMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>类描述：用人单位预警计算工具服务类 </p>
 * pw 2023/9/27
 **/
@Service
public class EntrustCrptWarnService {
    @Resource
    private EntrustCrptWarnMapper warnMapper;
    @Resource
    private TbTjCrptWarnMapper crptWarnMapper;
    @Value("${heth-timer.crpt-warn.days}")
    private Integer warnDays;

    /**
     * <p>方法描述：存储 </p>
     * pw 2023/9/28
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateCrptWarn(List<Integer> crptRidList, List<TbTjCrptWarn> crptWarnList, TsSimpleCode resultSimpleCode){
        if(CollectionUtils.isEmpty(crptRidList)){
            return;
        }
        Date curDate = new Date();
        Date nextWarnDate = DateUtils.addDays(curDate, this.warnDays.intValue());
        List<Integer> distinctRidList = crptRidList.stream().distinct().collect(Collectors.toList());
        List<List<Integer>> groupList = StringUtils.splitListProxy(distinctRidList, 1000);
        for(List<Integer> curRidList : groupList){
            this.warnMapper.updateCrptWarnDate(nextWarnDate, curRidList);
        }
        if(CollectionUtils.isEmpty(crptWarnList)){
            return;
        }
        List<TbTjCrptWarn> addWarnList = new ArrayList<>();
        List<TbTjCrptWarn> updateWarnList = new ArrayList<>();
        for(TbTjCrptWarn crptWarn : crptWarnList){
            if(null == crptWarn.getRid()){
                crptWarn.setWarnDate(curDate);
                crptWarn.setState(0);
                crptWarn.setCreateDate(curDate);
                crptWarn.setCreateManid(0);
                addWarnList.add(crptWarn);
            }else{
                //更新为已处置
                crptWarn.setState(1);
                crptWarn.setFkByResultId(resultSimpleCode);
                crptWarn.setDealDate(curDate);
                crptWarn.setModifyDate(curDate);
                updateWarnList.add(crptWarn);
            }
        }
        if(!CollectionUtils.isEmpty(addWarnList)){
            this.crptWarnMapper.insertBatch(addWarnList);
        }
        if(!CollectionUtils.isEmpty(updateWarnList)){
            this.crptWarnMapper.updateFullBatchById(updateWarnList);
        }
    }

    /**
     * <p>方法描述：查询准备处理的企业rid集合 </p>
     * pw 2023/9/27
     **/
    public List<CrptWarnOcchethCardInfoVo> findPrepareWarnCrptIds(String searchZoneGb, Integer pageSize){
        if(StringUtils.isNotBlank(searchZoneGb)){
            searchZoneGb = ZoneUtil.zoneSelect(searchZoneGb);
        }
        return this.warnMapper.findPrepareWarnCrptIds(DateUtils.formatDate(new Date())+" 23:59:59",searchZoneGb, null == pageSize? 100 : pageSize);
    }

    /**
     * <p>方法描述：通过企业rid 获取最新的在线申报相关信息 </p>
     * pw 2023/9/28
     **/
    public Map<Integer, CrptWarnUnitbasicInfoVo> findLastestUnitBasicInfoMapByCrptIds(List<Integer> crptRidList){
        if(CollectionUtils.isEmpty(crptRidList)){
            return Collections.emptyMap();
        }
        List<Integer> distinctRidList = crptRidList.stream().distinct().collect(Collectors.toList());
        Map<Integer, CrptWarnUnitbasicInfoVo> resultMap = new HashMap<>(crptRidList.size());
        List<List<Integer>> groupList = StringUtils.splitListProxy(distinctRidList, 1000);
        distinctRidList.clear();
        for(List<Integer> curCrptIdList : groupList){
            List<Integer> mainInfoRidList = this.pinckLastestUnitBasicInfoRids(this.warnMapper.findLastestUnitBasicInfoByCrptIds(curCrptIdList));
            if(CollectionUtils.isEmpty(mainInfoRidList)){
                continue;
            }
            distinctRidList.addAll(mainInfoRidList);
        }
        if(CollectionUtils.isEmpty(distinctRidList)){
            return Collections.emptyMap();
        }
        groupList = StringUtils.splitListProxy(distinctRidList, 1000);
        List<CrptWarnUnitbasicInfoVo> allQueryResultList = new ArrayList<>(distinctRidList.size());
        for(List<Integer> curRidList : groupList){
            List<CrptWarnUnitbasicInfoVo> queryResultList = this.warnMapper.findLastestUnitBasicInfoByRids(curRidList);
            if(CollectionUtils.isEmpty(queryResultList)){
                continue;
            }
            allQueryResultList.addAll(queryResultList);
        }
        if(CollectionUtils.isEmpty(allQueryResultList)){
            return Collections.emptyMap();
        }
        for(CrptWarnUnitbasicInfoVo infoVo : allQueryResultList){
            resultMap.put(infoVo.getCrptId(), infoVo);
        }
        return resultMap;
    }

    /**
     * <p>方法描述：通过企业rid 获取最新的在线监测相关信息 </p>
     * pw 2023/9/28
     **/
    public Map<Integer, CrptWarnZxjcUnitInfoVo> findLastestZxjcBasicInfoMapByCrptIds(List<Integer> crptRidList){
        if(CollectionUtils.isEmpty(crptRidList)){
            return Collections.emptyMap();
        }
        List<Integer> distinctRidList = crptRidList.stream().distinct().collect(Collectors.toList());
        Map<Integer, CrptWarnZxjcUnitInfoVo> resultMap = new HashMap<>(crptRidList.size());
        List<List<Integer>> groupList = StringUtils.splitListProxy(distinctRidList, 1000);
        distinctRidList.clear();
        for(List<Integer> curCrptIdList : groupList){
            List<Integer> mainInfoRidList = this.pinckLastestZxjcBasicInfoRids(this.warnMapper.findLastestZxjcBasicInfoByCrptIds(curCrptIdList));
            if(CollectionUtils.isEmpty(mainInfoRidList)){
                continue;
            }
            distinctRidList.addAll(mainInfoRidList);
        }
        if(CollectionUtils.isEmpty(distinctRidList)){
            return Collections.emptyMap();
        }
        groupList = StringUtils.splitListProxy(distinctRidList, 1000);
        List<CrptWarnZxjcUnitInfoVo> allQueryResultList = new ArrayList<>(distinctRidList.size());
        for(List<Integer> curRidList : groupList){
            List<CrptWarnZxjcUnitInfoVo> queryResultList = this.warnMapper.findLastestZxjcBasicInfoByRIds(curRidList);
            if(CollectionUtils.isEmpty(queryResultList)){
                continue;
            }
            allQueryResultList.addAll(queryResultList);
        }
        if(CollectionUtils.isEmpty(allQueryResultList)){
            return Collections.emptyMap();
        }
        for(CrptWarnZxjcUnitInfoVo infoVo : allQueryResultList){
            resultMap.put(infoVo.getCrptId(), infoVo);
        }
        return resultMap;
    }

    /**
     * <p>方法描述：通过企业rid 获取最新体检日期 </p>
     * pw 2023/9/28
     **/
    public Map<Integer,Date> findLastestBhkDateMapByEntrustCrptIds(List<Integer> crptRidList){
        if(CollectionUtils.isEmpty(crptRidList)){
            return Collections.emptyMap();
        }
        List<Integer> distinctRidList = crptRidList.stream().distinct().collect(Collectors.toList());
        List<List<Integer>> groupList = StringUtils.splitListProxy(distinctRidList, 1000);
        List<CrptWarnTjBhkVo> allResultList = new ArrayList<>();
        for(List<Integer> curRidList : groupList){
            List<CrptWarnTjBhkVo> queryResultList = this.warnMapper.findLastestBhkDateByEntrustCrptIds(curRidList);
            if(CollectionUtils.isEmpty(queryResultList)){
                continue;
            }
            allResultList.addAll(queryResultList);
        }
        if(CollectionUtils.isEmpty(allResultList)){
            return Collections.emptyMap();
        }
        Map<Integer,Date> resultMap = new HashMap<>(allResultList.size());
        for(CrptWarnTjBhkVo bhkVo : allResultList){
            resultMap.put(bhkVo.getCrptId(), bhkVo.getBhkDate());
        }
        return resultMap;
    }

    /**
     * <p>方法描述：通过企业rid 获取最新体检创建日期 </p>
     * pw 2023/9/28
     **/
    public Map<Integer, Date> findZybLastestCreateDateMapByEntrustCrptIds(List<Integer> crptRidList, String bhkDate){
        if(CollectionUtils.isEmpty(crptRidList)){
            return Collections.emptyMap();
        }
        List<Integer> distinctRidList = crptRidList.stream().distinct().collect(Collectors.toList());
        List<List<Integer>> groupList = StringUtils.splitListProxy(distinctRidList, 1000);
        List<CrptWarnTjBhkVo> allResultList = new ArrayList<>();
        for(List<Integer> curRidList : groupList){
            List<CrptWarnTjBhkVo> queryResultList = this.warnMapper.findZybLastestCreateDateByEntrustCrptIds(curRidList, bhkDate);
            if(CollectionUtils.isEmpty(queryResultList)){
                continue;
            }
            allResultList.addAll(queryResultList);
        }
        if(CollectionUtils.isEmpty(allResultList)){
            return Collections.emptyMap();
        }
        Map<Integer,Date> resultMap = new HashMap<>(allResultList.size());
        for(CrptWarnTjBhkVo bhkVo : allResultList){
            resultMap.put(bhkVo.getCrptId(), bhkVo.getCreateDate());
        }
        return resultMap;
    }

    /**
     * <p>方法描述：通过企业rid获取最新的非标删已提交，并且是职业病危害因素检测的职业卫生技术服务信息报送卡出具技术报告日期 </p>
     * pw 2023/9/28
     **/
    public Map<Integer, Date> findOcchethCardInfoMapByCrptRids(List<Integer> crptRidList){
        if(CollectionUtils.isEmpty(crptRidList)){
            return Collections.emptyMap();
        }
        List<Integer> distinctRidList = crptRidList.stream().distinct().collect(Collectors.toList());
        List<List<Integer>> groupList = StringUtils.splitListProxy(distinctRidList, 1000);
        List<CrptWarnOcchethCardInfoVo> allResultList = new ArrayList<>();
        for(List<Integer> curRidList : groupList){
            List<CrptWarnOcchethCardInfoVo> queryResultList = this.warnMapper.findOcchethCardInfoByCrptRids(curRidList);
            if(CollectionUtils.isEmpty(queryResultList)){
                continue;
            }
            allResultList.addAll(queryResultList);
        }
        if(CollectionUtils.isEmpty(allResultList)){
            return Collections.emptyMap();
        }
        Map<Integer, Date> resultMap = new HashMap<>(allResultList.size());
        for(CrptWarnOcchethCardInfoVo cardInfoVo : allResultList){
            resultMap.put(cardInfoVo.getCrptId(), cardInfoVo.getRptDate());
        }
        return resultMap;
    }

    /**
     * <p>方法描述：通过企业rid 获取最新的预警信息 </p>
     * pw 2023/9/28
     **/
    public Map<String, TbTjCrptWarn> selectLastestWarnByCrptIds(List<Integer> crptRidList){
        if(CollectionUtils.isEmpty(crptRidList)){
            return Collections.emptyMap();
        }
        List<Integer> distinctRidList = crptRidList.stream().distinct().collect(Collectors.toList());
        List<List<Integer>> groupList = StringUtils.splitListProxy(distinctRidList, 1000);
        List<TbTjCrptWarn> allResultList = new ArrayList();
        for(List<Integer> curRidList : groupList){
            List<TbTjCrptWarn> queryResultList = this.crptWarnMapper.selectLastestWarnByCrptIds(curRidList);
            if(CollectionUtils.isEmpty(queryResultList)){
                continue;
            }
            allResultList.addAll(queryResultList);
        }
        if(CollectionUtils.isEmpty(allResultList)){
            return Collections.emptyMap();
        }
        Map<String, TbTjCrptWarn> resultMap = new HashMap<>();
        for(TbTjCrptWarn crptWarn : allResultList){
            String key = crptWarn.getFkByCrptId().getRid()+"@"+crptWarn.getFkByWarnId().getExtends1();
            if(resultMap.containsKey(key)){
                continue;
            }
            resultMap.put(key, crptWarn);
        }
        return resultMap;
    }

    /**
     * <p>方法描述：获取企业最新的在线申报信息rid </p>
     * pw 2023/9/28
     **/
    private List<Integer> pinckLastestUnitBasicInfoRids(List<CrptWarnUnitbasicInfoVo> queryResultList){
        if(CollectionUtils.isEmpty(queryResultList)){
            return Collections.emptyList();
        }
        Set<Integer> crptIdSet = new HashSet<>();
        List<Integer> resultList = new ArrayList<>();
        for(CrptWarnUnitbasicInfoVo infoVo : queryResultList){
            Integer crptId = infoVo.getCrptId();
            Integer rid = infoVo.getRid();
            if(null == crptId || null == rid || crptIdSet.contains(crptId)){
                continue;
            }
            resultList.add(rid);
            crptIdSet.add(crptId);
        }
        return resultList;
    }

    /**
     * <p>方法描述：获取企业最新的在线监测信息rid </p>
     * pw 2023/9/28
     **/
    private List<Integer> pinckLastestZxjcBasicInfoRids(List<CrptWarnZxjcUnitInfoVo> queryResultList){
        if(CollectionUtils.isEmpty(queryResultList)){
            return Collections.emptyList();
        }
        Set<Integer> crptIdSet = new HashSet<>();
        List<Integer> resultList = new ArrayList<>();
        for(CrptWarnZxjcUnitInfoVo infoVo : queryResultList){
            Integer crptId = infoVo.getCrptId();
            Integer rid = infoVo.getRid();
            if(null == crptId || null == rid || crptIdSet.contains(crptId)){
                continue;
            }
            resultList.add(rid);
            crptIdSet.add(crptId);
        }
        return resultList;
    }
}

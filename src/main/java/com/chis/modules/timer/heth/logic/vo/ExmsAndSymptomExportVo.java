package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 异步导出其他和症状VO
 *
 * @ClassAuthor pw,2021年12月21日,ExmsAndSymptomExportVo
 */
@Data
public class ExmsAndSymptomExportVo implements Serializable {
    private static final long serialVersionUID = -3515526349926333279L;
    private Integer bhkRid;
    /** 家族史 */
    private String jzs;
    /** 个人史 */
    private String grs;
    /** 其他 */
    private String other;
    /** 自觉症状 */
    private String sym;
    /** 其他症状 */
    private String othsym;
    /**目前吸烟情况*/
    private String smksta;
    /**吸烟史（年）*/
    private String smkyerqty;
    /**吸烟史（月）*/
    private String smkmthqty;
    /**平均每天吸烟量（支）*/
    private String smkdayble;
    /**烟酒史饮酒情况*/
    private String winsta;
    /**烟酒史酒量*/
    private String windaymlx;
    /**烟酒史酒龄*/
    private String winyerqty;
    /**婚姻史结婚日期*/
    private String mrydat;
    /**配偶接触放射线情况*/
    private String cplrdtcnd;
    /**配偶职业及健康状况*/
    private String cplprfhthcnd;


}

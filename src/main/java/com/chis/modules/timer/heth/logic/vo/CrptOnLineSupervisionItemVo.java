package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
/**
 * <p>类描述：企业在线申报 职业健康监护开展情况明细</p>
 * @ClassAuthor： pw 2022/9/7
 **/
@Data
public class CrptOnLineSupervisionItemVo implements Serializable {
    private static final long serialVersionUID = 3304794595076264827L;
    private Integer hazardsSort;
    private String hazardsName;
    private String codeNo;
    private Boolean supervisionRequirement;
    private Integer peNum;
}

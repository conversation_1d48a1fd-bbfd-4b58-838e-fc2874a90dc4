package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2023-11-10,TdZwWarnPsns
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_WARN_PSNS")
public class TdZwWarnPsns extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwWarnInfo fkByMainId;

    @TableField("BUS_ID")
    private Integer busId;

    @TableField("RCV_DATE")
    private Date rcvDate;

    @TableField("DEAL_DATE")
    private Date dealDate;

    @TableField(value = "DIS_ID" , el = "fkByDisId.rid")
    private TsSimpleCode fkByDisId;


    public TdZwWarnPsns(Integer rid) {
        super(rid);
    }


}

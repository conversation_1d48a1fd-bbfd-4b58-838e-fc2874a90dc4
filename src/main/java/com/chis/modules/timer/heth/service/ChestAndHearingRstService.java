package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.logic.ChestRstPO;
import com.chis.modules.timer.heth.logic.HearingRstPO;
import com.chis.modules.timer.heth.logic.HearingRulePO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

@Slf4j
@Service
public class ChestAndHearingRstService {

    @Value("${heth-timer.chest-hearing-rst.size}")
    private Integer size;

    @Autowired
    private TdTjBhkService bhkService;
    @Autowired
    private TdTjBhksubService bhkSubService;

    private final static Object lock = new Object();

    /**
     * 处理胸片结论，根据不同的胸片结论更新相应的数据列表。
     *
     * @param bhkStartDate      体检开始日期
     * @param bhkEndDate        体检结束日期
     * @param rptPrintStartDate 报表打印开始日期
     * @param rptPrintEndDate   报表打印结束日期
     */
    public void dealChestRst(String bhkStartDate, String bhkEndDate, String rptPrintStartDate, String rptPrintEndDate) {
        int count = 0;
        // 定义一个列表，用于存储查询到的胸片结论信息
        List<ChestRstPO> chestRstPOList;
        // 使用ConcurrentHashMap来存储不同结论的bhkId列表，保证线程安全
        Map<Integer, List<Integer>> rstMap = new ConcurrentHashMap<>();
        // 初始化状态map，分别对应未见异常、其他异常、尘肺样改变、未检查四种结论
        rstMap.put(0, new CopyOnWriteArrayList<>());
        rstMap.put(1, new CopyOnWriteArrayList<>());
        rstMap.put(2, new CopyOnWriteArrayList<>());
        rstMap.put(3, new CopyOnWriteArrayList<>());

        // 循环处理直到没有新的胸片结论需要处理
        do {
            // 从数据库查询指定日期范围内的胸片结论信息
            chestRstPOList = this.bhkSubService.selectChestRstList(
                    bhkStartDate, bhkEndDate, rptPrintStartDate, rptPrintEndDate, this.size
            );
            // 如果查询结论为空，则结束循环
            if (CollectionUtils.isEmpty(chestRstPOList)) {
                break;
            }
            count += chestRstPOList.size();
            // 遍历查询结果，根据胸片结论，将对应的bhkId添加到相应的结论列表中
            // 取权重最大的存储胸片结论（1-尘肺样改变>2-其他异常>0-未见异常>3-未检查）
            for (ChestRstPO chestRstPO : chestRstPOList) {
                // 将胸片结果状态字符串转换为列表
                List<String> chestRstList = StringUtils.string2list(chestRstPO.getRstFlags(), "@");
                // 根据结论列表中的内容，将bhkId添加到对应的结论列表中
                if (chestRstList.contains("1")) {
                    rstMap.get(1).add(chestRstPO.getBhkId());
                } else if (chestRstList.contains("2")) {
                    rstMap.get(2).add(chestRstPO.getBhkId());
                } else if (chestRstList.contains("0")) {
                    rstMap.get(0).add(chestRstPO.getBhkId());
                } else if (chestRstList.contains("3")) {
                    rstMap.get(3).add(chestRstPO.getBhkId());
                }
            }
            // 更新结论列表中的数据
            updateRstBatch(1, rstMap);
        } while (true);
        rstMap.clear();
        log.info("计算胸片结论-已完成, 共处理{}条", count);
    }

    /**
     * 处理电测听结论
     *
     * @param hearingRuleMap    电测听规则映射，键为体检项目编码，值为电测听规则对象
     * @param finalNormalRid    正常结果的标识RID
     * @param finalAbnormalRid  异常结果的标识RID
     * @param bhkStartDate      体检开始日期
     * @param bhkEndDate        体检结束日期
     * @param rptPrintStartDate 报告打印开始日期
     * @param rptPrintEndDate   报告打印结束日期
     */
    public void dealHearingRst(Map<String, HearingRulePO> hearingRuleMap,
                               Integer finalNormalRid,
                               Integer finalAbnormalRid,
                               String bhkStartDate, String bhkEndDate,
                               String rptPrintStartDate, String rptPrintEndDate) {
        int count = 0;
        // 定义一个列表，用于存储查询到的电测听结论信息
        List<HearingRstPO> hearingPOList;
        List<String> itemCodeList = new CopyOnWriteArrayList<>(hearingRuleMap.keySet());
        List<Integer> itemRidList = this.bhkSubService.findItemRidListByCodeList(itemCodeList);
        
        // 判断是否使用RGLTAG模式（如果所有规则都使用RGLTAG模式，则SQL查询也使用RGLTAG模式）
        boolean useRgltagMode = determineUseRgltagMode(hearingRuleMap);
        
        // 使用ConcurrentHashMap来存储不同结论的bhkId列表，保证线程安全
        Map<Integer, List<Integer>> rstMap = new ConcurrentHashMap<>();
        // 初始化状态map，分别对应正常、异常两种结论
        rstMap.put(finalNormalRid, new CopyOnWriteArrayList<>());
        rstMap.put(finalAbnormalRid, new CopyOnWriteArrayList<>());

        // 循环处理直到没有新的电测听结论需要处理
        do {
            // 从数据库查询指定日期范围内的电测听信息
            hearingPOList = this.bhkSubService.selectHearingRstList(
                    itemRidList, bhkStartDate, bhkEndDate, rptPrintStartDate, rptPrintEndDate, this.size, useRgltagMode
            );
            // 如果查询结果为空，则结束循环
            if (CollectionUtils.isEmpty(hearingPOList)) {
                break;
            }
            count += hearingPOList.size();
            // 遍历查询结果
            Map<Integer, List<HearingRstPO>> hearingRstPOMap = new ConcurrentHashMap<>();
            hearingPOList.forEach(hearingRstPO -> {
                if (!hearingRstPOMap.containsKey(hearingRstPO.getBhkId())) {
                    hearingRstPOMap.put(hearingRstPO.getBhkId(), new CopyOnWriteArrayList<>());
                }
                hearingRstPOMap.get(hearingRstPO.getBhkId()).add(hearingRstPO);
            });
            // 判断是否异常
            for (Map.Entry<Integer, List<HearingRstPO>> entry : hearingRstPOMap.entrySet()) {
                Integer bhkId = entry.getKey();
                List<HearingRstPO> results = entry.getValue();
                if (isAbnormal(results, hearingRuleMap)) {
                    rstMap.get(finalAbnormalRid).add(bhkId);
                } else {
                    rstMap.get(finalNormalRid).add(bhkId);
                }
            }

            // 更新列表中的数据
            updateRstBatch(2, rstMap);
        } while (true);
        rstMap.clear();
        log.info("计算电测听结论-已完成, 共处理{}条", count);
    }
    
    /**
     * 判断是否使用RGLTAG模式
     * 如果所有规则都使用RGLTAG模式，则SQL查询也使用RGLTAG模式
     * 否则使用ITEM_RST模式
     */
    private boolean determineUseRgltagMode(Map<String, HearingRulePO> hearingRuleMap) {
        if (hearingRuleMap.isEmpty()) {
            return false;
        }
        
        // 检查是否所有规则都使用RGLTAG模式
        for (HearingRulePO rule : hearingRuleMap.values()) {
            if (!Boolean.TRUE.equals(rule.getUseRgltag())) {
                return false; // 如果有任何一个规则不使用RGLTAG模式，则返回false
            }
        }
        return true; // 所有规则都使用RGLTAG模式
    }

    /**
     * 判断听力结果是否异常。
     * 支持两种判断模式：
     * 1. 原有模式：通过比较听力结果和对应规则中的期望结果，判断是否存在异常
     * 2. 新模式：通过体检子表的合格标记(RGLTAG)判断，有一个不合格则判定异常
     *
     * @param hearingRstPOs  听力结果列表，包含每个项目的代码和结果。
     * @param hearingRuleMap 听力规则映射，通过项目代码获取规则信息，包括期望结果和比较符号。
     * @return 如果存在任何结果与规则比较异常，则返回true；否则返回false。
     */
    private boolean isAbnormal(List<HearingRstPO> hearingRstPOs, Map<String, HearingRulePO> hearingRuleMap) {
        // 遍历每个听力结果
        for (HearingRstPO hearingRstPO : hearingRstPOs) {
            // 根据项目代码获取对应的规则信息
            HearingRulePO rule = hearingRuleMap.get(hearingRstPO.getItemCode());
            if (rule == null) {
                continue;
            }
            
            // 根据规则类型选择判断方式
            if (Boolean.TRUE.equals(rule.getUseRgltag())) {
                // 新模式：根据RGLTAG判断
                if (isAbnormalByRgltag(hearingRstPO)) {
                    return true;
                }
            } else {
                // 原有模式：根据数值比较判断
                if (isAbnormalByValue(hearingRstPO, rule)) {
                    return true;
                }
            }
        }
        // 如果所有结果都符合规则，则返回false
        return false;
    }
    
    /**
     * 根据RGLTAG判断是否异常（新模式）
     * RGLTAG为0表示不合格，为1表示合格
     * 有一个不合格则判定异常
     */
    private boolean isAbnormalByRgltag(HearingRstPO hearingRstPO) {
        if (hearingRstPO.getRgltag() == null) {
            return false; // RGLTAG为空时不判定为异常
        }
        // RGLTAG为0表示不合格，返回true表示异常
        return hearingRstPO.getRgltag() == 0;
    }
    
    /**
     * 根据数值比较判断是否异常（原有模式）
     */
    private boolean isAbnormalByValue(HearingRstPO hearingRstPO, HearingRulePO rule) {
        // 将结果转换为BigDecimal类型，以进行精确的数值比较
        BigDecimal rstValue;
        try {
            rstValue = new BigDecimal(StringUtils.objectToString(hearingRstPO.getItemRst()));
        } catch (Exception e) {
            return false; // 转换失败时不判定为异常
        }
        // 比较规则中的期望结果和实际结果
        int compareResult = rstValue.compareTo(rule.getRst());
        // 根据比较结果和规则中的比较符号判断是否异常
        return compareWithSign(compareResult, rule.getSign());
    }

    /**
     * 根据比较结果和运算符判断比较是否满足条件。
     * 此方法用于根据给定的比较结果（如两个数值的差）和比较运算符（如">","<="等），
     * 判断比较结果是否符合特定的条件。这在需要动态根据不同的比较要求进行判断时非常有用，
     * 它避免了硬编码的条件判断，使代码更加灵活。
     *
     * @param compareResult 比较的结果，通常是一个整数差值。
     * @param sign          比较的运算符，包括">","<=","=","<",">="。
     * @return 如果比较结果符合运算符指定的条件，则返回true；否则返回false。
     * @throws IllegalArgumentException 如果传入的运算符不是有效的比较运算符，则抛出此异常。
     */
    private boolean compareWithSign(int compareResult, String sign) {
        switch (sign) {
            case ">":
                return compareResult > 0;
            case ">=":
                return compareResult >= 0;
            case "=":
                return compareResult == 0;
            case "<":
                return compareResult < 0;
            case "<=":
                return compareResult <= 0;
            default:
                break;
        }
        return false;
    }


    /**
     * 批量更新胸片/电测听结论
     *
     * @param type   更新类型<pre>1: 胸片</pre><pre>2: 电测听</pre>
     * @param rstMap 胸片/电测听结论映射表，键为胸片/电测听结论，值为对应结论下的ID列表
     */
    public void updateRstBatch(int type, Map<Integer, List<Integer>> rstMap) {
        // 遍历胸片判断标记映射表中的每一对键值对
        rstMap.entrySet().stream()
                .filter(entry -> CollectionUtils.isNotEmpty(entry.getValue()))
                .forEach(entry -> {
                    try {
                        synchronized (lock) {
                            // 尝试执行批量更新操作
                            this.bhkService.updateRstBatch(type, entry.getValue(), entry.getKey());
                        }
                    } catch (Exception e) {
                        log.error("批量更新结论出错，{}", e.getMessage(), e);
                    } finally {
                        // 无论成功与否，清空当前胸片判断标记的ID列表，以便下次处理
                        rstMap.put(entry.getKey(), new CopyOnWriteArrayList<>());
                    }
                });
    }
}

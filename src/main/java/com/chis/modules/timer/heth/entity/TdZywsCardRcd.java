package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-12-21,TdZywsCardRcd
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZYWS_CARD_RCD")
public class TdZywsCardRcd extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("BUS_TYPE")
    private Integer busType;

    @TableField("BUS_ID")
    private Integer busId;

    @TableField("STATE")
    private Integer state;

    @TableField("UPLOAD_DATE")
    private Date uploadDate;

    @TableField("REPORT_ID")
    private Integer reportId;

    @TableField("REPORT_CODE")
    private String reportCode;

    @TableField("REPORT_YEAR")
    private Integer reportYear;

    @TableField("ERR_MSG")
    private String errMsg;


    public TdZywsCardRcd(Integer rid) {
        super(rid);
    }


}

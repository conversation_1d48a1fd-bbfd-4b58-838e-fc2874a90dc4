package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>类描述：专项治理数据下载 整改前情况明细 </p>
 * pw 2023/9/21
 **/
@Data
public class SpecialRectficationDetailVo implements Serializable {
    private static final long serialVersionUID = 7301136218434511135L;
    private String hazardName;
    private String hazardCode;
    private Integer detectionStation;
    private Integer overweightStation;
    private Integer detectionPoint;
    private Integer overweightPoint;
}

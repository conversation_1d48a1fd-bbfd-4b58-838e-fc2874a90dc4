package com.chis.modules.timer.heth.handler;

import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

/**
 * 用于国家数据导入生成错误文件
 */
@Slf4j
public class ErrorFileCustomCellWriteHandler implements CellWriteHandler {

    private boolean ifNormalStyle;
    //通用单元格样式
    private CellStyle normalStyle;
    public ErrorFileCustomCellWriteHandler() {}

    public ErrorFileCustomCellWriteHandler(boolean ifNormalStyle) {
        this.ifNormalStyle = ifNormalStyle;
    }

    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        Cell cell = context.getCell();
        if (this.ifNormalStyle && null == this.normalStyle) {
            this.normalStyle = context.getWriteWorkbookHolder().getWorkbook().createCellStyle();
            this.normalStyle.setAlignment(HorizontalAlignment.CENTER);
            this.normalStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            this.normalStyle.setWrapText(true);
            BorderStyle borderStyle = BorderStyle.THIN;
            this.normalStyle.setBorderBottom(borderStyle);
            this.normalStyle.setBorderLeft(borderStyle);
            this.normalStyle.setBorderRight(borderStyle);
            this.normalStyle.setBorderTop(borderStyle);
        }
        if (this.ifNormalStyle) {
            cell.setCellStyle(this.normalStyle);
        }
        if (context.getRowIndex() == 0) {
            if ("失败信息".equals(context.getOriginalValue())) {
                CellStyle cellStyle = context.getWriteWorkbookHolder().getWorkbook().createCellStyle();
                Font font = context.getWriteWorkbookHolder().getWorkbook().createFont();
                font.setColor(IndexedColors.RED.getIndex());
                cellStyle.setFont(font);
                if (this.ifNormalStyle) {
                    cellStyle.setAlignment(HorizontalAlignment.CENTER);
                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    cellStyle.setWrapText(true);
                    BorderStyle borderStyle = BorderStyle.THIN;
                    cellStyle.setBorderBottom(borderStyle);
                    cellStyle.setBorderLeft(borderStyle);
                    cellStyle.setBorderRight(borderStyle);
                    cellStyle.setBorderTop(borderStyle);
                }
                cell.setCellStyle(cellStyle);
            }
        }
    }

}

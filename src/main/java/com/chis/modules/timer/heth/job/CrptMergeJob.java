package com.chis.modules.timer.heth.job;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.entity.TbTjCrpt;
import com.chis.modules.timer.heth.logic.vo.TdTjCrptTaskSubVo;
import com.chis.modules.timer.heth.logic.vo.TdTjCrptTaskVo;
import com.chis.modules.timer.heth.service.CrptMergeService;
import com.chis.modules.timer.heth.service.TbTjCrptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>类描述： 修订企业信息定时器
 * 更新最新企业记录
 * 停用停用企业记录
 * 停用企业相关业务记录关联到最新企业记录
 * </p>
 * pw 2023/8/9
 **/
@Slf4j
@Component
public class CrptMergeJob {
    @Value("${heth-timer.crpt-merge.dataSize}")
    private Integer dataSize;
    @Resource
    private CrptMergeService crptMergeService;
    @Resource
    private TbTjCrptService crptService;

    @Scheduled(cron = "${heth-timer.sche-cron.crptMergeCron}")
    //@Scheduled(initialDelay = 3000,fixedDelay = 60000)
    public void start(){
        log.info("修订企业信息定时任务启动，当前程序版本：{} 启动时间：{} ","20230814",
                DateUtils.formatDateTime(new Date()));
        List<TdTjCrptTaskVo> crptTaskList = this.crptMergeService.findTdTjCrptTaskVoListBySize(this.dataSize);
        long curTime;
        while(!CollectionUtils.isEmpty(crptTaskList)){
            curTime = System.currentTimeMillis();
            this.distributeTask(crptTaskList);
            log.info("修订企业信息定时任务，处理任务数量：{} 用时：{}ms ",crptTaskList.size(),
                    (System.currentTimeMillis() - curTime));
            crptTaskList = this.crptMergeService.findTdTjCrptTaskVoListBySize(this.dataSize);
        }
    }

    /**
     * <p>方法描述：分发任务 </p>
     * pw 2023/8/10
     **/
    private void distributeTask(List<TdTjCrptTaskVo> crptTaskList){
        List<Integer> mainRidList = crptTaskList.stream().mapToInt(TdTjCrptTaskVo::getRid).boxed().collect(Collectors.toList());
        List<TdTjCrptTaskSubVo> taskSubVoList = this.crptMergeService.findTdTjCrptTaskSubVoListByMainIdList(mainRidList);
        Map<Integer, List<TdTjCrptTaskSubVo>> taskSubMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(taskSubVoList)){
            taskSubMap = taskSubVoList.stream().collect(Collectors.groupingBy(TdTjCrptTaskSubVo::getMainId));
        }
        this.fillStopCrptIdList(crptTaskList, taskSubMap);
        for(TdTjCrptTaskVo taskVo : crptTaskList){
            this.executeTask(taskVo);
        }
    }

    /**
     * <p>方法描述： 执行任务 </p>
     * pw 2023/8/10
     **/
    private void executeTask(TdTjCrptTaskVo taskVo){
        String errorMsg = this.validateMergeCrpt(taskVo);
        if(StringUtils.isNotBlank(errorMsg)){
            log.error("修订企业信息定时器异步任务rid：{} 未通过校验，原因： {}", taskVo.getRid(), errorMsg);
            this.mergeFail(taskVo, errorMsg);
            return;
        }
        try{
            this.crptMergeService.executeMergeCrpt(taskVo);
        }catch(Exception e){
            log.error("修订企业信息定时器-执行任务返回异常，异步任务rid："+taskVo.getRid(),e);
            this.mergeFail(taskVo, e.getMessage());
        }
    }

    /**
     * <p>方法描述：异常处理逻辑 </p>
     * pw 2023/8/10
     **/
    private void mergeFail(TdTjCrptTaskVo taskVo, String errMsg){
        try{
            this.crptMergeService.mergeCrptFail(taskVo, errMsg);
        }catch(Exception e){
            log.error("修订企业信息定时器-执行异常处理逻辑返回异常，异步任务rid："+taskVo.getRid(),e);
            e.printStackTrace();
        }
    }

    /**
     * <p>方法描述：校验 </p>
     * pw 2023/8/10
     **/
    private String validateMergeCrpt(TdTjCrptTaskVo taskVo){
        TbTjCrpt opeCrpt = this.crptService.selectByEntity(new TbTjCrpt(taskVo.getNewestCrptId()));
        if(null == opeCrpt){
            return "通过企业Id，未找到最新企业";
        }
        if(null == opeCrpt.getIfSubOrg()){
            return "是否分支机构异常";
        }
        String crptName = StringUtils.isBlank(taskVo.getCrptName()) ? opeCrpt.getCrptName() : taskVo.getCrptName();
        String institutionCode = StringUtils.isBlank(taskVo.getCreditCode()) ? opeCrpt.getInstitutionCode() : taskVo.getCreditCode();
        if(StringUtils.isBlank(crptName)){
            return "需要操作的最新企业信息单位名称不允许为空";
        }
        if(StringUtils.isBlank(institutionCode)){
            return "需要操作的最新企业信息社会信用代码不允许为空";
        }
        if(CollectionUtils.isEmpty(taskVo.getStopCrptIdList())){
            return "任务子表未找到或者需要停用企业ID为空";
        }
        Integer ifSubOrg = opeCrpt.getIfSubOrg();
        List<Integer> crptRidList = new ArrayList<>();
        crptRidList.add(taskVo.getNewestCrptId());
        crptRidList.addAll(taskVo.getStopCrptIdList());
        if(1 == ifSubOrg){
            return this.validateSubCrpt(crptRidList, crptName, institutionCode);
        }else{
            return this.validateMainCrpt(crptRidList, crptName, institutionCode);
        }
    }

    /**
     * <p>方法描述：主体机构修订校验 </p>
     * pw 2023/8/11
     **/
    private String validateMainCrpt(List<Integer> crptRidList, String crptName, String institutionCode){
        StringBuilder errorBuilder = new StringBuilder();
        //主体机构社会信用代码唯一
        List<Integer> existMainCrptRidList = this.crptMergeService.findMainCrptRidListByInstitutionCode(institutionCode);
        if(CollectionUtils.isEmpty(existMainCrptRidList)){
            errorBuilder.append("通过社会信用代码").append(institutionCode).append("未找到对应的主体机构，不符合逻辑");
            return errorBuilder.toString();
        }
        existMainCrptRidList.removeAll(crptRidList);
        if(!CollectionUtils.isEmpty(existMainCrptRidList)){
            errorBuilder.append("通过社会信用代码")
                    .append(institutionCode)
                    .append("找到多条主体机构，rid:").append(StringUtils.list2string(existMainCrptRidList, "、"));
            return errorBuilder.toString();
        }
        //单位名称不能重复
        Map<String,List<String>> subCrptNameMap = this.crptMergeService.findSubCrptNameByFatherIdList(crptRidList);
        if(CollectionUtils.isEmpty(subCrptNameMap)){
            return null;
        }
        List<String> existCrptNameRidList = subCrptNameMap.get(crptName);
        if(!CollectionUtils.isEmpty(existCrptNameRidList)){
            errorBuilder.append("，").append("修订后会出现主体机构单位名称与分支机构单位名称相同的情况，对应分支机构rid：")
                    .append(StringUtils.list2string(existCrptNameRidList, "、"));
        }
        List<Map.Entry<String,List<String>>> tmpList = subCrptNameMap.entrySet().stream()
                .filter(v -> !CollectionUtils.isEmpty(v.getValue()) && v.getValue().size()>1)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(tmpList)){
            return errorBuilder.length() > 0 ? errorBuilder.substring(1) : null;
        }
        errorBuilder.append("，").append("修订后会出现分支机构名称重复的情况");
        for(Map.Entry<String,List<String>> mapEntity : tmpList){
            errorBuilder.append("，").append(mapEntity.getKey())
                    .append("rid：").append(StringUtils.list2string(mapEntity.getValue(), "、"));
        }
        return errorBuilder.length() > 0 ? errorBuilder.substring(1) : null;
    }

    /**
     * <p>方法描述：分支机构修订校验 </p>
     * pw 2023/8/11
     **/
    private String validateSubCrpt(List<Integer> crptRidList, String crptName, String institutionCode){
        Integer fatherCrptId = this.crptMergeService.findSingleMainCrptRidByInstitutionCode(institutionCode);
        StringBuilder errBuilder = new StringBuilder();
        if(null == fatherCrptId){
            errBuilder.append("，").append("分支机构修订，未找到对应的主体机构");
        }
        List<Integer> existRidList = this.crptMergeService.findRidListByCrptNameAndInstitutionCode(crptName, institutionCode);
        if(!CollectionUtils.isEmpty(existRidList)){
            existRidList.removeAll(crptRidList);
        }
        if(CollectionUtils.isEmpty(existRidList)){
            return errBuilder.length() > 0 ? errBuilder.substring(1) : null;
        }
        errBuilder.append("，")
                .append("分支机构修订，当前修订后会出现社会信用代码与单位名称重复的数据，对应企业rid：")
                .append(StringUtils.list2string(existRidList, "、"));
        return  errBuilder.substring(1);
    }

    /**
     * <p>方法描述：填充停用的企业rid集合 </p>
     * pw 2023/8/10
     **/
    private void fillStopCrptIdList(List<TdTjCrptTaskVo> crptTaskList, Map<Integer, List<TdTjCrptTaskSubVo>> taskSubMap){
        if(CollectionUtils.isEmpty(taskSubMap)){
            return;
        }
        for(TdTjCrptTaskVo taskVo : crptTaskList){
            Integer taskId = taskVo.getRid();
            if(taskSubMap.containsKey(taskId)){
                taskVo.setStopCrptIdList(taskSubMap.get(taskId).stream().mapToInt(TdTjCrptTaskSubVo::getStopCrptId)
                        .boxed().collect(Collectors.toList()));
            }
        }
    }
}

package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdTjBhk;
import com.chis.modules.timer.heth.entity.TdTjBhkAbnomal;
import com.chis.modules.timer.heth.logic.vo.TjPersonSearchConditionQueryPO;
import com.chis.modules.timer.heth.mapper.TdTjBhkMapper;
import com.chis.modules.timer.heth.pojo.BhkCheckCondition;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;
import java.util.Collections;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdTjBhkService
 */
@Service
public class TdTjBhkService extends ZwxBaseServiceImpl<TdTjBhkMapper, TdTjBhk> {
    /**
     * 批量处理最大条数
     */
    private static final int MAX_BATCH_SIZE = 250;

    @Resource
    private TdTjBhkAbnomalService abnomalService;
    /**
     	 * <p>方法描述：查询待处理数据</p>
     	 * @MethodAuthor qrr,2020-10-29,selectBhks
     * */
    public List<TdTjBhk> selectBhks(Integer dataSize, String startDate){
        return this.baseMapper.selectBhks(dataSize,startDate);
    }

    /**
     * @MethodName: selectOutRangeBhks
     * @Description: 超范围服务工具
     * @Param: [dataSize, startDate]
     * @Return: java.util.List<com.chis.modules.timer.heth.entity.TdTjBhk>
     * @Author: maox
     * @Date: 2020-11-12
     **/
    public List<TdTjBhk> selectOutRangeBhks(Integer dataSize, String startDate){
        return this.baseMapper.selectOutRangeBhks(dataSize,startDate);
    }
    
    
    /**
     * <p>
     *     方法描述：危急值工具
     * </p>
     *
     * @MethodAuthor pw,2020年11月23日,
     */
    public List<TdTjBhk> selectDangerValToolBhks(Integer dataSize, String startDate){
        return this.baseMapper.selectDangerValToolBhks(dataSize, startDate);
    }


    /**
     * @Description: 获取个案审核工具准备处理的体检信息
     *
     * @MethodAuthor pw,2021年05月11日
     */
    public List<TdTjBhk> selectAuditCalculationBhks(Integer dataSize, String startDate, Integer ifNotCheckFsBadRsn){
        return this.baseMapper.selectAuditCalculationBhks(dataSize, startDate, ifNotCheckFsBadRsn);
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeUpdateBhksWithBhkItemStd(List<TdTjBhk> bhkList,
                                                List<TdTjBhkAbnomal> bhkAbnomalList, List<Integer> abnomalBhkRidList){
        if(!CollectionUtils.isEmpty(bhkList)){
            this.updateBatchById(bhkList, bhkList.size());
        }
        //先删除异常信息 再新增异常信息
        if(!CollectionUtils.isEmpty(abnomalBhkRidList)){
            this.abnomalService.deleteBhkAbnomalByBhkIdList(abnomalBhkRidList);
        }
        if(!CollectionUtils.isEmpty(bhkAbnomalList)){
            this.abnomalService.insertBatch(bhkAbnomalList);
        }
    }

    /**
     * <p>描述：获取个案查询条件查询的体检信息</p>
     * @param conditionPO 查询条件
     *
     * @return {@link List< Integer>}
     * @Author: 龚哲,2021/12/21 15:36,findTjPersonSearch
     */
    public List<Integer> findTjPersonSearch(TjPersonSearchConditionQueryPO conditionPO) {
        return this.baseMapper.findTjPersonSearch(CollectionUtils.isEmpty(conditionPO.getSearchUnitId()) ? null : conditionPO.getSearchUnitId().get(0)
                , conditionPO.getSearchBhkNum(), conditionPO, conditionPO.getIfAdmin(),conditionPO.getStartRow(),conditionPO.getEndRow());
    }
    /**
     * <p>描述：获取个案查询条件查询的总数</p>
     * @param conditionPO 查询条件
     *
     * @return {@link Integer}
     * @Author: 龚哲,2021/12/28 9:17,findTjPersonSearchCounts
     */
    public Integer findTjPersonSearchCounts(TjPersonSearchConditionQueryPO conditionPO){
        return this.baseMapper.findTjPersonSearchCounts(CollectionUtils.isEmpty(conditionPO.getSearchUnitId()) ? null : conditionPO.getSearchUnitId().get(0)
                , conditionPO.getSearchBhkNum(), conditionPO, conditionPO.getIfAdmin());
    }

    /**
     * <p>方法描述：通过初检rid获取对应的最新复检rid</p>
     * 返回结果 key 初检rid value 对应的最新复检rid
     * @MethodAuthor： pw 2022/7/2
     **/
    public Map<Integer,Integer> findLastestRhkRidByFstRid(List<Integer> mainRidList){
        Map<Integer,Integer> resultMap = new HashMap<>();
        List<List<Integer>> groupList = StringUtils.splitListProxy(mainRidList, 1000);
        if(CollectionUtils.isEmpty(groupList)){
            this.fillResultMap(mainRidList, resultMap);
        }else{
            for(List<Integer> tmpList : groupList){
                this.fillResultMap(tmpList, resultMap);
            }
        }
        return resultMap;
    }

    /**
     * <p>方法描述：For findLastestRhkRidByFstRid  辅助方法</p>
     * 辅助通过初检rid获取对应的最新复检rid
     * @MethodAuthor： pw 2022/7/2
     **/
    private void fillResultMap(List<Integer> tmpList, Map<Integer,Integer> resultMap){
        List<Map<String,BigDecimal>> queryResultList = this.baseMapper.findLastestRhkRidByFstRid(tmpList);
        for(Map<String,BigDecimal> map : queryResultList){
            BigDecimal fatherId = map.get("FATHERID");
            BigDecimal rid = map.get("RID");
            if(null != fatherId && null != rid){
                resultMap.put(fatherId.intValue(), rid.intValue());
            }
        }
    }

    /**
     * 批量更新胸片/电测听结论
     *
     * @param type    更新类型<pre>1: 胸片</pre><pre>2: 电测听</pre>
     * @param ridList 体检主表ID列
     * @param rst     结论
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateRstBatch(int type, List<Integer> ridList, Integer rst) {
        if (CollectionUtils.isEmpty(ridList) || rst == null) {
            return;
        }
        int listSize = ridList.size();
        if (listSize < MAX_BATCH_SIZE) {
            if (type == 1) {
                this.baseMapper.updateChestRstBatch(ridList, rst);
            } else if (type == 2) {
                this.baseMapper.updateHearingRstBatch(ridList, rst);
            }
            return;
        }
        IntStream.range(0, (int) Math.ceil((double) listSize / MAX_BATCH_SIZE))
                .forEach(i -> {
                    int start = i * MAX_BATCH_SIZE;
                    int end = Math.min(start + MAX_BATCH_SIZE, ridList.size());
                    if (type == 1) {
                        this.baseMapper.updateChestRstBatch(ridList.subList(start, end), rst);
                    } else if (type == 2) {
                        this.baseMapper.updateHearingRstBatch(ridList.subList(start, end), rst);
                    }
                });
    }
    
    /**
    * <p>Description：根据查询条件查询体检数据 </p>
    * <p>Author： yzz 2024-10-14 </p>
    */
    public List<TdTjBhk> queryCheckByCondition(BhkCheckCondition bhkCheckCondition) {
        return this.baseMapper.queryCheckByCondition(bhkCheckCondition);
    }

    /**
     * 根据条件批量查询体检记录
     * @param conditions 查询条件列表，每个条件包含checkNo和checkOrg
     * @return 体检记录列表
     */
    public List<TdTjBhk> selectBhksByConditions(List<Map<String, String>> conditions) {
        if (conditions == null || conditions.isEmpty()) {
            return Collections.emptyList();
        }

        List<TdTjBhk> result = new ArrayList<>();
        int totalConditions = conditions.size();

        // 分批处理
        for (int i = 0; i < totalConditions; i += MAX_BATCH_SIZE) {
            int end = Math.min(i + MAX_BATCH_SIZE, totalConditions);
            List<Map<String, String>> batch = conditions.subList(i, end);
            List<TdTjBhk> batchResult = this.baseMapper.selectBhksByConditions(batch);
            result.addAll(batchResult);
        }

        return result;
    }
}

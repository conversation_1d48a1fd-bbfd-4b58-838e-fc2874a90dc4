package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-30,TdZdzybAnalyPgItem
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZDZYB_ANALY_PG_ITEM")
public class TdZdzybAnalyPgItem extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZdzybAnalyItmType fkByMainId;

    @TableField(value = "ITEM_ID" , el = "fkByItemId.rid")
    private TbTjItems fkByItemId;

    @TableField("JDGPTN")
    private String jdgptn;

    @TableField("HG_FLAG")
    private String hgFlag;

    @TableField("GE")
    private String ge;

    @TableField("GT")
    private String gt;

    @TableField("LE")
    private String le;

    @TableField("LT")
    private String lt;

    @TableField("XH")
    private String xh;

    @TableField("MSRUNT")
    private String msrunt;

    @TableField("RST_DESC")
    private String rstDesc;

    @TableField("RST_MATCH_VAL")
    private String rstMatchVal;

    @TableField("MSRUNT_ID")
    private String msruntId;


    public TdZdzybAnalyPgItem(Integer rid) {
        super(rid);
    }


}

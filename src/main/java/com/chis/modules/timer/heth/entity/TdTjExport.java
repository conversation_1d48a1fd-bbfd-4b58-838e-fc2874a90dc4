package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;

import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsUnit;
import com.chis.modules.sys.entity.TsUserInfo;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-12-18,TdTjExport
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_TJ_EXPORT")
public class TdTjExport extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "BUS_TYPE_ID" , el = "fkByBusTypeId.rid")
    private TsSimpleCode fkByBusTypeId;

    @TableField("EXPORT_CONDITION")
    private String exportCondition;//CLOB类型的换成String

    @TableField("EXPORT_CONDITION_SHOW")
    private String exportConditionShow;

    @TableField("EXPORT_DATE")
    private Date exportDate;

    @TableField("STATE")
    private Integer state;

    @TableField(value = "OPER_UNIT_ID" , el = "fkByOperUnitId.rid")
    private TsUnit fkByOperUnitId;

    @TableField(value = "OPER_PSN_ID" , el = "fkByOperPsnId.rid")
    private TsUserInfo fkByOperPsnId;

    @TableField("EXPORT_FILE_NAME")
    private String exportFileName;

    @TableField("EXPORT_FILE_PATH")
    private String exportFilePath;

    @TableField("EXPORT_FILE_DATE")
    private Date exportFileDate;

    @TableField("ERROR_MSG")
    private String errorMsg;

    // 0/空=未处理 1=成功 2=失败
    @TableField("DEL_FILE_STATE")
    private Integer delFileState;

    @TableField("DEL_FILE_ERROR_MSG")
    private String delFileErrorMsg;


    public TdTjExport(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 问诊(非)放射职业史信息 异步导出VO
 *
 * @ClassAuthor pw,2021年12月21日,EmhistoryExportVo
 */
@Data
public class EmhistoryExportVo implements Serializable {
    private static final long serialVersionUID = 729760404225652215L;
    private Integer bhkRId;
    /** 类型 1 放射 2 非放射 */
    private Integer hisType;
    /** 起止日期 */
    private String stastpDate;
    /** 工作单位名称 */
    private String unitName;
    /** 部门车间 */
    private String department;
    /** 非放射的 防护措施 */
    private String defendStep;

    /** 放射 职业史每日工作时数或工作量 */
    private String prfwrklod;
    /** 放射 职业史累积受照剂量 */
    private String prfshnvlu;
    /** 放射 职业史过量照射史 */
    private String prfexcshn;
    /** 放射 职业照射种类 */
    private String prfraysrt2;
    /** 放射 放射线种类 */
    private String fsszl;
}

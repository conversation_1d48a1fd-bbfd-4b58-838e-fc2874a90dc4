package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZwTjorginfo;
import com.chis.modules.timer.heth.pojo.bo.warn.CheckOrgWarnBO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人, 2020-11-13,TdZwTjorginfoMapper
 */
@Repository
public interface TdZwTjorginfoMapper extends ZwxBaseMapper<TdZwTjorginfo> {
    public List<TdZwTjorginfo> selectAllOrgInfoList();

    List<CheckOrgWarnBO> selectOrgWarnInfoList(@Param("date") Date date,
                                               @Param("newSimpleCodeId") Integer newSimpleCodeId);
}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人, 2022-09-02,TdZwcfKfRcd
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWCF_KF_RCD")
public class TdZwcfKfRcd extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID", el = "fkByMainId.rid")
    private TdZwcfPatientInfo fkByMainId;

    @TableField("YEAR")
    private String year;

    @TableField("AREA")
    private String area;

    @TableField("HOS_NAME")
    private String hosName;

    @TableField("FIRST_RECVERY_TIME")
    private Date firstRecveryTime;

    @TableField("LAST_RECVERY_TIME")
    private Date lastRecveryTime;

    @TableField("HAS_APPARAT_USRECOVERY")
    private Integer hasApparatUsrecovery;

    @TableField("HAS_RECOVERY")
    private Integer hasRecovery;

    @TableField("RECOVERY_NUM")
    private Integer recoveryNum;

    @TableField("RECOVERY_TIME")
    private Integer recoveryTime;

    @TableField("RECOVERYAVE_TIME")
    private Integer recoveryaveTime;

    @TableField("FIRST_SPO2")
    private String firstSpo2;

    @TableField("THIS_YEARSPO2")
    private String thisYearspo2;

    @TableField("FIRST_HR")
    private String firstHr;

    @TableField("THIS_YEAR_HR")
    private String thisYearHr;

    @TableField("FIRST_BLOOD")
    private String firstBlood;

    @TableField("THIS_YEAR_BLOOD")
    private String thisYearBlood;

    @TableField("FIRST_DISTANCE")
    private String firstDistance;

    @TableField("THIS_YEAR_DISTANCE")
    private String thisYearDistance;

    @TableField("FIRST_MMRC_SCORE")
    private String firstMmrcScore;

    @TableField("THIS_YEAR_MMRC_SCORE")
    private String thisYearMmrcScore;

    @TableField("FIRST_ACT_SCORE")
    private String firstActScore;

    @TableField("THIS_YEAR_ACT_SCORE")
    private String thisYearActScore;

    @TableField("FIRST_MIP")
    private String firstMip;

    @TableField("THIS_YEAR_MIP")
    private String thisYearMip;

    @TableField("FIRST_MEP")
    private String firstMep;

    @TableField("THIS_YEAR_MEP")
    private String thisYearMep;


    public TdZwcfKfRcd(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TbTjCrpt;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2020-11-19,TbTjCrptMapper
 */
@Repository
public interface TbTjCrptMapper extends ZwxBaseMapper<TbTjCrpt> {
    /**
     * <p>方法描述：通过社会信用代码查询主体机构 </p>
     * pw 2023/9/20
     **/
    List<TbTjCrpt> findMainCrptInfoByInstitutionCode(@Param("list") List<String> list);

    /**
     * <p>方法描述：通过企业rid获取企业信息，包含地区名称 </p>
     * pw 2023/11/11
     **/
    List<TbTjCrpt> findCrptWithZoneInfoByRids (@Param("list") List<Integer> list);
}

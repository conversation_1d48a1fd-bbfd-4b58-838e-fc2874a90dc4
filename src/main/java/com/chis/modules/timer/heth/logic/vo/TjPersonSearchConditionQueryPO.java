package com.chis.modules.timer.heth.logic.vo;


import lombok.Data;

import java.util.List;

/**
 * <p>描述：个案查询条件PO</p>
 *
 *  @Author: 龚哲,2021/12/21 15:31,TjPersonSearchConditionPO
 */
@Data
public class TjPersonSearchConditionQueryPO {

	/***查询条件-人员姓名*/
	private String searchPersonName;
	/***查询条件-身份证号*/
	private String searchIDC;
	/**查询条件：体检机构*/
    private List<Integer> searchUnitId;
    /**查询条件：年龄*/
    private List<TdZdzybAnalyDetailComm> selectAgeAnalyDetails;
    /**查询条件：工龄*/
    private List<TdZdzybAnalyDetailComm> selectWorkAnalyDetails;
	/***查询条件-用人单位地区编码*/
	private String searchZoneCode;
	/**用人单位名称*/
	private String searchCrptName;
	/**体检类型*/
	private List<Integer> searchBhkType;
	/**体检类型*/
	private boolean searchBhkTypeHas3;
	/***查询条件-体检开始时间*/
	private String searchStartTime;
	/***查询条件-体检结束时间*/
	private String searchEndTime;
	/***查询条件-报告打印日期开始时间*/
	private String startRptPrintDate;
	/***查询条件-报告打印日期结束时间*/
	private String endRptPrintDate;
	/***查询条件-填报日期开始时间*/
	private String startCreateDate;
	/***查询条件-填报日期结束时间*/
	private String endCreateDate;
	/**查询条件-在岗状态*/
	private List<Integer> selectOnGuardIds;
	/**查询条件-体检危害因素*/
	private List<Integer> selectBadRsnIds;
	 /**查询条件：选择的单危害因素结论*/
	private List<Integer> searchSelBhkrstIds;
	/**选择的体检项目*/
	private String searchItemIds;
	 /**档案份数*/
    private Integer searchBhkNum;
    /**查询条件-身份证类型*/
    private Integer searchPsnType;
    /**查询条件-监测类型*/
    private List<Integer> searchJcType;
    /**是否质检员*/
	private Boolean ifAdmin;
	/**类型1个案查询2个案内部查询*/
	private Integer type;
	/** 分页查询的开始行 与endRow同时使用 少一个分页条件都忽略*/
	private Integer startRow;
	/** 分页查询的结束行 与startRow同时使用 少一个分页条件都忽略*/
	private Integer endRow;
	/** 是否需要脱敏 true是*/
	private Boolean ifNeedEnctryInfo;

	/** 用工单位地区 */
	private String searchEntrustCrptZoneGb;
	/** 用工单位名称 */
	private String searchEntrustCrptName;
	/** 主检结论 */
	private List<Integer> searchSelMhkrstIds;

	/**
	 * 质控编号
	 */
	private String zkBhkCode;


}

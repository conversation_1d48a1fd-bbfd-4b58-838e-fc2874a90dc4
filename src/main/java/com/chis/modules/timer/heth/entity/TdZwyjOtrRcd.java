package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TbTjSrvorg;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-12,TdZwyjOtrRcd
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_OTR_RCD")
@KeySequence(value = "TD_ZWYJ_OTR_RCD_SEQ",clazz = Integer.class)
public class TdZwyjOtrRcd extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("BHK_CODE")
    private String bhkCode;

    @TableField(value = "BHKORG_ID" , el = "fkByBhkorgId.rid")
    private TbTjSrvorg fkByBhkorgId;

    @TableField("IF_OUT_RANGE")
    private String ifOutRange;

    @TableField("PRO_MSG")
    private String proMsg;


    public TdZwyjOtrRcd(Integer rid) {
        super(rid);
    }


}

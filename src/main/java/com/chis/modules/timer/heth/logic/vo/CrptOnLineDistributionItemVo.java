package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
/**
 * <p>类描述：企业在线申报 危害因素接触情况明细</p>
 * @ClassAuthor： pw 2022/9/7
 **/
@Data
public class CrptOnLineDistributionItemVo implements Serializable {
    private static final long serialVersionUID = 6887111692046610422L;
    private Integer hazardsSort;
    private String hazardsName;
    private String codeNo;
    private Boolean supervisionRequirement;
    private Integer contactNumber;
}

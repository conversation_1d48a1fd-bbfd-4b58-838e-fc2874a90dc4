package com.chis.modules.timer.heth.job;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.service.TsSimpleCodeService;
import com.chis.modules.sys.service.TsZoneService;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.enums.BadRsnCaffeineKeyEnum;
import com.chis.modules.timer.heth.service.*;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 个案审核计算
 *
 * @ClassAuthor pw,2021年05月11日,BhkAuditCalculationJob
 */
@Slf4j
@Component
public class BhkAuditCalculationJob {

    @Resource
    private TdZdzybAnalyTypeService analyTypeService;
    @Resource
    private TdTjBhkService bhkService;
    @Resource
    private TsZoneService zoneService;
    @Resource
    private TdTjBadrsnsService badrsnsService;
    @Resource
    private TsSimpleCodeService codeService;
    @Resource
    private TdTjBhksubService bhksubService;

    @Value("${heth-timer.calculat.dataSize}")
    private Integer dataSize;
    @Value("${heth-timer.calculat.startDate}")
    private String startDate;
    @Value("${heth-timer.calculat.ifNotCheckFsBadRsn}")
    private Integer ifNotCheckFsBadRsn;

    //配置的企业规模与行业类别查询KEY前缀
    private final String PRE_ANALYTYPE_ITEM_KEY = "PRE_ANALYTYPE_ITEM_KEY";
    //企业所属地区
    private final String PROVINCE_RIDS_KEY = "PROVINCE_RIDS_KEY";
    /** 企业所属地区 省直属rid集合 */
    private List<Integer> provinceRidList;
    /** 非放射危害因素rid集合 */
    private List<Integer> unRadioBadRsnIdList;
    /** key 码表的rid value码表对象 */
    private Map<Integer,TsSimpleCode> simpleCodeMap;
    /** 粉尘类大类码表编码集合 */
    private List<String> dustCodeList;

    @Scheduled(cron = "${heth-timer.sche-cron.calculatCron}")
    //@Scheduled(initialDelay = 3000,fixedDelay = 60000)
    public void start(){
        //初始化 省直属rid集合
        initIfProvinceRidList();
        this.initUnRadioBadRsnId();
        this.initSimpleCode();
        //获取需要处理的数据
        List<TdTjBhk> bhkList = this.bhkService.selectAuditCalculationBhks(dataSize, startDate, ifNotCheckFsBadRsn);
        while(!CollectionUtils.isEmpty(bhkList)){
            executeData(bhkList);
            bhkList = this.bhkService.selectAuditCalculationBhks(dataSize, startDate, ifNotCheckFsBadRsn);
        }
    }

    private void executeData(List<TdTjBhk> bhkList){
        try{
            if(CollectionUtils.isEmpty(bhkList)){
                return;
            }
            List<Integer> crptSizeRidList = initAnalyTypeItemRidList(2);
            List<Integer> indusTypeRidList = initAnalyTypeItemRidList(1);
            //危害因素
            List<Integer> badRsnRidList = initAnalyTypeItemRidList(4);
            //社会信用代码
            List<String> institutionCodeList = initAnalyUnitCodeList(5);
            //体检机构编码
            List<String> tjOrgCodeList = initAnalyUnitCodeList(6);

            List<Integer> bhkRidList = bhkList.stream()
                    .mapToInt(TdTjBhk::getRid).distinct().boxed().collect(Collectors.toList());
            //key 体检主表rid value对应的体检危害因素
            Map<Integer, List<TdTjBadrsns>> badrsnWithBhkMap = this.findBadRsnListWithBhkId(bhkRidList);
            //体检rid对应体检危害因素rid集合的Map
            Map<Integer,List<Integer>> bhkRidWithBadRsnListMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(badrsnWithBhkMap)){
                for(Map.Entry<Integer, List<TdTjBadrsns>> mapEntity : badrsnWithBhkMap.entrySet()){
                    bhkRidWithBadRsnListMap.put(mapEntity.getKey(), mapEntity.getValue().stream().mapToInt(v ->
                            v.getFkByBadrsnId().getRid()).distinct().boxed().collect(Collectors.toList()));
                }
            }
            //匹配体检危害因素无异常的体检rid集合
            List<Integer> unAbnormalBadRsnBhkRidList = findBadRsnUnAbnomalBhkRidList(badRsnRidList,
                    bhkRidWithBadRsnListMap);
            //胸片体检项目的体检子表集合
            List<TdTjBhksub> radioTdTjBhkSubList = this.findRadioTdTjBhkSubListByRids(bhkRidList);
            //key 体检主表rid value 对应的胸片体检项目的体检子表
            Map<Integer,List<TdTjBhksub>> radioTdTjBhkSubMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(radioTdTjBhkSubList)){
                radioTdTjBhkSubMap = radioTdTjBhkSubList.stream().filter(v -> null != v.getBhkId())
                        .collect(Collectors.groupingBy(v -> Integer.parseInt(v.getBhkId())));
            }

            //主动监测异常信息
            List<TdTjBhkAbnomal> bhkAbnomalList = new ArrayList<>();
            //错误信息拼接buffer
            StringBuffer errorBuffer = new StringBuffer();
            //用于临时赋值的字段 比如行业类别与企业规模
            String tmpTip;
            String tmpTip2;
            //用工单位企业规模是否匹配异常
            boolean crptSizeFlag;
            //用工单位行业类别是否匹配异常
            boolean indusTypeFlag;
            //用工单位社会信用代码是否匹配异常
            boolean crptInstitutionFlag;
            //体检危害因素是否匹配异常
            boolean badrsnFlag;
            //体检机构编码是否匹配异常
            boolean orgCodeFlag;
            //用工单位社会信用代码
            String entrustCrptInstitutionCode;
            //体检机构编码
            String tjOrgCode;
            //体检危害因素全部是放射类的体检rid集合
            List<Integer> exceptRidList = new ArrayList<>();
            if(null != this.ifNotCheckFsBadRsn && 1 == this.ifNotCheckFsBadRsn
                    && !CollectionUtils.isEmpty(this.unRadioBadRsnIdList)
                    && !CollectionUtils.isEmpty(bhkRidWithBadRsnListMap)){
                for(Map.Entry<Integer,List<Integer>> mapEntity : bhkRidWithBadRsnListMap.entrySet()){
                    Integer rid = mapEntity.getKey();
                    List<Integer> curBadRsnRidList = mapEntity.getValue();
                    if(CollectionUtils.isEmpty(curBadRsnRidList)){
                        continue;
                    }
                    if(curBadRsnRidList.stream().anyMatch(v -> this.unRadioBadRsnIdList.contains(v))){
                        continue;
                    }
                    exceptRidList.add(rid);
                }
            }
            for(TdTjBhk tdTjBhk : bhkList){
                Integer curBhkId = tdTjBhk.getRid();
                if(null != this.ifNotCheckFsBadRsn && 1 == this.ifNotCheckFsBadRsn && exceptRidList.contains(curBhkId)){
                    tdTjBhk.setIfIntoCheck("0");
                    tdTjBhk.setDealCompleteDate(new Date());
                    continue;
                }

                crptSizeFlag = true;
                indusTypeFlag = true;
                crptInstitutionFlag = true;
                badrsnFlag = true;
                orgCodeFlag = true;
                tmpTip = null;
                tmpTip2 = null;
                entrustCrptInstitutionCode = null;
                tjOrgCode = null == tdTjBhk.getFkByBhkorgId() ? null : tdTjBhk.getFkByBhkorgId().getUnitCode();
                //需要判断监测类型 不是主动监测 不需要匹配一二条规则
                if(StringUtils.isNotBlank(tdTjBhk.getJcType()) && tdTjBhk.getJcType().equals("2")){
                    //用工单位
                    TbTjCrpt crpt = tdTjBhk.getEntrustId();
                    tmpTip = tdTjBhk.getCrptSizeCodeName();
                    tmpTip2 = tdTjBhk.getIndusTypeCodeName();
                    entrustCrptInstitutionCode = null != crpt ? crpt.getInstitutionCode() : "";

                    //规则二用工单位的企业规模与配置的不匹配
                    if(CollectionUtils.isEmpty(crptSizeRidList)){
                        crptSizeFlag = false;
                    }else if(null != crpt && null != crpt.getFkByCrptSizeId() && !CollectionUtils.isEmpty(crptSizeRidList)
                            && crptSizeRidList.contains(crpt.getFkByCrptSizeId().getRid()) ){
                        crptSizeFlag = false;
                    }
                    //规则一用工单位的行业类别与配置的不匹配
                    if(CollectionUtils.isEmpty(indusTypeRidList)){
                        indusTypeFlag = false;
                    }else if(null != crpt && null != crpt.getFkByIndusTypeId() && !CollectionUtils.isEmpty(indusTypeRidList)
                            && indusTypeRidList.contains(crpt.getFkByIndusTypeId().getRid()) ){
                        indusTypeFlag = false;
                    }
                }else{
                    crptSizeFlag = false;
                    indusTypeFlag = false;
                }

                if(crptSizeFlag){
                    errorBuffer.setLength(0);
                    errorBuffer.append("该用工单位的企业规模");
                    if(StringUtils.isNotBlank(tmpTip)){
                        errorBuffer.append("（").append(tmpTip).append("）");
                    }
                    errorBuffer.append("不符合主动监测任务要求");
                    bhkAbnomalList.add(this.generateAbnomal(curBhkId, 2, errorBuffer.toString()));
                }
                if(indusTypeFlag){
                    errorBuffer.setLength(0);
                    errorBuffer.append("该用工单位的行业类别");
                    if(StringUtils.isNotBlank(tmpTip2)){
                        errorBuffer.append("（").append(tmpTip2).append("）");
                    }
                    errorBuffer.append("不符合主动监测任务要求");
                    bhkAbnomalList.add(this.generateAbnomal(curBhkId, 1, errorBuffer.toString()));
                }
                //默认正常
                tdTjBhk.setIfAbnomal("0");

                //规则三 工龄是否不规范
                Date bhkDate = tdTjBhk.getBhkDate();
                Date birthDay = tdTjBhk.getBrth();
                if(null != bhkDate && null != birthDay){
                    Date tmpDate = DateUtils.getNDayAfterTime(birthDay, 16, Calendar.YEAR);
                    boolean wrkAgeFlag = false;
                    //1、体检日期-出生日期  < 16周岁
                    if(tmpDate.after(bhkDate)){
                        wrkAgeFlag = true;
                    }
                    Integer wrklnt = this.convertStr2Integer(tdTjBhk.getWrklnt());
                    Integer wrklntMonth = this.convertStr2Integer(tdTjBhk.getWrklntmonth());
                    //2、体检日期-出生日期-总工龄（包含月） < 16周岁
                    wrkAgeFlag = this.checkWrkAge(bhkDate, wrkAgeFlag, tmpDate, wrklnt, wrklntMonth);
                    Integer tchBadrsntim = this.convertStr2Integer(tdTjBhk.getTchbadrsntim());
                    Integer tchbadrsnMonth = this.convertStr2Integer(tdTjBhk.getTchbadrsnmonth());
                    //3、体检日期-出生日期-接害工龄（包含月） < 16周岁
                    wrkAgeFlag = this.checkWrkAge(bhkDate, wrkAgeFlag, tmpDate, tchBadrsntim, tchbadrsnMonth);
                    if(wrkAgeFlag){
                        //工龄不规范 设置状态
                        tdTjBhk.setIfAbnomal("1");
                        errorBuffer.setLength(0);
                        errorBuffer.append("该劳动者从事工作年龄小于16周岁，请确认数据真实性");
                        bhkAbnomalList.add(this.generateAbnomal(curBhkId, 3, errorBuffer.toString()));
                    }
                }

                if(StringUtils.isNotBlank(tdTjBhk.getJcType()) && tdTjBhk.getJcType().equals("2")){
                    //规则四 体检数据的监测类别为主动监测，但是体检危害因素与配置危害因素的全部不匹配，体检危害因素未在配置的范围内
                    if(CollectionUtils.isEmpty(unAbnormalBadRsnBhkRidList)){
                        badrsnFlag = false;
                    }else if(!CollectionUtils.isEmpty(unAbnormalBadRsnBhkRidList) &&
                            unAbnormalBadRsnBhkRidList.contains(curBhkId)){
                        badrsnFlag = false;
                    }
                    //规则五 体检数据的监测类别为主动监测，但是当前用工单位未在主动监测企业名单内
                    if(CollectionUtils.isEmpty(institutionCodeList)){
                        crptInstitutionFlag = false;
                    }else if(!CollectionUtils.isEmpty(institutionCodeList) &&
                            StringUtils.isNotBlank(entrustCrptInstitutionCode) &&
                            institutionCodeList.contains(entrustCrptInstitutionCode)){
                        crptInstitutionFlag = false;
                    }
                    //规则六 体检数据的监测类别为主动监测，但是当前体检机构未在主动监测体检机构名单内
                    if(CollectionUtils.isEmpty(tjOrgCodeList)){
                        orgCodeFlag = false;
                    }else if(!CollectionUtils.isEmpty(tjOrgCodeList) && StringUtils.isNotBlank(tjOrgCode)
                            && tjOrgCodeList.contains(tjOrgCode)){
                        orgCodeFlag = false;
                    }
                }else{
                    badrsnFlag = false;
                    crptInstitutionFlag = false;
                    orgCodeFlag = false;
                }

                if(badrsnFlag){
                    errorBuffer.setLength(0);
                    errorBuffer.append("该劳动者所体检危害因素不符合主动监测任务要求");
                    bhkAbnomalList.add(this.generateAbnomal(curBhkId, 4, errorBuffer.toString()));
                }
                if(crptInstitutionFlag){
                    errorBuffer.setLength(0);
                    errorBuffer.append("该用工单位不在开展工作场所职业病危害因素监测的企业名单中");
                    bhkAbnomalList.add(this.generateAbnomal(curBhkId, 5, errorBuffer.toString()));
                }
                if(orgCodeFlag){
                    errorBuffer.setLength(0);
                    errorBuffer.append("该职业健康检查机构本年度不承担主动监测任务");
                    bhkAbnomalList.add(this.generateAbnomal(curBhkId, 6, errorBuffer.toString()));
                }
                //规则八 核实年龄真实性
                if(null != bhkDate && null != birthDay){
                    //年龄小于16或大于65时，异常信息为“当前劳动者年龄在16岁以下或65岁以上，请核实劳动者年龄是否准确。”，年龄计算=体检日期-出生日期
                    //birthDay+16年大于bhkDate  或者  bhkDate大于birthDay+65年
                    if(this.checkWrkAge(bhkDate, false, birthDay, 16, null) ||
                            bhkDate.after(DateUtils.getNDayAfterTime(birthDay, 65, Calendar.YEAR))){
                        errorBuffer.setLength(0);
                        errorBuffer.append("当前劳动者年龄在16岁以下或65岁以上，请核实劳动者年龄是否准确");
                        bhkAbnomalList.add(this.generateAbnomal(curBhkId, 7, errorBuffer.toString()));
                    }
                }

                List<TdTjBhksub> radioBhkSubList = radioTdTjBhkSubMap.get(curBhkId);
                // 没有粉尘类项目时，粉尘类危害因素的结论为“疑似职业病”标记
                boolean ifSupError=false;
                // 粉尘类项目 有非尘肺样改变标记
                boolean ifHasNoCf=false;
                // 粉尘类 胸片项目 是否全部未检
                boolean ifAllCheck=true;
                //有胸片项目
                if(!CollectionUtils.isEmpty(radioBhkSubList)){
                    boolean ifRstError = false;
                    boolean ifBadRsnError = false;
                    List<Integer> rstFlagList = new ArrayList<>();
                    //体检危害因素
                    List<TdTjBadrsns> badList = badrsnWithBhkMap.get(curBhkId);
                    for(TdTjBhksub bhksub : radioBhkSubList){
                        String rglTag = bhksub.getRgltag();
                        Integer rstFlag = bhksub.getRstFlag();
                        if(null == rstFlag){
                            continue;
                        }
                        //当“结果判定标记”为1或2时，是否合格为“合格”时，提示异常信息“请核实该劳动者的胸片结果判定标记与合格标记是否正确。”
                        if((1 == rstFlag || 2 == rstFlag) && "1".equals(rglTag)){
                            ifRstError = true;
                        }
                        //当“结果判定标记”为0或3时，是否合格为不“合格”时，提示异常信息“请核实该劳动者的胸片结果判定标记与合格标记是否正确。”
                        if((0 == rstFlag || 3 == rstFlag) && "0".equals(rglTag)){
                            ifRstError = true;
                        }
                        // 都是未检
                        ifAllCheck = ifAllCheck && new Integer("3").equals(rstFlag);
                        // 存在非未检时且非尘肺样改变的项目
                        if (!new Integer("3").equals(rstFlag) && !new Integer("1").equals(rstFlag)) {
                            ifHasNoCf = true;
                        }
                        rstFlagList.add(rstFlag);
                    }
                    //结果判定标记优先级：1：尘肺样改变 > 2：其他异常 > 0：未见异常 > 3：未检查
                    if(!CollectionUtils.isEmpty(badList) && !CollectionUtils.isEmpty(rstFlagList)){
                        Collections.sort(rstFlagList, comparator);
                        Integer rstFlag = rstFlagList.get(0);
                        for(TdTjBadrsns badrsns : badList){
                            //获取粉尘类危害因素体检结论
                            String extends2 = getDustBadRsnConclusion(badrsns);
                            //项目结果判定标记是1“尘肺样改变”时，且粉尘类的体检结论为【未见异常】或【其他疾病或异常】
                            if(1 == rstFlag && ("1".equals(extends2) || "3".equals(extends2))){
                                ifBadRsnError = true;
                            }
                            //项目结果判定标记是2“其他异常”时，且粉尘类的体检结论为【未见异常】
                            if(2 == rstFlag && ("1".equals(extends2))){
                                ifBadRsnError = true;
                            }
                            //项目结果判定标记是0“未见异常”或3“未检查”时，且粉尘类的体检结论为【疑似职业病】、【职业禁忌证】
                            if((0 == rstFlag || 3 == rstFlag) && ("4".equals(extends2) || "5".equals(extends2))){
                                ifBadRsnError = true;
                            }
                            //粉尘类危害因素体检结论为【疑似职业病】，但是胸片项目都为未检或者 非未检项目判定标记存在不为“尘肺样改变”
                            if("5".equals(extends2) && (ifHasNoCf || ifAllCheck)){
                                ifSupError=true;
                            }
                        }
                    }
                    //规则九 核实胸片的结果判定标记与是否合格是否不符
                    if(ifRstError){
                        errorBuffer.setLength(0);
                        errorBuffer.append("请核实该劳动者的胸片结果判定标记与合格标记是否正确");
                        bhkAbnomalList.add(this.generateAbnomal(curBhkId, 8, errorBuffer.toString()));
                    }
                    //规则十 核实胸片的结果判定标记与危害因素体检结论是否不符
                    if(ifBadRsnError){
                        errorBuffer.setLength(0);
                        errorBuffer.append("请核实该劳动者的胸片结果判定标记与粉尘类的体检结论是否正确");
                        bhkAbnomalList.add(this.generateAbnomal(curBhkId, 9, errorBuffer.toString()));
                    }
                }else {
                    //没有胸片项目判断逻辑
                    //体检危害因素
                    List<TdTjBadrsns> badList = badrsnWithBhkMap.get(curBhkId);
                    if(!CollectionUtils.isEmpty(badList)){
                        for(TdTjBadrsns badrsns : badList) {
                            if("5".equals(getDustBadRsnConclusion(badrsns))){
                                ifSupError=true;
                                break;
                            }
                        }
                    }
                }
                //20250314 追加验证，初检/复检 粉尘类危害因素体检结论为疑似职业病，但是无胸片项目或者胸片项目判定标记不为“尘肺样改变”
                if(ifSupError){
                    errorBuffer.setLength(0);
                    errorBuffer.append("粉尘类的体检结论为疑似职业病时，胸片项目结果缺项或结果判定标记非尘肺样改变，请核实");
                    bhkAbnomalList.add(this.generateAbnomal(curBhkId, 10, errorBuffer.toString()));
                }

                if(bhkAbnomalList.stream().anyMatch(v -> v.getBhkId().compareTo(curBhkId) == 0)){
                    tdTjBhk.setIfAbnomal("1");
                }

                //规则七 初检的要判断GBZ188不规范 已经异常的状态不需要重复设置
                if(tdTjBhk.getIfAbnomal().equals("0") && StringUtils.isNotBlank(tdTjBhk.getIfRhk())
                        && tdTjBhk.getIfRhk().equals("0") && null != tdTjBhk.getIfInteitmLack()
                        && tdTjBhk.getIfInteitmLack().equals("1")){
                    tdTjBhk.setIfAbnomal("1");
                }

                tdTjBhk.setIfIntoCheck("1");
                tdTjBhk.setDealCompleteDate(new Date());
                Integer zoneRid = (null == tdTjBhk.getEntrustId() || null == tdTjBhk.getEntrustId().getFkByZoneId()
                        || null == tdTjBhk.getEntrustId().getFkByZoneId().getRid()) ? null :
                        tdTjBhk.getEntrustId().getFkByZoneId().getRid();
                //省直属地区 状态存5
                if(null != zoneRid && null != provinceRidList && provinceRidList.contains(zoneRid)){
                    tdTjBhk.setState("5");
                }else{
                    tdTjBhk.setState("1");
                }
            }
            //批量处理
            this.bhkService.executeUpdateBhksWithBhkItemStd(bhkList, bhkAbnomalList, bhkRidList);
        }catch (Exception e){
            log.error(e.getMessage());
            e.printStackTrace();
        }
    }

    /**
    * <p>Description：获取粉尘类危害因素的体检结论 </p>
    * <p>Author： yzz 2025/3/14 </p>
    */
    public String getDustBadRsnConclusion(TdTjBadrsns badrsns){
        // 获取危害因素ID和体检结论ID
        Integer rsnId = Optional.ofNullable(badrsns.getFkByBadrsnId())
                .map(TsSimpleCode::getRid)
                .orElse(null);
        Integer conRid = Optional.ofNullable(badrsns.getFkByExamConclusionId())
                .map(TsSimpleCode::getRid)
                .orElse(null);

        // 如果危害因素ID或体检结论ID为空，跳过当前记录
        if (rsnId == null || conRid == null) {
            return null;
        }

        // 获取危害因素的编码
        TsSimpleCode rsnCode = simpleCodeMap.get(rsnId);
        String codeLevelNo = Optional.ofNullable(rsnCode)
                .map(TsSimpleCode::getCodeLevelNo)
                .orElse("");

        // 如果危害因素不是粉尘，跳过当前记录
        if (!isDustCode(codeLevelNo)) {
            return null;
        }

        // 获取体检结论的扩展字段
        TsSimpleCode conCode = simpleCodeMap.get(conRid);
        return Optional.ofNullable(conCode)
                .map(TsSimpleCode::getExtends2)
                .orElse("");
    }

    /**
    * <p>Description：判断是否为粉尘编码 </p>
    * <p>Author： yzz 2025/3/14 </p>
    */
    private boolean isDustCode(String codeLevelNo) {
        if (StringUtils.isBlank(codeLevelNo)) {
            return false;
        }
        String codePrefix = codeLevelNo.split("\\.")[0].trim();
        return dustCodeList.contains(codePrefix);
    }

    /**
     *  <p>方法描述：1：尘肺样改变 > 2：其他异常 > 0：未见异常 > 3：未检查
     *  自定义排序：排序顺序是 1,2,0,3</p>
     * @MethodAuthor hsj 2023-05-31 16:38
     */
    Comparator<Integer> comparator = new Comparator<Integer>() {
        @Override
        public int compare(Integer o1, Integer o2) {
            // 如果o1是1或2,o2是0,则o1小
            if (o1 == 1 || o1 == 2) {
                if (o2 == 0) return -1;
            }
            // 如果o2是1或2,o1是0,则o2小
            if (o2 == 1 || o2 == 2) {
                if (o1 == 0) return 1;
            }
            // 其他情况,正常比较
            return o1.compareTo(o2);
        }
    };
    /**
     * <p>方法描述：检测工龄是否异常 </p>
     * @MethodAuthor： pw 2022/7/9
     **/
    private boolean checkWrkAge(Date bhkDate, boolean wrkAgeFlag, Date tmpDate, Integer year, Integer month){
        if(wrkAgeFlag){
            return wrkAgeFlag;
        }
        if(null != year || null != month){
            Date compareDate = tmpDate;
            if(null != year){
                compareDate = DateUtils.getNDayAfterTime(compareDate, year, Calendar.YEAR);
            }
            if(null != month){
                compareDate = DateUtils.getNDayAfterTime(compareDate, month, Calendar.MONTH);
            }
            //2、体检日期-出生日期-总工龄（包含月） < 16周岁
            if(compareDate.after(bhkDate)){
                wrkAgeFlag = true;
            }
        }
        return wrkAgeFlag;
    }

    /**
     * <p>方法描述：字符串转换成Integer </p>
     * @MethodAuthor： pw 2022/7/9
     **/
    private Integer convertStr2Integer(String valueStr){
        if(StringUtils.isBlank(valueStr)){
            return null;
        }
        Integer result = null;
        try{
            result = Integer.parseInt(valueStr);
        }catch(Exception e){
            result = null;
        }
        return result;
    }

    /**
     * <p>方法描述：创建异常信息对象 </p>
     * @MethodAuthor： pw 2022/8/25
     **/
    private TdTjBhkAbnomal generateAbnomal(Integer bhkId, Integer type, String abnomalInfo){
        TdTjBhkAbnomal abnomal = new TdTjBhkAbnomal();
        abnomal.setBhkId(bhkId);
        abnomal.setType(type);
        abnomal.setAbnomalInfo(abnomalInfo);
        abnomal.setCreateDate(new Date());
        abnomal.setCreateManid(1);
        return abnomal;
    }

    /**
     * @Description: 配置的企业规模rid集合与行业类别rid集合以及危害因素rid集合
     *
     * @MethodAuthor pw,2021年05月11日
     */
    private List<Integer> initAnalyTypeItemRidList(Integer analyType){
        String key = PRE_ANALYTYPE_ITEM_KEY+analyType;
        Object cacheObj = CaffeineUtil.getIfPresent(BadRsnCaffeineKeyEnum.BHK_AUDIT_CALCULATION, key);
        List<Integer> resultList = null == cacheObj ? null : (List<Integer>)cacheObj;
        if(CollectionUtils.isEmpty(resultList)){
            resultList = this.analyTypeService.findAnalyItemIdListByAnalyTypeAndBusType(4, analyType);
            CaffeineUtil.put(BadRsnCaffeineKeyEnum.BHK_AUDIT_CALCULATION,key,resultList);
        }
        return resultList;
    }

    /**
     * <p>方法描述：配置的用人单位社会信用代码与体检机构编码</p>
     * @MethodAuthor： pw 2022/8/25
     **/
    private List<String> initAnalyUnitCodeList(Integer analyType){
        //使用同一个缓存前缀
        String key = PRE_ANALYTYPE_ITEM_KEY+analyType;
        Object cacheObj = CaffeineUtil.getIfPresent(BadRsnCaffeineKeyEnum.BHK_AUDIT_CALCULATION, key);
        List<String> resultList = null == cacheObj ? null : (List<String>)cacheObj;
        if(CollectionUtils.isEmpty(resultList)){
            resultList = this.analyTypeService.findAnalyUnitCodeListByAnalyTypeAndBusType(4, analyType);
            CaffeineUtil.put(BadRsnCaffeineKeyEnum.BHK_AUDIT_CALCULATION,key,resultList);
        }
        return resultList;
    }

    /** 初始化 省直属rid集合*/
    public void initIfProvinceRidList(){
        //PROVINCE_RIDS_KEY
        Object cacheObj = CaffeineUtil.getIfPresent(BadRsnCaffeineKeyEnum.BHK_AUDIT_CALCULATION, PROVINCE_RIDS_KEY);
        if(null == cacheObj){
            provinceRidList = new ArrayList<>();
            TsZone tsZoneQuery = new TsZone();
            tsZoneQuery.setIfReveal(1);
            List<TsZone> zoneList = zoneService.selectListByEntity(tsZoneQuery);
            if(!CollectionUtils.isEmpty(zoneList)){
                for(TsZone tsZone : zoneList){
                    if(null != tsZone.getRid() && null != tsZone.getIfProvDirect()
                            && tsZone.getIfProvDirect().equals("1")){
                        provinceRidList.add(tsZone.getRid());
                    }
                }
                //加入缓存
                CaffeineUtil.put(BadRsnCaffeineKeyEnum.BHK_AUDIT_CALCULATION,PROVINCE_RIDS_KEY,provinceRidList);
            }
        }else{
            provinceRidList = (List<Integer>) cacheObj;
        }
    }

    /**
     * <p>方法描述：获取体检危害因素匹配不异常的体检rid集合 </p>
     * @param badRsnRidList 规则中的体检危害因素rid集合
     * @param rsnWithBhkIdMap 体检rid对应体检危害因素rid集合Map
     * @MethodAuthor： pw 2022/8/25
     **/
    private List<Integer> findBadRsnUnAbnomalBhkRidList(List<Integer> badRsnRidList,
                                                      Map<Integer,List<Integer>> rsnWithBhkIdMap){
        if(CollectionUtils.isEmpty(badRsnRidList) || CollectionUtils.isEmpty(rsnWithBhkIdMap)){
            return Collections.EMPTY_LIST;
        }
        List<Integer> resultList = new ArrayList<>();
        for(Map.Entry<Integer,List<Integer>> mapEntity : rsnWithBhkIdMap.entrySet()){
            if(mapEntity.getValue().stream().anyMatch(v -> badRsnRidList.contains(v))){
                resultList.add(mapEntity.getKey());
            }
        }
        return resultList;
    }

    /**
     * <p>方法描述： 通过体检主表rid集合获取对应的体检危害因素集合 </p>
     * @MethodAuthor： pw 2022/10/20
     **/
    private Map<Integer, List<TdTjBadrsns>> findBadRsnListWithBhkId(List<Integer> bhkIdList){
        Map<Integer,List<TdTjBadrsns>> resultMap = new HashMap<>();
        List<TdTjBadrsns> badrsnsList = badrsnsService.findTdTjBadrsnsByBhkIdList(bhkIdList);
        if(CollectionUtils.isEmpty(badrsnsList)){
            return resultMap;
        }
        for(TdTjBadrsns tjBadrsns : badrsnsList){
            Integer bhkRid = null == tjBadrsns.getFkByBhkId() ? null : tjBadrsns.getFkByBhkId().getRid();
            Integer badRsnId = null == tjBadrsns.getFkByBadrsnId() ? null : tjBadrsns.getFkByBadrsnId().getRid();
            if(null == bhkRid || null == badRsnId){
                continue;
            }
            List<TdTjBadrsns> tmpList = resultMap.get(bhkRid);
            if(null == tmpList){
                tmpList = new ArrayList<>();
            }
            tmpList.add(tjBadrsns);
            resultMap.put(bhkRid, tmpList);
        }
        return resultMap;
    }

    /**
     * <p>方法描述： 获取胸片的体检子表集合 </p>
     * @MethodAuthor： pw 2022/10/20
     **/
    private List<TdTjBhksub> findRadioTdTjBhkSubListByRids(List<Integer> bhkIdList){
        return this.bhksubService.selectSubBhkListByItemTagAndBhkRidList(bhkIdList, 30);
    }

    /**
     * <p>方法描述： 参数ifNotCheckFsBadRsn为1时 初始化非放射危害因素rid集合  </p>
     * @MethodAuthor： pw 2022/9/23
     **/
    private void initUnRadioBadRsnId(){
        List<String> codeTypeNameList = new ArrayList<>();
        codeTypeNameList.add("5007");
        Map<Integer, TsSimpleCode> tmpMap = null != this.ifNotCheckFsBadRsn && 1 == this.ifNotCheckFsBadRsn ?
                this.codeService.findMultipleSimpleCode(codeTypeNameList) : null;
        this.unRadioBadRsnIdList = new ArrayList<>();
        if(null != this.ifNotCheckFsBadRsn && 1 == this.ifNotCheckFsBadRsn && !CollectionUtils.isEmpty(tmpMap)){
            for(Map.Entry<Integer, TsSimpleCode> mapEntity : tmpMap.entrySet()){
                TsSimpleCode simpleCode = mapEntity.getValue();
                //放射类 扩展字段1为1
                if(!"1".equals(simpleCode.getExtends1())){
                    unRadioBadRsnIdList.add(simpleCode.getRid());
                }
            }
        }
    }

    /**
     * <p>方法描述： 码表初始化 </p>
     * @MethodAuthor： pw 2022/10/20
     **/
    private void initSimpleCode(){
        //粉尘类大类编码list
        this.dustCodeList = new ArrayList<>();
        this.simpleCodeMap = new HashMap<>();
        List<TsSimpleCode> simpleCodeList = this.codeService.findAllTsSimpleCodeList("5005");
        if(!CollectionUtils.isEmpty(simpleCodeList)){
            for(TsSimpleCode simpleCode : simpleCodeList){
                this.simpleCodeMap.put(simpleCode.getRid(), simpleCode);
            }
        }
        simpleCodeList = this.codeService.findAllTsSimpleCodeList("5007");
        if(!CollectionUtils.isEmpty(simpleCodeList)){
            for(TsSimpleCode simpleCode : simpleCodeList){
                if("1".equals(simpleCode.getExtends3())){
                    this.dustCodeList.add(simpleCode.getCodeNo().trim());
                }
                this.simpleCodeMap.put(simpleCode.getRid(), simpleCode);
            }
        }
    }
}

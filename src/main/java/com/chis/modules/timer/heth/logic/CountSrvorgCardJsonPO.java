package com.chis.modules.timer.heth.logic;

import com.chis.modules.timer.heth.logic.vo.CardBaseEntity;
import lombok.Data;


/**
 * <p>类描述： 放射卫生技术服务信息报告卡传输对象 </p>
 * @ClassAuthor： pw 2022/12/21
 **/
@Data
public class CountSrvorgCardJsonPO extends CardBaseEntity {
    private static final long serialVersionUID = -5996237154718651409L;
    private String cname;
    private String cphone;
    private String soname;
    private String socode;
    private String sssxbm;
    private String saddress;
    private String scname;
    private String scphone;
    private String stype;
    private Integer sisz;
    //private Integer sisw;
    private Integer siswfszl;
    private Integer siswhyx;
    private Integer siswjrfsx;
    private Integer siswxsxyxzd;
    private Integer sisg;
    private Integer sisq;
    private String sfintimes;
    private String sfintimen;
    private String sametimes;
    private String sametimen;
    private Integer sisws;
    private Integer siszl;
    private Integer sisgr;
    private Integer sisqc;
    private Integer swsnums;
    private Integer swsexnums;
    private Integer swsexy;
    private Integer swsexa;
    private Integer swsexzh;
    private Integer swsexd;
    private Integer swsexzz;
    private Integer swsexzl;
    private Integer swsexq;
    private Integer swsexqt;
    private Integer swsznums;
    private Integer swszexnums;
    private Integer szsexnums;
    private Integer szsexy;
    private Integer szsexa;
    private Integer szsexyzh;
    private Integer szsexyd;
    private Integer szsexyzz;
    private Integer szsexyzl;
    private Integer szsexq;
    private Integer szsexqt;
    private Integer sksnums;
    private Integer sksexnums;
    private Integer sksexy;
    private Integer sksexa;
    private Integer sksexyzh;
    private Integer sksexyd;
    private Integer sksexyzz;
    private Integer sksexyzl;
    private Integer sksexq;
    private Integer sksexqt;
    private Integer sgsnums;
    private Integer sgsnumsa;
    private Integer sgsnumsb;
    private Integer sqsnums;
    private Integer sqsexnums;
    private String sqsexnames;
    private Integer scsnums;
    private Integer scsexnums;
    private String scsexnames;
    private String sreportnumber;
    private Integer swszysjc;
    private Integer swszztjc;
}

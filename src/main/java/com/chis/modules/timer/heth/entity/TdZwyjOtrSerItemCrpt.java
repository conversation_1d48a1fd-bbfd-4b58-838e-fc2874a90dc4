package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-19,TdZwyjOtrSerItemCrpt
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_OTR_SER_ITEM_CRPT")
@KeySequence(value = "TD_ZWYJ_OTR_SER_ITEM_CRPT_SEQ",clazz = Integer.class)
public class TdZwyjOtrSerItemCrpt extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwyjOtrCrptList fkByMainId;

    @TableField(value = "SERVICE_ID" , el = "fkByServiceId.rid")
    private TsSimpleCode fkByServiceId;


    public TdZwyjOtrSerItemCrpt(Integer rid) {
        super(rid);
    }


}

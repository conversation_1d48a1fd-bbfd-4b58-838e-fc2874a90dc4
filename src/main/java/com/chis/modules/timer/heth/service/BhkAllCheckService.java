package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.entity.TdTjBhk;
import com.chis.modules.timer.heth.entity.TdTjCheckTask;
import com.chis.modules.timer.heth.entity.TdZwBgkFlow;
import com.chis.modules.timer.heth.mapper.TbTjJcTaskPsnMapper;
import com.chis.modules.timer.heth.mapper.TdTjBhkMapper;
import com.chis.modules.timer.heth.mapper.TdZwBgkFlowMapper;
import com.chis.modules.timer.heth.pojo.BhkCheckCondition;
import com.chis.modules.timer.heth.pojo.BhkCheckPojo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @description:
 */
@Service
public class BhkAllCheckService {

    @Autowired
    private TdZwBgkFlowMapper tdZwBgkFlowMapper;
    @Autowired
    private TdTjBhkMapper tdTjBhkMapper;
    @Autowired
    private TbTjJcTaskPsnMapper tbTjJcTaskPsnMapper;


    /**
     * <p>Description：全部审核数据处理 </p>
     * <p>Author： yzz 2024-10-14 </p>
     */
    @Transactional
    public int saveBatchBhkCheck(List<TdTjBhk> list, TdTjCheckTask task, BhkCheckCondition bhkCheckCondition) {
        // key: 上次流程操作标识; value: 体检记录RID;
        Map<Integer, List<Integer>> lastMarkBhkRidMap = new HashMap<>();
        // key: 当前流程操作标识; value: 体检记录RID;
        Map<Integer, List<Integer>> currentMarkBhkRidMap = new HashMap<>();

        List<BhkCheckPojo> markBhkRidMapList = new ArrayList<>();
        List<List<Integer>> bhkRidMapList = new ArrayList<>();

        // 审核结果是否通过
        boolean approved = new Integer("1").equals(task.getCheckRst());
        // 保存前数据封装
        pakMarkBhkCheck(list, task, bhkCheckCondition, lastMarkBhkRidMap, currentMarkBhkRidMap, markBhkRidMapList, bhkRidMapList, approved);
        // 批量保存
        int result = saveBatchOperation(lastMarkBhkRidMap, markBhkRidMapList, bhkRidMapList, currentMarkBhkRidMap, task, list.size(), bhkCheckCondition);
        //退回时需要清空主动监测花名册中的体检编号
        if (approved) {
            return result;
        }
        List<Integer> allBhkRidList = new ArrayList<>();
        for (List<Integer> curList : bhkRidMapList) {
            if (CollectionUtils.isEmpty(curList)) {
                continue;
            }
            allBhkRidList.addAll(curList);
        }
        this.clearJcTaskPsnBhkCode(allBhkRidList);
        return result;
    }


    /**
    * <p>Description：退回时清空主动监测花名册中的体检编号 </p>
    * <p>Author： yzz 2024-10-15 </p>
    */
    public void clearJcTaskPsnBhkCode(List<Integer> allBhkRidList) {
        if (CollectionUtils.isEmpty(allBhkRidList)) {
            return;
        }
        List<List<Integer>> groupList = StringUtils.splitListProxy(allBhkRidList, 1000);
        for (List<Integer> bhkRidList : groupList) {
            tbTjJcTaskPsnMapper.updateBatchByBhkRid(bhkRidList);
        }
    }


    /**
     * <p>Description：批量保存逻辑 </p>
     * <p>Author： yzz 2024-10-15 </p>
     */
    public int saveBatchOperation(Map<Integer, List<Integer>> lastMarkBhkRidMap,
                                  List<BhkCheckPojo> markBhkRidMapList,
                                  List<List<Integer>> bhkRidMapList,
                                  Map<Integer, List<Integer>> currentMarkBhkRidMap,
                                  TdTjCheckTask task, int total, BhkCheckCondition bhkCheckCondition) {
        int resultNumber = 0;
        // 新增操作标识为21,33,44的操作记录
        insertInitialFlowBatch(lastMarkBhkRidMap.get(21), 21, task);
        insertInitialFlowBatch(lastMarkBhkRidMap.get(33), 33, task);
        insertInitialFlowBatch(lastMarkBhkRidMap.get(44), 44, task);

        // 1、根据lastMarkBhkRidMap获取List<Object[]> processReceiptDateList {体检记录RID, 流程接收日期, 流程RID}
        List<TdZwBgkFlow> processReceiptDateList = new ArrayList<>(total);
        for (Integer lastMark : lastMarkBhkRidMap.keySet()) {
            if (!CollectionUtils.isEmpty(lastMarkBhkRidMap.get(lastMark))) {
                processReceiptDateList.addAll(getProcessReceiptDateList(lastMark, lastMarkBhkRidMap.get(lastMark)));
            }
        }
        // 2、遍历processReceiptDateList计算是否及时，存于Map<> bhkRidTimelyMap key:是否及时; value: 流程RID List;
        Map<Integer, List<Integer>> bhkRidTimelyMap = new HashMap<>();
        bhkRidTimelyMap.put(0, new ArrayList<>());
        bhkRidTimelyMap.put(1, new ArrayList<>());
        for (TdZwBgkFlow bgkFlow : processReceiptDateList) {
            int calLimitTime = bgkFlow.getRcvDate().before(bhkCheckCondition.getLimitDate()) ? 0 : 1;
            bhkRidTimelyMap.get(calLimitTime).add(bgkFlow.getRid());
        }

        // 根据传入的字段名更新BHK表对应RID主键的字段值
        if (!CollectionUtils.isEmpty(bhkRidMapList) && !CollectionUtils.isEmpty(markBhkRidMapList)) {
            for (int i = 0; i < bhkRidMapList.size(); i++) {
                markBhkRidMapList.get(i).setIndex(i);
                resultNumber += this.updateTdTjBhkListByIds(bhkRidMapList.get(i), markBhkRidMapList.get(i),task.getFkByCheckRsnId().getRid());
            }
        }
        // 更新上次流程记录 OPER_DATE OPER_PSN_ID IF_IN_TIME
        for (Integer timely : bhkRidTimelyMap.keySet()) {
            if (!CollectionUtils.isEmpty(bhkRidTimelyMap.get(timely))) {
                updateLastFlowBatch(bhkRidTimelyMap.get(timely), timely, task);
            }
        }
        // 新增当前流程记录
        for (Integer operationFlag : currentMarkBhkRidMap.keySet()) {
            if (!CollectionUtils.isEmpty(currentMarkBhkRidMap.get(operationFlag))) {
                insertCurrentFlowBatch(currentMarkBhkRidMap.get(operationFlag), operationFlag, task, bhkCheckCondition.getUsername());
            }
        }
        return resultNumber;
    }


    /**
     * <p>Description：插入当前流程记录 </p>
     * <p>Author： yzz 2024-10-15 </p>
     */
    public void insertCurrentFlowBatch(List<Integer> ids, int operationFlag, TdTjCheckTask task, String userName) {
        int length = ids.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = ids.subList(i * 1000, endIndex);
            tdZwBgkFlowMapper.batchInsertBgkFlow(operationFlag, task.getFkByCheckRsnId().getRid(), task.getFkByCheckRsnId().getRid(), task.getCheckAdv(), userName, subList);
        }
    }


    /**
     * <p>Description：更新上一次操作记录 </p>
     * <p>Author： yzz 2024-10-15 </p>
     */
    public void updateLastFlowBatch(List<Integer> ids, int timely, TdTjCheckTask task) {
        int length = ids.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = ids.subList(i * 1000, endIndex);
            this.tdZwBgkFlowMapper.updateBgkFlow(task.getFkByCheckRsnId().getRid(), timely, task.getFkByCheckRsnId().getRid(),subList);
        }
    }


    /**
     * <p>Description：根据传入的字段名更新对应RID主键的字段值 </p>
     * <p>Author： yzz 2024-10-15 </p>
     */
    private int updateTdTjBhkListByIds(List<Integer> ids, BhkCheckPojo bhkCheckPojo,Integer psnId) {
        int resultNumber = 0;
        if (!CollectionUtils.isEmpty(ids) && bhkCheckPojo != null) {
            int length = ids.size();
            int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
            for (int i = 0; i < allDataCount; i++) {
                int endIndex = Math.min((i + 1) * 1000, length);
                List<Integer> subList = ids.subList(i * 1000, endIndex);
                resultNumber += tdTjBhkMapper.updateBhkBytype(bhkCheckPojo, subList,psnId);
            }
        }
        return resultNumber;
    }


    /**
     * <p>Description：根据体检记录RID及上次操作标识获取上一次操作时间 </p>
     * <p>Author： yzz 2024-10-15 </p>
     */
    public List<TdZwBgkFlow> getProcessReceiptDateList(Integer lastMark, List<Integer> bhkRidList) {
        int length = bhkRidList.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        List<TdZwBgkFlow> list = new ArrayList<>();
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = bhkRidList.subList(i * 1000, endIndex);
            list.addAll(tdZwBgkFlowMapper.selectBgkFlowByLastMark(lastMark, subList));
        }
        List<TdZwBgkFlow> result = new ArrayList<>(list.size());
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) {
                String last = StringUtils.objectToString(list.get(i - 1).getBusId());
                String now = StringUtils.objectToString(list.get(i).getBusId());
                // 上一条和当前条的BUS_ID一致，直接跳过，同一BUS_ID仅记录CREATE_DATE最新的一条
                if (last != null && last.equals(now)) {
                    continue;
                }
            }
            result.add(list.get(i));
        }
        return result;
    }


    /**
     * <p>Description：插入历史流程记录 </p>
     * <p>Author： yzz 2024-10-15 </p>
     */
    public void insertInitialFlowBatch(List<Integer> ids, Integer operationFlag, TdTjCheckTask task) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        int length = ids.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = ids.subList(i * 1000, endIndex);
            tdZwBgkFlowMapper.batchInsertBgkFlow(operationFlag, task.getFkByCheckRsnId().getRid(), null, null, null, subList);
        }
    }

    /**
     * <p>Description：数据封装 </p>
     * <p>Author： yzz 2024-10-14 </p>
     */
    public void pakMarkBhkCheck(List<TdTjBhk> list, TdTjCheckTask task, BhkCheckCondition bhkCheckCondition, Map<Integer, List<Integer>> lastMarkBhkRidMap, Map<Integer, List<Integer>> currentMarkBhkRidMap, List<BhkCheckPojo> markBhkRidMapList, List<List<Integer>> bhkRidMapList, boolean approved) {
        // 数据封装 体检主表需要更新的数据
        pakMarkBhkField(task, markBhkRidMapList, bhkRidMapList, approved);
        // 封装 需要更新的体检主表rid集合，以及上一条操作标记和当前操作标记
        for (TdTjBhk bhk : list) {
            try {
                int bhkRid = bhk.getRid();
                boolean cityDirect = new Integer("1").equals(bhk.getIfCityDirect());
                boolean provinceDirect = new Integer("1").equals(bhk.getIfProvDirect());
                // 上一次操作标记
                int lastMark;
                // 当前操作标记
                int currentMark;
                if ("3".equals(bhkCheckCondition.getCheckLevel())) {
                    if (bhkCheckCondition.getZoneType() > 3) {
                        // 初审-区级
                        lastMark = 21;
                        currentMark = approved ? 31 : 11;
                        bhkRidMapList.get(0).add(bhkRid);
                    } else if (bhkCheckCondition.getZoneType() == 3) {
                        if (cityDirect) {
                            // 初审-市级
                            lastMark = 33;
                            currentMark = approved ? 41 : 13;
                            bhkRidMapList.get(1).add(bhkRid);
                        } else {
                            // 复审-市级
                            lastMark = 31;
                            currentMark = approved ? 41 : 22;
                            bhkRidMapList.get(2).add(bhkRid);
                        }
                    } else {
                        // 终审-省级
                        lastMark = 41;
                        currentMark = approved ? 42 : 32;
                        bhkRidMapList.get(3).add(bhkRid);
                    }
                } else if ("2".equals(bhkCheckCondition.getCheckLevel())) {
                    if (bhkCheckCondition.getZoneType() > 3) {
                        // 初审-区级
                        lastMark = 21;
                        currentMark = approved ? 43 : 11;
                        bhkRidMapList.get(1).add(bhkRid);
                    } else {
                        // 终审-市/省级
                        currentMark = approved ? 42 : 32;
                        if (provinceDirect) {
                            // 省直
                            lastMark = 44;
                        } else {
                            // 非省直
                            lastMark = 43;
                        }
                        bhkRidMapList.get(3).add(bhkRid);
                    }
                } else {
                    return;
                }

                if (!lastMarkBhkRidMap.containsKey(lastMark)) {
                    lastMarkBhkRidMap.put(lastMark, new ArrayList<>());
                }
                lastMarkBhkRidMap.get(lastMark).add(bhkRid);

                if (!currentMarkBhkRidMap.containsKey(currentMark)) {
                    currentMarkBhkRidMap.put(currentMark, new ArrayList<>());
                }
                currentMarkBhkRidMap.get(currentMark).add(bhkRid);
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("数据处理异常：" + e.getMessage());
            }
        }
    }


    /**
     * <p>Description：封装体检主表不同审核情况需要更新的数据 </p>
     * <p>Author： yzz 2024-10-14 </p>
     */
    public void pakMarkBhkField(TdTjCheckTask task, List<BhkCheckPojo> markBhkRidMapList, List<List<Integer>> bhkRidMapList, boolean approved) {

        // 三级审核 初审方式
        bhkRidMapList.add(new ArrayList<>());
        BhkCheckPojo checkPojo = new BhkCheckPojo();
        checkPojo.setState(approved ? 3 : 0);
        checkPojo.setCountyCheckWay(3);
        checkPojo.setCountyRst(task.getCheckRst());
        checkPojo.setCountyAuditAdv(task.getCheckAdv());
        checkPojo.setCountyChkOrgid(task.getFkByCheckUnitId().getRid());
        checkPojo.setCountySmtDate(DateUtils.formatDate(new Date()) + " 23:59:59");
        markBhkRidMapList.add(checkPojo);

        // 市直属 初审方式
        bhkRidMapList.add(new ArrayList<>());
        BhkCheckPojo checkPojo1 = new BhkCheckPojo();
        checkPojo1.setState(approved ? 5 : 0);
        checkPojo1.setCountyCheckWay(3);
        checkPojo1.setCountyRst(task.getCheckRst());
        checkPojo1.setCountyAuditAdv(task.getCheckAdv());
        checkPojo1.setCountyChkOrgid(task.getFkByCheckUnitId().getRid());
        checkPojo1.setCountySmtDate(DateUtils.formatDate(new Date()) + " 23:59:59");
        markBhkRidMapList.add(checkPojo1);

        // 非市直属 复审方式
        bhkRidMapList.add(new ArrayList<>());
        BhkCheckPojo checkPojo2 = new BhkCheckPojo();
        checkPojo2.setState(approved ? 5 : 2);
        checkPojo2.setCityCheckWay(3);
        checkPojo2.setCityRst(task.getCheckRst());
        checkPojo2.setCityAuditAdv(task.getCheckAdv());
        checkPojo2.setCityChkOrgid(task.getFkByCheckUnitId().getRid());
        checkPojo2.setCitySmtDate(DateUtils.formatDate(new Date()) + " 23:59:59");
        markBhkRidMapList.add(checkPojo2);

        // 省级操作终审结果
        bhkRidMapList.add(new ArrayList<>());
        BhkCheckPojo checkPojo3 = new BhkCheckPojo();
        checkPojo3.setState(approved ? 6 : 4);
        checkPojo3.setProCheckWay(3);
        checkPojo3.setCityRst2(task.getCheckRst());
        checkPojo3.setProAuditAdv(task.getCheckAdv());
        checkPojo3.setProChkOrgid(task.getFkByCheckUnitId().getRid());
        checkPojo3.setProSmtDate(DateUtils.formatDate(new Date()) + " 23:59:59");
        checkPojo3.setProChkPsnid(task.getFkByCheckRsnId().getRid());
        markBhkRidMapList.add(checkPojo3);
    }


}

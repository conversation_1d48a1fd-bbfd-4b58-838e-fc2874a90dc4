package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p> 类描述： </p>
 *
 * <AUTHOR> 2024-07-12,TdZwOrgWarnMain
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_ORG_WARN_MAIN")
@KeySequence(value = "TD_ZW_ORG_WARN_MAIN_SEQ",clazz = Integer.class)
public class TdZwOrgWarnMain extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("BUS_TYPE")
    private String busType;

    @TableField("BUS_ID")
    private String busId;

    @TableField("IF_EXCEPT")
    private Integer ifExcept;

    @TableField("WARN_DATE")
    private Date warnDate;

    @TableField(exist = false)
    private List<TdZwOrgWarnExcept> orgWarnExceptList;


    public TdZwOrgWarnMain(Integer rid) {
        super(rid);
    }

}

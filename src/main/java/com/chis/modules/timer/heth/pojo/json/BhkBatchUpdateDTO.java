package com.chis.modules.timer.heth.pojo.json;

import com.chis.modules.timer.heth.pojo.BhkCheckPojo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 */
@Data
@NoArgsConstructor
public class BhkBatchUpdateDTO {
    private BhkCheckPojo pojo;      // 原有参数
    private Integer psnId;           // 操作人ID
    private Map<Integer, String> ridToAdvMap; // RID与审核意见的映射
    private List<Integer> ridList;
}

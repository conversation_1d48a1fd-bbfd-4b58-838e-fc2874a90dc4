package com.chis.modules.timer.heth.job.country;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chis.comm.utils.*;
import com.chis.modules.timer.heth.enums.CardUploadCountryBusTypeEnum;
import com.chis.modules.timer.heth.service.SrvorgCardUploadService;
import com.chis.modules.timer.heth.service.TdZwOcchethCardVoService;
import com.chis.modules.timer.heth.service.TdZywsCardRcdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.regex.Matcher;

/**
 * 职业/放射卫生技术服务信息报送卡附件上传&提交
 *
 * <AUTHOR>
 * @version 1.0
 **/
@Slf4j
@Component
public class HealthReportCardUpdateFileAndSubmitJob {
    @Autowired
    TdZywsCardRcdService tdZywsCardRcdService;
    @Autowired
    TdZwOcchethCardVoService tdZwOcchethCardVoService;
    @Autowired
    SrvorgCardUploadService srvorgCardUploadService;

    @Value("${heth-timer.virtual.directory}")
    protected String virtualDir;
    /**
     * 传输密钥
     */
    @Value("${heth-timer.country.security-key}")
    protected String securityKey;
    /**
     * 加密密钥
     */
    @Value("${heth-timer.country.encrypt-key}")
    protected String encryptKey;
    /**
     * 单次循环处理数据条数
     */
    @Value("${heth-timer.country.dataSize}")
    protected Integer dataSize;
    /**
     * 职业卫生技术服务信息报送卡附件上传接口
     */
    @Value("${heth-timer.country.occHealthUpdateFileUrl}")
    protected String occHealthUpdateFileUrl;
    /**
     * 职业卫生技术服务信息报送卡提交上报接口
     */
    @Value("${heth-timer.country.occHealthSubmitUrl}")
    protected String occHealthSubmitUrl;
    /**
     * 放射卫生技术服务信息报送卡附件上传接口
     */
    @Value("${heth-timer.country.srvOrgUpdateFileUrl}")
    protected String srvOrgUpdateFileUrl;
    /**
     * 放射卫生技术服务信息报送卡提交上报接口
     */
    @Value("${heth-timer.country.srvOrgSubmitUrl}")
    protected String srvOrgSubmitUrl;
    /**
     * 职业卫生技术服务信息报送卡创建后可附件上传&提交天数
     */
    @Value("${heth-timer.country.occHealthAvailableSubmitDays}")
    protected Integer occHealthAvailableSubmitDays;
    /**
     * 放射卫生技术服务信息报送卡创建后可附件上传&提交天数
     */
    @Value("${heth-timer.country.srvOrgAvailableSubmitDays}")
    protected Integer srvOrgAvailableSubmitDays;

    @Scheduled(cron = "${heth-timer.country.sche-cron.occHealthUpdateFileAndSubmitCron}")
    public void occHealthUpdateFileAndSubmitJob() {
        updateFileAndSubmit(CardUploadCountryBusTypeEnum.OCCCARD);
    }

    @Scheduled(cron = "${heth-timer.country.sche-cron.srvOrgUpdateFileAndSubmitCron}")
    public void srvOrgUpdateFileAndSubmitJob() {
        updateFileAndSubmit(CardUploadCountryBusTypeEnum.SRVORGCARD);
    }

    private void updateFileAndSubmit(CardUploadCountryBusTypeEnum busType) {
        if (!checkConfigParam(busType)) {
            return;
        }
        dealFailedData(busType);
        List<Map<String, Object>> dataMapList = findNeedUpdateFileAndSubmitData(busType);
        while (!ObjectUtils.isEmpty(dataMapList)) {
            for (Map<String, Object> dataMap : dataMapList) {
                boolean flag = ObjectUtils.isEmpty(dataMap)
                        || ObjectUtils.isEmpty(dataMap.get("REPORT_ID"))
                        || ObjectUtils.isEmpty(dataMap.get("CREDIT_CODE"))
                        || ObjectUtils.isEmpty(dataMap.get("ANNEX_PATH"))
                        || ObjectUtils.isEmpty(dataMap.get("SIGN_ADDRESS"));
                if (flag) {
                    continue;
                }
                int reportId = Integer.parseInt(StringUtils.objectToString(dataMap.get("REPORT_ID")));
                String updateFile = updateFile(busType, dataMap);
                if (!ObjectUtils.isEmpty(updateFile)) {
                    updateRcdState(busType, reportId, 4, updateFile);
                    continue;
                }
                String submitData = submitData(busType, dataMap);
                if (!ObjectUtils.isEmpty(submitData)) {
                    updateRcdState(busType, reportId, 5, submitData);
                    continue;
                }
                updateRcdState(busType, reportId, 3, "");
            }
            dataMapList = findNeedUpdateFileAndSubmitData(busType);
        }
    }

    /**
     * 检查配置参数
     *
     * @param busType 业务类型 <pre>12：职业卫生报送卡</pre><pre>22：放射卫生报送卡</pre>
     */
    private boolean checkConfigParam(CardUploadCountryBusTypeEnum busType) {
        boolean flag = true;
        if (this.dataSize == null) {
            log.error("职业/放射卫生技术服务信息报送卡-国家接口-配置参数：单次循环处理数据条数不能为空！");
            flag = false;
        }
        if (CardUploadCountryBusTypeEnum.OCCCARD.equals(busType)) {
            if (this.occHealthAvailableSubmitDays == null) {
                log.error("职业卫生技术服务信息报送卡-国家接口-配置参数：职业卫生技术服务信息报送卡创建后可附件上传&提交天数不能为空！");
                flag = false;
            }
        } else if (CardUploadCountryBusTypeEnum.SRVORGCARD.equals(busType)) {
            if (this.srvOrgAvailableSubmitDays == null) {
                log.error("放射卫生技术服务信息报送卡-国家接口-配置参数：放射卫生技术服务信息报送卡创建后可附件上传&提交天数不能为空！");
                flag = false;
            }
        } else {
            return false;
        }
        return flag;
    }

    /**
     * 将主表日志状态为4或5的更新为1
     *
     * @param busType 业务类型 <pre>12：职业卫生报送卡</pre><pre>22：放射卫生报送卡</pre>
     */
    private void dealFailedData(CardUploadCountryBusTypeEnum busType) {
        this.tdZywsCardRcdService.updateCardRcdStateForFailed(busType.getCode());
    }

    /**
     * 查询主表和子表关联日志记录状态全为1且创建日期+15天（配置文件）<=当天的数据
     *
     * @param busType 业务类型 <pre>12：职业卫生报送卡</pre><pre>22：放射卫生报送卡</pre>
     */
    private List<Map<String, Object>> findNeedUpdateFileAndSubmitData(CardUploadCountryBusTypeEnum busType) {
        if (CardUploadCountryBusTypeEnum.OCCCARD.equals(busType)) {
            return this.tdZwOcchethCardVoService.findNeedUpdateFileAndSubmitData(
                    this.dataSize, DateUtils.getDateByDays(-this.occHealthAvailableSubmitDays)
            );
        } else if (CardUploadCountryBusTypeEnum.SRVORGCARD.equals(busType)) {
            return this.srvorgCardUploadService.findNeedUpdateFileAndSubmitData(
                    this.dataSize, DateUtils.getDateByDays(-this.srvOrgAvailableSubmitDays)
            );
        }
        return new ArrayList<>();
    }

    private String updateFile(CardUploadCountryBusTypeEnum busType, Map<String, Object> dataMap) {
        String url = "";
        if (CardUploadCountryBusTypeEnum.OCCCARD.equals(busType)) {
            url = StringUtils.objectToString(this.occHealthUpdateFileUrl);
        } else if (CardUploadCountryBusTypeEnum.SRVORGCARD.equals(busType)) {
            url = StringUtils.objectToString(this.srvOrgUpdateFileUrl);
        }
        if (ObjectUtils.isEmpty(url)) {
            return "请求地址为空！";
        }
        if (ObjectUtils.isEmpty(dataMap)) {
            return "";
        }
        try {
            Map<String, Object> paramMap = new HashMap<>(4);
            String path = this.virtualDir + File.separator + StringUtils.objectToString(dataMap.get("ANNEX_PATH"));
            String signPath = this.virtualDir + File.separator + StringUtils.objectToString(dataMap.get("SIGN_ADDRESS"));
            String separator = Matcher.quoteReplacement(File.separator);
            path = path.replaceAll(Matcher.quoteReplacement("/".equals(File.separator) ? "\\" : "/"), separator);
            path = path.replaceAll(separator + "+", separator);
            //签发页附件
            signPath = signPath.replaceAll(Matcher.quoteReplacement("/".equals(File.separator) ? "\\" : "/"), separator);
            signPath = signPath.replaceAll(separator + "+", separator);
            File file = new File(path);
            File file1 = new File(signPath);
            paramMap.put("filea", file);
            paramMap.put("fileb", file1);
            return post(
                    url,
                    paramMap,
                    StringUtils.objectToString(dataMap.get("REPORT_ID")),
                    StringUtils.objectToString(dataMap.get("CREDIT_CODE"))
            );
        } catch (Exception e) {
            log.error(e.getMessage(), new Throwable(e));
            return "请求异常：" + e;
        }
    }

    private String submitData(CardUploadCountryBusTypeEnum busType, Map<String, Object> dataMap) {
        String url = "";
        if (CardUploadCountryBusTypeEnum.OCCCARD.equals(busType)) {
            url = StringUtils.objectToString(this.occHealthSubmitUrl);
        } else if (CardUploadCountryBusTypeEnum.SRVORGCARD.equals(busType)) {
            url = StringUtils.objectToString(this.srvOrgSubmitUrl);
        }
        if (ObjectUtils.isEmpty(url)) {
            return "请求地址为空！";
        }
        if (ObjectUtils.isEmpty(dataMap)) {
            return "";
        }
        try {
            return post(
                    url,
                    new HashMap<>(4),
                    StringUtils.objectToString(dataMap.get("REPORT_ID")),
                    StringUtils.objectToString(dataMap.get("CREDIT_CODE"))
            );
        } catch (Exception e) {
            log.error(e.getMessage(), new Throwable(e));
            return "请求异常：" + e;
        }
    }

    private String post(String url, Map<String, Object> paramMap, String reportId, String creditCode) throws Exception {
        Map<String, String> headerMap = new HashMap<>(4);
        Map<String, String> apiDataMap = new HashMap<>(4);
        apiDataMap.put("securityKey", StringUtils.objectToString(securityKey));
        apiDataMap.put("reportId", StringUtils.objectToString(reportId));
        apiDataMap.put("ocode", StringUtils.objectToString(creditCode));
        String apiData = JSON.toJSONString(apiDataMap);
        log.info("职业/放射卫生技术服务信息报送卡附件上传&提交-apiData: {}", apiData);
        log.info("职业/放射卫生技术服务信息报送卡附件上传&提交-请求参数: {}",  JSONUtil.toJsonStr(paramMap));
        paramMap.put("apiData", DesEncryptUtil.encryptByKey(apiData, this.encryptKey));
        headerMap.put("Content-Type", "multipart/form-data;");
        String responseBody = OkHttpUtils.postManyParams(url, paramMap, headerMap);
        if (StringUtils.isBlank(responseBody)) {
            return "接口返回信息为空！";
        }
        JSONObject responseJson = JSONObject.parseObject(responseBody);
        if (ObjectUtils.isEmpty(responseJson)) {
            return "处理接口返回信息异常！";
        }
        if ("0".equals(responseJson.get("code"))) {
            return "接口返回信息：" + StringUtils.objectToString(responseBody);
        }
        return "";
    }

    private void updateRcdState(CardUploadCountryBusTypeEnum busType, int reportId, int state, String errMsg) {
        errMsg = StringUtils.objectToString(errMsg);
        if (errMsg.length() > 1000) {
            errMsg = errMsg.substring(0, 1000);
        }
        this.tdZywsCardRcdService.updateCardRcdStateByReportId(busType.getCode(), reportId, state, errMsg);
    }
}

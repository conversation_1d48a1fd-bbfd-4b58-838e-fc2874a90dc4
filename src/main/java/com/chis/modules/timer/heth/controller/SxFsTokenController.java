package com.chis.modules.timer.heth.controller;

import com.alibaba.fastjson.JSON;
import com.chis.comm.utils.AesEncryptUtils;
import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.HttpRequestUtil;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsSystemParam;
import com.chis.modules.sys.enums.JdbcCaffeineKeyEnum;
import com.chis.modules.sys.service.TsSystemParamService;
import com.chis.modules.timer.heth.pojo.TokenReturnPojo;
import com.chis.modules.timer.heth.pojo.UnitDataPojo;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 *  <p>类描述：陕西放射人员获取tokenid</p>
 * @ClassAuthor hsj 2022-09-09 8:56
 */
@Slf4j
@RestController
@RequestMapping("/sxFs")
public class SxFsTokenController {

    @Autowired
    private TsSystemParamService tsSystemParamService;

    private String unitCode ;
    private String password ;
    private String fsPsnurl;
    private String fsPsnKey;
    private String fsPsnVipara;
    private Map<String, TsSystemParam> tsSystemParamMap;
    private Map<String, String> systemParamMap;

    @PostMapping("/gainToken")
    public TokenReturnPojo gainToken(){
        log.info("获取tokenId 当前时间{}",  new Date());
        TokenReturnPojo tokenReturnPojo = new TokenReturnPojo();
        tsSystemParamMap = tsSystemParamService.findTsSystemParamByType("120");
        unitCode = getTsSystemParamByName("FS_PSN_UNIT_CODE");
        StringBuilder errorBuffer = new StringBuilder();
        if(StringUtils.isBlank(unitCode)){
            errorBuffer.append("；").append("系统参数机构编码为空");
        }
        password = getTsSystemParamByName("FS_PSN_PASSWORD");
        if(StringUtils.isBlank(password)){
            errorBuffer.append("；").append("系统参数密码为空");
        }
        fsPsnurl = getTsSystemParamByName("FS_PSN_URL");
        if(StringUtils.isBlank(fsPsnurl)){
            errorBuffer.append("；").append("系统参数放射人员信息地址为空");
        }
        fsPsnKey = getTsSystemParamByName("FS_PSN_KEY");
        if(StringUtils.isBlank(fsPsnKey)){
            errorBuffer.append("；").append("系统参数加密密钥为空");
        }
        fsPsnVipara = getTsSystemParamByName("FS_PSN_VIPARA");
        if(StringUtils.isBlank(fsPsnVipara)){
            errorBuffer.append("；").append("系统参数偏移量为空");
        }
        String errorStr = errorBuffer.toString();
        if (StringUtils.isNotBlank(errorStr)) {
            log.error(errorStr.substring(1));
            tokenReturnPojo.setMess(errorStr.substring(1));
            tokenReturnPojo.setType("00");
            return tokenReturnPojo;
        }
        systemParamMap = new HashMap<>();
        systemParamMap.put("fsPsnurl",fsPsnurl);
        systemParamMap.put("fsPsnKey",fsPsnKey);
        systemParamMap.put("fsPsnVipara",fsPsnVipara);
        //先根据
        String key = unitCode+ "_" +password;
        TokenReturnPojo returnPojos = (TokenReturnPojo) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.SX_FS_TOKEN,key);
        if(null != returnPojos ){
            if(returnPojos.getEndDate().after(new Date())){
                return returnPojos;
            }
        }
        //获取token
        UnitDataPojo json = new UnitDataPojo();
        json.setUnitCode(unitCode);
        json.setPassword(password);
        String requestJson = JSON.toJSONString(json);
        String encodeJson = AesEncryptUtils.encrypt(requestJson, fsPsnKey,fsPsnVipara);
        String reposeJson ="";
        try {
            reposeJson = HttpRequestUtil.httpRequestByRaw(fsPsnurl+"/apiv2/token",encodeJson);
            String returnJson = AesEncryptUtils.desEncrypt(reposeJson, fsPsnKey,fsPsnVipara);
            if(StringUtils.isNotBlank(returnJson)){
                TokenReturnPojo returnPojo = JSON.parseObject(returnJson, TokenReturnPojo.class);
                if(null == returnPojo || !"1".equals(returnPojo.getType())){
                    log.error("获取token失败！");
                    tokenReturnPojo.setMess("获取token失败！");
                    tokenReturnPojo.setType("00");
                    return tokenReturnPojo;
                }else {
                    String time = returnPojo.getValidateTime();
                    if(StringUtils.isNotBlank(time)){
                        Integer hour = Integer.valueOf(time) - 1 ;
                        Date date = DateUtils.addHours(new Date(),hour);
                        returnPojo.setEndDate(date);
                        CaffeineUtil.put(JdbcCaffeineKeyEnum.SX_FS_TOKEN,key,returnPojo);
                    }
                    returnPojo.setTsSystemParamMap(systemParamMap);
                    return returnPojo;
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("获取token失败！");
            tokenReturnPojo.setMess("获取token失败！");
            tokenReturnPojo.setType("00");
            return tokenReturnPojo;
        }
        return tokenReturnPojo;

    }
    private String getTsSystemParamByName(String name) {
        TsSystemParam tsSystemParam = tsSystemParamMap.get(name);
        if(null == tsSystemParam || StringUtils.isBlank(tsSystemParam.getParamValue())){
            return null;
        }else {
            return tsSystemParam.getParamValue();
        }

    }
}

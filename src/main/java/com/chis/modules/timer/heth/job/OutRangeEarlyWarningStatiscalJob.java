package com.chis.modules.timer.heth.job;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.comm.utils.ZoneUtil;
import com.chis.modules.sys.entity.TbTjSrvorg;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsUnit;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.service.TbTjSrvorgService;
import com.chis.modules.sys.service.TsSimpleCodeService;
import com.chis.modules.sys.service.TsUnitService;
import com.chis.modules.sys.service.TsZoneService;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <p>
 *     类描述：超范围服务预警统计工具
 * </p>
 *
 * @ClassAuthor pw,2020年11月17日,OutRangeEarlyWarningStatiscalJob
 */
@Slf4j
@Component
public class OutRangeEarlyWarningStatiscalJob {

    @Autowired
    private TdZwyjOtrBhkService tdZwyjOtrBhkService;
    @Autowired
    private TsZoneService tsZoneService;
    @Autowired
    private TdZwyjOtrServiceItemService otrServiceItemService;
    @Autowired
    private TbTjSrvorgService tjSrvorgService;
    @Autowired
    private TsUnitService unitService;
    @Autowired
    private TsSimpleCodeService simpleCodeService;
    @Autowired
    private TbTjCrptService crptService;
    @Autowired
    private TdZwyjOtrSmyRcdService smyRcdService;

    /** 缓存企业市级地区 */
    private Map<String, TsZone> cityZoneMap = new HashMap<>();
    /** 缓存企业地区 */
    private Map<Integer,TsZone> companyZoneMap = new HashMap<>();
    /** 缓存服务项目 */
    private Map<Integer,TsSimpleCode> simpleCodeMap = new HashMap<>();
    /** 缓存体检机构 */
    private Map<Integer,TbTjSrvorg> tjSrvorgMap = new HashMap<>();
    /** 资质服务机构注册信息rid与单位缓存 */
    private Map<Integer,TsUnit> tsUnitMap = new HashMap<>();
    /** 所有单位缓存 */
    private Map<Integer,TsUnit> allUnitMap = new HashMap<>();
    /** 所有地区缓存 */
    private Map<Integer,TsZone> allZoneMap = new HashMap<>();
    /** 企业信息缓存 */
    private Map<Integer,TbTjCrpt> crptMap = new HashMap<>();
    /** 超范围服务体检记录rid对应的服务项目rid缓存 */
    private Map<Integer,List<Integer>> itemListMap = new HashMap<>();

    @Value("${heth-timer.outRange.warnDataSize}")
    private Integer dataSize;

    @Scheduled(cron = "${heth-timer.sche-cron.warnStatiscal}")
    public void start(){
        Date beginTime = new Date();
        List<TsZone> zoneList = tsZoneService.selectListByEntity(null);
        if(null == zoneList || zoneList.size() == 0){
            log.error("获取系统地区信息失败，超范围服务预警统计任务终止执行");
            return;
        }
        for(TsZone t : zoneList){
            allZoneMap.put(t.getRid().intValue(),t);
            if(3 == t.getZoneType()){
                cityZoneMap.put(t.getZoneGb(),t);
            }
        }

        List<TsUnit> unitList = unitService.selectListByEntity(null);
        if(null == unitList || unitList.size() == 0){
            log.error("获取系统单位失败，超范围服务预警统计任务终止执行");
            return;
        }
        for(TsUnit tt : unitList){
            allUnitMap.put(tt.getRid().intValue(), tt);
        }

        //获取所有的体检机构列表
        List<TbTjSrvorg> tjSrvorgList = tjSrvorgService.selectListByEntity(null);
        if(null == tjSrvorgList || tjSrvorgList.size() == 0){
            log.error("获取资质服务机构注册信息失败，超范围服务预警统计任务终止执行");
            return;
        }

        for(TbTjSrvorg tbTjSrvorg : tjSrvorgList){
            tjSrvorgMap.put(tbTjSrvorg.getRid().intValue(), tbTjSrvorg);
            if(StringUtils.isNotBlank(tbTjSrvorg.getRegOrgid())){
                TsUnit tsUnit = allUnitMap.get(Integer.parseInt(tbTjSrvorg.getRegOrgid()));
                if(null != tsUnit){
                    tsUnitMap.put(tbTjSrvorg.getRid().intValue(), tsUnit);
                }
            }
        }

        //获取所有项目 不区分是否启用
        List<TsSimpleCode> simpleCodeList =simpleCodeService.findAllTsSimpleCodeList("5018");
        if(null == simpleCodeList || simpleCodeList.size() == 0){
            log.error("获取体检机构服务项目失败，超范围服务预警统计任务终止执行");
            return;
        }
        for(TsSimpleCode simpleCode : simpleCodeList){
            simpleCodeMap.put(simpleCode.getRid().intValue(), simpleCode);
        }

        excuteDate();
        Date endTime = new Date();
        log.info("超范围服务预警统计开始时间：{} 结束时间：{} 耗时: {} 毫秒",DateUtils.formatDateTime(beginTime),DateUtils.formatDateTime(endTime),(endTime.getTime() - beginTime.getTime()));
    }

    private void excuteDate(){
        boolean reFlag = false;
        List<TdZwyjOtrBhk> bhkList = null;
        Date date = new Date();
        try{
            bhkList = getTdZwyjOtrBhks();
            bhkList = validateBhkList(bhkList);
            if(null == bhkList || bhkList.size() == 0){
                return;
            }
            log.info("超范围服务预警统计准备处理{}条记录",bhkList.size());
            //按体检机构分组
            Map<Integer,List<TdZwyjOtrBhk>> bhkMap = new HashMap<>();
            for(TdZwyjOtrBhk otrBhk : bhkList){
                List<TdZwyjOtrBhk> tmpBhkList = bhkMap.get(otrBhk.getFkByBhkorgId().getRid().intValue());
                if(null == tmpBhkList){
                    tmpBhkList = new ArrayList<>();
                }
                tmpBhkList.add(otrBhk);
                bhkMap.put(otrBhk.getFkByBhkorgId().getRid().intValue(), tmpBhkList);
            }
            if(bhkMap.isEmpty()){
                return;
            }
            for(Map.Entry<Integer,List<TdZwyjOtrBhk>> bhkMapEntry : bhkMap.entrySet()){
                List<TdZwyjOtrBhk> totalBhkList = bhkMapEntry.getValue();
                // 备案地区超范围
                List<TdZwyjOtrBhk> areaList = null;
                // 服务项目超范围
                List<TdZwyjOtrBhk> projList = null;
                for(TdZwyjOtrBhk t : totalBhkList){
                    if("1".equals(t.getOtrType().trim())){
                        if(null == areaList){
                            areaList = new ArrayList<>();
                        }
                        areaList.add(t);
                    }else if("2".equals(t.getOtrType().trim())){
                        if(null == projList){
                            projList = new ArrayList<>();
                        }
                        projList.add(t);
                    }
                }

                if(null != areaList){
                    reFlag = tdZwyjOtrBhkService.areaExcute(areaList, true,cityZoneMap,companyZoneMap,
                            tsUnitMap,itemListMap,simpleCodeMap,tjSrvorgMap,allZoneMap,crptMap);
                }
                if(null != projList){
                    reFlag = tdZwyjOtrBhkService.areaExcute(projList, false,cityZoneMap,companyZoneMap,
                            tsUnitMap,itemListMap,simpleCodeMap,tjSrvorgMap,allZoneMap,crptMap);
                }
            }
        }catch(Exception e){
            log.error(e.getMessage());
        }
        log.info("超范围服务预警统计处理完{}条记录，用时：{}毫秒",((null == bhkList) ? 0 : bhkList.size()),(new Date().getTime() - date.getTime()));
        if(reFlag){
            //执行完成走下一轮
            excuteDate();
        }
    }

    /** 信息验证 */
    private List<TdZwyjOtrBhk> validateBhkList(List<TdZwyjOtrBhk> bhkList){
        itemListMap.clear();
        List<TdZwyjOtrBhk> resultList = new ArrayList<>();
        List<TdZwyjOtrSmyRcd> errList = new ArrayList<>();

        if(null != bhkList && bhkList.size() > 0){
            List<Integer> mainIds = new ArrayList<>();
            for(TdZwyjOtrBhk areaOtrBhk : bhkList){
                if("2".equals(areaOtrBhk.getOtrType())){
                    mainIds.add(areaOtrBhk.getRid().intValue());
                }
            }
            if(null != mainIds && mainIds.size() > 0){
                List<TdZwyjOtrServiceItem> otrServiceItemList = findOtrServiceItemListByMainIds(mainIds);
                if(null != otrServiceItemList && otrServiceItemList.size() > 0){
                    itemListMap = new HashMap<>();
                    for(TdZwyjOtrServiceItem serviceItem : otrServiceItemList){
                        List<Integer> itemList = itemListMap.get(serviceItem.getFkByMainId().getRid().intValue());
                        if(null == itemList){
                            itemList = new ArrayList<>();
                        }
                        if(!itemList.contains(serviceItem.getFkByServiceId().getRid().intValue())){
                            itemList.add(serviceItem.getFkByServiceId().getRid().intValue());
                        }
                        itemListMap.put(serviceItem.getFkByMainId().getRid().intValue(), itemList);
                    }
                }
            }

            for(TdZwyjOtrBhk tdZwyjOtrBhk : bhkList){
                if(null == tdZwyjOtrBhk.getFkByBhkorgId() || null == tdZwyjOtrBhk.getFkByBhkorgId().getRid()){
                    errList.add(createSmyRcd(tdZwyjOtrBhk,"0","没有体检机构信息"));
                    continue;
                }

                StringBuilder builder = new StringBuilder("");
                if(StringUtils.isBlank(tdZwyjOtrBhk.getOtrType())){
                    builder.append("，").append("缺少超范围类型");
                }else if(!"1".equals(tdZwyjOtrBhk.getOtrType()) && !"2".equals(tdZwyjOtrBhk.getOtrType())){
                    builder.append("，").append("超范围类型未知");
                }

                TsUnit bhkOrg = tsUnitMap.get(tdZwyjOtrBhk.getFkByBhkorgId().getRid().intValue());
                TbTjSrvorg tjSrvorg = tjSrvorgMap.get(tdZwyjOtrBhk.getFkByBhkorgId().getRid().intValue());
                if(null == bhkOrg || null == tjSrvorg){
                    builder.append("，").append("获取体检机构失败");
                }

                TsZone tsZone = null;
                if(null != tjSrvorg && StringUtils.isNotBlank(tjSrvorg.getZoneId())){
                    tsZone = allZoneMap.get(Integer.parseInt(tjSrvorg.getZoneId()));
                }
                if(null == tsZone){
                    builder.append("，").append("获取体检机构地区失败");
                }

                TbTjCrpt tjCrpt  = crptMap.get(tdZwyjOtrBhk.getFkByCrptId().getRid().intValue());
                if(null == tjCrpt){
                    TbTjCrpt tmpT = new TbTjCrpt();
                    tmpT.setRid(tdZwyjOtrBhk.getFkByCrptId().getRid().intValue());
                    tjCrpt = crptService.selectByEntity(tmpT);
                    if(null == tjCrpt){
                        builder.append("，").append("获取企业信息失败");
                    }else{
                        crptMap.put(tdZwyjOtrBhk.getFkByCrptId().getRid().intValue(), tjCrpt);
                    }
                }

                if(!zoneVaild(tdZwyjOtrBhk.getFkByCrptZoneId().getRid().intValue())){
                    builder.append("，").append("通过企业地区ID未找到地区");
                }

                if("2".equals(tdZwyjOtrBhk.getOtrType())){
                    List<Integer> itemIds = (null == itemListMap || null == itemListMap.get(tdZwyjOtrBhk.getRid())) ? null : itemListMap.get(tdZwyjOtrBhk.getRid()) ;
                    if(null == itemIds || itemIds.size() == 0){
                        builder.append("，").append("缺少超服务项目范围_人员信息");
                    }else{
                        for(Integer itemId : itemIds){
                            TsSimpleCode tmpSimpleCode = simpleCodeMap.get(itemId.intValue());
                            if(null == tmpSimpleCode){
                                builder.append("，").append("项目rid：").append(itemId).append("获取项目失败");
                            }
                        }
                    }
                }

                String errMsg = builder.toString();
                if(StringUtils.isNotBlank(errMsg)){
                    errList.add(createSmyRcd(tdZwyjOtrBhk,"0",errMsg.substring(1)));
                    continue;
                }

                resultList.add(tdZwyjOtrBhk);
            }
        }
        //错误信息日志保存
        if(null != errList && errList.size() > 0){
            smyRcdService.saveBatch(errList);
        }
        return resultList;
    }

    /** 创建超范围服务预警统计日志 */
    private TdZwyjOtrSmyRcd createSmyRcd(TdZwyjOtrBhk areaOtrBhk,String proTag,String proMsg){
        TdZwyjOtrSmyRcd smyRcd = new TdZwyjOtrSmyRcd();
        smyRcd.setFkByBhkId(areaOtrBhk);
        smyRcd.setProTag(proTag);
        if(StringUtils.isNotBlank(proMsg) && proMsg.length() >= 1000){
            proMsg = proMsg.substring(0,990);
        }
        smyRcd.setProMsg(proMsg);
        smyRcd.setCreateDate(new Date());
        smyRcd.setCreateManid(1);
        return smyRcd;
    }

    /** 获取超服务项目范围_人员 列表 */
    private List<TdZwyjOtrServiceItem> findOtrServiceItemListByMainIds(List<Integer> mainIds){
        return otrServiceItemService.findOtrServiceItemListByMainIds(mainIds);
    }

    /** 验证企业地区 */
    private boolean zoneVaild(Integer rid){
        boolean flag = true;
        fillTsZone(rid);
        if(null == companyZoneMap.get(rid.intValue())){
            flag = false;
        }else if(null ==
                cityZoneMap.get(ZoneUtil.getParentCode(companyZoneMap.get(rid.intValue()).getZoneGb(),4))){
            flag = false;
        }
        return flag;
    }

    /** 缓存赋值 */
    private void fillTsZone(Integer rid){
        if(null != companyZoneMap.get(rid.intValue())){
            return;
        }

        TsZone companyZone = allZoneMap.get(rid);
        if(null != companyZone){
            companyZoneMap.put(companyZone.getRid(), companyZone);
        }
    }

    /** 查询不在超范围服务预警统计日志中的超范围服务体检记录列表  */
    private List<TdZwyjOtrBhk> getTdZwyjOtrBhks(){
        return tdZwyjOtrBhkService.findTdZwyjOtrBhkExcludeSmyRCD(dataSize);
    }

    /** 测试批量保存 */
    private void testSave(){
        List<TdZwyjOtrBhk> bhkList = getTdZwyjOtrBhks();
        List<TdZwyjOtrSmyRcd> smyRcdList = new ArrayList<>();
        if(null != bhkList && bhkList.size() > 0){
            for(TdZwyjOtrBhk otrBhk : bhkList){
                smyRcdList.add(createSmyRcd(otrBhk,"1",""));
            }
        }

        if(null != smyRcdList && smyRcdList.size() > 0){
            // 批量新增超范围服务预警统计日志
            smyRcdService.saveBatch(smyRcdList);
        }
    }

    /** 测试批量更新 */
    private void test(){
        TdZwyjOtrSmyRcd smR = new TdZwyjOtrSmyRcd();
        List<TdZwyjOtrSmyRcd> smyRcdList = smyRcdService.selectListByEntity(smR);
        if(null != smyRcdList && smyRcdList.size() > 0){
            for(TdZwyjOtrSmyRcd s : smyRcdList){
                s.setCreateManid(3);
                s.setModifyDate(new Date());
                s.setModifyManid(2);
            }
        }
        if(null != smyRcdList && smyRcdList.size() > 0){
            // 批量更新超范围服务预警统计日志
            //updateFullBatchById 不可以用
            smyRcdService.updateBatchById(smyRcdList);
        }
    }
}

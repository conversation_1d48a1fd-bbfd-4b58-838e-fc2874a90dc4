package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsUnit;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TbTjRadheth
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TB_TJ_RADHETH")
@KeySequence(value = "TB_TJ_RADHETH_SEQ",clazz = Integer.class)
public class TbTjRadheth extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "ZONE_ID" , el = "fkByZoneId.rid")
    private TsZone fkByZoneId;

    @TableField("UNIT_NAME")
    private String unitName;

    @TableField("UNIT_CODE")
    private String unitCode;

    @TableField("UNIT_GRADE_ID")
    private Integer unitGradeId;

    @TableField("UNIT_LEVEL_ID")
    private Integer unitLevelId;

    @TableField("LINK_TEL")
    private String linkTel;

    @TableField("INSTITUTION_CODE")
    private String institutionCode;

    @TableField("ADDRESS")
    private String address;

    @TableField("WORK_FORCE")
    private Integer workForce;

    @TableField("RAD_PSNS")
    private Integer radPsns;

    @TableField("RAD_CERT_PSNS")
    private Integer radCertPsns;

    @TableField("MANAGE_UNIT_ID")
    private Integer manageUnitId;

    @TableField("LINK_MAN")
    private String linkMan;

    @TableField("NEW_CHECK_UNIT_ID")
    private Integer newCheckUnitId;

    @TableField("UUID")
    private String uuid;

    @TableField("UPDATETAG")
    private Integer updatetag;

    @TableField("ERROR_MSG")
    private String errorMsg;

    @TableField("RAD_LICENSE_NO")
    private String radLicenseNo;

    @TableField("STATE")
    private Integer state;

    @TableField(value = "RCD_UNIT_ID" , el = "fkByRcdUnitId.rid")
    private TsUnit fkByRcdUnitId;


    public TbTjRadheth(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwyjBsnCsionItemSub;
import com.chis.modules.timer.heth.entity.TdZwyjBsnCsionItems;
import com.chis.modules.timer.heth.mapper.TdZwyjBsnCsionItemsMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnCsionItemsService
 */
@Service
public class TdZwyjBsnCsionItemsService extends ZwxBaseServiceImpl<TdZwyjBsnCsionItemsMapper, TdZwyjBsnCsionItems> {
    /**
     * <p>方法描述：查询项目配置规则</p>
     * @MethodAuthor qrr,2020-10-30,findItemsByMainId
     * */
    public List<TdZwyjBsnCsionItems>  findItemsByMainId(Integer mainId){
        TdZwyjBsnCsionItems csionItems = new TdZwyjBsnCsionItems();
        csionItems.setFkByMainId(new TdZwyjBsnCsionItemSub(mainId));
        return this.baseMapper.selectListByEntity(csionItems);
    }
}

package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>类描述：企业在线申报 result节点信息</p>
 * @ClassAuthor： pw 2022/9/7
 **/
@Data
public class CrptOnLineResultVo implements Serializable {
    private static final long serialVersionUID = -1703433639798048254L;

    private String uuid;
    private String employerUuid;
    private Boolean ifBranch;
    private String parentUuid;
    private String parentUnitName;
    private String parentCreditCode;
    private String parentBusinessZone;
    private Integer declareYear;
    private Integer declareType;
    private Integer declareStatus;
    private Date declareDate;
    private Date approveDate;
    private String reasonNo;
    private String remark;
    private String businessZone;
    private String unitName;
    private String creditCode;
    private String regAddr;
    private String workAddr;
    private String enterpriseScale;
    private String industryCateNo;
    private String economicNo;
    private String fillMan;
    private String fillPhone;
    private String legalPerson;
    private String legalPersonPhone;
    private String linkManager;
    private String linkPhone;
    private Integer empNum;
    private Integer externalNum;
    private Integer victimsNum;
    private Integer occupationalDiseasesNum;
    private Boolean ifWarProduct;
    private Integer operationStatus;
    private Boolean existsLeaderTrain;
    private Boolean existsManagersTrain;
    private Integer trainSum;

    private List<CrptOnLineProductVo> productList;
    private CrptOnLineDistributionVo distribution;
    private List<CrptOnLineDistributionItemVo> distributionItemList;
    private CrptOnLineDetectionVo detection;
    private List<CrptOnLineDetectionItemVo> detectionItemList;
    private List<CrptOnLineDetectionOrgVo> detectionOrgList;
    private CrptOnLineSupervisionVo supervision;
    private List<CrptOnLineSupervisionItemVo> supervisionItemList;
    private List<CrptOnLineSupervisionOrgVo> supervisionOrgList;
}

package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdRectificationList;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2023-09-20,TdRectificationListMapper
 */
@Repository
public interface TdRectificationListMapper extends ZwxBaseMapper<TdRectificationList> {
    /**
     * <p>方法描述：通过uuid集合获取专项治理-基本信息 </p>
     * pw 2023/9/20
     **/
    List<TdRectificationList> findByUuidList(@Param("list") List<String> list);
}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p> 类描述： </p>
 *
 * <AUTHOR> 2024-07-12,TdZwOrgWarnDetail
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_ORG_WARN_DETAIL")
@KeySequence(value = "TD_ZW_ORG_WARN_DETAIL_SEQ", clazz = Integer.class)
public class TdZwOrgWarnDetail extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID", el = "fkByMainId.rid")
    private TdZwOrgWarnExcept fkByMainId;

    @TableField(value = "WARN_ID", el = "fkByWarnId.rid")
    private TbZwOrgWarnConfig fkByWarnId;

    @TableField(value = "WARN_TYPE_ID", el = "fkByWarnTypeId.rid")
    private TsSimpleCode fkByWarnTypeId;

    @TableField("PSN_NUMS")
    private Integer psnNums;

    @TableField(exist = false)
    private List<TdZwOrgWarnPsns> orgWarnPsnsList;

    @TableField(exist = false)
    private List<TdZwOrgWarnInst> orgWarnInstList;


    public TdZwOrgWarnDetail(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.job.country;

import com.alibaba.fastjson.JSON;
import com.chis.comm.utils.DesEncryptUtil;
import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.HttpRequestUtil;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsContraSub;
import com.chis.modules.sys.service.TsContraSubService;
import com.chis.modules.timer.heth.entity.TdZywsCardRcd;
import com.chis.modules.timer.heth.enums.CardUploadCountryBusTypeEnum;
import com.chis.modules.timer.heth.logic.vo.CardBaseEntity;
import com.chis.modules.timer.heth.logic.vo.CardResponsAddVo;
import com.chis.modules.timer.heth.logic.vo.CardResponsBaseVo;
import com.chis.modules.timer.heth.service.TdZywsCardRcdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>类描述：职业/放射卫生技术服务信息报送卡-基类 </p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年12月20日
 **/
@Slf4j
@Component
public abstract class OcchethCardAbstract<T extends CardBaseEntity>{

    @Resource
    private TdZywsCardRcdService cardRcdService;

    @Resource
    private TsContraSubService tsContraSubService;

    /**传输密钥*/
    @Value("${heth-timer.country.security-key}")
    protected String securityKey;

    @Value("${heth-timer.country.dataSize}")
    protected Integer dataSize;

    @Value("${heth-timer.country.occhethCardAdd_url}")
    protected String occhethCardAddUrl;

    @Value("${heth-timer.country.occhethCardDel_url}")
    protected String occhethCardDelUrl;
    @Value("${heth-timer.country.srvorgcardAdd_url}")
    private String srvorgCardAddUrl;
    @Value("${heth-timer.country.srvorgcardDel_url}")
    private String srvorgCardDelUrl;
    @Value("${heth-timer.country.encrypt-key}")
    private String encryptKey;
    @Value("${heth-timer.country.rptCreateDate}")
    private String startDate;
    
    /**
     * <p>方法描述：开始执行</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-21
     **/
    public abstract void start();


    /**
     * <p>方法描述：报告卡上传</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-20
     **/
    public void uploadReportInfo() {
        //第一步  更新失败记录的状态 1:职业卫生技术服务信息报送卡  2:放射卫生技术服务信息报送卡
        this.cardRcdService.updateCardRcdByType(getBusType(), "2,9");
        //第二步  查询需要处理的数据
        List<T> list = this.findDealData(dataSize,this.startDate);
        while (!CollectionUtils.isEmpty(list)){
            this.executeUploadData(list);
            list = this.findDealData(dataSize, this.startDate);
        }

    }

    /**
     * <p>方法描述： 执行上传 </p>
     * @MethodAuthor： pw 2022/12/23
     **/
    public void executeUploadData(List<T> list){
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<T> removeList = new ArrayList<>();
        for (T t : list) {
            Integer reportId = t.getReportId();
            if (null!=reportId) {
                //删除接口
                boolean delBool = delCardReport(t);
                if (!delBool) {
                    removeList.add(t);
                }
            }
        }

        if(!CollectionUtils.isEmpty(removeList)){
            list.removeAll(removeList);
        }
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        //key:主表rid+"&"+日志表rid  value:错误信息
        Map<String, String> errorMsgMap = new HashMap<>();
        //数据校验不通过
        List<T> addList = dealDate(list,errorMsgMap);
        if(!errorMsgMap.isEmpty()){
            errorMsgMap.forEach((k,v)->{
                String errorMsg = StringUtils.isBlank(v) || v.length() <= 1000 ? v : v.substring(0, 1000);
                //更新日志记录
                if(k.contains("&")){
                    if(StringUtils.isNotBlank(k.split("&")[1])){
                        cardRcdService.updateCardRcdState(getBusType(), "2", null, errorMsg, DateUtils.formatDate(new Date()), Integer.valueOf(k.split("&")[1]));
                    }
                }else{
                    //新增日志记录
                    TdZywsCardRcd cardRcd=new TdZywsCardRcd();
                    cardRcd.setBusType(getBusType());
                    cardRcd.setBusId(Integer.valueOf(k));
                    cardRcd.setUploadDate(new Date());
                    cardRcd.setErrMsg(errorMsg);
                    cardRcd.setState(2);
                    cardRcd.setCreateDate(new Date());
                    cardRcd.setCreateManid(1);
                    cardRcdService.insertEntity(cardRcd);
                }
            });
        }
        //新增接口
        if(!CollectionUtils.isEmpty(addList)){
            addList.forEach((v)->{
                v.setSecurityKey(this.securityKey);
                try {
                    // 加密后传输
                    Map<String, String> addMap = new HashMap<>();
                    String apiData = JSON.toJSONString(v);
                    addMap.put("apiData",  DesEncryptUtil.encryptByKey(apiData,encryptKey));
                    log.info("{}新增接口，封装参数：{}",CardUploadCountryBusTypeEnum.OCCCARD.getCode().compareTo(getBusType()) == 0 ? "职业卫生报送卡" : "放射卫生报送卡", apiData);
                    String result = HttpRequestUtil.httpRequestByFormData(CardUploadCountryBusTypeEnum.OCCCARD.getCode().compareTo(getBusType()) == 0 ? this.occhethCardAddUrl : this.srvorgCardAddUrl, addMap);
                    log.info("{}新增接口，返回：{}",CardUploadCountryBusTypeEnum.OCCCARD.getCode().compareTo(getBusType()) == 0 ? "职业卫生报送卡" : "放射卫生报送卡", result);
                    CardResponsAddVo addVo = JSON.toJavaObject(JSON.parseObject(result), CardResponsAddVo.class);
                    String msg =  "接口返回信息：" + result;
                    msg = msg.length()>1000 ? msg.substring(0,1000) : msg;
                    if (addVo != null) {
                        if (StringUtils.isNotBlank(addVo.getCode()) && "1".equals(addVo.getCode())) {
                            TdZywsCardRcd cardRcd=new TdZywsCardRcd();
                            cardRcd.setBusType(getBusType());
                            cardRcd.setBusId(v.getRid());
                            cardRcd.setUploadDate(new Date());
                            cardRcd.setReportId(addVo.getData().getReportId());
                            cardRcd.setReportYear(addVo.getData().getYear());
                            cardRcd.setReportCode(addVo.getData().getReportNo());
                            cardRcd.setState(1);
                            cardRcd.setCreateDate(new Date());
                            cardRcd.setCreateManid(1);
                            if(v.getRcdRid()==null){
                                cardRcdService.insertEntity(cardRcd);
                            }else{
                                cardRcd.setRid(v.getRcdRid());
                                cardRcdService.updateFullById(cardRcd);
                            }
                        } else {
                            TdZywsCardRcd cardRcd=new TdZywsCardRcd();
                            cardRcd.setBusType(getBusType());
                            cardRcd.setBusId(v.getRid());
                            cardRcd.setUploadDate(new Date());
                            cardRcd.setErrMsg(msg);
                            cardRcd.setState(2);
                            cardRcd.setCreateDate(new Date());
                            cardRcd.setCreateManid(1);
                            if(v.getRcdRid()==null){
                                cardRcdService.insertEntity(cardRcd);
                            }else{
                                cardRcd.setRid(v.getRcdRid());
                                cardRcdService.updateFullById(cardRcd);
                            }
                        }
                    }else{
                        TdZywsCardRcd cardRcd=new TdZywsCardRcd();
                        cardRcd.setBusType(getBusType());
                        cardRcd.setBusId(v.getRid());
                        cardRcd.setUploadDate(new Date());
                        cardRcd.setErrMsg(msg);
                        cardRcd.setState(2);
                        cardRcd.setCreateDate(new Date());
                        cardRcd.setCreateManid(1);
                        if(v.getRcdRid()==null){
                            cardRcdService.insertEntity(cardRcd);
                        }else{
                            cardRcd.setRid(v.getRcdRid());
                            cardRcdService.updateFullById(cardRcd);
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), new Throwable(e));
                    TdZywsCardRcd cardRcd=new TdZywsCardRcd();
                    cardRcd.setBusType(getBusType());
                    cardRcd.setBusId(v.getRid());
                    cardRcd.setUploadDate(new Date());
                    String msg =  e.toString();
                    msg = msg.length()>1000 ? msg.substring(0,1000) : msg;
                    cardRcd.setErrMsg(msg);
                    cardRcd.setState(2);
                    cardRcd.setCreateDate(new Date());
                    cardRcd.setCreateManid(1);
                    if(v.getRcdRid()==null){
                        cardRcdService.insertEntity(cardRcd);
                    }else{
                        cardRcd.setRid(v.getRcdRid());
                        cardRcdService.updateFullById(cardRcd);
                    }
                    log.error(e.toString(),new Throwable(e));
                }
            });
        }
    }



    /**
     * <p>方法描述：查询需要处理的数据</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-22
     **/
    public abstract List<T> findDealData(Integer dataSize, String startDate);



    /**
     * <p>方法描述：校验并处理数据，并存储错误信息，
     * 通过校验的数据从list中删除，map中只存错误信息</p>
     * key:主表rid+"&"+日志表rid  value:错误信息
     * @MethodAuthor： yzz
     * @Date：2022-12-21
     **/
    public abstract List<T> dealDate(List<T> list, Map<String, String> errorMsgMap);



    /**
     * <p>方法描述：删除接口</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-21
     **/
    public boolean delCardReport(T t) {
        boolean flag = true;
        try {
            Map<String, String> delMap = new HashMap<>();
            Map<String,Object> paramMap = new HashMap<>(3);
            paramMap.put("securityKey",this.securityKey);
            paramMap.put("reportId", t.getReportId());
            paramMap.put("ocode", t.getOcode());
            String apiData = JSON.toJSONString(paramMap);
            log.info("{}删除接口，封装参数：{}",CardUploadCountryBusTypeEnum.OCCCARD.getCode().compareTo(getBusType()) == 0 ? "职业卫生报送卡" : "放射卫生报送卡", apiData);
            // 加密传输
            delMap.put("apiData",  DesEncryptUtil.encryptByKey(apiData,encryptKey));
            String result = HttpRequestUtil.httpRequestByFormData(CardUploadCountryBusTypeEnum.OCCCARD.getCode().compareTo(getBusType()) == 0 ? this.occhethCardDelUrl : this.srvorgCardDelUrl, delMap);
            log.info("{}删除接口，返回：{}",CardUploadCountryBusTypeEnum.OCCCARD.getCode().compareTo(getBusType()) == 0 ? "职业卫生报送卡" : "放射卫生报送卡", result);
            CardResponsBaseVo cardVo = JSON.toJavaObject(JSON.parseObject(result), CardResponsBaseVo.class);
            String msg =  "接口返回信息：" + result;
            msg = msg.length()>1000 ? msg.substring(0,1000) : msg;
            if (cardVo != null) {
                if (StringUtils.isNotBlank(cardVo.getCode()) && "1".equals(cardVo.getCode())) {
                    cardRcdService.updateCardRcdReportId(getBusType(),t.getRcdRid());
                } else {
                    cardRcdService.updateCardRcdState(getBusType(), "9", null, msg, DateUtils.formatDate(new Date()), t.getRcdRid());
                    flag = false;
                }
            }else{
                cardRcdService.updateCardRcdState(getBusType(), "9", null, msg, DateUtils.formatDate(new Date()), t.getRcdRid());
                flag = false;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), new Throwable(e));
            String msg = e.toString();
            msg = msg.length()>1000 ? msg.substring(0,1000) : msg;
            cardRcdService.updateCardRcdState(getBusType(), "9", null, msg, DateUtils.formatDate(new Date()), t.getRcdRid());
            log.error(e.toString(),new Throwable(e));
            flag = false;
        }
        return flag;
    }


    /**
     * <p>方法描述：业务类型</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-20
     **/
    public abstract Integer getBusType();

    /**
     * <p>方法描述： 截取指定长度字符串 </p>
     * @MethodAuthor： pw 2022/12/22
     **/
    public String executeCheckStr(String str, int length){
        if(StringUtils.isBlank(str) || str.length() <= length){
            return str;
        }
        return str.substring(0, length);
    }

    /**
     * <p>方法描述： 获取对照的国家编码 </p>
     * @MethodAuthor： pw 2022/12/23
     **/
    public String contraSubExchange(String contraCode,String busType, String leftCode){
        Map<String, TsContraSub> contraSubMap = this.tsContraSubService.findTsContraSub(contraCode, busType);
        if(CollectionUtils.isEmpty(contraSubMap)){
            return null;
        }
        if(StringUtils.isNotBlank(leftCode)){
            TsContraSub contraSub = contraSubMap.get(leftCode);
            if(null != contraSub){
                return contraSub.getRightCode();
            }
        }
        return null;
    }

    /**
     * <p>方法描述： 校验数字 </p>
     * @MethodAuthor： pw 2022/12/23
     **/
    public boolean validateInteger(Integer num, Integer min, Integer max){
        if(null == num){
            return true;
        }
        if(null != min && min.compareTo(num) > 0){
            return false;
        }
        if(null != max && num.compareTo(max) > 0){
            return false;
        }
        return true;
    }


    /**
     * <p>方法描述： 固定电话号码与区号间增加横线分隔</p>
     * @MethodAuthor： pw 2022/12/30
     **/
    public String fixTelphone(String phoneNo){
        if(StringUtils.vertyMobilePhone(phoneNo)){
            return phoneNo;
        }
        //固定电话 号码八位
        int length = phoneNo.length();
        if(length > 8 && !phoneNo.contains("-")){
            return phoneNo.substring(0, length-8)+"-"+phoneNo.substring(length-8);
        }else{
            return phoneNo;
        }
    }


}

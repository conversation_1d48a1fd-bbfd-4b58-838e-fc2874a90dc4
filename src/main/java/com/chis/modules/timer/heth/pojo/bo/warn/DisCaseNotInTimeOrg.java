package com.chis.modules.timer.heth.pojo.bo.warn;

import com.chis.modules.timer.heth.entity.TdZwWarnInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class DisCaseNotInTimeOrg implements Serializable {
    private static final long serialVersionUID = 6194888044966750991L;

    public DisCaseNotInTimeOrg(Integer orgId, String orgName, Integer zoneId, String zoneName) {
        this.orgId = orgId;
        this.orgName = orgName;
        this.zoneId = zoneId;
        this.zoneName = zoneName;
        this.busDataMap = new HashMap<>();
        this.zwWarnInfoMap = new HashMap<>();
    }

    /**
     * 业务机构ID
     */
    private Integer orgId;
    /**
     * 业务名称
     */
    private String orgName;
    /**
     * 业务机构地区ID
     */
    private Integer zoneId;
    /**
     * 地区名称 原始fullName不去除省以及下划线
     */
    private String zoneName;
    /**
     * 预警级别对应业务数据Map
     * <pre>key: 预警级别</pre>
     * <pre>value: 业务数据</pre>
     */
    private Map<Integer, List<DisCaseNotInTime>> busDataMap;
    /**
     * 未处置预警
     */
    private Map<String, TdZwWarnInfo> zwWarnInfoMap;

}

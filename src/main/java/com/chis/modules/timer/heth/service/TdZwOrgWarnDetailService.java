package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwOrgWarnDetail;
import com.chis.modules.timer.heth.mapper.TdZwOrgWarnDetailMapper;
import org.springframework.stereotype.Service;

/**
 * <p> 服务实现类：  </p>
 *
 * <AUTHOR> 2024-07-12,TdZwOrgWarnDetailService
 */
@Service
public class TdZwOrgWarnDetailService extends ZwxBaseServiceImpl<TdZwOrgWarnDetailMapper, TdZwOrgWarnDetail> {

    public void deleteByOrgWarnMainId(Integer orgWarnId) {
        baseMapper.deleteByOrgWarnMainId(orgWarnId);
    }
}

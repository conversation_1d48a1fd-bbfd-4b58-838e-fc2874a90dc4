package com.chis.modules.timer.heth.pojo.bo.warn;

import lombok.Data;

/**
 * 预警模型配置BO(例数)
 */
@Data
public class DisCaseWarnModelBO {
    /**
     * RID
     */
    private Integer rid;
    /**
     * 预警级别
     */
    private Integer warnLevel;
    /**
     * 下限
     */
    private Integer lowerLimit;
    /**
     * 监测周期天数
     */
    private Integer cycleDays;

    public DisCaseWarnModelBO() {
    }

    public DisCaseWarnModelBO(Integer rid, Integer warnLevel, Integer lowerLimit, Integer cycleDays) {
        this.rid = rid;
        this.warnLevel = warnLevel;
        this.lowerLimit = lowerLimit;
        this.cycleDays = cycleDays;
    }
}

package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwOrgWarnInst;
import com.chis.modules.timer.heth.mapper.TdZwOrgWarnInstMapper;
import org.springframework.stereotype.Service;

/**
 * <p> 服务实现类：  </p>
 *
 * <AUTHOR> 2024-07-12,TdZwOrgWarnInstService
 */
@Service
public class TdZwOrgWarnInstService extends ZwxBaseServiceImpl<TdZwOrgWarnInstMapper, TdZwOrgWarnInst> {

    public void deleteByOrgWarnMainId(Integer orgWarnId) {
        baseMapper.deleteByOrgWarnMainId(orgWarnId);
    }
}

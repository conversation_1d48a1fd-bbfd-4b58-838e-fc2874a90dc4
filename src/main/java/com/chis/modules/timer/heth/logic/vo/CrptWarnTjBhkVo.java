package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>类描述： 用人单位预警-体检记录对应数据封装 </p>
 * pw 2023/9/27
 **/
@Data
public class CrptWarnTjBhkVo implements Serializable {
    private static final long serialVersionUID = -419256733121119241L;
    /** 用工单位Id */
    private Integer crptId;
    /** 该用工单位最大体检日期 */
    private Date bhkDate;
    /** 该用工单位最大创建日期 */
    private Date createDate;
}

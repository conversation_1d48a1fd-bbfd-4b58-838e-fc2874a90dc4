package com.chis.modules.timer.heth.logic.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 获取患者康复处方-返回结果-data
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GetPatientKffaDataVo {
    /**
     * 患者姓名
     */
    private String username;
    /**
     * 患者唯一标识
     */
    private String patientid;
    /**
     * 每个处方的唯一标识
     */
    private String prescriptionid;
    /**
     * 处方开出时间戳
     */
    private Integer prescriptionTime;
    /**
     * 展示处方PDF的URL
     */
    private String prescription_url;
    /**
     * 执行情况
     */
    private List<GetPatientKffaCarryoutVo> carryout;
}

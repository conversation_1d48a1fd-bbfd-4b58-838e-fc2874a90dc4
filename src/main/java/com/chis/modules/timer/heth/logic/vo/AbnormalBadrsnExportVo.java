package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 异步导出个案审核部分的危害因素异常VO
 *
 * @ClassAuthor pw,2021年12月22日,AbnormalBadrsnExportVo
 */
@Data
public class AbnormalBadrsnExportVo implements Serializable {
    private static final long serialVersionUID = -1767869569177359536L;
    private Integer bhkRid;
    /** 不规范标记 */
    private Integer nostdFlag;
    /** 危害因素rid */
    private Integer badRsnRid;
    /** 危害因素名称 */
    private String badRsnName;
    /** 项目名称 */
    private String itemName;
    /** 配置表里的项目说明 */
    private String itmDesc;
    /** 项目判定方式 */
    private Integer deterWay;
}

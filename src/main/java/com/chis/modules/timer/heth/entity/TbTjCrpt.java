package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-09-11,TbTjCrpt
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TB_TJ_CRPT")
public class TbTjCrpt extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("CRPT_NAME")
    private String crptName;

    @TableField("ADDRESS")
    private String address;

    @TableField("PHONE")
    private String phone;

    @TableField("CORPORATE_DEPUTY")
    private String corporateDeputy;

    @TableField("WORK_FORCE")
    private String workForce;

    @TableField("WORKMAN_NUM")
    private String workmanNum;

    @TableField("WORKMISTRESS_NUM")
    private String workmistressNum;

    @TableField("HOLD_CARD_MAN")
    private String holdCardMan;

    @TableField("POSTALCODE")
    private String postalcode;

    @TableField("WORK_AREA")
    private String workArea;

    @TableField("REGISTER_FUND")
    private String registerFund;

    @TableField(value = "INDUS_TYPE_ID" , el = "fkByIndusTypeId.rid")
    private TsSimpleCode fkByIndusTypeId;

    @TableField(value = "ZONE_ID" , el = "fkByZoneId.rid")
    private TsZone fkByZoneId;

    @TableField(value = "TOWN_ID" , el = "fkByTownId.rid")
    private TsZone fkByTownId;

    @TableField(value = "ECONOMY_ID" , el = "fkByEconomyId.rid")
    private TsSimpleCode fkByEconomyId;

    @TableField("INSTITUTION_CODE")
    private String institutionCode;

    @TableField(value = "CRPT_SIZE_ID" , el = "fkByCrptSizeId.rid")
    private TsSimpleCode fkByCrptSizeId;

    @TableField("SAFETY_PRINCIPAL")
    private String safetyPrincipal;

    @TableField("FILING_DATE")
    private Date filingDate;

    @TableField("BUILD_DATE")
    private Date buildDate;

    @TableField("LINKMAN1")
    private String linkman1;

    @TableField("POSITION1")
    private String position1;

    @TableField("LINKPHONE1")
    private String linkphone1;

    @TableField("LINKMAN2")
    private String linkman2;

    @TableField("POSITION2")
    private String position2;

    @TableField("LINKPHONE2")
    private String linkphone2;

    @TableField("SAFEPOSITION")
    private String safeposition;

    @TableField("SAFEPHONE")
    private String safephone;

    @TableField("SUBJE_CONN")
    private String subjeConn;

    @TableField("ENROL_ADDRESS")
    private String enrolAddress;

    @TableField("ENROL_POSTALCODE")
    private String enrolPostalcode;

    @TableField("OCC_MANA_OFFICE")
    private String occManaOffice;

    @TableField("REG_CODE")
    private String regCode;

    @TableField("REG_MARK")
    private String regMark;

    @TableField("UUID")
    private String uuid;

    @TableField("TAR_UUID")
    private String tarUuid;

    @TableField("POSTCODE")
    private String postcode;

    @TableField("UPDATETAG")
    private String updatetag;

    @TableField("ERROR_MSG")
    private String errorMsg;

    @TableField("PYM")
    private String pym;

    @TableField("INTER_PRC_TAG")
    private String interPrcTag;

    @TableField("FEMALE_WORK_NUM")
    private String femaleWorkNum;

    @TableField("FEMALE_STAFF_NUM")
    private String femaleStaffNum;

    @TableField("CREATE_ORG_ID")
    private String createOrgId;

    @TableField("IF_SUB_ORG")
    private Integer ifSubOrg;

    @TableField("OUTSOURCE_NUM")
    private String outsourceNum;

    @TableField(value = "UPPER_UNIT_ID" , el = "fkByUpperUnitId.rid")
    private TbTjCrpt fkByUpperUnitId;

    public TbTjCrpt(Integer rid) {
        super(rid);
    }


}

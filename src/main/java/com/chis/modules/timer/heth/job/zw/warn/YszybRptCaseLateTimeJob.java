package com.chis.modules.timer.heth.job.zw.warn;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.pojo.bo.warn.DisCaseNotInTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *  <p>类描述：疑似职业病报告不及时</p>
 * @ClassAuthor hsj 2023-11-12 8:13
 */
@Slf4j
@Component
public class YszybRptCaseLateTimeJob  extends NotInTimeWarnBase{
    @Value("${warn-timer.zwWarn.yszyb-rpt-case-late-days}")
    private Integer lateDays;
    @Scheduled(cron = "${warn-timer.zwWarn.cron.yszyb-rpt-case-late}")
    public void start() {
        super.executeData();
    }
    @Override
    public void initJobParam() {
        this.warnDays = this.lateDays;
        this.warnType = 4;
        this.warnName = "疑似职业病报告不及时";
        this.busType = 2;
    }
    @Override
    public void queryDataList() {
        this.dataList = new ArrayList<>();
        warnStart(1);
        warnStart(2);
        warnStart(3);
    }
    /**
     *  <p>方法描述：开始预警信息查询-处理</p>
     * @MethodAuthor hsj 2023-11-12 8:27
     */
    private void warnStart(Integer type) {
        if(!warnModelMap.containsKey(type)){
            return;
        }
        //体检信息查询
        List<DisCaseNotInTime> bhks = this.disCaseJobService.findNotTimeBhkDatas(limitDate,beginDate,this.zoneCode,type);
        if(CollectionUtils.isEmpty(bhks)){
            return;
        }
        List<Integer> ids = bhks.stream().map(DisCaseNotInTime::getId) .collect(Collectors.toList());
        List<List<Integer>> groupList = StringUtils.splitListProxy(ids,1000);
        //TD_TJ_SUPOCCDISELIST(病种去重)
        List<DisCaseNotInTime> supoccdiseList = new ArrayList<>();
        //TD_ZW_YSZYB_RPT
        List<DisCaseNotInTime> yszybRptList = new ArrayList<>();
        groupList.forEach(v -> {
            List<DisCaseNotInTime> supoccdises = this.disCaseJobService.findSupoccdiseList(v);
            if(CollectionUtils.isNotEmpty(supoccdises)){
                supoccdiseList.addAll(supoccdises);
            }
            List<DisCaseNotInTime> yszybRpts = this.disCaseJobService.findYszybRptList(v);
            if(CollectionUtils.isNotEmpty(yszybRpts)){
                yszybRptList.addAll(yszybRpts);
            }
        });
        //疑似职业病子表根据体检rid分组
        Map<Integer,List<DisCaseNotInTime>> supoccdiseMap =supoccdiseList.stream() .collect(Collectors.groupingBy(DisCaseNotInTime::getId));
        //疑似职业病报告卡 (体检子表+病种rid)
        Map<String,List<DisCaseNotInTime>> yszybRptMap =yszybRptList.stream() .collect(Collectors.groupingBy(DisCaseNotInTime::getIdAndDisId));
        //根据机构分组
        Map<Integer,List<DisCaseNotInTime>> srvorgMap =bhks.stream() .collect(Collectors.groupingBy(DisCaseNotInTime::getOrgId));
        srvorgMap.forEach((orgId, datas) -> {
            datas.forEach(bhk->{
                List<DisCaseNotInTime> supoccdises = supoccdiseMap.get(bhk.getId());
                if(CollectionUtils.isNotEmpty(supoccdises)){
                    supoccdises.forEach(occ->{
                        String key = occ.getId()+"_"+occ.getDisId();
                        DisCaseNotInTime dis = new DisCaseNotInTime();
                        dis.setId(bhk.getId());
                        dis.setOrgId(bhk.getOrgId());
                        dis.setOrgName(bhk.getOrgName());
                        dis.setZoneId(bhk.getZoneId());
                        dis.setZoneName(bhk.getZoneName());
                        dis.setRcvDate(bhk.getRcvDate());
                        dis.setDisId(occ.getDisId());
                        dis.setWarnLevel(type);
                        if(yszybRptMap.containsKey(key)){
                            dis.setDealDate(yszybRptMap.get(key).get(0).getDealDate());
                        }
                        this.dataList.add(dis);
                    });
                }
            });
        });
    }
}

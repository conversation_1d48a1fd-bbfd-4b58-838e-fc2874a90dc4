package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnCsionNoSub
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_BSN_CSION_NO_SUB")
public class TdZwyjBsnCsionNoSub extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwyjBsnCsionNo fkByMainId;

    @TableField(value = "BHKRST_ID" , el = "fkByBhkrstId.rid")
    private TsSimpleCode fkByBhkrstId;


    public TdZwyjBsnCsionNoSub(Integer rid) {
        super(rid);
    }


}

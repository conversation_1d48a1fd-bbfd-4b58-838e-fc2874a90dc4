package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdZyUnitmainprod
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZY_UNITMAINPROD")
@KeySequence(value = "TD_ZY_UNITMAINPROD_SEQ",clazz = Integer.class)
public class TdZyUnitmainprod extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZyUnitbasicinfo fkByMainId;

    @TableField("PROD_NAME")
    private String prodName;

    @TableField("ANNUAL_OUTPUT")
    private Double annualOutput;

    @TableField("UNIT_NAME")
    private String unitName;


    public TdZyUnitmainprod(Integer rid) {
        super(rid);
    }


}

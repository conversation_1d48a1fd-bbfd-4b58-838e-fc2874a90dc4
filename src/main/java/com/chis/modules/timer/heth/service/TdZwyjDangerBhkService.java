package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.mapper.TdZwyjDangerBhkMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-11-21,TdZwyjDangerBhkService
 */
@Service
public class TdZwyjDangerBhkService extends ZwxBaseServiceImpl<TdZwyjDangerBhkMapper, TdZwyjDangerBhk> {

    @Autowired
    private TdZwyjDangerResultService dangerResultService;
    @Autowired
    private TdZwyjDangerBsnService dangerBsnService;
    @Autowired
    private TdZwyjDangerLogService dangerLogService;

    public List<TdZwyjDangerBhk> selectListByOrgIdAndbhkCode(Integer orgId, List<String> bhkCodeList){
        if(null == orgId || null == bhkCodeList || bhkCodeList.size() == 0){
            return null;
        }
        return this.baseMapper.selectListByOrgIdAndbhkCode(orgId, bhkCodeList);
    }

    @Transactional(readOnly = false)
    public void excuteTdTjBhkData(Map<String,TdZwyjDangerBhk> dangerBhkMap, TdTjBhk tjBhk,
                                  Map<Integer, List<TdTjBhksub>> subBhkMap,
                                  Map<Integer, List<TdZwyjDangerItems>> itemsMap,
                                  List<TdTjBadrsns> badrsnsList){
        TdZwyjDangerLog dangerLog = null;
        try{
            if(null == itemsMap || itemsMap.isEmpty()){
                dangerLog = createDangerLog(tjBhk, "未找到危急值项目维护",false);
            }else if(null == subBhkMap || subBhkMap.isEmpty()){
                //或者未找到对应体检子表，
                dangerLog = createDangerLog(tjBhk, "未匹配到危急值项目维护",true);
            }
            if(null != dangerLog){
                if(null != dangerBhkMap && null != dangerBhkMap.get(tjBhk.getBhkCode())){
                    //删除危急值项目结果与危急值接触危害因素 和危急值体检
                    delDangerBhkInfo(dangerBhkMap.get(tjBhk.getBhkCode()), true);
                }
                dangerLogService.save(dangerLog);
                return;
            }
            boolean fkBol = false;
            if(null != dangerBhkMap && null != dangerBhkMap.get(tjBhk.getBhkCode())){
                TdZwyjDangerBhk dangerBhk = dangerBhkMap.get(tjBhk.getBhkCode());
                //危急值体检存在
                if(null != subBhkMap && null != subBhkMap.get(tjBhk.getRid().intValue())){
                    //更新危急值体检 新增危急值项目结果与危急值接触危害因素
                    fkBol = saveOrUpdateDangerBhkInfo(dangerBhk, tjBhk, subBhkMap.get(tjBhk.getRid().intValue()),itemsMap, badrsnsList);
                }else{
                    //不存在危急项目 删除危急值项目结果与危急值接触危害因素 和危急值体检
                    delDangerBhkInfo(dangerBhk, true);
                }
            }else{
                //危急值体检不存在
                if(null != subBhkMap && null != subBhkMap.get(tjBhk.getRid().intValue())){
                    //存在危急项目 新增危急值体检 危急值项目结果与危急值接触危害因素
                    fkBol = saveOrUpdateDangerBhkInfo(null, tjBhk, subBhkMap.get(tjBhk.getRid().intValue()),itemsMap, badrsnsList);
                }
            }
            dangerLog = createDangerLog(tjBhk, fkBol ? "与危急值项目维护值未匹配上" : "",true);
            dangerLogService.save(dangerLog);
        }catch (Exception e){
            e.printStackTrace();
            dangerLog = createDangerLog(tjBhk, e.getMessage(),false);
            dangerLogService.save(dangerLog);
        }
    }

    /** 更新危急值体检 新增危急值项目结果与危急值接触危害因素 */
    public boolean saveOrUpdateDangerBhkInfo(TdZwyjDangerBhk dangerBhk, TdTjBhk tjBhk, List<TdTjBhksub> subList,
                                     Map<Integer, List<TdZwyjDangerItems>> itemsMap,
                                    List<TdTjBadrsns> badrsnsList) throws Exception{
        boolean flag = false;
        if(null != dangerBhk){
            delDangerBhkInfo(dangerBhk, false);
        }
        dangerBhk = fillData(dangerBhk, tjBhk);
        if(null != dangerBhk.getRid()){
            this.updateById(dangerBhk);
        }else{
            this.save(dangerBhk);
        }

        //处理危急值项目结果
        List<TdZwyjDangerResult> dangerResultList = new ArrayList<>();
        //如果同一个项目 有两个标准 那么会出现 同样的体检子表 出现两次
        List<BigInteger> exitsList = new ArrayList<>();
        if(null != itemsMap && null != subList && subList.size() > 0){
            for(TdTjBhksub bhksub : subList){
                if(null != exitsList && exitsList.contains(bhksub.getRid())){
                    continue;
                }
                exitsList.add(bhksub.getRid());
                List<TdZwyjDangerItems> itemList = itemsMap.get(bhksub.getFkByItemId().getRid().intValue());
                if(null != itemList && itemList.size() > 0){
                    TdZwyjDangerItems dangerItm = null;
                    for(TdZwyjDangerItems dangerItems : itemList){
                        if(null == bhksub.getMsruntId()){
                            if(StringUtils.isNotBlank(bhksub.getMsrunt())){//单位空 不处理
                                break;
                            }
                            if(dangerItems.getFkByMsruntId().getCodeName().equals(bhksub.getMsrunt())){
                                dangerItm = dangerItems;
                                break;
                            }
                        }else{
                            if(dangerItems.getFkByMsruntId().getRid() == Integer.parseInt(bhksub.getMsruntId())){
                                dangerItm = dangerItems;
                                break;
                            }
                        }
                    }
                    if(null != dangerItm){
                        BigDecimal decimal = transferStrToBigDecimal(bhksub.getItemRst());
                        if(null == decimal){
                            continue;
                        }
                        if(StringUtils.isNotBlank(dangerItm.getGeVal())){
                            BigDecimal ge = new BigDecimal(dangerItm.getGeVal());
                            if(decimal.compareTo(ge) >= 0){
                                dangerResultList.add(fillDangerResult(dangerBhk,dangerItm,bhksub));
                                continue;
                            }
                        }
                        if(StringUtils.isNotBlank(dangerItm.getGtVal())){
                            BigDecimal gt = new BigDecimal(dangerItm.getGtVal());
                            if(decimal.compareTo(gt) > 0){
                                dangerResultList.add(fillDangerResult(dangerBhk,dangerItm,bhksub));
                                continue;
                            }
                        }
                        if(StringUtils.isNotBlank(dangerItm.getLeVal())){
                            BigDecimal le = new BigDecimal(dangerItm.getLeVal());
                            if(decimal.compareTo(le) <= 0){
                                dangerResultList.add(fillDangerResult(dangerBhk,dangerItm,bhksub));
                                continue;
                            }
                        }
                        if(StringUtils.isNotBlank(dangerItm.getLtVal())){
                            BigDecimal lt = new BigDecimal(dangerItm.getLtVal());
                            if(decimal.compareTo(lt) < 0){
                                dangerResultList.add(fillDangerResult(dangerBhk,dangerItm,bhksub));
                                continue;
                            }
                        }
                    }
                }
            }
        }

        if(null != dangerResultList && dangerResultList.size() > 0){
            dangerResultService.saveBatch(dangerResultList);
            //存在危急值项目结果 处理危急值接触危害因素
            List<TdZwyjDangerBsn> dangerBsnList = new ArrayList<>();
            if(null != badrsnsList && badrsnsList.size() > 0){
                for(TdTjBadrsns badrsns : badrsnsList){
                    TdZwyjDangerBsn bsn = new TdZwyjDangerBsn();
                    bsn.setFkByBhkId(dangerBhk);
                    bsn.setBadrsnId(badrsns.getFkByBadrsnId().getRid().toString());
                    bsn.setFkByExamConclusionId(badrsns.getFkByExamConclusionId());
                    bsn.setQtjbName(badrsns.getQtjbName());
                    bsn.setCreateDate(new Date());
                    bsn.setCreateManid(1);
                    dangerBsnList.add(bsn);
                }
            }
            if(null != dangerBsnList && dangerBsnList.size() > 0){
                dangerBsnService.saveBatch(dangerBsnList);
            }
        }else{
            flag = true;
            if(null != dangerBhk.getRid()){
                //没有危急值项目结果 删除主表
                this.removeById(dangerBhk.getRid());
            }
        }
        return flag;
    }

    private BigDecimal transferStrToBigDecimal(String str){
        BigDecimal decimal = null;
        try{
            decimal = new BigDecimal(str);
        }catch(Exception e){}
        return decimal;
    }

    private TdZwyjDangerResult fillDangerResult(TdZwyjDangerBhk mainId, TdZwyjDangerItems dangerItm,
                                                TdTjBhksub bhksub) throws Exception{
        TdZwyjDangerResult result = new TdZwyjDangerResult();
        result.setFkByMainId(mainId);
        result.setFkByDangerId(dangerItm);
        result.setItemRst(bhksub.getItemRst());
        result.setMsrunt(bhksub.getMsrunt());
        result.setItemStdvalue(bhksub.getItemStdvalue());
        result.setCreateDate(new Date());
        result.setCreateManid(1);
        return result;
    }

    public void delDangerBhkInfo(TdZwyjDangerBhk dangerBhk, boolean delMain){
        dangerResultService.deleteByMainId(dangerBhk.getRid());
        dangerBsnService.deleteByMainId(dangerBhk.getRid());
        if(delMain){
            this.removeById(dangerBhk.getRid());
        }
    }

    /** 填充数据 */
    private TdZwyjDangerBhk fillData(TdZwyjDangerBhk dangerBhk, TdTjBhk tjBhk) throws Exception{
        if(null == dangerBhk){
            dangerBhk = new TdZwyjDangerBhk();
            dangerBhk.setBhkCode(tjBhk.getBhkCode());
            dangerBhk.setFkByBhkorgId(tjBhk.getFkByBhkorgId());
            dangerBhk.setCreateDate(new Date());
            dangerBhk.setCreateManid(1);
        }else{
            dangerBhk.setModifyDate(new Date());
            dangerBhk.setModifyManid(1);
        }
        dangerBhk.setFkByCrptId(tjBhk.getFkByCrptId());
        dangerBhk.setCrptName(tjBhk.getCrptName());
        dangerBhk.setPersonName(tjBhk.getPersonName());
        dangerBhk.setSex(tjBhk.getSex());
        dangerBhk.setFkByCardTypeId(tjBhk.getFkByCardTypeId());
        dangerBhk.setIdc(tjBhk.getIdc());
        dangerBhk.setPsnType(tjBhk.getPsnType());
        dangerBhk.setBrth(tjBhk.getBrth());
        dangerBhk.setAge(tjBhk.getAge());
        dangerBhk.setIsxmrd(tjBhk.getIsxmrd());
        dangerBhk.setLnktel(tjBhk.getLnktel());
        dangerBhk.setDpt(tjBhk.getDpt());
        dangerBhk.setWrknum(tjBhk.getWrknum());
        dangerBhk.setWrklnt(tjBhk.getWrklnt());
        dangerBhk.setWrklntmonth(tjBhk.getWrklntmonth());
        dangerBhk.setTchbadrsntim(tjBhk.getTchbadrsntim());
        dangerBhk.setTchbadrsnmonth(tjBhk.getTchbadrsnmonth());
        dangerBhk.setWorkName(tjBhk.getWorkName());
        dangerBhk.setBhkType(tjBhk.getBhkType());
        dangerBhk.setFkByOnguardStateid(tjBhk.getFkByOnguardStateid());
        dangerBhk.setBhkDate(tjBhk.getBhkDate());
        dangerBhk.setBhkrst(tjBhk.getBhkrst());
        dangerBhk.setMhkadv(tjBhk.getMhkadv());
        dangerBhk.setOcpBhkrstdes(tjBhk.getOcpBhkrstdes());
        dangerBhk.setJdgdat(tjBhk.getJdgdat());
        dangerBhk.setBadrsn(tjBhk.getBadrsn());
        dangerBhk.setRptPrintDate(tjBhk.getRptPrintDate());
        dangerBhk.setIfRhk(tjBhk.getIfRhk());
        dangerBhk.setLastBhkCode(tjBhk.getLastBhkCode());
        dangerBhk.setLastFstBhkCode(tjBhk.getLastFstBhkCode());
        dangerBhk.setUuid(tjBhk.getUuid());
        dangerBhk.setFkByEntrustCrptId(tjBhk.getEntrustId());
        return dangerBhk;
    }

    /** 创建危急值处理日志 */
    private TdZwyjDangerLog createDangerLog(TdTjBhk bhk, String errMsg, boolean flag){
        TdZwyjDangerLog dangerLog = new TdZwyjDangerLog();
        dangerLog.setFkByBhkId(bhk);
        dangerLog.setDataMark(flag ? "1" : (StringUtils.isBlank(errMsg) ? "1" : "2"));
        if(null != errMsg && errMsg.length() > 1000){
            errMsg = errMsg.substring(0, 990);
        }
        dangerLog.setErrMsg(errMsg);
        dangerLog.setCreateDate(new Date());
        dangerLog.setCreateManid(1);
        return dangerLog;
    }
}

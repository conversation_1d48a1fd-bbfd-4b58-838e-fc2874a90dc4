package com.chis.modules.timer.heth.service;

import com.alibaba.fastjson.JSON;
import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.entity.TdTjBhk;
import com.chis.modules.timer.heth.entity.TdTjCheckTask;
import com.chis.modules.timer.heth.entity.TdZwBgkFlow;
import com.chis.modules.timer.heth.logic.vo.CheckDataVo;
import com.chis.modules.timer.heth.mapper.TbTjJcTaskPsnMapper;
import com.chis.modules.timer.heth.mapper.TdTjBhkMapper;
import com.chis.modules.timer.heth.mapper.TdZwBgkFlowMapper;
import com.chis.modules.timer.heth.pojo.BhkCheckPojo;
import com.chis.modules.timer.heth.pojo.json.BhkBatchUpdateDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @description:
 */
@Service
public class BhkCheckBackService {

    @Autowired
    private TdZwBgkFlowMapper tdZwBgkFlowMapper;
    @Autowired
    private TdTjBhkMapper tdTjBhkMapper;
    @Autowired
    private TbTjJcTaskPsnMapper tbTjJcTaskPsnMapper;

    /**
     * <p>Description：批量保存退回数据 </p>
     * <p>Author： yzz 2024-10-14 </p>
     */
    @Transactional
    public int saveBatchBackData(List<CheckDataVo> list, TdTjCheckTask task) {
         // key: 上次流程操作标识; value: 体检记录RID;
         Map<Integer, List<Integer>> lastMarkBhkRidMap = new HashMap<>();
         // key: 当前流程操作标识; value: 体检记录RID;
         Map<Integer, List<Integer>> currentMarkBhkRidMap = new HashMap<>();
         
        List<BhkCheckPojo> markBhkRidMapList = new ArrayList<>();
        List<List<Integer>> bhkRidMapList = new ArrayList<>();
        Map<String, Object> condition = JSON.parseObject(task.getExportCondition());
         // 保存前数据封装
         pakMarkBhkBackCheck(list, task, condition, lastMarkBhkRidMap, currentMarkBhkRidMap, markBhkRidMapList, bhkRidMapList);
          // 批量保存
        int result = saveBatchOperation(lastMarkBhkRidMap,markBhkRidMapList, list, bhkRidMapList, currentMarkBhkRidMap, task, list.size(), condition);
        //退回时需要清空主动监测花名册中的体检编号
        List<Integer> allBhkRidList = new ArrayList<>();
        for (List<Integer> curList : bhkRidMapList) {
            if (CollectionUtils.isEmpty(curList)) {
                continue;
            }
            allBhkRidList.addAll(curList);
        }
        this.clearJcTaskPsnBhkCode(allBhkRidList);
        return result;
    }


    /**
     * <p>Description：退回时清空主动监测花名册中的体检编号 </p>
     * <p>Author： yzz 2024-10-15 </p>
     */
    public void clearJcTaskPsnBhkCode(List<Integer> allBhkRidList) {
        if (CollectionUtils.isEmpty(allBhkRidList)) {
            return;
        }
        List<List<Integer>> groupList = StringUtils.splitListProxy(allBhkRidList, 1000);
        for (List<Integer> bhkRidList : groupList) {
            tbTjJcTaskPsnMapper.updateBatchByBhkRid(bhkRidList);
        }
    }

    /**
     * <p>Description：批量保存逻辑 </p>
     * <p>Author： yzz 2024-10-15 </p>
     */
    public int saveBatchOperation(Map<Integer, List<Integer>> lastMarkBhkRidMap,
                                  List<BhkCheckPojo> markBhkRidMapList,
                                  List<CheckDataVo> list,
                                  List<List<Integer>> bhkRidMapList,
                                  Map<Integer, List<Integer>> currentMarkBhkRidMap,
                                  TdTjCheckTask task, int total, Map<String, Object> condition) {
        int resultNumber = 0;
        // 新增操作标识为21,33,44的操作记录
        insertInitialFlowBatch(lastMarkBhkRidMap.get(21), 21, task);
        insertInitialFlowBatch(lastMarkBhkRidMap.get(33), 33, task);
        insertInitialFlowBatch(lastMarkBhkRidMap.get(44), 44, task);

        // 1、根据lastMarkBhkRidMap获取List<Object[]> processReceiptDateList {体检记录RID, 流程接收日期, 流程RID}
        List<TdZwBgkFlow> processReceiptDateList = new ArrayList<>(total);
        for (Integer lastMark : lastMarkBhkRidMap.keySet()) {
            if (!CollectionUtils.isEmpty(lastMarkBhkRidMap.get(lastMark))) {
                processReceiptDateList.addAll(getProcessReceiptDateList(lastMark, lastMarkBhkRidMap.get(lastMark)));
            }
        }
        // 2、遍历processReceiptDateList计算是否及时，存于Map<> bhkRidTimelyMap key:是否及时; value: 流程RID List;
        Map<Integer, List<Integer>> bhkRidTimelyMap = new HashMap<>();
        bhkRidTimelyMap.put(0, new ArrayList<>());
        bhkRidTimelyMap.put(1, new ArrayList<>());
        for (TdZwBgkFlow bgkFlow : processReceiptDateList) {
            int calLimitTime = bgkFlow.getRcvDate().before(DateUtils.parseDate(condition.get("limitDate"))) ? 0 : 1;
            bhkRidTimelyMap.get(calLimitTime).add(bgkFlow.getRid());
        }

        Map<Integer, String> ridToAdvMap = new HashMap<>();
        List<String> ridList = new ArrayList<>();
        list.forEach(item -> {
            ridToAdvMap.put(item.getBhk().getRid(), item.getRejectReason());
            ridList.add(String.valueOf(item.getBhk().getRid()));
        });
        // 根据传入的字段名更新BHK表对应RID主键的字段值
        if (!CollectionUtils.isEmpty(bhkRidMapList) && !CollectionUtils.isEmpty(list)) {
            for (int i = 0; i < bhkRidMapList.size(); i++) {
                if(CollectionUtils.isEmpty(bhkRidMapList.get(i))){
                    continue;
                }
                BhkCheckPojo bhkCheckPojo = markBhkRidMapList.get(i);
                bhkCheckPojo.setIndex(i);

                BhkBatchUpdateDTO dto = new BhkBatchUpdateDTO();
                dto.setPojo(bhkCheckPojo);
                dto.setPsnId(task.getFkByCheckRsnId().getRid());
                dto.setRidToAdvMap(ridToAdvMap);
                dto.setRidList(bhkRidMapList.get(i));
                //当前需要更新的字段
                resultNumber += this.updateTdTjBhkListByIds(dto);
            }
        }
        // 更新上次流程记录 OPER_DATE OPER_PSN_ID IF_IN_TIME
        for (Integer timely : bhkRidTimelyMap.keySet()) {
            if (!CollectionUtils.isEmpty(bhkRidTimelyMap.get(timely))) {
                updateLastFlowBatch(bhkRidTimelyMap.get(timely), timely, task);
            }
        }

        // 新增当前流程记录
        for (Integer operationFlag : currentMarkBhkRidMap.keySet()) {
            if (!CollectionUtils.isEmpty(currentMarkBhkRidMap.get(operationFlag))) {
                List<Integer> bhkRids = currentMarkBhkRidMap.get(operationFlag);
                List<Map<String, String>> params = new ArrayList<>();
                list.forEach(item -> {
                    if(bhkRids.contains(item.getBhk().getRid())){
                        Map<String, String> ridAndAdvMap = new HashMap<>();
                        ridAndAdvMap.put("rid", item.getBhk().getRid().toString());
                        ridAndAdvMap.put("auditAdv", item.getRejectReason());
                        params.add(ridAndAdvMap);
                    }
                });
                insertCurrentFlowBatch(operationFlag, task, condition.get("username").toString(),params);
            }
        }
        return resultNumber;
    }

    /**
     * <p>Description：插入当前流程记录 </p>
     * <p>Author： yzz 2024-10-15 </p>
     */
    public void insertCurrentFlowBatch(int operationFlag, TdTjCheckTask task, String userName,List<Map<String, String>> params) {
        int length = params.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Map<String, String>> subList = params.subList(i * 1000, endIndex);
            tdZwBgkFlowMapper.batchInsertBgkFlowBack(operationFlag, task.getFkByCheckRsnId().getRid(), task.getFkByCheckRsnId().getRid(),  userName,subList);
        }
    }

    /**
     * <p>Description：更新上一次操作记录 </p>
     * <p>Author： yzz 2024-10-15 </p>
     */
    public void updateLastFlowBatch(List<Integer> ids, int timely, TdTjCheckTask task) {
        int length = ids.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = ids.subList(i * 1000, endIndex);
            this.tdZwBgkFlowMapper.updateBgkFlow(task.getFkByCheckRsnId().getRid(), timely, task.getFkByCheckRsnId().getRid(),subList);
        }
    }

    /**
     * <p>Description：根据传入的字段名更新对应RID主键的字段值 </p>
     * <p>Author： yzz 2024-10-15 </p>
     */
    private int updateTdTjBhkListByIds(BhkBatchUpdateDTO dto) {
        int resultNumber = 0;
        List<Integer> ids=dto.getRidList();
        BhkCheckPojo bhkCheckPojo=dto.getPojo();
        if (!CollectionUtils.isEmpty(ids) && bhkCheckPojo != null) {
            int length = ids.size();
            int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
            for (int i = 0; i < allDataCount; i++) {
                int endIndex = Math.min((i + 1) * 1000, length);
                List<Integer> subList = ids.subList(i * 1000, endIndex);
                dto.setRidList(subList);
                resultNumber += tdTjBhkMapper.updateBhkBackBytype(dto);
            }
        }
        return resultNumber;
    }

    /**
     * <p>Description：根据体检记录RID及上次操作标识获取上一次操作时间 </p>
     * <p>Author： yzz 2024-10-15 </p>
     */
    public List<TdZwBgkFlow> getProcessReceiptDateList(Integer lastMark, List<Integer> bhkRidList) {
        int length = bhkRidList.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        List<TdZwBgkFlow> list = new ArrayList<>();
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = bhkRidList.subList(i * 1000, endIndex);
            list.addAll(tdZwBgkFlowMapper.selectBgkFlowByLastMark(lastMark, subList));
        }
        List<TdZwBgkFlow> result = new ArrayList<>(list.size());
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) {
                String last = StringUtils.objectToString(list.get(i - 1).getBusId());
                String now = StringUtils.objectToString(list.get(i).getBusId());
                // 上一条和当前条的BUS_ID一致，直接跳过，同一BUS_ID仅记录CREATE_DATE最新的一条
                if (last != null && last.equals(now)) {
                    continue;
                }
            }
            result.add(list.get(i));
        }
        return result;
    }

    /**
     * <p>Description：插入历史流程记录 </p>
     * <p>Author： yzz 2024-10-15 </p>
     */
    public void insertInitialFlowBatch(List<Integer> ids, Integer operationFlag, TdTjCheckTask task) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        int length = ids.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = ids.subList(i * 1000, endIndex);
            tdZwBgkFlowMapper.batchInsertBgkFlow(operationFlag, task.getFkByCheckRsnId().getRid(), null, null, null, subList);
        }
    }

    /**
     * <p>Description：数据封装 </p>
     * <p>Author： yzz 2024-10-14 </p>
     */
    public void pakMarkBhkBackCheck(List<CheckDataVo> list, TdTjCheckTask task, Map<String, Object> condition ,Map<Integer, List<Integer>> lastMarkBhkRidMap, Map<Integer, List<Integer>> currentMarkBhkRidMap, List<BhkCheckPojo> markBhkRidMapList, List<List<Integer>> bhkRidMapList) {
        Integer zoneType = Integer.parseInt(condition.get("zoneType").toString());
        String checkLevel = condition.get("checkLevel").toString();
        // 数据封装 体检主表需要更新的数据
        pakMarkBhkBackField(task, markBhkRidMapList, bhkRidMapList);
        // 封装 需要更新的体检主表rid集合，以及上一条操作标记和当前操作标记
        for (CheckDataVo CheckDataVo : list) {
            try {
                TdTjBhk bhk = CheckDataVo.getBhk();
                int bhkRid = bhk.getRid();
                boolean cityDirect = new Integer("1").equals(bhk.getIfCityDirect());
                boolean provinceDirect = new Integer("1").equals(bhk.getIfProvDirect());
                // 上一次操作标记
                int lastMark;
                // 当前操作标记
                int currentMark;
                if ("3".equals(checkLevel)) {
                    if (zoneType > 3) {
                        // 初审-区级
                        lastMark = 21;
                        currentMark =  11;
                        bhkRidMapList.get(0).add(bhkRid);
                        //更新退回原因
                        setRejectReason(markBhkRidMapList, CheckDataVo,0);
                    } else if (zoneType == 3) {
                        if (cityDirect) {
                            // 初审-市级
                            lastMark = 33;
                            currentMark =  13;
                            bhkRidMapList.get(1).add(bhkRid);
                            //更新退回原因
                            setRejectReason(markBhkRidMapList, CheckDataVo,1);
                        } else {
                            // 复审-市级
                            lastMark = 31;
                            currentMark =22;
                            bhkRidMapList.get(2).add(bhkRid);
                            //更新退回原因
                            setRejectReason(markBhkRidMapList, CheckDataVo,2);
                        }
                    } else {
                        // 终审-省级
                        lastMark = 41;
                        currentMark = 32;
                        bhkRidMapList.get(3).add(bhkRid);
                        //更新退回原因
                        setRejectReason(markBhkRidMapList, CheckDataVo,3);
                    }
                } else if ("2".equals(checkLevel)) {
                    if (zoneType > 3) {
                        // 初审-区级
                        lastMark = 21;
                        currentMark = 11;
                        bhkRidMapList.get(1).add(bhkRid);
                        //更新退回原因
                        setRejectReason(markBhkRidMapList, CheckDataVo,1);
                    } else {
                        // 终审-市/省级
                        currentMark = 32;
                        if (provinceDirect) {
                            // 省直
                            lastMark = 44;
                        } else {
                            // 非省直
                            lastMark = 43;
                        }
                        bhkRidMapList.get(3).add(bhkRid);
                        //更新退回原因
                        setRejectReason(markBhkRidMapList, CheckDataVo,3);
                    }
                } else {
                    return;
                }

                if (!lastMarkBhkRidMap.containsKey(lastMark)) {
                    lastMarkBhkRidMap.put(lastMark, new ArrayList<>());
                }
                lastMarkBhkRidMap.get(lastMark).add(bhkRid);

                if (!currentMarkBhkRidMap.containsKey(currentMark)) {
                    currentMarkBhkRidMap.put(currentMark, new ArrayList<>());
                }
                currentMarkBhkRidMap.get(currentMark).add(bhkRid);
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("数据处理异常：" + e.getMessage());
            }
        }
    }
    
    /**
    * <p>Description：给退回原因赋值 </p>
    * <p>Author： yzz 2025/4/11 </p>
    */
    private static void setRejectReason(List<BhkCheckPojo> markBhkRidMapList, CheckDataVo CheckDataVo, Integer index) {
        BhkCheckPojo bhkCheckPojo = markBhkRidMapList.get(index);
        if(index==0 || index==1){
            bhkCheckPojo.setCountyAuditAdv(CheckDataVo.getRejectReason());
        }else if(index==2){
            bhkCheckPojo.setCityAuditAdv(CheckDataVo.getRejectReason());
        }else{
            bhkCheckPojo.setProAuditAdv(CheckDataVo.getRejectReason());
        }
        CheckDataVo.setBhkCheckPojo(bhkCheckPojo);
    }


    /**
     * <p>Description：封装体检主表不同审核情况需要更新的数据 </p>
     * <p>Author： yzz 2024-10-14 </p>
     */
    public void pakMarkBhkBackField(TdTjCheckTask task, List<BhkCheckPojo> markBhkRidMapList, List<List<Integer>> bhkRidMapList) {

        // 三级审核 初审方式
        bhkRidMapList.add(new ArrayList<>());
        BhkCheckPojo checkPojo = new BhkCheckPojo();
        checkPojo.setState(0);
        checkPojo.setCountyCheckWay(4);
        checkPojo.setCountyRst(task.getCheckRst());
        checkPojo.setCountyAuditAdv(task.getCheckAdv());
        checkPojo.setCountyChkOrgid(task.getFkByCheckUnitId().getRid());
        checkPojo.setCountySmtDate(DateUtils.formatDate(new Date()) + " 23:59:59");
        markBhkRidMapList.add(checkPojo);

        // 市直属 初审方式
        bhkRidMapList.add(new ArrayList<>());
        BhkCheckPojo checkPojo1 = new BhkCheckPojo();
        checkPojo1.setState(0);
        checkPojo1.setCountyCheckWay(4);
        checkPojo1.setCountyRst(task.getCheckRst());
        checkPojo1.setCountyAuditAdv(task.getCheckAdv());
        checkPojo1.setCountyChkOrgid(task.getFkByCheckUnitId().getRid());
        checkPojo1.setCountySmtDate(DateUtils.formatDate(new Date()) + " 23:59:59");
        markBhkRidMapList.add(checkPojo1);

        // 非市直属 复审方式
        bhkRidMapList.add(new ArrayList<>());
        BhkCheckPojo checkPojo2 = new BhkCheckPojo();
        checkPojo2.setState(2);
        checkPojo2.setCityCheckWay(4);
        checkPojo2.setCityRst(task.getCheckRst());
        checkPojo2.setCityAuditAdv(task.getCheckAdv());
        checkPojo2.setCityChkOrgid(task.getFkByCheckUnitId().getRid());
        checkPojo2.setCitySmtDate(DateUtils.formatDate(new Date()) + " 23:59:59");
        markBhkRidMapList.add(checkPojo2);

        // 省级操作终审结果
        bhkRidMapList.add(new ArrayList<>());
        BhkCheckPojo checkPojo3 = new BhkCheckPojo();
        checkPojo3.setState(4);
        checkPojo3.setProCheckWay(4);
        checkPojo3.setCityRst2(task.getCheckRst());
        checkPojo3.setProAuditAdv(task.getCheckAdv());
        checkPojo3.setProChkOrgid(task.getFkByCheckUnitId().getRid());
        checkPojo3.setProSmtDate(DateUtils.formatDate(new Date()) + " 23:59:59");
        checkPojo3.setProChkPsnid(task.getFkByCheckRsnId().getRid());
        markBhkRidMapList.add(checkPojo3);
    }


    /**
    * <p>Description：单条处理 </p>
    * <p>Author： yzz 2025/4/10 </p>
    */
    public void saveSingleRecord(CheckDataVo data,TdTjCheckTask task) {
        List<CheckDataVo> validRecords=new ArrayList<>();
        validRecords.add(data);
        this.saveBatchBackData(validRecords, task);
    }
}

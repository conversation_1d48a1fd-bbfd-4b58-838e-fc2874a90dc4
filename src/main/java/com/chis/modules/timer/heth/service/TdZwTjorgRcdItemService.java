package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwTjorgRcdItem;
import com.chis.modules.timer.heth.mapper.TdZwTjorgRcdItemMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-11-13,TdZwTjorgRcdItemService
 */
@Service
public class TdZwTjorgRcdItemService extends ZwxBaseServiceImpl<TdZwTjorgRcdItemMapper, TdZwTjorgRcdItem> {
    public List<TdZwTjorgRcdItem> selectTdZwTjorgRcdItem(Integer mainId){
        return baseMapper.selectTdZwTjorgRcdItem(mainId);
    }
}

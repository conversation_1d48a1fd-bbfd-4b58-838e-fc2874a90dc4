package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TbTjSrvorg;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-12,TdZwyjOtrBhk
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_OTR_BHK")
@KeySequence(value = "TD_ZWYJ_OTR_BHK_SEQ",clazz = Integer.class)
public class TdZwyjOtrBhk extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("BHK_CODE")
    private String bhkCode;

    @TableField(value = "BHKORG_ID" , el = "fkByBhkorgId.rid")
    private TbTjSrvorg fkByBhkorgId;

    @TableField(value = "CRPT_ZONE_ID" , el = "fkByCrptZoneId.rid")
    private TsZone fkByCrptZoneId;

    @TableField(value = "CRPT_ID" , el = "fkByCrptId.rid")
    private TbTjCrpt fkByCrptId;

    @TableField("PERSON_NAME")
    private String personName;

    @TableField(value = "CARD_TYPE_ID" , el = "fkByCardTypeId.rid")
    private TsSimpleCode fkByCardTypeId;

    @TableField("IDC")
    private String idc;

    @TableField("BHK_DATE")
    private Date bhkDate;

    @TableField("BADRSN")
    private String badrsn;

    @TableField("OTR_TYPE")
    private String otrType;

    @TableField("HAPPEN_DATE")
    private Date happenDate;


    public TdZwyjOtrBhk(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import java.sql.Clob;

import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-21,TdZwyjDangerResult
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_DANGER_RESULT")
@KeySequence(value = "TD_ZWYJ_DANGER_RESULT_SEQ",clazz = Integer.class)
public class TdZwyjDangerResult extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwyjDangerBhk fkByMainId;

    @TableField(value = "DANGER_ID" , el = "fkByDangerId.rid")
    private TdZwyjDangerItems fkByDangerId;

    @TableField("ITEM_RST")
    private String itemRst;

    @TableField("MSRUNT")
    private String msrunt;

    @TableField("ITEM_STDVALUE")
    private String itemStdvalue;


    public TdZwyjDangerResult(Integer rid) {
        super(rid);
    }


}

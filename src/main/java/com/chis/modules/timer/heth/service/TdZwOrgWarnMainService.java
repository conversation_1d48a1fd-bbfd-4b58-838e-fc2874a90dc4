package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.ObjectUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwOrgWarnMain;
import com.chis.modules.timer.heth.mapper.TdZwOrgWarnMainMapper;
import com.chis.modules.timer.heth.pojo.bo.warn.CheckOrgWarnPsnBO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p> 服务实现类：  </p>
 *
 * <AUTHOR> 2024-07-12,TdZwOrgWarnMainService
 */
@Service
public class TdZwOrgWarnMainService extends ZwxBaseServiceImpl<TdZwOrgWarnMainMapper, TdZwOrgWarnMain> {
    /**
     * 根据检查机构id查询检查机构资质机构预警主表
     *
     * @param orgIdList 检查机构id
     * @return 检查机构资质机构预警主表
     */
    public Map<String, TdZwOrgWarnMain> findCheckOrgWarnMainMapByOrgId(List<Integer> orgIdList) {
        Map<String, TdZwOrgWarnMain> warnMainMap = new HashMap<>();
        if (ObjectUtils.isEmpty(orgIdList)) {
            return warnMainMap;
        }
        List<TdZwOrgWarnMain> warnMainList = this.baseMapper.selectCheckOrgWarnMainListByOrgId(orgIdList);
        if (CollectionUtils.isEmpty(warnMainList)) {
            return warnMainMap;
        }
        return warnMainList.stream()
                .filter(orgWarnMain -> StringUtils.isNotBlank(orgWarnMain.getBusId()))
                .collect(Collectors.toMap(TdZwOrgWarnMain::getBusId, Function.identity()));
    }

    /**
     * 根据检查机构id查询预警人员信息
     *
     * @param orgId 检查机构id
     * @return 预警人员信息
     */
    public List<CheckOrgWarnPsnBO> selectCheckOrgWarnPsnListByOrgId(String orgId) {
        if (ObjectUtils.isEmpty(orgId)) {
            return new ArrayList<>();
        }
        List<CheckOrgWarnPsnBO> warnPsnBOList = this.baseMapper.selectCheckOrgWarnPsnListByOrgId(orgId);
        if (CollectionUtils.isEmpty(warnPsnBOList)) {
            return new ArrayList<>();
        }
        return warnPsnBOList;
    }
}

package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>类描述：专项治理数据下载 基本信息 </p>
 * pw 2023/9/21
 **/
@Data
public class SpecialRectficationBaseInfoVo implements Serializable {
    private static final long serialVersionUID = -8536336165917317652L;
    private String uid;
    private String bizAddr;
    private String bizZone;
    private String zoneFullName;
    private String employerName;
    private String creditCode;
    private String industryCateName;
    private String industryCategory;
    private String industryLevelCode;
    private String economicCode;
    private String economicLevelCode;
    private String economicName;
    private String enterpriseScale;
    private String managementPerson;
    private String managementPersonContact;
    private Boolean existsAbove10Staff;
    private Boolean existsMainFactorOverproof ;
    private Boolean existsWorkplaceDetection;
    private Boolean existsOtherFactorOverproof;
    private Integer dataSource;
    private String regulationReason;
    private Date confirmDate;
    private Date reportDate;
    private Date completeDateName;
    private Integer rectificationProgress;
    private Boolean previousTotalQualified;
    private Integer resultAbnormal;
    private Integer rectificationResult;
    private Integer reportState;
    private String dismissUnit;
    private String dismissPsn;
    private Date dismissDate;
    private String dismissReason;
    private String serviceOrganName ;
    private Date modifyDate;
    private List<SpecialRectficationDetailVo> tdPreviousSituationExpList;
    private List<SpecialRectficationSchemeVo> rectificationSolutionExp;
    private List<SpecialRectficationResultVo> rectificationDetectionDetailExpList;
}

package com.chis.modules.timer.heth.enums;
/**
 * <p>类描述：企业在线申报 返回状态枚举 </p>
 * @ClassAuthor： pw 2022/9/6
 **/
public enum RespCodeEnum {
    SUCCESS("00","成功"),
    FAIL("01","失败"),
    EXCEPTION("99","其他异常");

    private String code;
    private String msg;

    RespCodeEnum(String code,String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}

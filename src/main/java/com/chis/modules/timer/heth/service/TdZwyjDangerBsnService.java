package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwyjDangerBsn;
import com.chis.modules.timer.heth.mapper.TdZwyjDangerBsnMapper;
import org.springframework.stereotype.Service;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-11-21,TdZwyjDangerBsnService
 */
@Service
public class TdZwyjDangerBsnService extends ZwxBaseServiceImpl<TdZwyjDangerBsnMapper, TdZwyjDangerBsn> {

    public int deleteByMainId(Integer mainId){
        if(null != mainId){
            return this.baseMapper.removeByMainId(mainId);
        }
        return 0;
    }
}

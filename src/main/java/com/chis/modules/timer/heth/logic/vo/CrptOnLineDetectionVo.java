package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
/**
 * <p>类描述：企业在线申报 职业病危害因素检测情况 </p>
 * @ClassAuthor： pw 2022/9/7
 **/
@Data
public class CrptOnLineDetectionVo implements Serializable {
    private static final long serialVersionUID = -7139310861941179746L;
    private Boolean existsDetection;
    private Boolean existsDust;
    private Integer dustCheckPoints;
    private Integer dustOverproofPoints;
    private Boolean existsChemical;
    private Integer chemicalCheckPoints;
    private Integer chemicalOverproofPoints;
    private Boolean existsPhysical;
    private Integer physicalCheckPoints;
    private Integer physicalOverproofPoints;
    private Boolean existsRadioactivity;
    private Integer radioactivityCheckPoints;
    private Integer radioactivityOverproofPoints;
    private Boolean existsBioticOther;
    private Integer bioticOtherCheckPoints;
    private Integer bioticOtherOverproofPoints;
}

package com.chis.modules.timer.heth.logic.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GetCfbRecoveryRecordDataVo {
    /**
     * 患者姓名
     */
    private String userName;
    /**
     * 患者唯一标识
     */
    private String patientid;
    /**
     * 身份证号
     */
    private String identityCard;
    /**
     * 患者所属区县
     */
    private String area;
    /**
     * 患者所属站点名称
     */
    private String hosName;
    /**
     * 当年首次康复时间 格式:yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date firstRecverytime;
    /**
     * 当年末次康复时间 格式:yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date lastRecverytime;
    /**
     * 有无器械康复  0 -无 1-有
     */
    private Integer hasApparatusrecovery;
    /**
     * 有无康复记录  0 -无 1-有
     */
    private Integer hasRecovery;
    /**
     * 康复训练总次数
     */
    private Integer recoveryNum;
    /**
     * 康复训练总时间  单位分钟
     */
    private Integer recoveryTime;
    /**
     * 康复训练平均持续时间
     */
    private Integer recoveryAvetime;
    /**
     * 首次康复前血氧饱和度（%）
     */
    private String firstSpo2;
    /**
     * 当年康复后血氧饱和度（%）
     */
    private String thisyearSpo2;
    /**
     * 首次康复前心率（次/分）
     */
    private String firstHr;
    /**
     * 当年康复后心率（次/分）
     */
    private String thisyearHr;
    /**
     * 首次康复前血压（mmHg）
     */
    private String firstBlood;
    /**
     * 当年康复后血压（mmHg）
     */
    private String thisyearBlood;
    /**
     * 首次康复前六分钟步行距离（米）
     */
    private String firstDistance;
    /**
     * 当年康复后六分钟步行距离（米）
     */
    private String thisyearDistance;
    /**
     * 当年康复前呼吸困难指数(mMRC)得分（分级）
     */
    private String firstMmrcscore;
    /**
     * 当年康复后呼吸困难指数(mMRC)得分（分级）
     */
    private String thisyearMmrcscore;
    /**
     * 首次康复前生活质量问卷得分（分）
     */
    private String firstActscore;
    /**
     * 当年康复后生活质量问卷得分（分）
     */
    private String thisyearActscore;
    /**
     * 首次康复前最大吸气压(cmH2O）
     */
    private String firstMip;
    /**
     * 当年康复后最大吸气压(cmH2O）
     */
    private String thisyearMip;
    /**
     * 首次康复前最大呼气压(cmH2O）
     */
    private String firstMep;
    /**
     * 当年康复后最大呼气压(cmH2O）
     */
    private String thisyearMep;
}

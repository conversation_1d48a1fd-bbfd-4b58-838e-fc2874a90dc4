package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZwyjOtrSerItemCrpt;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2020-11-19,TdZwyjOtrSerItemCrptMapper
 */
@Repository
public interface TdZwyjOtrSerItemCrptMapper extends ZwxBaseMapper<TdZwyjOtrSerItemCrpt> {

    /** 查询预警主表下所有超服务项目范围_企业信息 */
    List<TdZwyjOtrSerItemCrpt> selectListByWarnId(@Param("mainId") Integer mainId);
}

package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdTjBadrsns;
import com.chis.modules.timer.heth.entity.TdTjBhk;
import com.chis.modules.timer.heth.mapper.TdTjBadrsnsMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdTjBadrsnsService
 */
@Service
public class TdTjBadrsnsService extends ZwxBaseServiceImpl<TdTjBadrsnsMapper, TdTjBadrsns> {
    /**
     * <p>方法描述：根据体检rid查询接触危害因素</p>
     * @MethodAuthor qrr,2020-10-29,findTdTjBadrsnsByBhkId
     * */
    public List<TdTjBadrsns> findTdTjBadrsnsByBhkId(Integer bhkId){
        TdTjBadrsns badrsns = new TdTjBadrsns();
        badrsns.setFkByBhkId(new TdTjBhk(bhkId));
        return this.baseMapper.selectListByEntity(badrsns);
    }

    public List<TdTjBadrsns> findTdTjBadrsnsExtends2ByBhkId(Integer bhkId){
        return baseMapper.findTdTjBadrsnsExtends2ByBhkId(bhkId);
    }

    /** 通过体检记录rid集合获取接触危害因素 */
    public List<TdTjBadrsns> findTdTjBadrsnsByBhkIdList(List<Integer> bhkIdList){
        if(CollectionUtils.isEmpty(bhkIdList)){
            return null;
        }else if(bhkIdList.size() <= 1000){
            return this.baseMapper.findTdTjBadrsnsByBhkIdList(bhkIdList);
        }
        List<List<Integer>> ridGroupList = StringUtils.splitListProxy(bhkIdList, 1000);
        List<TdTjBadrsns> resultList = new ArrayList<>();
        for(List<Integer> ridList : ridGroupList){
            List<TdTjBadrsns> tmpList = this.baseMapper.findTdTjBadrsnsByBhkIdList(ridList);
            if(!CollectionUtils.isEmpty(tmpList)){
                resultList.addAll(tmpList);
            }
        }
        return resultList;
    }
}

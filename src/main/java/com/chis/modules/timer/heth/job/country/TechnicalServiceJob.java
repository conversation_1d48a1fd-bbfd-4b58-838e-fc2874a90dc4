package com.chis.modules.timer.heth.job.country;

import com.alibaba.fastjson.JSON;
import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.DesEncryptUtil;
import com.chis.comm.utils.HttpRequestUtil;
import com.chis.comm.utils.StringUtils;
import com.chis.comm.utils.sm.Sm4Util;
import com.chis.modules.sys.entity.TsContraSub;
import com.chis.modules.sys.entity.TsSystemParam;
import com.chis.modules.sys.service.TsContraSubService;
import com.chis.modules.sys.service.TsSystemParamService;
import com.chis.modules.timer.heth.enums.CardUploadCountryBusTypeEnum;
import com.chis.modules.timer.heth.logic.vo.*;
import com.chis.modules.timer.heth.service.TdZywsCardRcdService;
import com.chis.modules.timer.heth.service.TechnicalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 *  <p>类描述：技术服务机构上传接口</p>
 * @ClassAuthor hsj 2022-12-21 14:24
 */
@Slf4j
@Component
public class TechnicalServiceJob {
    @Autowired
    private TechnicalService technicalService;
    @Autowired
    private TdZywsCardRcdService tdZywsCardRcdService;
    @Autowired
    private TsContraSubService contraSubService;
    @Autowired
    private TsSystemParamService tsSystemParamService;


    @Value("${heth-timer.country.dataSize}")
    private Integer dataSize;
    /**传输密钥*/
    @Value("${heth-timer.country.security-key}")
    protected String securityKey;
    @Value("${heth-timer.country.occhethInfoAdd_url}")
    protected String occhethInfoAddUrl;
    @Value("${heth-timer.country.srvorgInfoAdd_url}")
    protected String srvorgInfoAddUrl;
    @Value("${heth-timer.country.encrypt-key}")
    private String encryptKey;
    /** 对照-地区*/
    private Map<String, TsContraSub> contraSubTypeMap;
    private Boolean isInfoEncrypt = false;
    private String infoEncryptKey;


    /**
     *  <p>方法描述：调用职业卫生技术服务机构上传接口</p>
     * @MethodAuthor hsj 2022-12-21 14:34
     */
    @Scheduled(cron = "${heth-timer.country.sche-cron.occhethCron}")
    public void occhethStart(){
        log.info("定时调用职业卫生技术服务机构上传接口 任务启动 程序版本{} 当前时间{}", "1.0", new Date());
        //地区对照
        this.contraSubTypeMap = contraSubService.findTsContraSub("15", "1");

        fetchEncryptInfo();

        //将所有失败的更新成0
        this.tdZywsCardRcdService.updateCardRcdByType(CardUploadCountryBusTypeEnum.OCCORG.getCode(),"2");
        for(;;){
            List<OcchethInfoDataVo> occhethInfoDataVos = this.technicalService.getOcchethInfoVo(CardUploadCountryBusTypeEnum.OCCORG.getCode(),dataSize);
            if(CollectionUtils.isEmpty(occhethInfoDataVos)){
                return;
            }
            Map<Integer,OcchethInfoDataVo> occhethInfoVoMap =new HashMap<>();
            List<Integer> ridList =new ArrayList<>();
            for(OcchethInfoDataVo vo:occhethInfoDataVos){
                if (this.isInfoEncrypt) {
                    vo.setCphone(Sm4Util.strDecode(vo.getCphone(), this.infoEncryptKey));
                }
                vo.setSecurityKey(securityKey);
                //地区对照
                String rssxbm = technicalService.findRssxbm(vo.getRid(),vo.getZoneGb(),CardUploadCountryBusTypeEnum.OCCORG.getCode(),contraSubTypeMap,vo.getCardId());
                if(StringUtils.isNotBlank(rssxbm)){
                    vo.setRssxbm(rssxbm);
                }else {
                    continue;
                }
                //发证日期-日期转格式
                if(null != vo.getIssuedDate()){
                    vo.setIssued(DateUtils.formatDate(vo.getIssuedDate(),"yyyy-MM-dd"));
                }
                //日期转格式
                if(null != vo.getValidDate()){
                    vo.setVld(DateUtils.formatDate(vo.getValidDate(),"yyyy-MM-dd"));
                }
                vo.setIsck("0");
                vo.setIshg("0");
                vo.setIsyj("0");
                vo.setIsjx("0");
                vo.setIshs("0");
                vo.setIshj("0");
                occhethInfoVoMap.put(vo.getRid(),vo);
                ridList.add(vo.getRid());
            }
            if(CollectionUtils.isNotEmpty(ridList)){
                List<ItemsVo> itemsVos = technicalService.getOcchethItems(ridList);
                if(CollectionUtils.isNotEmpty(itemsVos)){
                    Map<Integer, List<ItemsVo>> groupMap = itemsVos.stream()
                            .collect(Collectors.groupingBy(ItemsVo::getOrgId));
                    for(Map.Entry<Integer, List<ItemsVo>> map : groupMap.entrySet()){
                        Integer key = map.getKey();
                        List<ItemsVo> list = map.getValue();
                        OcchethInfoDataVo occhethInfoDataVo = occhethInfoVoMap.get(key);
                        for(ItemsVo itemsVo :list){
                            String str = itemsVo.getExt();
                            if(StringUtils.isBlank(str)){
                                continue;
                            }
                            switch (str){
                                case "1":
                                    occhethInfoDataVo.setIsck("1");
                                    break;
                                case "2":
                                    occhethInfoDataVo.setIshg("1");
                                    break;
                                case "3":
                                    occhethInfoDataVo.setIsyj("1");
                                    break;
                                case "4":
                                    occhethInfoDataVo.setIsjx("1");
                                    break;
                                case "5":
                                    occhethInfoDataVo.setIshs("1");
                                    break;
                                case "6":
                                    occhethInfoDataVo.setIshj("1");
                                    break;
                                default:
                            }
                        }
                    }
                }
            }
            for(Map.Entry<Integer, OcchethInfoDataVo> map : occhethInfoVoMap.entrySet()){
                OcchethInfoDataVo occhethInfoDataVo = map.getValue();
                occhethInfoDataVoLen(occhethInfoDataVo);
                //错误信息
                String msg = "";
                try {
                    log.info("职业卫生技术服务机构上传接口："+JSON.toJSONString(occhethInfoDataVo));
                    Map<String, String> addMap = new HashMap<>();
                    addMap.put("apiData",  DesEncryptUtil.encryptByKey(JSON.toJSONString(occhethInfoDataVo),encryptKey));
                    String reposeJson = HttpRequestUtil.httpRequestByFormData(occhethInfoAddUrl,addMap);
                    log.info("职业卫生技术服务机构上传接口返回值："+reposeJson);
                    if(StringUtils.isBlank(reposeJson)){
                        msg = "调用职业卫生技术服务机构上传接口失败。";
                    }else {
                        CardResponsBaseVo cardResponsBaseVo = JSON.parseObject(reposeJson, CardResponsBaseVo.class);
                        if (cardResponsBaseVo != null && "1".equals(cardResponsBaseVo.getCode())) {
                            this.technicalService.addOrUpdateTdZywsCardRcd(occhethInfoDataVo.getRid(),null,1,CardUploadCountryBusTypeEnum.OCCORG.getCode(),occhethInfoDataVo.getCardId());
                        }else {
                            msg = "接口返回信息：" + reposeJson;
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), new Throwable(e));
                    msg = e.toString();
                }
                if(StringUtils.isNotBlank(msg)){
                    this.technicalService.addOrUpdateTdZywsCardRcd(occhethInfoDataVo.getRid(),msg.length()>1000 ? msg.substring(0,1000) : msg,2,CardUploadCountryBusTypeEnum.OCCORG.getCode(),occhethInfoDataVo.getCardId());
                }
            }
        }

    }
    /**
     *  <p>方法描述：长度截取</p>
     * @MethodAuthor hsj 2022-12-22 16:30
     */
    private void occhethInfoDataVoLen(OcchethInfoDataVo vo) {
        //机构名称 -超过64长度截取
        vo.setOname(paramStringLength(vo.getOname(),64));
        //lname-超过16长度截取
        vo.setLname(paramStringLength(vo.getLname(),16));
        //bno-超过64长度截取
        vo.setBno(paramStringLength(vo.getBno(),64));
        //raddress-超过128长度截取
        vo.setRaddress(paramStringLength(vo.getRaddress(),128));
        //laddress-超过512长度截取
        if(StringUtils.isNotBlank(vo.getLaddress())){
            vo.setLaddress(paramStringLength(vo.getLaddress(),512));
        }else {
            vo.setLaddress(vo.getRaddress());
        }

        //cname-超过16长度截取
        vo.setCname(paramStringLength(vo.getCname(),16));


    }
    /**
     *  <p>方法描述：长度截取</p>
     * @MethodAuthor hsj 2022-12-22 16:36
     */
    private String paramStringLength(String str, Integer len) {
        if(StringUtils.isBlank(str)){
            return null;
        }
        if(str.length()<=len){
            return str;
        }
        return str.substring(0,len);
    }

    /**
     *  <p>方法描述：上传放射卫生技术服务机构工具</p>
     * @MethodAuthor hsj 2022-12-22 15:46
     */
    @Scheduled(cron = "${heth-timer.country.sche-cron.srvorgCron}")
    public void srvorgStart(){
        log.info("定时调用放射卫生技术服务机构上传接口 任务启动 程序版本{} 当前时间{}", "1.0", new Date());
        //地区对照
        this.contraSubTypeMap = contraSubService.findTsContraSub("15", "1");

        fetchEncryptInfo();

        //将所有失败的更新成0
        this.tdZywsCardRcdService.updateCardRcdByType(CardUploadCountryBusTypeEnum.SRVORGORG.getCode(),"2");
        for(;;){
            List<SrvorgInfoDataVo> srvorgInfoDataVos = this.technicalService.getSrvorgInfoVo(CardUploadCountryBusTypeEnum.SRVORGORG.getCode(),dataSize);
            if(CollectionUtils.isEmpty(srvorgInfoDataVos)){
                return;
            }
            Map<Integer,SrvorgInfoDataVo> srvorgInfoDataVoHashMap =new HashMap<>();
            List<Integer> ridList =new ArrayList<>();
            for(SrvorgInfoDataVo vo:srvorgInfoDataVos){
                if (this.isInfoEncrypt) {
                    vo.setCphone(Sm4Util.strDecode(vo.getCphone(), this.infoEncryptKey));
                }
                vo.setSecurityKey(securityKey);
                String rssxbm = technicalService.findRssxbm(vo.getRid(),vo.getZoneGb(),CardUploadCountryBusTypeEnum.SRVORGORG.getCode(),contraSubTypeMap,vo.getCardId());
                if(StringUtils.isNotBlank(rssxbm)){
                    vo.setRssxbm(rssxbm);
                }else {
                    continue;
                }
                //日期转格式
                if(null != vo.getValidDate()){
                    vo.setVld(DateUtils.formatDate(vo.getValidDate(),"yyyy-MM-dd"));
                }
                if(null != vo.getFirstGetday()){
                    vo.setIssued(DateUtils.formatDate(vo.getFirstGetday(),"yyyy-MM-dd"));
                }
                vo.setIspja("0");
                vo.setIspjb("0");
                vo.setIsfs("0");
                vo.setIsgr("0");
                vo.setIsqc("0");
                vo.setPtnum(0);
                srvorgInfoDataVoHashMap.put(vo.getRid(),vo);
                ridList.add(vo.getRid());
            }
            if(CollectionUtils.isNotEmpty(ridList)){
                List<ItemsVo> itemsVos = technicalService.getSrvorgItems(ridList);
                if(CollectionUtils.isNotEmpty(itemsVos)){
                    Map<Integer, List<ItemsVo>> groupMap = itemsVos.stream()
                            .collect(Collectors.groupingBy(ItemsVo::getOrgId));
                    for(Map.Entry<Integer, List<ItemsVo>> map : groupMap.entrySet()){
                        Integer key = map.getKey();
                        List<ItemsVo> list = map.getValue();
                        SrvorgInfoDataVo srvorgInfoDataVo = srvorgInfoDataVoHashMap.get(key);
                        for(ItemsVo itemsVo :list){
                            String str = itemsVo.getExt();
                            if(StringUtils.isBlank(str)){
                                continue;
                            }
                            switch (str){
                                case "1":
                                    srvorgInfoDataVo.setIspja("1");
                                    break;
                                case "2":
                                    srvorgInfoDataVo.setIspjb("1");
                                    break;
                                case "3":
                                    srvorgInfoDataVo.setIsfs("1");
                                    break;
                                case "4":
                                    srvorgInfoDataVo.setIsgr("1");
                                    break;
                                case "5":
                                    srvorgInfoDataVo.setIsqc("1");
                                    break;
                                default:
                            }
                        }
                    }
                }
                //专业技术人员数
                List<PsnNumVo> psnNums = technicalService.getPsnNums(ridList);
                if(CollectionUtils.isNotEmpty(psnNums)){
                    for (PsnNumVo psnNumVo:psnNums){
                        srvorgInfoDataVoHashMap.get(psnNumVo.getOrgId()).setPtnum(psnNumVo.getCount());
                    }
                }
            }
            for(Map.Entry<Integer, SrvorgInfoDataVo> map : srvorgInfoDataVoHashMap.entrySet()){
                SrvorgInfoDataVo srvorgInfoDataVo = map.getValue();
                if(srvorgInfoDataVo.getPtnum() == null || srvorgInfoDataVo.getPtnum() == 0){
                    this.technicalService.addOrUpdateTdZywsCardRcd(srvorgInfoDataVo.getRid(),"专业技术人员数应为正整数："+srvorgInfoDataVo.getPtnum()+"。",2,CardUploadCountryBusTypeEnum.SRVORGORG.getCode(),srvorgInfoDataVo.getCardId());
                    continue;
                }
                srvorgInfoDataVoLen(srvorgInfoDataVo);
                //错误信息
                String msg = "";
                try {
                    log.info("放射卫生技术服务机构上传接口:"+JSON.toJSONString(srvorgInfoDataVo));
                    Map<String, String> addMap = new HashMap<>();
                    addMap.put("apiData",  DesEncryptUtil.encryptByKey(JSON.toJSONString(srvorgInfoDataVo),encryptKey));
                    String reposeJson = HttpRequestUtil.httpRequestByFormData(srvorgInfoAddUrl,addMap);
                    log.info("放射卫生技术服务机构上传接口返回值："+reposeJson);
                    if(StringUtils.isBlank(reposeJson)){
                        msg = "调用放射卫生技术服务机构上传接口失败。";
                    }else {
                        CardResponsBaseVo cardResponsBaseVo = JSON.parseObject(reposeJson, CardResponsBaseVo.class);
                        if (cardResponsBaseVo != null && "1".equals(cardResponsBaseVo.getCode())) {
                            this.technicalService.addOrUpdateTdZywsCardRcd(srvorgInfoDataVo.getRid(),null,1,CardUploadCountryBusTypeEnum.SRVORGORG.getCode(),srvorgInfoDataVo.getCardId());
                        }else {
                            msg = "接口返回信息："+ reposeJson;
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), new Throwable(e));
                    msg =  e.toString();
                }
                if(StringUtils.isNotBlank(msg)){
                    this.technicalService.addOrUpdateTdZywsCardRcd(srvorgInfoDataVo.getRid(),msg.length()>1000 ? msg.substring(0,1000) : msg,2,CardUploadCountryBusTypeEnum.SRVORGORG.getCode(),srvorgInfoDataVo.getCardId());
                }
            }
        }

    }
    /**
     *  <p>方法描述：长度截取</p>
     * @MethodAuthor hsj 2022-12-22 16:56
     */
    private void srvorgInfoDataVoLen(SrvorgInfoDataVo vo) {
        //机构名称 -超过64长度截取
        vo.setOname(paramStringLength(vo.getOname(),64));
        //lname-超过16长度截取
        vo.setLname(paramStringLength(vo.getLname(),16));
        //bno-超过64长度截取
        vo.setBno(paramStringLength(vo.getBno(),64));
        //raddress-超过128长度截取
        vo.setRaddress(paramStringLength(vo.getRaddress(),128));
        vo.setLaddress(vo.getRaddress());
        //laddress-超过512长度截取
        vo.setLaddress(paramStringLength(vo.getLaddress(),512));
        //cname-超过16长度截取
        vo.setCname(paramStringLength(vo.getCname(),16));
    }

    /**
     * 获取系统参数判断是否开启了敏感信息加密相关
     */
    private void fetchEncryptInfo() {
        this.isInfoEncrypt = false;
        Map<String, TsSystemParam> tsSystemParamMap = tsSystemParamService.findTsSystemParamByType("0");
        TsSystemParam isInfoEncrypt = tsSystemParamMap.get("IF_INFO_ENCRY");
        if (null == isInfoEncrypt || !"1".equals(isInfoEncrypt.getParamValue())) {
            return;
        }
        TsSystemParam infoEncryKey = tsSystemParamMap.get("INFO_ENCRY_KEY");
        if (null == infoEncryKey || !StringUtils.isNotBlank(infoEncryKey.getParamValue())) {
            return;
        }
        this.isInfoEncrypt = true;
        this.infoEncryptKey = infoEncryKey.getParamValue();
    }

}

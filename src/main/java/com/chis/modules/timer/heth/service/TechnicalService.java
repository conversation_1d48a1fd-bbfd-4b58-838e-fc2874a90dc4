package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsContraSub;
import com.chis.modules.timer.heth.entity.TdZywsCardRcd;
import com.chis.modules.timer.heth.logic.vo.ItemsVo;
import com.chis.modules.timer.heth.logic.vo.OcchethInfoDataVo;
import com.chis.modules.timer.heth.logic.vo.PsnNumVo;
import com.chis.modules.timer.heth.logic.vo.SrvorgInfoDataVo;
import com.chis.modules.timer.heth.mapper.TechnicalServiceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *  <p>类描述：技术服务服务</p>
 * @ClassAuthor hsj 2022-12-21 14:38
 */
@Service
public class TechnicalService {
    @Autowired
    private TechnicalServiceMapper technicalServiceMapper;
    @Autowired
    private TdZywsCardRcdService tdZywsCardRcdService;
    public List<OcchethInfoDataVo> getOcchethInfoVo(Integer type ,Integer dataSize) {
        return technicalServiceMapper.getOcchethInfoVo(type,dataSize);
    }

    /**
     *  <p>方法描述：地区对照</p>
     * @MethodAuthor hsj 2022-12-22 9:42
     */
    public String findRssxbm(Integer rid,String zoneGb,Integer type, Map<String, TsContraSub> contraSubTypeMap,Integer cardId) {
        if(StringUtils.isNotBlank(zoneGb)){
            if(!contraSubTypeMap.containsKey(zoneGb)){
                //对照失败
                this.addOrUpdateTdZywsCardRcd(rid,"注册地址-行政区划码:"+zoneGb+"对照失败。",2,type,cardId);
                return null;
            }
            return contraSubTypeMap.get(zoneGb).getRightCode();
        }else {
            //地区为空
            this.addOrUpdateTdZywsCardRcd(rid,"地区不能为空。",2,type,cardId);
            return null;
        }
    }
    /**
     *  <p>方法描述：更新或者新增日志</p>
     * @MethodAuthor hsj 2022-12-22 9:56
     */
    public void addOrUpdateTdZywsCardRcd(Integer rid, String msg, Integer state, Integer type,Integer cardId) {
        TdZywsCardRcd tdZywsCardRcd = new TdZywsCardRcd();
        if(null != cardId){
            //存在
            this.tdZywsCardRcdService.updateCardRcdByState(type,state,msg,cardId);
        }else {
            //不存在
            tdZywsCardRcd.setBusId(rid);
            tdZywsCardRcd.setBusType(type);
            tdZywsCardRcd.setErrMsg(msg);
            tdZywsCardRcd.setState(state);
            tdZywsCardRcd.setUploadDate(new Date());
            this.tdZywsCardRcdService.insertEntity(tdZywsCardRcd);
        }


    }

    public List<ItemsVo> getOcchethItems(List<Integer> ridList) {
        return this.technicalServiceMapper.getOcchethItems(ridList);
    }

    public List<SrvorgInfoDataVo> getSrvorgInfoVo(Integer type,Integer dataSize) {
        return this.technicalServiceMapper.getSrvorgInfoVo(type,dataSize);
    }

    public List<ItemsVo> getSrvorgItems(List<Integer> ridList) {
        return this.technicalServiceMapper.getSrvorgItems(ridList);
    }

    public List<PsnNumVo> getPsnNums(List<Integer> ridList) {
        return this.technicalServiceMapper.getPsnNums(ridList);
    }
}

package com.chis.modules.timer.heth.job;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.ObjectUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsUnit;
import com.chis.modules.sys.service.TsSimpleCodeService;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.enums.WarnExceptEnums;
import com.chis.modules.timer.heth.pojo.bo.warn.CheckOrgWarnBO;
import com.chis.modules.timer.heth.pojo.bo.warn.CheckOrgWarnPsnBO;
import com.chis.modules.timer.heth.pojo.bo.warn.InstInfoWarnBO;
import com.chis.modules.timer.heth.service.CheckOrgWarnService;
import com.chis.modules.timer.heth.service.TbZwOrgWarnConfigService;
import com.chis.modules.timer.heth.service.TdZwOrgWarnMainService;
import com.chis.modules.timer.heth.service.TdZwTjorginfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 定时预警资质机构工具（检查机构）
 */
@Slf4j
@Component
public class CheckOrgWarnJob {
    @Value("${warn-timer.check-org.icp-days}")
    private Integer icpDays;
    /**
     * 设备预警天数
     */
    @Value("${warn-timer.check-org.inst-days}")
    private Integer instDays;
    /**
     * 下次再教育预警天数
     */
    @Value("${warn-timer.check-org.re-education-days}")
    private Integer reEducationDays;

    @Autowired
    private TsSimpleCodeService simpleCodeService;
    @Autowired
    private TbZwOrgWarnConfigService configService;
    @Autowired
    private TdZwTjorginfoService tjOrgInfoService;
    @Autowired
    private TdZwOrgWarnMainService orgWarnMainService;
    @Autowired
    private CheckOrgWarnService checkOrgWarnService;

    Cache<String, Map<Integer, Map<Integer, Map<Integer, TbZwOrgWarnConfig>>>> configMapCache = CacheUtil.newFIFOCache(1);
    Cache<String, Map<String, TsSimpleCode>> warnExceptMapCache = CacheUtil.newFIFOCache(1);
    private Map<Integer, Map<Integer, Map<Integer, TbZwOrgWarnConfig>>> configMap;
    private Map<String, TsSimpleCode> warnExceptMap;

    @Scheduled(cron = "${warn-timer.check-org.cron}")
    public void start() {
        long startTimeMillis = System.currentTimeMillis();
        long nowTimeMillis, lastTimeMillis;
        log.info("定时预警资质机构工具-开始");
        String exceptionError = "定时预警资质机构工具-异常中止，请检查！";

        // 校验配置参数格式
        boolean check = true;
        if (this.icpDays == null) {
            log.error("定时预警资质机构工具-未配置备案预警天数(warn-timer.check-org.icp-days)！");
            check = false;
        }
        if (this.reEducationDays == null) {
            log.error("定时预警资质机构工具-未配置下次再教育预警天数(warn-timer.check-org.re-education-days)！");
            check = false;
        }
        if (this.instDays == null) {
            log.error("定时预警资质机构工具-未配置设备预警天数(warn-timer.check-org.inst-days)！");
            check = false;
        }
        if (!check) {
            log.info(exceptionError);
            return;
        }

        // 查询预警异常码表
        try {
            this.warnExceptMap = getWarnExceptMapByCache();
        } catch (Exception e) {
            log.info(exceptionError);
            return;
        }
        Integer newSimpleCodeId = this.warnExceptMap.get("1").getRid();

        // 从缓存中加载预警配置 映射结构为：{预警类型映射} -> {备案类型映射} -> {岗位/设备类型映射} -> {预警配置}
        try {
            this.configMap = getConfigMapByCache();
        } catch (Exception e) {
            log.info(exceptionError);
            return;
        }

        // 查询 检查机构及机构所有备案类型
        Date dateTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(new Date(), -this.icpDays), "yyyy-MM-dd"), "yyyy-MM-dd");
        List<CheckOrgWarnBO> orgWarnBOList;
        try {
            lastTimeMillis = System.currentTimeMillis();
            orgWarnBOList = this.tjOrgInfoService.selectOrgWarnInfoList(dateTime, newSimpleCodeId);
            nowTimeMillis = System.currentTimeMillis();
            log.info("定时预警资质机构工具-查询检查机构及机构所有备案类型, 共耗时: {}毫秒", nowTimeMillis - lastTimeMillis);
        } catch (Exception e) {
            log.info(exceptionError);
            log.error("定时预警资质机构工具-查询检查机构及机构所有备案类型失败", e);
            return;
        }

        if (ObjectUtils.isEmpty(orgWarnBOList)) {
            log.info("定时预警资质机构工具-已完成, 共处理0家机构, 共耗时: {}毫秒", nowTimeMillis - startTimeMillis);
            return;
        }

        // 遍历检查机构进行预警处理
        List<Integer> orgIdList = new ArrayList<>();
        for (CheckOrgWarnBO checkOrgWarnBO : orgWarnBOList) {
            orgIdList.add(checkOrgWarnBO.getRid());
        }
        // 查询资质机构预警主表
        Map<String, TdZwOrgWarnMain> orgWarnMainMap;
        try {
            lastTimeMillis = System.currentTimeMillis();
            orgWarnMainMap = this.orgWarnMainService.findCheckOrgWarnMainMapByOrgId(orgIdList);
            nowTimeMillis = System.currentTimeMillis();
            log.info("定时预警资质机构工具-查询所有资质机构预警主表, 共耗时: {}毫秒", nowTimeMillis - lastTimeMillis);
        } catch (Exception e) {
            log.info(exceptionError);
            log.error("定时预警资质机构工具-查询所有资质机构预警主表失败", e);
            return;
        }

        //查询资质机构设备key:机构rid value:(key:设备类型;value:设备信息)
        Map<Integer, Map<Integer, List<InstInfoWarnBO>>> instInfoWarnBOMap = this.checkOrgWarnService.findInstInfoWarnBOByOrgId(orgIdList);
        for (CheckOrgWarnBO checkOrgWarnBO : orgWarnBOList) {
            String orgId = StringUtils.objectToString(checkOrgWarnBO.getRid());
            TdZwOrgWarnMain orgWarnMain = orgWarnMainMap.get(orgId);
            if (ObjectUtils.isEmpty(orgWarnMain)) {
                orgWarnMain = new TdZwOrgWarnMain();
                orgWarnMain.setBusType("0");
                orgWarnMain.setBusId(orgId);
                orgWarnMainMap.put(orgId, orgWarnMain);
            }
            //预警时间：当前时间
            orgWarnMain.setWarnDate(new Date());
            // 初始化异常信息
            orgWarnMain.setOrgWarnExceptList(new ArrayList<>());
            try {
                dealCheckOrgWarn(checkOrgWarnBO, orgWarnMain, instInfoWarnBOMap.get(checkOrgWarnBO.getRid()));
            } catch (Exception e) {
                log.info("定时预警资质机构工具-处理机构(rid: {})信息异常，请检查！", orgId);
            }
        }

        nowTimeMillis = System.currentTimeMillis();
        log.info("定时预警资质机构工具-已完成, 共处理{}家机构, 共耗时: {}毫秒", orgWarnBOList.size(), nowTimeMillis - startTimeMillis);
    }

    /**
     * 通过缓存获取预警异常码表。
     * 如果缓存中不存在“warnExceptMap”，则尝试从源数据中封装并存入缓存。
     *
     * @return 配置映射表，映射结构为：{码表拓展字段1} -> {码表}。
     */
    public Map<String, TsSimpleCode> getWarnExceptMapByCache() {
        // 检查缓存中是否已经有“warnExceptMap”，如果没有则进行封装并存入缓存
        if (!ObjectUtils.isEmpty(this.warnExceptMapCache.get("warnExceptMap"))) {
            return this.warnExceptMapCache.get("warnExceptMap");
        }
        Map<String, TsSimpleCode> warnExceptMap;
        List<TsSimpleCode> warnExceptList = this.simpleCodeService.findTsSimpleCodeList("5623");
        if (ObjectUtils.isEmpty(warnExceptList)) {
            log.error("定时预警资质机构工具-未配置预警异常码表(5623)！");
            throw new RuntimeException();
        }
        warnExceptMap = warnExceptList.stream()
                .filter(tsSimpleCode -> StringUtils.isNotBlank(tsSimpleCode.getExtends1()))
                .collect(Collectors.toMap(TsSimpleCode::getExtends1, Function.identity()));
        this.warnExceptMapCache.put("warnExceptMap", warnExceptMap);
        // 返回缓存中的“warnExceptMap”，无论是否是新封装的
        return this.warnExceptMapCache.get("warnExceptMap");
    }

    /**
     * 通过缓存获取配置映射表。
     * 如果缓存中不存在“configMap”，则尝试从源数据中封装并存入缓存。
     *
     * @return 配置映射表，其中包含三级映射关系，分别代表不同的配置维度。
     * 映射结构为：{预警类型映射} -> {备案类型映射} -> {岗位/设备类型映射} -> {预警配置}
     */
    public Map<Integer, Map<Integer, Map<Integer, TbZwOrgWarnConfig>>> getConfigMapByCache() {
        // 检查缓存中是否已经有“configMap”，如果没有则进行封装并存入缓存
        if (!ObjectUtils.isEmpty(this.configMapCache.get("configMap"))) {
            return this.configMapCache.get("configMap");
        }
        try {
            long startTimeMillis = System.currentTimeMillis();
            this.configMapCache.put("configMap", pakConfigMap());
            long nowTimeMillis = System.currentTimeMillis();
            log.info("定时预警资质机构工具-初始化预警基础配置, 共耗时: {}毫秒", nowTimeMillis - startTimeMillis);
        } catch (Exception e) {
            // 如果在封装配置过程中出现异常，记录错误日志
            log.error("定时预警资质机构工具-初始化预警基础配置失败", e);
            throw new RuntimeException();
        }
        // 返回缓存中的“configMap”，无论是否是新封装的
        return this.configMapCache.get("configMap");
    }

    /**
     * 将预警配置列表转换为多层映射结构，方便根据类型和ID快速查询预警配置。
     * 映射结构为：{预警类型映射} -> {备案类型映射} -> {岗位/设备类型映射} -> {预警配置}
     * 其中，人员类型映射和设备类型映射分别对应于预警类型的1和2。
     *
     * @return 多层映射结构，包含所有预警配置。
     */
    public Map<Integer, Map<Integer, Map<Integer, TbZwOrgWarnConfig>>> pakConfigMap() {
        List<TbZwOrgWarnConfig> configList = this.configService.dataAndSubList(0);
        Map<Integer, Map<Integer, Map<Integer, TbZwOrgWarnConfig>>> configMap = new HashMap<>();
        configMap.put(1, new HashMap<>());
        configMap.put(2, new HashMap<>());
        for (TbZwOrgWarnConfig warnConfig : configList) {
            // 1：人员基本要求预警；2：人员特殊要求预警；3：仪器基本要求预警；4：仪器特殊要求预警
            String warnType = StringUtils.objectToString(warnConfig.getWarnType());
            // 1 -> 人员;2 -> 设备
            int key1;
            // 备案类型 ITEM_ID(-1代表基本要求预警)
            Integer key2;
            // 岗位/设备类型 POST_ID/INST_TYPE_ID
            Integer key3;
            switch (warnType) {
                case "1":
                    boolean judgmentOfNullity1 = ObjectUtils.isEmpty(warnConfig.getFkByPostId())
                            || ObjectUtils.isEmpty(warnConfig.getFkByPostId().getRid());
                    if (judgmentOfNullity1) {
                        continue;
                    }
                    key1 = 1;
                    key2 = -1;
                    key3 = warnConfig.getFkByPostId().getRid();
                    break;
                case "2":
                    boolean judgmentOfNullity2 = ObjectUtils.isEmpty(warnConfig.getFkByItemId())
                            || ObjectUtils.isEmpty(warnConfig.getFkByItemId().getRid())
                            || ObjectUtils.isEmpty(warnConfig.getWarnTypeId());
                    if (judgmentOfNullity2) {
                        continue;
                    }
                    key1 = 1;
                    key2 = warnConfig.getFkByItemId().getRid();
                    key3 = warnConfig.getWarnTypeId();
                    break;
                case "3":
                    boolean judgmentOfNullity3 = ObjectUtils.isEmpty(warnConfig.getFkByInstTypeId())
                            || ObjectUtils.isEmpty(warnConfig.getFkByInstTypeId().getRid());
                    if (judgmentOfNullity3) {
                        continue;
                    }
                    key1 = 2;
                    key2 = -1;
                    key3 = warnConfig.getFkByInstTypeId().getRid();
                    break;
                case "4":
                    boolean judgmentOfNullity4 = ObjectUtils.isEmpty(warnConfig.getFkByItemId())
                            || ObjectUtils.isEmpty(warnConfig.getFkByItemId().getRid())
                            || ObjectUtils.isEmpty(warnConfig.getWarnTypeId());
                    if (judgmentOfNullity4) {
                        continue;
                    }
                    key1 = 2;
                    key2 = warnConfig.getFkByItemId().getRid();
                    key3 = warnConfig.getWarnTypeId();
                    break;
                default:
                    continue;
            }
            if (!configMap.get(key1).containsKey(key2)) {
                configMap.get(key1).put(key2, new HashMap<>());
            }
            warnConfig.setWarnTypeId(key3);
            configMap.get(key1).get(key2).put(key3, warnConfig);
        }
        if (!configMap.get(1).containsKey(-1)) {
            configMap.get(1).put(-1, new HashMap<>());
        }
        if (!configMap.get(2).containsKey(-1)) {
            configMap.get(2).put(-1, new HashMap<>());
        }
        return configMap;
    }

    /**
     * 处理职业健康检查机构预警
     */
    public void dealCheckOrgWarn(CheckOrgWarnBO checkOrgWarnBO, TdZwOrgWarnMain orgWarnMain,
                                 Map<Integer, List<InstInfoWarnBO>> instInfoMap) {
        // 备案预警
        try {
            this.icpWarning(checkOrgWarnBO, orgWarnMain);
        } catch (Exception e) {
            log.error("定时预警资质机构工具-备案预警处理异常", e);
            throw new RuntimeException(e);
        }
        // 人员预警
        try {
            this.psnWarning(checkOrgWarnBO, orgWarnMain);
        } catch (Exception e) {
            log.error("定时预警资质机构工具-人员预警处理异常", e);
            throw new RuntimeException(e);
        }
        // 设备配置要求预警
        try {
            this.instWarning(checkOrgWarnBO, orgWarnMain, instInfoMap);
        } catch (Exception e) {
            log.error("定时预警资质机构工具-设备配置要求预警处理异常", e);
            throw new RuntimeException(e);
        }
        // 设备检定要求预警
        try {
            this.testInstWarning(orgWarnMain, instInfoMap);
        } catch (Exception e) {
            log.error("定时预警资质机构工具-设备检定要求预警处理异常", e);
            throw new RuntimeException(e);
        }
        // 新增/更新预警主表+删除历史异常信息表及子表(+插入异常信息表及子表)
        try {
            long startTimeMillis = System.currentTimeMillis();
            this.checkOrgWarnService.saveOrUpdate(orgWarnMain);
            long nowTimeMillis = System.currentTimeMillis();
            log.info("定时预警资质机构工具-预警信息存储, 共耗时: {}毫秒", nowTimeMillis - startTimeMillis);
        } catch (Exception e) {
            log.error("定时预警资质机构工具-预警信息存储异常", e);
            throw new RuntimeException();
        }
    }


    /**
     * <p>方法描述：备案预警
     * 1：当前日期-备案日期【FIRST_GETDAY】<= 配置时间预警
     * 2：备案续展时间-当前日期（yyyy-MM-dd）<= 配置时间预警
     * </p>
     *
     * @MethodAuthor hsj 2024-07-12 15:13
     */
    public void icpWarning(CheckOrgWarnBO checkOrgWarnBO, TdZwOrgWarnMain orgWarnMain) {
        Date firstGetday = checkOrgWarnBO.getFirstGetday();
        Integer filingYear = checkOrgWarnBO.getFilingYear();
        if (ObjectUtils.isNull(firstGetday) || ObjectUtils.isNull(filingYear)) {
            return;
        }
        TdZwOrgWarnExcept except = new TdZwOrgWarnExcept();
        except.setFkByMainId(orgWarnMain);
        except.setFilingDate(firstGetday);
        except.setFilingYear(filingYear);
        //备案续展时间= 备案日期【FIRST_GETDAY】+备案有效期（年）【FILING_YEAR】
        Date nextDate = DateUtil.offset(firstGetday, DateField.YEAR, filingYear);
        except.setNextDate(nextDate);
        if (this.warnExceptMap.containsKey(WarnExceptEnums.FILING_RENEWAL.getExtents1())) {
            Date endDate = DateUtil.parse(DateUtil.format(nextDate, "yyyy-MM-dd"), "yyyy-MM-dd");
            Date startDate = DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
            double betweenDay = DateUtils.getDistanceOfTwoDate(startDate, endDate);
            if (betweenDay <= this.icpDays) {
                TdZwOrgWarnExcept except1 = new TdZwOrgWarnExcept();
                BeanUtil.copyProperties(except, except1);
                except1.setFkByExceptId(this.warnExceptMap.get(WarnExceptEnums.FILING_RENEWAL.getExtents1()));
                orgWarnMain.getOrgWarnExceptList().add(except1);
            }
        }
        Integer ifFilingFirst = checkOrgWarnBO.getIfFilingFirst();
        boolean f = !new Integer(1).equals(ifFilingFirst) || !this.warnExceptMap.containsKey(WarnExceptEnums.FILING_NEW.getExtents1());
        if (f) {
            return;
        }
        //当前日期-备案日期【FIRST_GETDAY】
        Date startDate = DateUtil.parse(DateUtil.format(firstGetday, "yyyy-MM-dd"), "yyyy-MM-dd");
        Date endDate = DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
        double betweenDay = DateUtils.getDistanceOfTwoDate(startDate, endDate);
        if (betweenDay <= this.icpDays) {
            TdZwOrgWarnExcept except2 = new TdZwOrgWarnExcept();
            BeanUtil.copyProperties(except, except2);
            except2.setFkByExceptId(this.warnExceptMap.get(WarnExceptEnums.FILING_NEW.getExtents1()));
            orgWarnMain.getOrgWarnExceptList().add(except2);
        }
    }

    /**
     * 人员预警
     *
     * @param checkOrgWarnBO 机构信息
     * @param orgWarnMain    预警主表
     */
    public void psnWarning(CheckOrgWarnBO checkOrgWarnBO, TdZwOrgWarnMain orgWarnMain) {
        Map<Integer, Map<Integer, TbZwOrgWarnConfig>> psnConfigMap = this.configMap.get(1);
        // 查询封装机构人员信息
        Map<Integer, List<CheckOrgWarnPsnBO>> warnPsnBOMap = getWarnPsnBOMap(orgWarnMain.getBusId());
        // 各异常信息对应预警明细Map key: 岗位id;value: 预警明细实体
        Map<Integer, TdZwOrgWarnDetail> psnRequirementsMap = new HashMap<>();
        Map<Integer, TdZwOrgWarnDetail> psnReEducationMap = new HashMap<>();
        Map<Integer, TdZwOrgWarnDetail> psnTitleLevelMap = new HashMap<>();
        Map<Integer, TdZwOrgWarnDetail> psnInMultipleLocationsMap = new HashMap<>();
        for (Map.Entry<Integer, TbZwOrgWarnConfig> e : psnConfigMap.get(-1).entrySet()) {
            Integer postId = e.getKey();
            TbZwOrgWarnConfig config = e.getValue();
            boolean hasPsn = warnPsnBOMap.containsKey(postId) && CollectionUtils.isNotEmpty(warnPsnBOMap.get(postId));
            List<CheckOrgWarnPsnBO> postWarnPsnBOList = warnPsnBOMap.get(postId);
            // 人数要求
            dealPsnRequirementsWarn(hasPsn, postWarnPsnBOList, postId, config, psnRequirementsMap);
            // 该岗位没有人，其他异常信息无需预警
            if (!hasPsn) {
                continue;
            }
            // 培训周期
            if (config.getTrainYear() != null) {
                dealPsnReEducationWarn(postWarnPsnBOList, postId, config, psnReEducationMap);
            }
            // 职称级别
            if (config.getFkByTitleId() != null && StringUtils.isNotBlank(config.getFkByTitleId().getExtends2())) {
                dealPsnInMultipleLocationsWarn(postWarnPsnBOList, postId, config, psnTitleLevelMap);
            }
            // 执业机构上限
            if (config.getOrgNums() != null) {
                dealPsnTitleLevelWarn(postWarnPsnBOList, postId, config, psnInMultipleLocationsMap);
            }
        }
        // 备案类别-人数要求
        List<Integer> itemList = checkOrgWarnBO.getItemList();
        if (CollectionUtils.isEmpty(itemList)) {
            itemList = new ArrayList<>();
        }
        itemList.stream()
                .filter(i -> psnConfigMap.get(i) != null)
                .forEach(i -> psnConfigMap.get(i).forEach((postId, config) -> {
                    boolean hasPsn = warnPsnBOMap.containsKey(postId);
                    List<CheckOrgWarnPsnBO> postWarnPsnBOList = warnPsnBOMap.get(postId);
                    dealPsnRequirementsWarn(hasPsn, postWarnPsnBOList, postId, config, psnRequirementsMap);
                }));
        // 预警实体封装
        pakWarnExcept(orgWarnMain, psnRequirementsMap, WarnExceptEnums.PSN_REQUIREMENTS);
        pakWarnExcept(orgWarnMain, psnReEducationMap, WarnExceptEnums.PSN_RE_EDUCATION);
        pakWarnExcept(orgWarnMain, psnInMultipleLocationsMap, WarnExceptEnums.PSN_IN_MULTIPLE_LOCATIONS);
        pakWarnExcept(orgWarnMain, psnTitleLevelMap, WarnExceptEnums.PSN_TITLE_LEVEL);
    }

    /**
     * 查询封装机构人员信息Map
     *
     * @param orgId 机构资质ID
     * @return 机构人员信息
     */
    private Map<Integer, List<CheckOrgWarnPsnBO>> getWarnPsnBOMap(String orgId) {
        Map<Integer, List<CheckOrgWarnPsnBO>> warnPsnBOMap = new HashMap<>();
        List<CheckOrgWarnPsnBO> warnPsnBOList = this.orgWarnMainService.selectCheckOrgWarnPsnListByOrgId(orgId);
        warnPsnBOList.forEach(warnPsnBO -> {
            Integer postId = warnPsnBO.getPostId();
            if (ObjectUtils.isEmpty(postId)) {
                return;
            }
            if (!warnPsnBOMap.containsKey(postId)) {
                warnPsnBOMap.put(postId, new ArrayList<>());
            }
            warnPsnBOMap.get(postId).add(warnPsnBO);
        });
        return warnPsnBOMap;
    }

    /**
     * 人员预警-人数要求处理
     *
     * @param hasPsn            该岗位是否有人
     * @param postWarnPsnBOList 人员列表
     * @param postId            岗位id
     * @param config            预警配置
     * @param psnWarnDetailMap  人数要求预警详情
     */
    private void dealPsnRequirementsWarn(boolean hasPsn, List<CheckOrgWarnPsnBO> postWarnPsnBOList,
                                         Integer postId, TbZwOrgWarnConfig config,
                                         Map<Integer, TdZwOrgWarnDetail> psnWarnDetailMap) {
        if (!this.warnExceptMap.containsKey(WarnExceptEnums.PSN_REQUIREMENTS.getExtents1())) {
            return;
        }
        // 人数要求
        Integer minNums = config.getMinNums();
        if (minNums == null || minNums <= 0) {
            return;
        }
        // 该岗位没有人，只需预警人数要求
        if (!hasPsn) {
            pakPsnRequirementsWarnDetail(psnWarnDetailMap, postId, config, 0);
            return;
        }
        // 该岗位人数小于配置人数，进行人数要求预警
        if (minNums > postWarnPsnBOList.size()) {
            pakPsnRequirementsWarnDetail(psnWarnDetailMap, postId, config, postWarnPsnBOList.size());
        }
    }

    /**
     * 封装人员人数要求预警详情
     *
     * @param psnWarnDetailMap 人数要求预警详情
     * @param postId           岗位
     * @param config           预警配置
     * @param num              实际岗位人数
     */
    private void pakPsnRequirementsWarnDetail(Map<Integer, TdZwOrgWarnDetail> psnWarnDetailMap,
                                              Integer postId, TbZwOrgWarnConfig config, int num) {
        Integer minNums = config.getMinNums();
        if (minNums == null || minNums <= 0) {
            return;
        }
        if (!psnWarnDetailMap.containsKey(postId)) {
            TdZwOrgWarnDetail warnDetail = new TdZwOrgWarnDetail();
            warnDetail.setFkByWarnId(config);
            warnDetail.setFkByWarnTypeId(new TsSimpleCode(postId));
            warnDetail.setPsnNums(num);
            psnWarnDetailMap.put(postId, warnDetail);
        }
        TdZwOrgWarnDetail warnDetail = psnWarnDetailMap.get(postId);
        if (config.getMinNums() <= warnDetail.getFkByWarnId().getMinNums()) {
            return;
        }
        // 取配置最大的
        warnDetail.setFkByWarnId(config);
    }

    /**
     * 人员预警-人员培训周期预警处理
     *
     * @param postWarnPsnBOList 人员列表
     * @param postId            岗位
     * @param config            预警配置
     * @param psnWarnDetailMap  培训周期预警详情
     */
    private void dealPsnReEducationWarn(List<CheckOrgWarnPsnBO> postWarnPsnBOList,
                                        Integer postId, TbZwOrgWarnConfig config,
                                        Map<Integer, TdZwOrgWarnDetail> psnWarnDetailMap) {
        if (!this.warnExceptMap.containsKey(WarnExceptEnums.PSN_RE_EDUCATION.getExtents1())) {
            return;
        }
        Integer trainYear = config.getTrainYear();
        // 当前日期+再教育预警天数
        DateTime dateTime = DateUtil.offsetDay(new Date(), this.reEducationDays);
        for (CheckOrgWarnPsnBO warnPsnBO : postWarnPsnBOList) {
            Date sendDate = warnPsnBO.getSendDate();
            // 发证日期为空跳过
            if (sendDate == null) {
                continue;
            }
            // 下次再教育时间小于等于当前日期+再教育预警天数，则需要预警
            Date nextReEducationDate = DateUtil.offset(sendDate, DateField.YEAR, trainYear);
            if (nextReEducationDate.compareTo(dateTime) > 0) {
                continue;
            }
            Integer configId = config.getRid();
            Integer psnId = warnPsnBO.getPsnId();
            pakPsnReEducationWarnDetail(psnWarnDetailMap, configId, postId, psnId, sendDate, nextReEducationDate);
        }
    }

    /**
     * 封装人员培训周期预警详情
     *
     * @param psnWarnDetailMap 培训周期预警详情
     * @param configId         预警配置ID
     * @param postId           岗位ID
     * @param psnId            人员ID
     * @param currDate         发证日期
     * @param nextDate         再教育日期
     */
    private void pakPsnReEducationWarnDetail(Map<Integer, TdZwOrgWarnDetail> psnWarnDetailMap,
                                             Integer configId, Integer postId, Integer psnId,
                                             Date currDate, Date nextDate) {
        if (currDate == null || nextDate == null) {
            return;
        }
        TdZwOrgWarnPsns warnPsn = pakWarnPsns(psnWarnDetailMap, configId, postId, psnId);
        warnPsn.setCurrDate(currDate);
        warnPsn.setNextDate(nextDate);
    }

    /**
     * 人员预警-职称级别预警处理
     *
     * @param postWarnPsnBOList 人员列表
     * @param postId            岗位
     * @param config            预警配置
     * @param psnWarnDetailMap  职称级别预警详情
     */
    private void dealPsnInMultipleLocationsWarn(List<CheckOrgWarnPsnBO> postWarnPsnBOList,
                                                Integer postId, TbZwOrgWarnConfig config,
                                                Map<Integer, TdZwOrgWarnDetail> psnWarnDetailMap) {
        if (!this.warnExceptMap.containsKey(WarnExceptEnums.PSN_TITLE_LEVEL.getExtents1())) {
            return;
        }
        String extends2 = config.getFkByTitleId().getExtends2();
        Integer level = ObjectUtils.convert(Integer.class, extends2);
        if (level == null) {
            return;
        }
        for (CheckOrgWarnPsnBO warnPsnBO : postWarnPsnBOList) {
            Integer psnTitleLevelExtends2 = warnPsnBO.getTitleLevelExtends2();
            if (psnTitleLevelExtends2 != null && psnTitleLevelExtends2 >= level) {
                continue;
            }
            // 人员职称级别为空或小于配置的级别，则需要预警
            pakPsnInMultipleLocationsWarnDetail(
                    psnWarnDetailMap, config.getRid(), postId, warnPsnBO.getPsnId(), warnPsnBO.getTitleLevelId()
            );
        }

    }

    /**
     * 封装人员职称级别预警详情
     *
     * @param psnWarnDetailMap 职称级别预警详情
     * @param configId         预警配置ID
     * @param postId           岗位ID
     * @param psnId            人员ID
     * @param titleLevelId     职称级别ID
     */
    private void pakPsnInMultipleLocationsWarnDetail(Map<Integer, TdZwOrgWarnDetail> psnWarnDetailMap,
                                                     Integer configId, Integer postId, Integer psnId,
                                                     Integer titleLevelId) {
        TdZwOrgWarnPsns warnPsn = pakWarnPsns(psnWarnDetailMap, configId, postId, psnId);
        if (titleLevelId != null) {
            warnPsn.setFkByTitleLevelId(new TsSimpleCode(titleLevelId));
        }
    }

    /**
     * 人员预警-执业机构上限预警处理
     *
     * @param postWarnPsnBOList 人员列表
     * @param postId            岗位
     * @param config            预警配置
     * @param psnWarnDetailMap  执业机构上限预警详情
     */
    private void dealPsnTitleLevelWarn(List<CheckOrgWarnPsnBO> postWarnPsnBOList,
                                       Integer postId, TbZwOrgWarnConfig config,
                                       Map<Integer, TdZwOrgWarnDetail> psnWarnDetailMap) {
        if (!this.warnExceptMap.containsKey(WarnExceptEnums.PSN_IN_MULTIPLE_LOCATIONS.getExtents1())) {
            return;
        }
        Integer orgNums = config.getOrgNums();
        if (orgNums == null) {
            return;
        }
        for (CheckOrgWarnPsnBO warnPsnBO : postWarnPsnBOList) {
            List<Integer> orgIdList = warnPsnBO.getOrgIdList();
            if (CollectionUtils.isEmpty(orgIdList)) {
                continue;
            }
            if (orgIdList.size() <= orgNums) {
                continue;
            }
            // 人员多点执业数大于配置的执业机构上限，则需要预警
            pakPsnTitleLevelWarnDetail(
                    psnWarnDetailMap, config.getRid(), postId, warnPsnBO.getPsnId(), orgIdList
            );
        }
    }

    /**
     * 封装人员执业机构上限预警详情
     *
     * @param psnWarnDetailMap 执业机构上限
     * @param configId         预警配置ID
     * @param postId           岗位ID
     * @param psnId            人员ID
     * @param orgIdList        职称级别ID
     */
    private void pakPsnTitleLevelWarnDetail(Map<Integer, TdZwOrgWarnDetail> psnWarnDetailMap,
                                            Integer configId, Integer postId, Integer psnId,
                                            List<Integer> orgIdList) {
        if (CollectionUtils.isEmpty(orgIdList)) {
            return;
        }
        TdZwOrgWarnPsns warnPsn = pakWarnPsns(psnWarnDetailMap, configId, postId, psnId);
        warnPsn.setWarnPsnOrgList(new ArrayList<>());
        for (Integer orgId : orgIdList) {
            TdZwWarnPsnOrg warnPsnOrg = new TdZwWarnPsnOrg();
            warnPsn.getWarnPsnOrgList().add(warnPsnOrg);
            warnPsnOrg.setFkByMainId(warnPsn);
            warnPsnOrg.setFkByOrgId(new TsUnit(orgId));
        }
    }

    /**
     * 封装预警人员表
     *
     * @param psnWarnDetailMap 预警详情
     * @param configId         预警配置ID
     * @param postId           岗位ID
     * @param psnId            人员ID
     */
    private TdZwOrgWarnPsns pakWarnPsns(Map<Integer, TdZwOrgWarnDetail> psnWarnDetailMap,
                                        Integer configId, Integer postId, Integer psnId) {
        if (!psnWarnDetailMap.containsKey(postId)) {
            TdZwOrgWarnDetail warnDetail = new TdZwOrgWarnDetail();
            warnDetail.setFkByWarnId(new TbZwOrgWarnConfig(configId));
            warnDetail.setFkByWarnTypeId(new TsSimpleCode(postId));
            warnDetail.setOrgWarnPsnsList(new ArrayList<>());
            psnWarnDetailMap.put(postId, warnDetail);
        }
        TdZwOrgWarnDetail warnDetail = psnWarnDetailMap.get(postId);
        TdZwOrgWarnPsns warnPsn = new TdZwOrgWarnPsns();
        warnDetail.getOrgWarnPsnsList().add(warnPsn);
        warnPsn.setFkByMainId(warnDetail);
        warnPsn.setPsnId(psnId);
        return warnPsn;
    }

    /**
     * 封装预警异常信息实体
     *
     * @param orgWarnMain      预警主表
     * @param psnWarnDetailMap 人员预警信息
     * @param exceptEnums      异常信息类型
     */
    private void pakWarnExcept(TdZwOrgWarnMain orgWarnMain, Map<Integer, TdZwOrgWarnDetail> psnWarnDetailMap,
                               WarnExceptEnums exceptEnums) {
        if (ObjectUtils.isEmpty(psnWarnDetailMap)) {
            return;
        }
        TdZwOrgWarnExcept warnExcept = new TdZwOrgWarnExcept();
        orgWarnMain.getOrgWarnExceptList().add(warnExcept);
        warnExcept.setFkByMainId(orgWarnMain);
        Integer exceptId = this.warnExceptMap.get(exceptEnums.getExtents1()).getRid();
        warnExcept.setFkByExceptId(new TsSimpleCode(exceptId));
        warnExcept.setDetailList(new ArrayList<>());
        psnWarnDetailMap.forEach((k, v) -> {
            v.setFkByMainId(warnExcept);
            warnExcept.getDetailList().add(v);
        });
    }

    /**
     * <p>方法描述：设备配置要求预警</p>
     *
     * @MethodAuthor hsj 2024-07-13 9:49
     */
    public void instWarning(CheckOrgWarnBO checkOrgWarnBO, TdZwOrgWarnMain orgWarnMain, Map<Integer, List<InstInfoWarnBO>> instInfoMap) {
        if (!this.warnExceptMap.containsKey(WarnExceptEnums.INST_REQUIREMENTS.getExtents1())) {
            return;
        }
        //key:设备类型 value:设备设置（最大值）
        Map<Integer, TbZwOrgWarnConfig> warnConfigMap = dealinstWarnConfigMap(checkOrgWarnBO.getItemList());
        if (null == warnConfigMap || warnConfigMap.size() == 0) {
            return;
        }
        TdZwOrgWarnExcept except = new TdZwOrgWarnExcept();
        except.setFkByMainId(orgWarnMain);
        except.setFkByExceptId(this.warnExceptMap.get(WarnExceptEnums.INST_REQUIREMENTS.getExtents1()));
        except.setDetailList(new ArrayList<>());
        warnConfigMap.forEach((k, v) -> {
            Integer minNums = v.getMinNums();
            TdZwOrgWarnDetail detail = new TdZwOrgWarnDetail();
            detail.setFkByMainId(except);
            detail.setFkByWarnId(v);
            detail.setFkByWarnTypeId(new TsSimpleCode(k));
            boolean flag = null == instInfoMap || instInfoMap.isEmpty() || !instInfoMap.containsKey(k);
            Integer size = flag ? 0 : instInfoMap.get(k).size();
            if (size < minNums) {
                detail.setPsnNums(size);
                except.getDetailList().add(detail);
            }
        });
        if (CollectionUtils.isEmpty(except.getDetailList())) {
            return;
        }
        orgWarnMain.getOrgWarnExceptList().add(except);
    }

    /**
     * <p>方法描述：设备配置要求预警处理</p>
     *
     * @MethodAuthor hsj 2024-07-13 9:59
     */
    private Map<Integer, TbZwOrgWarnConfig> dealinstWarnConfigMap(List<Integer> itemList) {
        Map<Integer, TbZwOrgWarnConfig> map = new HashMap<>();
        Map<Integer, Map<Integer, TbZwOrgWarnConfig>> allWarnConfigMap = this.configMapCache.get("configMap").get(2);
        if (null == allWarnConfigMap || allWarnConfigMap.isEmpty()) {
            return map;
        }
        List<TbZwOrgWarnConfig> configList = new ArrayList<>();
        if (allWarnConfigMap.containsKey(-1)) {
            configList.addAll(allWarnConfigMap.get(-1).values().stream().collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(itemList)) {
            itemList.forEach(item -> {
                if (allWarnConfigMap.containsKey(item)) {
                    configList.addAll(allWarnConfigMap.get(item).values().stream().collect(Collectors.toList()));
                }
            });
        }
        if (CollectionUtils.isEmpty(configList)) {
            return map;
        }
        map = configList.stream().collect(Collectors.toMap(
                config -> config.getWarnTypeId(),
                Function.identity(),
                BinaryOperator.maxBy(Comparator.comparingInt(TbZwOrgWarnConfig::getMinNums))
        ));
        return map;
    }

    /**
     * <p>方法描述：设备检定要求预警</p>
     *
     * @MethodAuthor hsj 2024-07-13 11:25
     */
    private void testInstWarning(TdZwOrgWarnMain orgWarnMain, Map<Integer, List<InstInfoWarnBO>> instInfoMap) {
        if (!this.warnExceptMap.containsKey(WarnExceptEnums.INST_VERIFICATION.getExtents1())) {
            return;
        }
        List<TbZwOrgWarnConfig> configList = dealTestInstWarnConfig();
        if (CollectionUtils.isEmpty(configList)) {
            return;
        }
        TdZwOrgWarnExcept except = new TdZwOrgWarnExcept();
        except.setFkByMainId(orgWarnMain);
        except.setFkByExceptId(this.warnExceptMap.get(WarnExceptEnums.INST_VERIFICATION.getExtents1()));
        except.setDetailList(new ArrayList<>());
        configList.forEach(o -> {
            TdZwOrgWarnDetail detail = new TdZwOrgWarnDetail();
            detail.setFkByMainId(except);
            detail.setFkByWarnId(o);
            detail.setFkByWarnTypeId(o.getFkByInstTypeId());
            detail.setOrgWarnInstList(new ArrayList<>());
            Integer key = o.getFkByInstTypeId().getRid();
            boolean flag = instInfoMap != null && !instInfoMap.isEmpty() && instInfoMap.containsKey(key);
            List<InstInfoWarnBO> instInfos = flag ? instInfoMap.get(key) : Collections.emptyList();
            instInfos.parallelStream()
                    .filter(inst -> dealTestInstNextDate(inst) != null)
                    .map(inst -> {
                        Date nextDate = inst.getNextDate();
                        Date startDate = DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
                        double betweenDay = DateUtils.getDistanceOfTwoDate(startDate, nextDate);
                        return new AbstractMap.SimpleEntry<>(inst, betweenDay);
                    })
                    .filter(entry -> entry.getValue() <= this.instDays)
                    .forEach(entry -> {
                        TdZwOrgWarnInst warnInst = new TdZwOrgWarnInst();
                        warnInst.setFkByMainId(detail);
                        warnInst.setInstId(entry.getKey().getRid());
                        warnInst.setCurrDate2(entry.getKey().getLastAcptDate());
                        warnInst.setNextDate2(entry.getKey().getNextDate());
                        detail.getOrgWarnInstList().add(warnInst);
                    });

            if (!detail.getOrgWarnInstList().isEmpty()) {
                except.getDetailList().add(detail);
            }
        });
        if (CollectionUtils.isEmpty(except.getDetailList())) {
            return;
        }
        orgWarnMain.getOrgWarnExceptList().add(except);
    }

    /**
     * <p>方法描述：获取下次预警时间</p>
     *
     * @MethodAuthor hsj 2024-07-13 11:58
     */
    private Date dealTestInstNextDate(InstInfoWarnBO inst) {
        Date lastAcptDate = inst.getLastAcptDate();
        Double lastAcptCircleDot = inst.getLastAcptCircleDot();
        if (null == lastAcptDate || null == lastAcptCircleDot) {
            return null;
        }
        int intPart = (int) Math.floor(lastAcptCircleDot);
        double decimalPart = lastAcptCircleDot - (double) intPart;
        Date date = DateUtil.offset(lastAcptDate, DateField.YEAR, intPart);
        int roundedDays = (int) Math.ceil(decimalPart * 365);
        date = DateUtil.offset(date, DateField.DAY_OF_YEAR, roundedDays);
        inst.setNextDate(date);
        return date;
    }

    /**
     * <p>方法描述：获取设备检定要求预警配置</p>
     *
     * @MethodAuthor hsj 2024-07-13 11:30
     */
    private List<TbZwOrgWarnConfig> dealTestInstWarnConfig() {
        List<TbZwOrgWarnConfig> list = new ArrayList<>();
        Map<Integer, Map<Integer, TbZwOrgWarnConfig>> allWarnConfigMap = this.configMapCache.get("configMap").get(2);
        boolean flag = null == allWarnConfigMap || allWarnConfigMap.isEmpty() || !allWarnConfigMap.containsKey(-1);
        if (flag) {
            return list;
        }
        list.addAll(allWarnConfigMap.get(-1).values().stream()
                .filter(config -> "1".equals(config.getIfTestInst()))
                .collect(Collectors.toList()));
        return list;
    }
}

package com.chis.modules.timer.heth.logic.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GetPatientPdDataVo {
    /**
     * 患者姓名
     */
    private String username;
    /**
     * 患者唯一标识
     */
    private String patientid;
    /**
     * 每次评估唯一标识
     */
    private String assessmentid;
    /**
     * 评估开出时间戳
     */
    private Integer assessmentTime;
    /**
     * 展示评估总报告PDF的URL
     */
    private String assessment_url;
}

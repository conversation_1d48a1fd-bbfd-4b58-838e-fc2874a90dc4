package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-05-12,TdZwBadrsnItem
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_BADRSN_ITEM")
public class TdZwBadrsnItem extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwBadrsnStd fkByMainId;

    @TableField(value = "ITEM_ID" , el = "fkByItemId.rid")
    private TbTjItems fkByItemId;

    @TableField("RMK")
    private String rmk;


    public TdZwBadrsnItem(Integer rid) {
        super(rid);
    }


}

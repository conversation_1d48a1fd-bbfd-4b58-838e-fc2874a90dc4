package com.chis.modules.timer.heth.job;

import com.alibaba.fastjson.JSONObject;
import com.chis.comm.utils.*;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.enums.BadRsnCaffeineKeyEnum;
import com.chis.modules.timer.heth.logic.vo.*;
import com.chis.modules.timer.heth.service.TdZwcfPatientInfoService;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 调用陕西尘肺康复管理系统接口
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class SxcfJob {
    /**
     * 接口地址
     */
    @Value("${heth-timer.jl.apiRoot}")
    private String apiRoot;
    /**
     * 获取token接口
     */
    @Value("${heth-timer.jl.url.getToken}")
    private String getTokenUrl;
    /**
     * 获取患者档案
     */
    @Value("${heth-timer.jl.url.getPatient}")
    private String getPatientUrl;
    /**
     * 获取患者评估处方
     */
    @Value("${heth-timer.jl.url.getPatientPd}")
    private String getPatientPdUrl;
    /**
     * 获取患者康复处方
     */
    @Value("${heth-timer.jl.url.getPatientKffa}")
    private String getPatientKffaUrl;
    /**
     * 患者尘肺病康复记录
     */
    @Value("${heth-timer.jl.url.getCfbRecoveryRecord}")
    private String getCfbRecoveryRecordUrl;
    /**
     * 请求token需要的key
     */
    @Value("${heth-timer.jl.token.key}")
    private String tokenKey;
    /**
     * 请求token需要的secret
     */
    @Value("${heth-timer.jl.token.secret}")
    private String tokenSecret;
    /**
     * AES密钥
     */
    @Value("${heth-timer.jl.encrypt.key}")
    private String encryptKey;
    /**
     * 是否加密
     */
    @Value("${heth-timer.jl.encrypt.debug}")
    private String encryptDebug;

    @Autowired
    private TdZwcfPatientInfoService patientInfoService;

    @Scheduled(cron = "${heth-timer.sche-cron.sxcfCron1}")
    public void dealKfData() {
        Date date = new Date();
        log.info("定时重置状态为1、2的数据为0 任务启动 程序版本{} 当前时间{}", "1.0", date);
        try {
            int count = this.patientInfoService.updateSyncKfState();
            log.info("定时重置状态为1、2的数据为0任务完成 共处理{}条数据", count);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("定时重置状态为1、2的数据为0任务异常：{}", e.getMessage());
        }
        Date endDate = new Date();
        log.info("定时重置状态为1、2的数据为0任务完成，开始时间：{} 结束时间：{} 用时:{}ms", date, endDate, (endDate.getTime() - date.getTime()));
    }

    @Scheduled(cron = "${heth-timer.sche-cron.sxcfCron2}")
    public void start() {
        Date date = new Date();
        log.info("定时调用陕西尘肺康复管理系统接口 任务启动 程序版本{} 当前时间{}", "1.0", date);

        //处理无身份证人员
        int count1 = this.patientInfoService.updateSyncKfState3IdcNull(new Date(), "无身份证人员无需同步！");
        log.info("处理无身份证人员完成 共处理{}条数据", count1);

        StringBuilder errorBuffer = new StringBuilder();
        if (StringUtils.isBlank(this.apiRoot)) {
            errorBuffer.append("；").append("参数apiRoot为空");
        }
        if (StringUtils.isBlank(this.getTokenUrl)) {
            errorBuffer.append("；").append("参数getToken为空");
        }
        if (StringUtils.isBlank(this.getPatientUrl)) {
            errorBuffer.append("；").append("参数getPatient为空");
        }
        if (StringUtils.isBlank(this.getPatientPdUrl)) {
            errorBuffer.append("；").append("参数getPatientPd为空");
        }
        if (StringUtils.isBlank(this.getPatientKffaUrl)) {
            errorBuffer.append("；").append("参数getPatientKffa为空");
        }
        if (StringUtils.isBlank(this.getCfbRecoveryRecordUrl)) {
            errorBuffer.append("；").append("参数getCfbRecoveryRecord为空");
        }
        if (StringUtils.isBlank(this.tokenKey)) {
            errorBuffer.append("；").append("参数token.key为空");
        }
        if (StringUtils.isBlank(this.tokenSecret)) {
            errorBuffer.append("；").append("参数token.secret为空");
        }
        if (StringUtils.isBlank(this.encryptDebug) || !this.encryptDebug.trim().equals("true")) {
            if (StringUtils.isBlank(this.encryptKey)) {
                errorBuffer.append("；").append("参数encrypt.key为空");
            }
        }
        String errorStr = errorBuffer.toString();
        if (StringUtils.isNotBlank(errorStr)) {
            log.error(errorStr.substring(1));
            return;
        }
        String tokenId = "";
        List<TdZwcfPatientInfo> patientInfoList = this.patientInfoService.getPatientInfoListByState0();
        for (TdZwcfPatientInfo patientInfo : patientInfoList) {
            patientInfo.setSyncKfErrMsg("");
            tokenId = getToken();
            if (null == tokenId) {
                log.error("获取token失败！");
                return;
            }
            List<TdZwcfKfPg> patientPdList = new ArrayList<>();
            try {
                patientPdList = getPatientPd(tokenId, patientInfo);
            } catch (Exception e) {
                e.printStackTrace();
                patientInfo.setSyncKfErrMsg(patientInfo.getSyncKfErrMsg() + "；调用陕西尘肺康复管理系统获取患者评估处方接口 异常：" + e.getMessage());
                log.error("调用陕西尘肺康复管理系统获取患者评估处方接口 异常：{}", e.getMessage());
            }
            List<TdZwcfKfCf> patientKffaList = new ArrayList<>();
            try {
                patientKffaList = getPatientKffa(tokenId, patientInfo);
            } catch (Exception e) {
                e.printStackTrace();
                patientInfo.setSyncKfErrMsg(patientInfo.getSyncKfErrMsg() + "；调用陕西尘肺康复管理系统获取患者康复处方接口 异常：" + e.getMessage());
                log.error("调用陕西尘肺康复管理系统获取患者康复处方接口 异常：{}", e.getMessage());
            }
            List<TdZwcfKfRcd> cfbRecoveryRecordList = new ArrayList<>();
            try {
                cfbRecoveryRecordList = getCfbRecoveryRecord(tokenId, patientInfo);
            } catch (Exception e) {
                e.printStackTrace();
                patientInfo.setSyncKfErrMsg(patientInfo.getSyncKfErrMsg() + "；调用陕西尘肺康复管理系统获取患者尘肺病康复记录接口 异常：" + e.getMessage());
                log.error("调用陕西尘肺康复管理系统获取患者尘肺病康复记录接口 异常：{}", e.getMessage());
            }
            String error = "";
            try {
                error = this.patientInfoService.updateKfInfo(patientInfo, patientPdList, patientKffaList, cfbRecoveryRecordList);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("保存尘肺病人相关康复信息 异常：{}", e.getMessage());
            }
            if (!ObjectUtils.isEmpty(error)) {
                log.error("保存尘肺病人相关康复信息 异常：{}", error);
            }
        }
        Date endDate = new Date();
        log.info("定时调用陕西尘肺康复管理系统接口任务完成，开始时间：{} 结束时间：{} 用时:{}ms", date, endDate, (endDate.getTime() - date.getTime()));
    }

    /**
     * 调用获取token接口
     *
     * @return token
     */
    private String getToken() {
        String tokenId;
        Object cacheObj = CaffeineUtil.getIfPresent(BadRsnCaffeineKeyEnum.SXCF_API, "SXCF_API_TOKEN_TIME_KEY");
        if (null != cacheObj) {
            Date date = (Date) cacheObj;
            date = DateUtils.addMinutes(date, 25);
            if (!DateUtils.isAfter(date, new Date())) {
                cacheObj = CaffeineUtil.getIfPresent(BadRsnCaffeineKeyEnum.SXCF_API, "SXCF_API_TOKEN_KEY");
            }
        }
        tokenId = StringUtils.objectToString(cacheObj);
        if (ObjectUtils.isNotNull(tokenId)) {
            return tokenId;
        }
        String url = this.apiRoot + this.getTokenUrl;
        JSONObject object = new JSONObject();
        object.put("key", this.tokenKey);
        object.put("Secret", this.tokenSecret);
        String msg = JSONObject.toJSONString(object);
        try {
            log.info("调用陕西尘肺康复管理系统接口，URL:{} 明文参数:{} ", url, msg);
            msg = encryptParam(msg);
            String result = HttpRequestUtil.httpRequestByRaw(url, msg);
            log.info("调用陕西尘肺康复管理系统获取tokenId接口返回：{}", result);
            if (StringUtils.isNotBlank(result)) {
                JSONObject resultObj = JSONObject.parseObject(result);
                if (null != resultObj && ObjectUtils.isNotNull(resultObj.get("data"))) {
                    JSONObject resultData = (JSONObject) resultObj.get("data");
                    tokenId = StringUtils.objectToString(resultData.get("token"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用陕西尘肺康复管理系统获取tokenId接口 异常：{}", e.getMessage());
        }
        if (null != tokenId) {
            CaffeineUtil.put(BadRsnCaffeineKeyEnum.MIT_API, "SXCF_API_TOKEN_TIME_KEY", new Date());
            CaffeineUtil.put(BadRsnCaffeineKeyEnum.MIT_API, "SXCF_API_TOKEN_KEY", tokenId);
        }
        return tokenId;
    }

    /**
     * 调用获取患者评估处方接口
     *
     * @param token       token
     * @param patientInfo 尘肺病人档案信息主表
     * @throws IOException IO异常
     */
    private List<TdZwcfKfPg> getPatientPd(String token, TdZwcfPatientInfo patientInfo) throws IOException {
        List<TdZwcfKfPg> kfPgList = new ArrayList<>();
        String idc = patientInfo.getIdc();
        Header[] headers = {new BasicHeader("Authorization", token)};
        String url = this.apiRoot + this.getPatientPdUrl;
        String json = pakBodyOfJson(idc);
        log.info("调用陕西尘肺康复管理系统获取患者评估处方接口，URL:{} 明文参数:{} ", url, json);
        json = encryptParam(json);
        String result = HttpRequestUtil.httpRequestByRawNew(url, json, headers);
        log.info("调用陕西尘肺康复管理系统获取患者评估处方接口返回：{}", result);
        if (ObjectUtils.isEmpty(result)) {
            return kfPgList;
        }
        GetPatientPdVo responseVo = JSONObject.parseObject(result, GetPatientPdVo.class);
        if (responseVo.getCode() != 200) {
            patientInfo.setSyncKfErrMsg(patientInfo.getSyncKfErrMsg() + "；调用陕西尘肺康复管理系统获取患者评估处方接口返回错误信息：" + responseVo.getMsg());
        }
        if (ObjectUtils.isEmpty(responseVo.getData())) {
            return kfPgList;
        }
        List<GetPatientPdDataVo> getPatientPdDataVoList = responseVo.getData();
        for (GetPatientPdDataVo getPatientPdDataVo : getPatientPdDataVoList) {
            TdZwcfKfPg kfPg = new TdZwcfKfPg();
            kfPg.setFkByMainId(patientInfo);
            if (!ObjectUtils.isEmpty(getPatientPdDataVo.getAssessmentTime())) {
                kfPg.setPgTime(new Date(getPatientPdDataVo.getAssessmentTime() * 1000L));
            }
            kfPg.setPgPdfPath(getPatientPdDataVo.getAssessment_url());
            kfPg.setCreateDate(new Date());
            kfPg.setCreateManid(1);
            kfPgList.add(kfPg);
        }
        return kfPgList;
    }

    /**
     * 调用获取患者康复处方接口
     *
     * @param token       token
     * @param patientInfo 尘肺病人档案信息主表
     * @throws IOException IO异常
     */
    private List<TdZwcfKfCf> getPatientKffa(String token, TdZwcfPatientInfo patientInfo) throws IOException {
        List<TdZwcfKfCf> kfCfList = new ArrayList<>();
        String idc = patientInfo.getIdc();
        Header[] headers = {new BasicHeader("Authorization", token)};
        String url = this.apiRoot + this.getPatientKffaUrl;
        String json = pakBodyOfJson(idc);
        log.info("调用陕西尘肺康复管理系统获取患者康复处方接口，URL:{} 明文参数:{} ", url, json);
        json = encryptParam(json);
        String result = HttpRequestUtil.httpRequestByRawNew(url, json, headers);
        log.info("调用陕西尘肺康复管理系统获取患者评估处方接口返回：{}", result);
        if (ObjectUtils.isEmpty(result)) {
            return kfCfList;
        }
        GetPatientKffaVo responseVo = JSONObject.parseObject(result, GetPatientKffaVo.class);
        if (responseVo.getCode() != 200) {
            patientInfo.setSyncKfErrMsg(patientInfo.getSyncKfErrMsg() + "；调用陕西尘肺康复管理系统获取患者康复处方接口返回错误信息：" + responseVo.getMsg());
        }
        if (ObjectUtils.isEmpty(responseVo.getData())) {
            return kfCfList;
        }
        List<GetPatientKffaDataVo> patientKffaDataVoList = responseVo.getData();
        for (GetPatientKffaDataVo getPatientKffaDataVo : patientKffaDataVoList) {
            TdZwcfKfCf kfCf = new TdZwcfKfCf();
            kfCf.setFkByMainId(patientInfo);
            if (!ObjectUtils.isEmpty(getPatientKffaDataVo.getPrescriptionTime())) {
                kfCf.setOpenerDate(new Date(getPatientKffaDataVo.getPrescriptionTime() * 1000L));
            }
            kfCf.setCfPdfPath(getPatientKffaDataVo.getPrescription_url());
            List<TdZwcfKfCfOper> kfCfOperList = new ArrayList<>();
            if (!ObjectUtils.isEmpty(getPatientKffaDataVo.getCarryout())) {
                for (GetPatientKffaCarryoutVo getPatientKffaCarryoutVo : getPatientKffaDataVo.getCarryout()) {
                    TdZwcfKfCfOper kfCfOper = new TdZwcfKfCfOper();
                    kfCfOper.setOperDate(getPatientKffaCarryoutVo.getCarryoutTime());
                    kfCfOper.setOperPdfPath(getPatientKffaCarryoutVo.getCarryout_url());
                    kfCfOper.setCreateDate(new Date());
                    kfCfOper.setCreateManid(1);
                    kfCfOperList.add(kfCfOper);
                }
            }
            kfCf.setKfCfOperList(kfCfOperList);
            kfCf.setCreateDate(new Date());
            kfCf.setCreateManid(1);
            kfCfList.add(kfCf);
        }
        return kfCfList;
    }

    /**
     * 调用获取患者尘肺病康复记录接口
     *
     * @param token       token
     * @param patientInfo 尘肺病人档案信息主表
     * @throws IOException IO异常
     */
    private List<TdZwcfKfRcd> getCfbRecoveryRecord(String token, TdZwcfPatientInfo patientInfo) throws IOException {
        List<TdZwcfKfRcd> kfRcdList = new ArrayList<>();
        String idc = patientInfo.getIdc();
        Header[] headers = {new BasicHeader("Authorization", token)};
        String url = this.apiRoot + this.getCfbRecoveryRecordUrl;
        String json = pakBodyOfJson(idc);
        log.info("调用陕西尘肺康复管理系统获取患者尘肺病康复记录接口，URL:{} 明文参数:{} ", url, json);
        json = encryptParam(json);
        String result = HttpRequestUtil.httpRequestByRawNew(url, json, headers);
        log.info("调用陕西尘肺康复管理系统获取患者尘肺病康复记录接口返回：{}", result);
        if (ObjectUtils.isEmpty(result)) {
            return kfRcdList;
        }
        GetCfbRecoveryRecordVo responseVo = JSONObject.parseObject(result, GetCfbRecoveryRecordVo.class);
        if (responseVo.getCode() != 200) {
            patientInfo.setSyncKfErrMsg(patientInfo.getSyncKfErrMsg() + "；调用陕西尘肺康复管理系统获取患者尘肺病康复记录接口返回错误信息：" + responseVo.getMsg());
        }
        if (ObjectUtils.isEmpty(responseVo.getData())) {
            return kfRcdList;
        }
        List<GetCfbRecoveryRecordDataVo> recoveryRecordDataVoList = responseVo.getData();
        for (GetCfbRecoveryRecordDataVo data : recoveryRecordDataVoList) {
            TdZwcfKfRcd kfRcd = new TdZwcfKfRcd();
            kfRcd.setFkByMainId(patientInfo);
            kfRcd.setArea(data.getArea());
            kfRcd.setHosName(data.getHosName());
            kfRcd.setFirstRecveryTime(data.getFirstRecverytime());
            kfRcd.setLastRecveryTime(data.getLastRecverytime());
            kfRcd.setYear(DateUtils.formatDate(kfRcd.getLastRecveryTime(), "yyyy"));
            kfRcd.setHasApparatUsrecovery(data.getHasApparatusrecovery());
            kfRcd.setHasRecovery(data.getHasRecovery());
            kfRcd.setRecoveryNum(data.getRecoveryNum());
            kfRcd.setRecoveryTime(data.getRecoveryTime());
            kfRcd.setRecoveryaveTime(data.getRecoveryAvetime());
            kfRcd.setFirstSpo2(StringUtils.objectToString(data.getFirstSpo2()));
            kfRcd.setThisYearspo2(StringUtils.objectToString(data.getThisyearSpo2()));
            kfRcd.setFirstHr(StringUtils.objectToString(data.getFirstHr()));
            kfRcd.setThisYearHr(StringUtils.objectToString(data.getThisyearHr()));
            kfRcd.setFirstBlood(StringUtils.objectToString(data.getFirstBlood()));
            kfRcd.setThisYearBlood(StringUtils.objectToString(data.getThisyearBlood()));
            kfRcd.setFirstDistance(StringUtils.objectToString(data.getFirstDistance()));
            kfRcd.setThisYearDistance(StringUtils.objectToString(data.getThisyearDistance()));
            kfRcd.setFirstMmrcScore(StringUtils.objectToString(data.getFirstMmrcscore()));
            kfRcd.setThisYearMmrcScore(StringUtils.objectToString(data.getThisyearMmrcscore()));
            kfRcd.setFirstActScore(StringUtils.objectToString(data.getFirstActscore()));
            kfRcd.setThisYearActScore(StringUtils.objectToString(data.getThisyearActscore()));
            kfRcd.setFirstMip(StringUtils.objectToString(data.getFirstMip()));
            kfRcd.setThisYearMip(StringUtils.objectToString(data.getThisyearMip()));
            kfRcd.setFirstMep(StringUtils.objectToString(data.getFirstMep()));
            kfRcd.setThisYearMep(StringUtils.objectToString(data.getThisyearMep()));
            kfRcd.setCreateDate(new Date());
            kfRcd.setCreateManid(1);
            kfRcdList.add(kfRcd);
        }
        return kfRcdList;
    }

    private String pakBodyOfJson(String idc) {
        JSONObject object = new JSONObject();
        object.put("identityCard", idc);
        return JSONObject.toJSONString(object);
    }

    private String encryptParam(String param) {
        if (StringUtils.isBlank(this.encryptDebug) || !this.encryptDebug.trim().equals("true")) {
            try {
                String encryptResult = AesEncryptUtils.aesEncrypt(param, this.encryptKey);
                log.info("AES加密请求参数 key：{} 明文：{} 密文：{}", this.encryptKey, param, encryptResult);
                param = encryptResult;
            } catch (Exception e) {
                e.printStackTrace();
                log.error("AES加密参数异常：{} key：{} 明文：{}", e.getMessage(), this.encryptKey, param);
                param = null;
            }
        }
        return param;
    }
}
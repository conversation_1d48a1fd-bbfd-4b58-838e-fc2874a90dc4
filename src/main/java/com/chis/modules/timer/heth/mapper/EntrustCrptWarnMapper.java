package com.chis.modules.timer.heth.mapper;

import com.chis.modules.timer.heth.logic.vo.CrptWarnOcchethCardInfoVo;
import com.chis.modules.timer.heth.logic.vo.CrptWarnTjBhkVo;
import com.chis.modules.timer.heth.logic.vo.CrptWarnUnitbasicInfoVo;
import com.chis.modules.timer.heth.logic.vo.CrptWarnZxjcUnitInfoVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <p>类描述： 用人单位预警计算工具Mapper </p>
 * pw 2023/9/27
 **/
@Repository
public interface EntrustCrptWarnMapper {

    /**
     * <p>方法描述：查询预警地区范围内，启用状态为启用，非修订后停用，下次预警时间为空或小于等于传递时间的企业rid集合 </p>
     * @param curDateStr 包含时分秒的日期字符串
     * @param searchZoneGb 配置的预警地区，为空则忽略地区条件
     * @param pageSize 查询数据条数
     * pw 2023/9/27
     **/
    List<CrptWarnOcchethCardInfoVo> findPrepareWarnCrptIds(@Param("curDateStr") String curDateStr, @Param("searchZoneGb")String searchZoneGb,
                                         @Param("pageSize")Integer pageSize);

    /**
     * <p>方法描述：通过企业rid 获取最新的申报信息主表rid信息（若同一个申报日期有多条记录，优先获取创建日期最大的，若创建日期相同，优先获取rid最大的） </p>
     * pw 2023/9/27
     **/
    List<CrptWarnUnitbasicInfoVo> findLastestUnitBasicInfoByCrptIds(@Param("crptRidList") List<Integer> crptRidList);

    /**
     * <p>方法描述：通过主表rid集合获取申报相关信息 </p>
     * pw 2023/9/27
     **/
    List<CrptWarnUnitbasicInfoVo> findLastestUnitBasicInfoByRids(@Param("ridList") List<Integer> ridList);

    /**
     * <p>方法描述：通过用工单位rid集合 获取最新的体检日期 </p>
     * pw 2023/9/27
     **/
    List<CrptWarnTjBhkVo> findLastestBhkDateByEntrustCrptIds(@Param("crptRidList") List<Integer> crptRidList);

    /**
     * <p>方法描述：通过用工单位rid集合 获取疑似职业病体检记录的最新创建日期 </p>
     * pw 2023/9/28
     **/
    List<CrptWarnTjBhkVo> findZybLastestCreateDateByEntrustCrptIds(@Param("crptRidList") List<Integer> crptRidList,
                                                                   @Param("bhkDate") String bhkDate);

    /**
     * <p>方法描述：通过企业rid 获取最新的在线监测信息主表rid信息（最大年份记录里，创建日期、rid倒序） </p>
     * pw 2023/9/28
     **/
    List<CrptWarnZxjcUnitInfoVo> findLastestZxjcBasicInfoByCrptIds(@Param("crptRidList") List<Integer> crptRidList);

    /**
     * <p>方法描述：通过主表rid集合获取在线监测相关信息 </p>
     * pw 2023/9/28
     **/
    List<CrptWarnZxjcUnitInfoVo> findLastestZxjcBasicInfoByRIds(@Param("ridList") List<Integer> ridList);

    /**
     * <p>方法描述：通过企业rid 获取最新的非标删已提交，并且是职业病危害因素检测的职业卫生技术服务信息报送卡相关信息 </p>
     * pw 2023/9/28
     **/
    List<CrptWarnOcchethCardInfoVo> findOcchethCardInfoByCrptRids(@Param("crptRidList") List<Integer> crptRidList);

    /**
     * <p>方法描述：更新下次预警时间 </p>
     * pw 2023/9/28
     **/
    void updateCrptWarnDate(@Param("warnDate") Date warnDate, @Param("ridList") List<Integer> ridList);
}

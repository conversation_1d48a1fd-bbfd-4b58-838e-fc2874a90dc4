package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdTjRadhethPsn;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdTjRadhethPsnMapper
 */
@Repository
public interface TdTjRadhethPsnMapper extends ZwxBaseMapper<TdTjRadhethPsn> {

    List<TdTjRadhethPsn> selectRadhethPsn(@Param("dataSize") Integer dataSize);

    void updateRadhethPsn(@Param("psnsIds") List<Integer> psnsIds);

    List<TdTjRadhethPsn> selectRadhethPsnByState(@Param("dataSize") Integer dataSize);

    void updateRadhethPsns(@Param("idcList") List<String> idcList,@Param("state") String state,@Param("msg") String msg);
}

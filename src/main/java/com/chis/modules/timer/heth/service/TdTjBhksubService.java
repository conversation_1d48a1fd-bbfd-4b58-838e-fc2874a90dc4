package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdTjBhksub;
import com.chis.modules.timer.heth.logic.ChestRstPO;
import com.chis.modules.timer.heth.logic.HearingRstPO;
import com.chis.modules.timer.heth.mapper.TdTjBhksubMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdTjBhksubService
 */
@Service
public class TdTjBhksubService extends ZwxBaseServiceImpl<TdTjBhksubMapper, TdTjBhksub> {
    /**
     * <p>方法描述：查询当前体检记录的结果子表，过滤缺项</p>
     * @MethodAuthor qrr,2020-10-30,findTdTjBhksubByBhkId
     * */
    public List<TdTjBhksub> findTdTjBhksubByBhkId(Integer bhkId){
        if(null==bhkId){
            return null;
        }
        TdTjBhksub bhksub = new TdTjBhksub();
        bhksub.setBhkId(bhkId.toString());
        bhksub.setIfLack("0");
        return this.baseMapper.selectListByEntity(bhksub);
    }


    /**
     * <p>
     *     方法描述：依据体检主表rid集合批量查询 未缺项 定量项目 且危急值项目维护中存在数据的体检子表集合
     * </p>
     *
     * @MethodAuthor pw,2020年11月23日,
     */
    public List<TdTjBhksub> selectDangerValSubBhkList(List<Integer> bhkIdList){
        if(CollectionUtils.isEmpty(bhkIdList)){
            return null;
        }
        return this.baseMapper.selectDangerValSubBhkList(bhkIdList);
    }


    /**
     * @Description: 通过体检主表rid集合 获取体检子表信息
     *
     * @MethodAuthor pw,2021年05月12日
     */
    public List<TdTjBhksub> selectSubBhkListByBhkRidList(List<Integer> bhkIdList){
        if(CollectionUtils.isEmpty(bhkIdList)){
            return null;
        }
        List<List<Integer>> groupRidList = StringUtils.splitListProxy(bhkIdList, 1000);
        List<TdTjBhksub> resultList = new ArrayList<>();
        for(List<Integer> tmpList : groupRidList){
            List<TdTjBhksub> tmpResult = this.baseMapper.selectSubBhkListByBhkRidList(tmpList);
            if(!CollectionUtils.isEmpty(tmpResult)){
                resultList.addAll(tmpResult);
            }
        }
        return resultList;
    }

    /**
     * <p>方法描述： 依据体检主表rid集合以及特定项目标记 获取 体检子表集合 </p>
     * @MethodAuthor： pw 2022/10/20
     **/
    public List<TdTjBhksub> selectSubBhkListByItemTagAndBhkRidList(List<Integer> bhkIdList, Integer itemTag){
        if(CollectionUtils.isEmpty(bhkIdList)){
            return null;
        }
        List<List<Integer>> groupRidList = StringUtils.splitListProxy(bhkIdList, 1000);
        List<TdTjBhksub> resultList = new ArrayList<>();
        for(List<Integer> tmpList : groupRidList){
            List<TdTjBhksub> tmpResult = this.baseMapper.selectSubBhkListByItemTagAndBhkRidList(tmpList, itemTag);
            if(!CollectionUtils.isEmpty(tmpResult)){
                resultList.addAll(tmpResult);
            }
        }
        return resultList;
    }

    /**
     * 根据指定的日期范围查询未处理胸片结论的胸片检查结果列表。
     *
     * @param bhkStartDate      体检开始日期
     * @param bhkEndDate        体检结束日期
     * @param rptPrintStartDate 报告打印开始日期
     * @param rptPrintEndDate   报告打印结束日期
     * @param size              单次查询条数
     * @return 返回一个ChestRstPO类型的列表
     */
    public List<ChestRstPO> selectChestRstList(String bhkStartDate, String bhkEndDate,
                                               String rptPrintStartDate, String rptPrintEndDate, Integer size) {
        return this.baseMapper.selectChestRstList(bhkStartDate, bhkEndDate, rptPrintStartDate, rptPrintEndDate, size);
    }

    /**
     * 根据指定的日期范围查询未处理电测听结论的电测听结果列表。
     *
     * @param itemCodeList      电测听项目编码
     * @param bhkStartDate      体检开始日期
     * @param bhkEndDate        体检结束日期
     * @param rptPrintStartDate 报告打印开始日期
     * @param rptPrintEndDate   报告打印结束日期
     * @param size              单次查询条数
     * @param useRgltagMode     是否使用RGLTAG模式（true: 只判断RGLTAG不为空，false: 只判断ITEM_RST不为空）
     * @return 返回一个ChestRstPO类型的列表
     */
    public List<HearingRstPO> selectHearingRstList(List<Integer> itemCodeList,
                                                   String bhkStartDate,
                                                   String bhkEndDate,
                                                   String rptPrintStartDate,
                                                   String rptPrintEndDate,
                                                   Integer size,
                                                   boolean useRgltagMode) {
        return this.baseMapper.selectHearingRstList(
                itemCodeList, bhkStartDate, bhkEndDate, rptPrintStartDate, rptPrintEndDate, size, useRgltagMode
        );
    }

    /**
     * 通过体检项目编码集合获取体检项目RID
     */
    public List<Integer> findItemRidListByCodeList(List<String> itemCodeList) {
        List<Integer> ridList = this.baseMapper.findItemRidListByCodeList(itemCodeList);
        if (CollectionUtils.isEmpty(ridList)) {
            ridList = new ArrayList<>();
        }
        return ridList;
    }
}

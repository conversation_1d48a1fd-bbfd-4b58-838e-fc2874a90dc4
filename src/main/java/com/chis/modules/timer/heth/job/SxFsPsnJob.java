package com.chis.modules.timer.heth.job;
import com.chis.modules.timer.heth.entity.TdTjRadhethPsn;
import com.chis.modules.timer.heth.service.TdTjRadhethPsnService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  <p>类描述：放射工作人员培训结果同步功能-更新放射人员信息</p>
 * @ClassAuthor hsj 2022-09-06 14:25
 */
@Slf4j
@Component
public class SxFsPsnJob {
    @Autowired
    private TdTjRadhethPsnService tdTjRadhethPsnService;
    @Value("${heth-timer.dataSize}")
    private Integer dataSize;
    /**
     *  <p>方法描述：更新放射人员信息【TD_TJ_RADHETH_PSN】，
     *  同步培训信息状态置为0、同步培训信息日期置为空、同步培训失败原因置为空；</p>
     * @MethodAuthor hsj 2022-09-06 14:32
     */
    @Scheduled(cron = "${heth-timer.sche-cron.sxFsPsnCron}")
    public void start(){
        log.info("定时更新放射人员信息 当前时间{}", new Date());
        for(;;){
            try {
                List<TdTjRadhethPsn> psns = tdTjRadhethPsnService.selectRadhethPsn(dataSize);
                if(CollectionUtils.isEmpty(psns)){
                    return;
                }
                List<Integer> psnsIds = psns.stream().map(TdTjRadhethPsn::getRid).collect(Collectors.toList());
                this.tdTjRadhethPsnService.updateRadhethPsn(psnsIds);
                log.info("定时重置同步培训信息状态置为0、同步培训信息日期置为空、同步培训失败原因置为空任务完成 共处理{}条数据", psnsIds.size());
            }catch (Exception e){
                e.printStackTrace();
            }
        }

    }
}

package com.chis.modules.timer.heth.logic.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *  <p>类描述：技术服务机构查询</p>
 * @ClassAuthor hsj 2022-12-21 16:14
 */
@Data
public class SrvorgInfoDataVo extends TechnicalInfoVo implements Serializable {
    @JSONField(serialize = false)
    private Integer rid;
    @JSONField(serialize = false)
    private Date validDate;
    @JSONField(serialize = false)
    private String  zoneGb;
    @JSONField(serialize = false)
    private Integer realZoneType;
    @JSONField(serialize = false)
    private Integer cardId;
    @JSONField(serialize = false)
    private Date firstGetday;

    private String issued;

    private String ispja;

    private String ispjb;

    private String isfs;

    private String isgr;

    private String isqc;

    private Integer ptnum;


}

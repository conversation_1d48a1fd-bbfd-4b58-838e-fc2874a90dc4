package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * <AUTHOR> 2024-07-12,TdZwOrgWarnInst
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_ORG_WARN_INST")
@KeySequence(value = "TD_ZW_ORG_WARN_INST_SEQ",clazz = Integer.class)
public class TdZwOrgWarnInst extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID", el = "fkByMainId.rid")
    private TdZwOrgWarnDetail fkByMainId;

    @TableField(value = "INST_ID")
    private Integer instId;

    @TableField("CURR_DATE2")
    private Date currDate2;

    @TableField("NEXT_DATE2")
    private Date nextDate2;


    public TdZwOrgWarnInst(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.mapper.*;
import com.chis.modules.timer.heth.pojo.SpecialRectficationPojo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>类描述：专项治理下载服务类 </p>
 * pw 2023/9/20
 **/
@Service
public class SpecialRectficationService {
    @Resource
    private TdZxzlBeginDateMapper beginDateMapper;
    @Resource
    private TdRectificationListMapper mainInfoMapper;
    @Resource
    private TdPreviousDetailMapper detailMapper;
    @Resource
    private TdRectificationSchemeMapper schemeMapper;
    @Resource
    private TdRectificationResultMapper resultMapper;
    @Resource
    private TbTjCrptMapper crptMapper;


    /**
     * <p>方法描述：查询日志表 </p>
     * pw 2023/9/20
     **/
    public TdZxzlBeginDate findTdZxzlBeginDate(){
        TdZxzlBeginDate beginDate = new TdZxzlBeginDate();
        List<TdZxzlBeginDate> resultList = this.beginDateMapper.selectListByEntity(beginDate);
        return CollectionUtils.isEmpty(resultList) ? null : resultList.get(0);
    }

    /**
     * <p>方法描述：通过社会信用代码集合获取主体机构企业信息rid 返回Map key社会信用代码 value企业rid </p>
     * pw 2023/9/20
     **/
    public Map<String,Integer> findCrptIdByInstitutionCodeList(List<String> institutionList){
        if(CollectionUtils.isEmpty(institutionList)){
            return Collections.emptyMap();
        }
        List<List<String>> groupList = StringUtils.splitListProxy(institutionList, 1000);
        List<TbTjCrpt> crptList = new ArrayList<>(institutionList.size());
        for(List<String> tmpList : groupList){
            List<TbTjCrpt> resultList = this.crptMapper.findMainCrptInfoByInstitutionCode(tmpList);
            if(!CollectionUtils.isEmpty(resultList)){
                crptList.addAll(resultList);
            }
        }
        if(CollectionUtils.isEmpty(crptList)){
            return Collections.emptyMap();
        }
        Map<String,Integer> resultMap = new HashMap<>();
        for(TbTjCrpt crpt : crptList){
            resultMap.put(crpt.getInstitutionCode(), crpt.getRid());
        }
        return resultMap;
    }

    /**
     * <p>方法描述：通过uuid集合获取专项治理-基本信息，返回Map key uuid </p>
     * pw 2023/9/20
     **/
    public Map<String,TdRectificationList> findMainInfoByUuidResultMap(List<String> uuidList){
        if(CollectionUtils.isEmpty(uuidList)){
            return Collections.emptyMap();
        }
        List<List<String>> groupList = StringUtils.splitListProxy(uuidList, 1000);
        List<TdRectificationList> allResultList = new ArrayList<>(uuidList.size());
        for(List<String> tmpList : groupList){
            List<TdRectificationList> resultList = this.mainInfoMapper.findByUuidList(tmpList);
            if(!CollectionUtils.isEmpty(resultList)){
                allResultList.addAll(resultList);
            }
        }
        if(CollectionUtils.isEmpty(allResultList)){
            return Collections.emptyMap();
        }
        Map<String,TdRectificationList> resultMap = new HashMap<>();
        for(TdRectificationList result : allResultList){
            resultMap.put(result.getUuid(), result);
        }
        return resultMap;
    }

    /**
     * <p>方法描述：存储信息 </p>
     * pw 2023/9/20
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateSpecialRectficationInfo(SpecialRectficationPojo dataPo){
        if(null == dataPo){
            return;
        }
        //基本信息
        List<TdRectificationList> rectificationLists = dataPo.getRectificationLists();
        if(CollectionUtils.isEmpty(rectificationLists)){
            return;
        }
        for(TdRectificationList mainInfo : rectificationLists){
            if(null == mainInfo.getRid()){
                preInsert(mainInfo);
                this.mainInfoMapper.insertEntity(mainInfo);
                continue;
            }
            preUpdate(mainInfo);
            this.mainInfoMapper.updateFullById(mainInfo);
            TdPreviousDetail detail = new TdPreviousDetail();
            detail.setFkByMainId(mainInfo);
            this.detailMapper.removeByEntity(detail);
            TdRectificationScheme scheme = new TdRectificationScheme();
            scheme.setFkByMainId(mainInfo);
            this.schemeMapper.removeByEntity(scheme);
            TdRectificationResult result = new TdRectificationResult();
            result.setFkByMainId(mainInfo);
            this.resultMapper.removeByEntity(result);
        }
        //整改前情况明细
        List<TdPreviousDetail> previousDetailList = dataPo.getPreviousDetailList();
        preInsertDetail(previousDetailList);
        //整改方案
        List<TdRectificationScheme> rectificationSchemeList = dataPo.getRectificationSchemeList();
        preInsertScheme(rectificationSchemeList);
        //检测结果明细
        List<TdRectificationResult> rectificationResultList = dataPo.getRectificationResultList();
        preInsertResult(rectificationResultList);
        if(!CollectionUtils.isEmpty(previousDetailList)){
            this.detailMapper.insertBatch(previousDetailList);
        }
        if(!CollectionUtils.isEmpty(rectificationSchemeList)){
            this.schemeMapper.insertBatch(rectificationSchemeList);
        }
        if(!CollectionUtils.isEmpty(rectificationResultList)){
            this.resultMapper.insertBatch(rectificationResultList);
        }
        //日志处理
        TdZxzlBeginDate beginDate = dataPo.getZxzlBeginDate();
        if(null == beginDate){
            beginDate = new TdZxzlBeginDate();
        }
        TdRectificationList lastDate = rectificationLists.get(rectificationLists.size()-1);
        beginDate.setBeginDate(lastDate.getUpdateDate());
        beginDate.setUuid(lastDate.getUuid());
        if(null == beginDate.getRid()){
            preInsert(beginDate);
            this.beginDateMapper.insertEntity(beginDate);
        }else{
            preUpdate(beginDate);
            this.beginDateMapper.updateFullById(beginDate);
        }
        dataPo.setZxzlBeginDate(beginDate);
    }

    /**
     * <p>方法描述：存储检测结果明细前赋值创建人与创建日期 </p>
     * pw 2023/9/20
     **/
    private void preInsertResult(List<TdRectificationResult> resultList){
        if(CollectionUtils.isEmpty(resultList)){
            return;
        }
        for(TdRectificationResult result : resultList){
            preInsert(result);
        }
    }

    /**
     * <p>方法描述：存储整改方案前赋值创建人与创建日期 </p>
     * pw 2023/9/20
     **/
    private void preInsertScheme(List<TdRectificationScheme> schemeList){
        if(CollectionUtils.isEmpty(schemeList)){
            return;
        }
        for(TdRectificationScheme scheme : schemeList){
            preInsert(scheme);
        }
    }

    /**
     * <p>方法描述：存储整改前情况明细前赋值创建人与创建日期 </p>
     * pw 2023/9/20
     **/
    private void preInsertDetail(List<TdPreviousDetail> detailList){
        if(CollectionUtils.isEmpty(detailList)){
            return;
        }
        for(TdPreviousDetail detail : detailList){
            preInsert(detail);
        }
    }

    /**
     * <p>方法描述：保存前赋值创建人与创建日期 </p>
     * pw 2023/9/20
     **/
    private void preInsert(ZwxBaseEntity entity){
        entity.setCreateManid(0);
        entity.setCreateDate(new Date());
    }

    /**
     * <p>方法描述： 更新前赋值更新人与更新日期 </p>
     * pw 2023/9/20
     **/
    private void preUpdate(ZwxBaseEntity entity){
        entity.setModifyManid(0);
        entity.setModifyDate(new Date());
    }
}

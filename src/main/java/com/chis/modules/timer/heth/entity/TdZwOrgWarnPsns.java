package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * <AUTHOR> 2024-07-12,TdZwOrgWarnPsns
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_ORG_WARN_PSNS")
@KeySequence(value = "TD_ZW_ORG_WARN_PSNS_SEQ",clazz = Integer.class)
public class TdZwOrgWarnPsns extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID", el = "fkByMainId.rid")
    private TdZwOrgWarnDetail fkByMainId;

    @TableField(value = "PSN_ID")
    private Integer psnId;

    @TableField(value = "TITLE_LEVEL_ID", el = "fkByTitleLevelId.rid")
    private TsSimpleCode fkByTitleLevelId;

    @TableField("CURR_DATE")
    private Date currDate;

    @TableField("NEXT_DATE")
    private Date nextDate;

    @TableField(exist = false)
    private List<TdZwWarnPsnOrg> warnPsnOrgList;


    public TdZwOrgWarnPsns(Integer rid) {
        super(rid);
    }


}

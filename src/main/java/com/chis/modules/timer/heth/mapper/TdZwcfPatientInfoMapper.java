package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人, 2022-09-02,TdZwcfPatientInfoMapper
 */
@Repository
public interface TdZwcfPatientInfoMapper extends ZwxBaseMapper<TdZwcfPatientInfo> {
    int updateSyncKfState0();

    int updateSyncKfState3IdcNull(@Param("syncKfDate") Date syncKfDate, @Param("syncKfErrMsg") String syncKfErrMsg);

    List<TdZwcfPatientInfo> selectPatientInfoListByState0();

    void deleteKfInfo(@Param("mainId") Integer mainId, @Param("year") String year);

    void updateSyncKfSyncKfByRid(@Param("patientInfo") TdZwcfPatientInfo patientInfo);

    void insertTdZwcfKfPg(@Param("tdZwcfKfPg") TdZwcfKfPg tdZwcfKfPg);

    void insertTdZwcfKfCf(@Param("tdZwcfKfCf") TdZwcfKfCf tdZwcfKfCf);

    void insertTdZwcfKfCfOper(@Param("tdZwcfKfCfOper") TdZwcfKfCfOper tdZwcfKfCfOper);

    void insertTdZwcfKfRcd(@Param("tdZwcfKfRcd") TdZwcfKfRcd tdZwcfKfRcd);
}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdTjBhksub
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("TD_TJ_BHKSUB")
public class TdTjBhksub implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键：默认自增
     */
    @TableId
    protected BigInteger rid;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    protected Integer createManid;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    protected Date createDate;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected Integer modifyManid;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected Date modifyDate;

    @TableField("BHK_ID")
    private String bhkId;

    @TableField(value = "ITEM_ID" , el = "fkByItemId.rid")
    private TbTjItems fkByItemId;

    @TableField("MSRUNT")
    private String msrunt;

    @TableField("ITEM_STDVALUE")
    private String itemStdvalue;

    @TableField("ITEM_RST")
    private String itemRst;

    @TableField("RGLTAG")
    private String rgltag;

    @TableField("RST_DESC")
    private String rstDesc;

    @TableField("IF_LACK")
    private String ifLack;

    @TableField("CHKDAT")
    private Date chkdat;

    @TableField("CHKDOCT")
    private String chkdoct;

    @TableField("JDGPTN")
    private String jdgptn;

    @TableField("MINVAL")
    private String minval;

    @TableField("MAXVAL")
    private String maxval;

    @TableField("DIAG_REST")
    private String diagRest;

    @TableField("JG")
    private String jg;

    @TableField("MSRUNT_ID")
    private String msruntId;

    @TableField("RST_FLAG")
    private Integer rstFlag;


    public TdTjBhksub(BigInteger rid) {
        this.rid = rid;
    }


}

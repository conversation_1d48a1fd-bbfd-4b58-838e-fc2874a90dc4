package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwyjBsnCheck;
import com.chis.modules.timer.heth.mapper.TdZwyjBsnCheckMapper;
import org.springframework.stereotype.Service;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-11-03,TdZwyjBsnCheckService
 */
@Service
public class TdZwyjBsnCheckService extends ZwxBaseServiceImpl<TdZwyjBsnCheckMapper, TdZwyjBsnCheck> {
    /**
     * <p>方法描述：根据主表删除子表</p>
     * @MethodAuthor qrr,2020-10-30,deleteByMainId
     * */
    public void deleteByMainId(Integer mainId){
        this.baseMapper.deleteByMainId(mainId);
    }
}

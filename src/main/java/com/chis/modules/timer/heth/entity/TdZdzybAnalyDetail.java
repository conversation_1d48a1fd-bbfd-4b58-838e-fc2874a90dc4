package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-05-11,TdZdzybAnalyDetail
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZDZYB_ANALY_DETAIL")
public class TdZdzybAnalyDetail extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZdzybAnalyType fkByMainId;

    @TableField("XH")
    private String xh;

    @TableField(value = "ANALY_ITEM_ID" , el = "fkByAnalyItemId.rid")
    private TsSimpleCode fkByAnalyItemId;

    @TableField("GE")
    private String ge;

    @TableField("GT")
    private String gt;

    @TableField("LE")
    private String le;

    @TableField("LT")
    private String lt;

    @TableField("UNIT_NAME")
    private String unitName;

    @TableField("UNIT_CODE")
    private String unitCode;


    public TdZdzybAnalyDetail(Integer rid) {
        super(rid);
    }


}

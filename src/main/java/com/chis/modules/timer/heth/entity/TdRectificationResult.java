package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2023-09-20,TdRectificationResult
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_RECTIFICATION_RESULT")
public class TdRectificationResult extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdRectificationList fkByMainId;

    @TableField(value = "FACTOR_ID" , el = "fkByFactorId.rid")
    private TsSimpleCode fkByFactorId;

    @TableField(value = "ITEM_ID" , el = "fkByItemId.rid")
    private TsSimpleCode fkByItemId;

    @TableField("JOB_NAME")
    private String jobName;

    @TableField("JUDGEMENT_RESULT")
    private Integer judgementResult;

    @TableField("COMPOSITIVE_RESULT")
    private Integer compositiveResult;

    @TableField("PREVIOUS_TWA")
    private BigDecimal previousTwa;

    @TableField("CURRENT_TWA")
    private BigDecimal currentTwa;

    @TableField("FINAL_TWA")
    private BigDecimal finalTwa;

    @TableField("PREVIOUS_STEL")
    private BigDecimal previousStel;

    @TableField("CURRENT_STEL")
    private BigDecimal currentStel;

    @TableField("FINAL_STEL")
    private BigDecimal finalStel;

    @TableField("PREVIOUS_MAC")
    private BigDecimal previousMac;

    @TableField("CURRENT_MAC")
    private BigDecimal currentMac;

    @TableField("FINAL_MAC")
    private BigDecimal finalMac;

    @TableField("PREVIOUS_PE")
    private BigDecimal previousPe;

    @TableField("CURRENT_PE")
    private BigDecimal currentPe;

    @TableField("FINAL_PE")
    private BigDecimal finalPe;

    @TableField("PREVIOUS_LEX")
    private BigDecimal previousLex;

    @TableField("CURRENT_LEX")
    private BigDecimal currentLex;

    @TableField("FINAL_LEX")
    private BigDecimal finalLex;


    public TdRectificationResult(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TbTjSrvorg;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-12,TdZwyjOtrServiceItem
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_OTR_SERVICE_ITEM")
@KeySequence(value = "TD_ZWYJ_OTR_SERVICE_ITEM_SEQ",clazz = Integer.class)
public class TdZwyjOtrServiceItem extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwyjOtrBhk fkByMainId;

    @TableField(value = "SERVICE_ID" , el = "fkByServiceId.rid")
    private TsSimpleCode fkByServiceId;


    public TdZwyjOtrServiceItem(Integer rid) {
        super(rid);
    }


}

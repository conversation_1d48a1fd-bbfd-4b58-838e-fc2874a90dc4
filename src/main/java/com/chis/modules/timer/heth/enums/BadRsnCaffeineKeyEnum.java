package com.chis.modules.timer.heth.enums;

import com.chis.modules.webmvc.cache.enums.ICaffeineKeyEnum;

/***
 * <p>类描述: key的名称通用在这里管理，防止其他人覆盖 </p>
 *
 * @ClassAuthor mxp,2018/12/27,CaffeineKeyEnum
 */
public enum BadRsnCaffeineKeyEnum implements ICaffeineKeyEnum{
    BAD_RSN_CAFFEINE_KEY("BAD_RSN_CAFFEINE_KEY"),
    NO_RSN_CAFFEINE_KEY("NO_RSN_CAFFEINE_KEY"),
    CRPT_ONLINE_REPORT("CRPT_ONLINE_REPORT"),//企业在线申报相关key
    /**
     * 个案审核相关key
     */
    BHK_AUDIT_CALCULATION("BHK_AUDIT_CALCULATION"),
    /**
     * 曼荼罗接口相关key
     */
    MIT_API("MIT_API"),
    /**
     * 陕西尘肺康复管理系统接口相关key
     */
    SXCF_API("SXCF_API"),
    /**
     * 国家专项治理相关key
     * */
    SPECIAL_RECTIFICATION("SPECIAL_RECTIFICATION");



    private String key;

    BadRsnCaffeineKeyEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }
}

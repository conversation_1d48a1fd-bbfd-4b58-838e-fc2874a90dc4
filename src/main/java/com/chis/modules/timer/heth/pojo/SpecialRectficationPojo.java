package com.chis.modules.timer.heth.pojo;

import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.timer.heth.entity.*;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

/**
 * <p>类描述：专项治理数据下载 存储传输对象 </p>
 * pw 2023/9/20
 **/
@Data
public class SpecialRectficationPojo implements Serializable {
    private static final long serialVersionUID = 3741282429888778568L;
    /** 专项治理-基本信息 */
    private List<TdRectificationList> rectificationLists = new ArrayList<>();
    /** 专项治理-整改前情况明细 */
    private List<TdPreviousDetail> previousDetailList = new ArrayList<>();
    /** 专项治理-整改方案 */
    private List<TdRectificationScheme> rectificationSchemeList = new ArrayList<>();
    /** 专项治理-检测结果明细 */
    private List<TdRectificationResult> rectificationResultList = new ArrayList<>();
    /** 下载起始日期记录表【专项治理】 */
    private TdZxzlBeginDate zxzlBeginDate;

    /** 缓存通过uuid查询到的基本信息 用于判断是否修改基本信息 key uuid */
    private Map<String,TdRectificationList> baseInfoMap;

    /** 地区对照失败的编码 */
    private Set<String> zoneContraSet = new HashSet<>();
    /** 找不到地区的zoneGb */
    private Set<String> zoneSimpleSet = new HashSet<>();
    /** 行业分类对照失败的编码 */
    private Set<String> codeIndustryContraSet = new HashSet<>();
    /** 找不到行业分类的code_no */
    private Set<String> codeIndustrySimpleSet = new HashSet<>();
    /** 经济类型对照失败的编码 */
    private Set<String> codeEconomicContraSet = new HashSet<>();
    /** 找不到经济类型的code_no */
    private Set<String> codeEconomicSimpleSet = new HashSet<>();
    /** 企业规模对照失败的编码 */
    private Set<String> codeCrptSizeContraSet = new HashSet<>();
    /** 找不到企业规模的code_no */
    private Set<String> codeCrptSizeSimpleSet = new HashSet<>();
    /** 危害因素对照失败的编码 */
    private Set<String> codeBadRsnContraSet = new HashSet<>();
    /** 找不到危害因素的code_no */
    private Set<String> codeBadRsnSimpleSet = new HashSet<>();
    /** 检测项目对照失败的编码 */
    private Set<String> codeItemContraSet = new HashSet<>();
    /** 找不到检测项目的code_no */
    private Set<String> codeItemSimpleSet = new HashSet<>();
}

package com.chis.modules.timer.heth.job;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.entity.TdTjBhk;
import com.chis.modules.timer.heth.entity.TdTjCheckTask;
import com.chis.modules.timer.heth.pojo.BhkCheckCondition;
import com.chis.modules.timer.heth.service.BhkAllCheckService;
import com.chis.modules.timer.heth.service.TdTjBhkService;
import com.chis.modules.timer.heth.service.TdTjCheckTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @description: 职业健康检查数据审核 全部审核
 */
@Slf4j
@Component
public class BhkAllCheckJob {

    @Autowired
    private TdTjCheckTaskService tdTjCheckTaskService;
    @Autowired
    private TdTjBhkService tdTjBhkService;

    @Autowired
    private BhkAllCheckService bhkAllCheckService;

    @Value("${bhk-timer.batch_size:100}")
    private Integer BATCH_SIZE;

    @Value("${bhk-timer.thread-pool.core-pool-size:5}")
    private Integer corePoolSize;

    @Value("${bhk-timer.thread-pool.maximum-pool-size:5}")
    private Integer maximumPoolSize;

    @Scheduled(cron = "${bhk-timer.sche-cron.checkCron}")
    public void runScheduledTask() {
        // 创建线程池
        ExecutorService poolExecutor = new ThreadPoolExecutor(
                corePoolSize, maximumPoolSize, 100L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy()
        );
        for (; ; ) {
            try {
                // 1. 查询需要处理的任务数据
                List<TdTjCheckTask> tasks = tdTjCheckTaskService.queryTasks();
                if (CollectionUtils.isEmpty(tasks)) {
                    return;
                }

                for (TdTjCheckTask task : tasks) {
                    // 3. 对 List 集合中的每个任务使用多线程处理
                    poolExecutor.submit(() -> processTask(task));
                }

                poolExecutor.shutdown();
                try {
                    // 等待线程池任务执行完成
                    while (!poolExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                        log.info("线程等待状态==进行中");
                    }
                } catch (InterruptedException e) {
                    // 中断处理
                    log.error("线程中断");
                    e.printStackTrace();
                }
                // 重新初始化线程池，以便下一次循环使用
                poolExecutor = new ThreadPoolExecutor(
                        corePoolSize, maximumPoolSize, 100L, TimeUnit.MILLISECONDS,
                        new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy()
                );
            } catch (Exception e) {
                log.error(e.getMessage(), new Throwable(e));
            }
        }
    }


    /**
     * <p>Description：多线程处理任务 </p>
     * <p>Author： yzz 2024-10-14 </p>
     */
    private void processTask(TdTjCheckTask task) {
        // 解析任务查询条件，查询需要处理的数据
        String queryCondition = task.getExportCondition();
        if (StringUtils.isBlank(queryCondition)) {
            return;
        }
        BhkCheckCondition bhkCheckCondition;
        try {
            bhkCheckCondition = JSON.parseObject(queryCondition, BhkCheckCondition.class);
            log.info("查询条件：{}", queryCondition);
        } catch (Exception e) {
            log.error("查询条件解析异常：{}", queryCondition);
            return;
        }
        if (!("2".equals(bhkCheckCondition.getCheckLevel()) || "3".equals(bhkCheckCondition.getCheckLevel()))) {
            log.error("查询条件-审核级别解析异常：{}", queryCondition);
            return;
        }
        if (bhkCheckCondition.getZoneType() == null) {
            log.error("查询条件-地区级别解析异常：{}", queryCondition);
            return;
        }
        // 拼接状态查询语句
        dealStatue(bhkCheckCondition);
        // 根据查询条件 查询待审核的体检数据
        List<TdTjBhk> records = tdTjBhkService.queryCheckByCondition(bhkCheckCondition);
        // 无体检记录时，跳过
        if (CollectionUtils.isEmpty(records)) {
            tdTjCheckTaskService.updateTaskByRid(BigDecimal.valueOf(records.size()), 1, null, task.getFkByCheckRsnId().getRid(), task.getRid());
            return;
        }
        // 更新 审核总条数
        tdTjCheckTaskService.updateTaskByRid(BigDecimal.valueOf(records.size()), null, null, task.getFkByCheckRsnId().getRid(), task.getRid());

        // 根据BATCH_SIZE分成批次处理
        List<List<TdTjBhk>> batches = splitIntoBatches(records, BATCH_SIZE);
        StringBuilder errorMsg = new StringBuilder();
        int totalCount = 0;
        for (List<TdTjBhk> batch : batches) {
            // 4. 每个任务中的N条记录根据BATCH_SIZE分批次处理
            totalCount += processBatch(batch, task, bhkCheckCondition, errorMsg);
        }
        // 更新任务表 状态
        if (errorMsg.length() > 0) {
            String errorMsgStr = "成功审核" + totalCount + "条，失败" + (records.size() - totalCount) + "条，错误信息：" + errorMsg;
            tdTjCheckTaskService.updateTaskByRid(null, 2, errorMsg.length() > 1000 ? errorMsgStr.substring(0, 1000) : errorMsgStr, task.getFkByCheckRsnId().getRid(), task.getRid());
        } else {
            tdTjCheckTaskService.updateTaskByRid(null, 1, null, task.getFkByCheckRsnId().getRid(), task.getRid());
        }
    }

    /**
     * <p>Description：处理查询状态、是否迟报等条件 </p>
     * <p>Author： yzz 2024-10-14 </p>
     */
    public void dealStatue(BhkCheckCondition bhkCheckCondition) {
        List<String> statusList = new ArrayList<>();
        StringBuilder sb1 = new StringBuilder();
        StringBuilder sb2 = new StringBuilder();
        StringBuilder sb3 = new StringBuilder();
        if (new Integer(4).equals(bhkCheckCondition.getZoneType())) {
            statusList.add("1");
        } else if (new Integer(3).equals(bhkCheckCondition.getZoneType())) {
            statusList.add("3");
        } else if (new Integer(2).equals(bhkCheckCondition.getZoneType())) {
            statusList.add("5");
        }
        for (String status : statusList) {
            if ("1".equals(status)) {
                if ("2".equals(bhkCheckCondition.getCheckLevel())) {
                    sb1.append(", 1");
                } else {
                    sb2.append(" OR (T.STATE = 1 AND NVL(T6.IF_CITY_DIRECT, 0) = 0)");
                }
            } else if ("3".equals(status)) {
                sb1.append(", 3");
                sb2.append(" OR (T.STATE = 1 AND NVL(T6.IF_CITY_DIRECT, 0) = 1)");
            } else {
                sb1.append(", ").append(status);
            }
        }
        if (ObjectUtil.isNotEmpty(sb1)) {
            sb3.append(" T.STATE IN (").append(sb1.substring(2)).append(")");
        }
        if (ObjectUtil.isNotEmpty(sb2)) {
            sb3.append(ObjectUtil.isEmpty(sb3) ? sb2.substring(4) : sb2);
        }
        bhkCheckCondition.setStatueStr(sb3.toString());
    }

    /**
     * <p>Description：数据分批 </p>
     * <p>Author： yzz 2024-10-14 </p>
     */
    private List<List<TdTjBhk>> splitIntoBatches(List<TdTjBhk> records, int batchSize) {
        List<List<TdTjBhk>> batches = new ArrayList<>();
        for (int i = 0; i < records.size(); i += batchSize) {
            int end = Math.min(i + batchSize, records.size());
            batches.add(records.subList(i, end));
        }
        return batches;
    }

    /**
     * <p>Description：分批处理，每一批次再同一个事务中 </p>
     * <p>Author： yzz 2024-10-14 </p>
     */
    private int processBatch(List<TdTjBhk> batch, TdTjCheckTask task, BhkCheckCondition bhkCheckCondition, StringBuilder errorMsg) {
        // 5. 处理结果需要存储到数据库
        try {
            return bhkAllCheckService.saveBatchBhkCheck(batch, task, bhkCheckCondition);
        } catch (Exception e) {
            // 记录日志，不影响其他批次的处理
            log.error("批量处理异常数据：{}", batch);
            e.printStackTrace();
            errorMsg.append(e.getMessage());
            return 0;
        }
    }

}

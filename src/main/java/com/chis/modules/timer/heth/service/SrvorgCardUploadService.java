package com.chis.modules.timer.heth.service;

import com.chis.modules.timer.heth.enums.CardUploadCountryBusTypeEnum;
import com.chis.modules.timer.heth.logic.CountSrvorgCardJsonPO;
import com.chis.modules.timer.heth.logic.vo.TdZwOcchethCardPsnVo;
import com.chis.modules.timer.heth.logic.vo.TdZwUploadAddressVo;
import com.chis.modules.timer.heth.mapper.SrvorgCardUploadMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>类描述： 放射卫生技术服务信息报告卡服务类 </p>
 * @ClassAuthor： pw 2022/12/22
 **/
@Service
public class SrvorgCardUploadService {
    @Resource
    private SrvorgCardUploadMapper uploadMapper;
    /**
     * <p>方法描述： 查询待处理的放射卫生技术服务信息报告卡主表数据 </p>
     * @MethodAuthor： pw 2022/12/22
     **/
    public List<CountSrvorgCardJsonPO> findExecuteCardDataList(Integer pageSize, String startDate){
        if(null == pageSize){
            return Collections.EMPTY_LIST;
        }
        return this.uploadMapper.findExecuteCardDataList(pageSize, startDate);
    }

    /**
     * <p>方法描述： 查询待处理的放射卫生技术服务信息报告卡参与人员数据 </p>
     * @MethodAuthor： pw 2022/12/24
     **/
    public List<TdZwOcchethCardPsnVo> findExecutePsnDataList(Integer pageSize){
        if(null == pageSize){
            return Collections.EMPTY_LIST;
        }
        return this.uploadMapper.findExecutePsnDataList(pageSize);
    }

    /**
     * <p>方法描述： 查询待处理的放射卫生技术服务信息报告卡服务地址数据 </p>
     * @MethodAuthor： pw 2022/12/24
     **/
    public List<TdZwUploadAddressVo> findExecuteAddrDataList(Integer pageSize){
        if(null == pageSize){
            return Collections.EMPTY_LIST;
        }
        return this.uploadMapper.findExecuteAddrDataList(pageSize);
    }

    /**
     * 查询主表和子表关联日志记录状态全为1且创建日期+15天（配置文件）<=当天的数据
     *
     * @param dataSize 查询最大条数
     * @param lastDate 职业/放射卫生技术服务信息报送卡创建后可附件上传&提交天数
     * @return 查询到的报送卡的第三方唯一标识、统一社会信用代码、附件地址
     */
    public List<Map<String, Object>> findNeedUpdateFileAndSubmitData(Integer dataSize, Date lastDate) {
        return this.uploadMapper.selectNeedUpdateFileAndSubmitDataForSrvOrg(
                CardUploadCountryBusTypeEnum.SRVORGCARD.getCode(),
                CardUploadCountryBusTypeEnum.SRVORGPSN.getCode(),
                CardUploadCountryBusTypeEnum.SRVORGADDR.getCode(),
                dataSize,
                lastDate
        );
    }
}

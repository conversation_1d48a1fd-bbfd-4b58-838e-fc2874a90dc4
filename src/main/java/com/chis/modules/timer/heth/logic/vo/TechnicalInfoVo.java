package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

/**
 *  <p>类描述：技术服务机构查询</p>
 * @ClassAuthor hsj 2022-12-21 16:14
 */
@Data
public class TechnicalInfoVo {
    //传输密钥
    private String securityKey;
    //机构名称
    private String oname;
    //法定代表人（或主要负责人）
    private String lname;
    //资质证书编号
    private String bno;
    //证书有效期至
    private String vld;
    //系统已经存在的统一社会信用代码
    private String ocode;
    //注册地址-行政区划码
    private String rssxbm;
    //注册地址-详细地址
    private String raddress;
    //实验室地址
    private String laddress;
    //联系人
    private String cname;
    //联系电话
    private String cphone;
    
    //发证日期
    private String issued;
    






}

package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZwyjDangerBhk;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2020-11-21,TdZwyjDangerBhkMapper
 */
@Repository
public interface TdZwyjDangerBhkMapper extends ZwxBaseMapper<TdZwyjDangerBhk> {

    /** 通过体检机构id以及体检编号批量查询*/
    List<TdZwyjDangerBhk> selectListByOrgIdAndbhkCode(@Param("orgId") Integer orgId, @Param("bhkCodeList") List<String> bhkCodeList);

}

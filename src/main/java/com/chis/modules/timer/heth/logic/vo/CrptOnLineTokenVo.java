package com.chis.modules.timer.heth.logic.vo;

import com.chis.modules.webmvc.api.pojo.ReturnDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * <p>类描述：企业在线申报数据下载 token对象 </p>
 * @ClassAuthor： pw 2022/9/6
 **/
@Data
public class CrptOnLineTokenVo extends CrptOnLineReturnVo implements Serializable {
    private static final long serialVersionUID = 5954284867605579961L;
    private String tokenId;
    private Integer validateTime;
    private Date endDate;
}

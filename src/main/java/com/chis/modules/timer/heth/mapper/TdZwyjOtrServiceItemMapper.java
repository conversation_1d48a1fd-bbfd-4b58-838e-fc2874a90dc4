package com.chis.modules.timer.heth.mapper;


import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZwyjOtrServiceItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2020-11-12,TdZwyjOtrServiceItemMapper
 */
@Repository
public interface TdZwyjOtrServiceItemMapper extends ZwxBaseMapper<TdZwyjOtrServiceItem> {


    /**
     * <p>
     *     方法描述：通过主表rid列表批量查询超服务项目范围_人员
     * </p>
     *
     * @MethodAuthor pw,2020年11月19日,
     */
    List<TdZwyjOtrServiceItem> findOtrServiceItemListByMainIds(@Param("mainIds")List<Integer> mainIds);

}

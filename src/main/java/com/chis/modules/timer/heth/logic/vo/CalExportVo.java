package com.chis.modules.timer.heth.logic.vo;

import com.chis.comm.utils.StringUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 异步导出Vo对象
 *
 * @ClassAuthor pw,2021年12月20日,CalExportVo
 */
@Data
public class CalExportVo implements Serializable {
    private static final long serialVersionUID = 2111920979826352419L;
    /** 是否需要脱敏 默认不脱敏 */
    private boolean ifNeedEnctryInfo;
    /** 姓名 */
    private String name;
    /** 质控编号 */
    private String zkBhkCode;
    /** 体检编号 */
    private String bhkCode;
    /** 性别 */
    private String sex;
    /** 年龄 */
    private String age;
    /** 证件类型 */
    private String cardType;
    /** 证件号码 */
    private String idc;
    /** 联系电话 */
    private String linkTel;
    /** 用人单位名称 */
    private String workUnitName;
    /** 用人单位联系人 */
    private String workUnitLinkMan;
    /** 用人单位联系电话 */
    private String workUnitLinkTel;
    /** 用人单位所属地区 */
    private String workUnitZoneArea;
    /** 用人单位通讯地址 */
    private String workUnitAddress;
    /** 用人单位社会信用代码 */
    private String workUnitCreditCode;
    /** 用人单位规模 */
    private String workUnitCrptSize;
    /** 用人单位经济类型 */
    private String workUnitEconomy;
    /** 用人单位行业分类 取码表CODE_PATH */
    private String workUnitIndusType;
    /** 监测类型 */
    private String jcType;
    /** 在岗状态 */
    private String onguardState;
    /** 岗位 */
    private String dpt;//岗位取主表部门字段
    /** 工种 */
    private String workName;
    /** 总工龄 */
    private String wrklnt;//总工龄 拼接
    /** 接害工龄 */
    private String tchbadTotalTime;//接害工龄总时间 拼接
    /**
     * 体检类型
     */
    private String bhkType;
    /**
     * 体检危害因素
     */
    private String badRsnName;//拼接 体检危害因素
    /**
     * 体检危害因素明细结论
     * 显示格式“体检危害因素名称_体检结论”，多个中文逗号隔开
     */
    private String badRsnRstName;
    /** 主检结论 */
    private String mhkRst;//通过TD_TJ_MHKRST 取码表名称
    /**
     * 主检建议
     */
    private String mhkAdv;
    /**
     * 职业禁忌证名称
     * <p>体检主表关联的禁忌证子表 TD_TJ_CONTRAINDLIST</p>
     */
    private String contraindication;
    /**
     * 疑似职业病名称
     * <p>检主表关联的疑似职业病子表 TD_TJ_SUPOCCDISELIST</p>
     */
    private String suOccDisease;
    /** 体检机构 */
    private String orgName;//通过TB_TJ_SRVORG 获取
    /** 体检日期 */
    private String bhkDate;
    /** 报告打印日期 */
    private String rptPrintDate;
    /** 填报日期 CREATE_DATE*/
    private String fillDate;
    /** 个案审核的接收日期 DEAL_COMPLETE_DATE*/
    private String reciveDate;
    /**是否复查*/
    private String ifRhk;
    /** 个案审核的状态 */
    private String state;//需通过 zoneType以及checkLevel 判断
    /** 个案审核是否异常 */
    private String ifAbnomal;
    /** 个案审核异常原因 */
    private String errorMsg;//拼接异常
    /** 国家失败原因*/
    private String errMsg;
    /** 体检主表rid */
    private Integer bhkRid;
    /** 人员职业史 */
    private EmhistoryExportVo psnEmhistory;
    /** 放射史 */
    private EmhistoryExportVo fsEmhistory;
    /** 既往病史 */
    private AnamnesisExportVo anamnesis;
    /** 其他和症状 */
    private ExmsAndSymptomExportVo exmSym;
    /** 体检项目 */
    private List<BhkSubExportVo> itemList;

    /** 个案审核 初审提交日期 */
    private String countySmtDate;
    /** 个案审核 初审意见 */
    private String countyAuditAdv;
    /** 个案审核 复审提交日期 */
    private String citySmtDate;
    /** 个案审核 复审意见 */
    private String cityAuditAdv;
    /** 个案审核 终审提交日期 */
    private String proSmtDate;
    /** 个案审核 终审意见 */
    private String proAuditAdv;
    /** 个案审核 用工单位省直属 */
    private Integer ifProvDirect;
    /** 个案审核 用工单位市直属 */
    private Integer ifCityDirect;
    /** 个案审核状态 */
    private Integer chkState;

    /** 用工单位名称 */
    private String empCrptName;
    /** 用工单位联系人 */
    private String empLinkMan;
    /** 用工单位联系电话 */
    private String empLinkPhone;
    /** 用工单位所属地区 */
    private String empZone;
    /** 用工单位通讯地址 */
    private String empAddress;
    /** 用工单位社会信用代码 */
    private String empInstItutionCode;
    /** 用工单位企业规模 */
    private String empCrptSize;
    /** 用工单位经济类型 */
    private String empEconomy;
    /** 用工单位行业类别 */
    private String empIndusType;
    /** 接触危害因素 */
    private String touchBadName;
    /** 主动检测危害因素 */
    private String activeBadName;
    /**防护用品佩戴情况*/
    private String protectEquName;

    /**工种代码*/
    private String workNo;
    public String getIdc() {
        if(this.ifNeedEnctryInfo && StringUtils.isNotBlank(this.idc)){
            this.idc = StringUtils.encryptIdc(this.idc);
        }
        return idc;
    }

    public String getLinkTel() {
        if(this.ifNeedEnctryInfo && StringUtils.isNotBlank(this.linkTel)){
            this.linkTel = StringUtils.encryptPhone(this.linkTel);
        }
        return linkTel;
    }

    public String getWorkUnitLinkTel() {
        if(this.ifNeedEnctryInfo && StringUtils.isNotBlank(this.workUnitLinkTel)){
            this.workUnitLinkTel = StringUtils.encryptPhone(this.workUnitLinkTel);
        }
        return workUnitLinkTel;
    }

    public String getEmpLinkPhone() {
        if(this.ifNeedEnctryInfo && StringUtils.isNotBlank(this.empLinkPhone)){
            this.empLinkPhone = StringUtils.encryptPhone(this.empLinkPhone);
        }
        return empLinkPhone;
    }
}

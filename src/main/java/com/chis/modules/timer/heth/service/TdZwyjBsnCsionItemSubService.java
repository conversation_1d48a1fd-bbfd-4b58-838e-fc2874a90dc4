package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwyjBsnCsionItemSub;
import com.chis.modules.timer.heth.entity.TdZwyjBsnCsionItems;
import com.chis.modules.timer.heth.entity.TdZwyjBsnCsionPdjl;
import com.chis.modules.timer.heth.mapper.TdZwyjBsnCsionItemSubMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnCsionItemSubService
 */
@Service
public class TdZwyjBsnCsionItemSubService extends ZwxBaseServiceImpl<TdZwyjBsnCsionItemSubMapper, TdZwyjBsnCsionItemSub> {
    @Autowired
    private TdZwyjBsnCsionItemsService itemsService;
    @Autowired
    private TdZwyjBsnCsionPdjlService pdjlService;
    /**
     * <p>方法描述：查询判定项目</p>
     * @MethodAuthor qrr,2020-10-29,findCsionItemSubMap
     * */
    public Map<Integer,List<TdZwyjBsnCsionItemSub>> findCsionItemSubMap(){
        List<TdZwyjBsnCsionItemSub> subList = this.selectListByEntity(null);
        //key:配置主表Id,value:TdZwyjBsnCsionItemSub
        Map<Integer,List<TdZwyjBsnCsionItemSub>> subMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(subList)){
            for(TdZwyjBsnCsionItemSub t:subList){
                if(null==t.getFkByMainId()){
                    continue;
                }
                //项目配置规则
                List<TdZwyjBsnCsionItems> itemsList = itemsService.findItemsByMainId(t.getRid());
                if(!CollectionUtils.isEmpty(itemsList)){
                    t.setItemsList(itemsList);
                }
                if(null==subMap.get(t.getFkByMainId().getRid())){
                    List<TdZwyjBsnCsionItemSub> subs = new ArrayList<>();
                    subs.add(t);
                    subMap.put(t.getFkByMainId().getRid(),subs);
                }else {
                    List<TdZwyjBsnCsionItemSub> subs = subMap.get(t.getFkByMainId().getRid());
                    subs.add(t);
                }
            }
        }
        return subMap;
    }
}

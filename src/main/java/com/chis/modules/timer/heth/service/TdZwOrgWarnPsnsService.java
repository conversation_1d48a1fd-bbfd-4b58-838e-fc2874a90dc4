package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwOrgWarnPsns;
import com.chis.modules.timer.heth.mapper.TdZwOrgWarnPsnsMapper;
import org.springframework.stereotype.Service;

/**
 * <p> 服务实现类：  </p>
 *
 * <AUTHOR> 2024-07-12,TdZwOrgWarnPsnsService
 */
@Service
public class TdZwOrgWarnPsnsService extends ZwxBaseServiceImpl<TdZwOrgWarnPsnsMapper, TdZwOrgWarnPsns> {

    public void deleteByOrgWarnMainId(Integer orgWarnId) {
        baseMapper.deleteByOrgWarnMainId(orgWarnId);
    }
}

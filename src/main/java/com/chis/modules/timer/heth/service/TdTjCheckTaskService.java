package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdTjCheckTask;
import com.chis.modules.timer.heth.mapper.TdTjCheckTaskMapper;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2024-10-14,TdTjCheckTaskService
 */
@Service
public class TdTjCheckTaskService extends ZwxBaseServiceImpl<TdTjCheckTaskMapper, TdTjCheckTask> {

    public List<TdTjCheckTask> queryTasks() {
        return this.baseMapper.queryTasks();
    }

    public void updateTaskByRid(BigDecimal totalNum, Integer statue, String errorMsg, Integer psnId, Integer rid){
        this.baseMapper.updateTaskByRid(totalNum,statue,errorMsg,psnId,rid);
    }

     /**
     * 获取待处理的批量退回任务
     * 查询条件：TASK_TYPE=1，STATE=0
     * 排序：按创建时间正序
     * @return 待处理任务列表
     */
    public List<TdTjCheckTask> getPendingTasks() {
        return baseMapper.selectPendingTasks();
    }

    /**
     * 更新任务信息
     * @param task 待更新的任务对象
     * @return 更新结果
     */
    public int updateTask(TdTjCheckTask task) {
        return baseMapper.updateTask(task);
    }
}

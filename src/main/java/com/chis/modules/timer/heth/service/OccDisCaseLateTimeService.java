package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.entity.TdZwWarnPsns;
import com.chis.modules.timer.heth.mapper.OccDisCaseLateTimeMapper;
import com.chis.modules.timer.heth.pojo.IdcAndDiseIdPojo;
import com.chis.modules.timer.heth.pojo.OccDisCaseLatePojo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Service
public class OccDisCaseLateTimeService {

    @Autowired
    private OccDisCaseLateTimeMapper occDisCaseLateTimeMapper;

    /**
    * <p>Description：获取需要处理的体检数据 </p>
    * <p>Author： yzz 2023-11-10 </p>
    */
    public List<OccDisCaseLatePojo> selectBhkInfo(String beginDate,String zoneCode) {
        return occDisCaseLateTimeMapper.selectBhkInfo(beginDate,zoneCode);
    }

    public List<OccDisCaseLatePojo> selectOccDisCaseByBhkRids(List<IdcAndDiseIdPojo> idcAndDiseIds) {
        List<OccDisCaseLatePojo> result=new ArrayList<>();
        List<List<IdcAndDiseIdPojo>> IdcAndDiseIdList = StringUtils.splitListProxy(idcAndDiseIds, 100);
        if (CollectionUtils.isEmpty(IdcAndDiseIdList)) {
            return result;
        }
        for (List<IdcAndDiseIdPojo> idcAndDiseIdPojos : IdcAndDiseIdList) {
            List<OccDisCaseLatePojo> latePojos = occDisCaseLateTimeMapper.selectOccDisCaseByBhkRids(idcAndDiseIdPojos);
            if(CollectionUtils.isEmpty(latePojos)){
                continue;
            }
            result.addAll(latePojos);
        }

        return result;
    }
}

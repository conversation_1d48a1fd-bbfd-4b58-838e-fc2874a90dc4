package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.*;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人, 2022-09-02,TdZwcfPatientInfo
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWCF_PATIENT_INFO")
public class TdZwcfPatientInfo extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("PSN_NAME")
    private String psnName;

    @TableField("PSN_LINK_WAY")
    private String psnLinkWay;

    @TableField("IDC")
    private String idc;

    @TableField("BIRTHDAY")
    private Date birthday;

    @TableField("SEX")
    private String sex;

    @TableField(value = "CRPT_ZONE_ID", el = "fkByCrptZoneId.rid")
    private TsZone fkByCrptZoneId;

    @TableField("CRPT_NAME")
    private String crptName;

    @TableField(value = "INDUS_TYPE_ID", el = "fkByIndusTypeId.rid")
    private TsSimpleCode fkByIndusTypeId;

    @TableField("TCH_DUST_YEAR")
    private String tchDustYear;

    @TableField("TCH_DUST_MONTH")
    private String tchDustMonth;

    @TableField(value = "PNEUMSIS_TYPE_ID", el = "fkByPneumsisTypeId.rid")
    private TsSimpleCode fkByPneumsisTypeId;

    @TableField("OTHER_PNES_NAME")
    private String otherPnesName;

    @TableField("DIAG_1_DATE")
    private Date diag1Date;

    @TableField("DIAG_2_DATE")
    private Date diag2Date;

    @TableField("DIAG_3_DATE")
    private Date diag3Date;

    @TableField("LIVE_STATE")
    private String liveState;

    @TableField("DEATH_DATE")
    private Date deathDate;

    @TableField("DIRECT_DEATH_RSN")
    private String directDeathRsn;

    @TableField("INDIRECT_DEATH_RSN2")
    private String indirectDeathRsn2;

    @TableField("ORIGIN_DEATH_RSN2")
    private String originDeathRsn2;

    @TableField(value = "DEATH_RSN_ID", el = "fkByDeathRsnId.rid")
    private TsSimpleCode fkByDeathRsnId;

    @TableField(value = "RCD_USER_ID", el = "fkByRcdUserId.rid")
    private TsUserInfo fkByRcdUserId;

    @TableField(value = "RCD_UNIT_ID", el = "fkByRcdUnitId.rid")
    private TsUnit fkByRcdUnitId;

    @TableField(value = "MOD_USER_ID", el = "fkByModUserId.rid")
    private TsUserInfo fkByModUserId;

    @TableField(value = "MOD_UNIT_ID", el = "fkByModUnitId.rid")
    private TsUnit fkByModUnitId;

    @TableField("MAIN_CARD_ID")
    private String mainCardId;

    @TableField("SUB_CARD_ID")
    private String subCardId;

    @TableField("PATIENT_SOURCE")
    private String patientSource;

    @TableField("RPT_CARD_NO")
    private String rptCardNo;

    @TableField(value = "RPT_TYPE_ID", el = "fkByRptTypeId.rid")
    private TsSimpleCode fkByRptTypeId;

    @TableField("LINK_PSN_NAME")
    private String linkPsnName;

    @TableField("LINK_ADDR")
    private String linkAddr;

    @TableField("LINK_PHONE")
    private String linkPhone;

    @TableField("CRPT_CREDIT_CODE")
    private String crptCreditCode;

    @TableField("CRPT_ADDR")
    private String crptAddr;

    @TableField(value = "ECONOMY_ID", el = "fkByEconomyId.rid")
    private TsSimpleCode fkByEconomyId;

    @TableField(value = "CRPT_SIZE_ID", el = "fkByCrptSizeId.rid")
    private TsSimpleCode fkByCrptSizeId;

    @TableField(value = "ANALY_WORK_ID", el = "fkByAnalyWorkId.rid")
    private TsSimpleCode fkByAnalyWorkId;

    @TableField("OTHER_WORK_NAME")
    private String otherWorkName;

    @TableField("BEG_TCH_DUST")
    private Date begTchDust;

    @TableField("DIAG_UNIT_NAME")
    private String diagUnitName;

    @TableField("DIAG_RESP_PSN")
    private String diagRespPsn;

    @TableField("FILL_FORM_PSN")
    private String fillFormPsn;

    @TableField("FILL_LINK")
    private String fillLink;

    @TableField("FILL_DATE")
    private Date fillDate;

    @TableField("IF_TB")
    private String ifTb;

    @TableField("TB_DIAG_DATE")
    private Date tbDiagDate;

    @TableField("IF_PUL_INFECTION")
    private String ifPulInfection;

    @TableField("INFECTION_DIAG_DATE")
    private Date infectionDiagDate;

    @TableField("IF_THE_PNEUM")
    private String ifThePneum;

    @TableField("PNEUM_DIAG_DATE")
    private Date pneumDiagDate;

    @TableField("IF_PUL_HEART")
    private String ifPulHeart;

    @TableField("HEART_DIAG_DATE")
    private Date heartDiagDate;

    @TableField("IF_LUNG_CANCER")
    private String ifLungCancer;

    @TableField("LUNG_DIAG_DATE")
    private Date lungDiagDate;

    @TableField("IF_UNIT_EXISTS")
    private String ifUnitExists;

    @TableField("IF_WORK_INSURANCE")
    private String ifWorkInsurance;

    @TableField("IF_MEDICAL_TREAT")
    private String ifMedicalTreat;

    @TableField(value = "WORK_LEVEL_ID", el = "fkByWorkLevelId.rid")
    private TsSimpleCode fkByWorkLevelId;

    @TableField("IF_INJURY_TREAT")
    private String ifInjuryTreat;

    @TableField("IF_DEATH_TREAT")
    private String ifDeathTreat;

    @TableField("IF_OTHER_TREAT")
    private String ifOtherTreat;

    @TableField("IF_GET_MEDICAL_HELP")
    private String ifGetMedicalHelp;

    @TableField("IF_GET_LIVE_HELP")
    private String ifGetLiveHelp;

    @TableField("RMK")
    private String rmk;

    @TableField("CRPT_ZONE_COD_GJ")
    private String crptZoneCodGj;

    @TableField("CRPT_ZONE_NAM_GJ")
    private String crptZoneNamGj;

    @TableField("RPT_TYPE_COD_GJ")
    private String rptTypeCodGj;

    @TableField("RPT_TYPE_NAM_GJ")
    private String rptTypeNamGj;

    @TableField("ECO_COD_GJ")
    private String ecoCodGj;

    @TableField("ECO_NAM_GJ")
    private String ecoNamGj;

    @TableField("INDUS_COD_GJ")
    private String indusCodGj;

    @TableField("INDUS_NAM_GJ")
    private String indusNamGj;

    @TableField("SIZE_COD_GJ")
    private String sizeCodGj;

    @TableField("SIZE_NAM_GJ")
    private String sizeNamGj;

    @TableField("ANALY_WORK_COD_GJ")
    private String analyWorkCodGj;

    @TableField("ANALY_WORK_NAM_GJ")
    private String analyWorkNamGj;

    @TableField("OTHER_WORK_NAM_GJ")
    private String otherWorkNamGj;

    @TableField("PNE_COD_GJ")
    private String pneCodGj;

    @TableField("PNE_NAM_GJ")
    private String pneNamGj;

    @TableField("OTHER_PNE_NAM_GJ")
    private String otherPneNamGj;

    @TableField("DEATH_RSN_COD_GJ")
    private String deathRsnCodGj;

    @TableField("DEATH_RSN_NAM_GJ")
    private String deathRsnNamGj;

    @TableField("OTHER_DEATH_NAM_GJ")
    private String otherDeathNamGj;

    @TableField("LINKMAN")
    private String linkman;

    @TableField("POSTALCODE")
    private String postalcode;

    @TableField("IF_CRPT_INDEMNITY")
    private String ifCrptIndemnity;

    @TableField("IF_URBAN_RURAL_INSURE")
    private String ifUrbanRuralInsure;

    @TableField("IF_BIG_DIS_INSURE")
    private String ifBigDisInsure;

    @TableField("MEDICAL_IN_INSURE_RATIO")
    private String medicalInInsureRatio;

    @TableField("MEDICAL_OUT_INSURE_RATIO")
    private String medicalOutInsureRatio;

    @TableField("SUBSISTENCE_CASE")
    private String subsistenceCase;

    @TableField("SUBSISTENCE_AMO")
    private String subsistenceAmo;

    @TableField("NORMAL_ZONE_ID")
    private String normalZoneId;

    @TableField("NORMAL_ADDR")
    private String normalAddr;

    @TableField("FLOWUP_DATE")
    private Date flowupDate;

    @TableField("FLOWUP_PSN")
    private String flowupPsn;

    @TableField("NOW_ZONE_ID")
    private String nowZoneId;

    @TableField("NOW_ADDR")
    private String nowAddr;

    @TableField("SYNC_KF_STATE")
    private Integer syncKfState;

    @TableField("SYNC_KF_DATE")
    private Date syncKfDate;

    @TableField("SYNC_KF_ERR_MSG")
    private String syncKfErrMsg;


    public TdZwcfPatientInfo(Integer rid) {
        super(rid);
    }


}

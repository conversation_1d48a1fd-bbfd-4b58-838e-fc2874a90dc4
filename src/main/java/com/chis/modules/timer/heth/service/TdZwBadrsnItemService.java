package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwBadrsnItem;
import com.chis.modules.timer.heth.logic.vo.BadrsnItemVo;
import com.chis.modules.timer.heth.mapper.TdZwBadrsnItemMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2021-05-12,TdZwBadrsnItemService
 */
@Service
public class TdZwBadrsnItemService extends ZwxBaseServiceImpl<TdZwBadrsnItemMapper, TdZwBadrsnItem> {


    /**
     * @Description: 获取接触的有害因素下配置的所有必检体检项目
     *
     * @MethodAuthor pw,2021年05月12日
     */
    public Map<Integer,List<BadrsnItemVo>> findPartialTdZwBadrsnItemListByBhkRids(){
        Map<Integer,List<BadrsnItemVo>> resultMap = new HashMap<>();
        List<BadrsnItemVo> list = this.baseMapper.findPartialTdZwBadrsnItemListByBhkRids();
        if(!CollectionUtils.isEmpty(list)){
            for(BadrsnItemVo itemVo : list){
                if(null == itemVo.getBadrsnId() || null == itemVo.getItemId()){
                    continue;
                }
                List<BadrsnItemVo> itemVoList = resultMap.get(itemVo.getBadrsnId());
                if(null == itemVoList){
                    itemVoList = new ArrayList<>();
                }
                itemVoList.add(itemVo);
                resultMap.put(itemVo.getBadrsnId(), itemVoList);
            }
        }
        return resultMap;
    }

}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZdzybSpecialAnaly
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZDZYB_SPECIAL_ANALY")
public class TdZdzybSpecialAnaly extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("ANALY_TYPE")
    private String analyType;

    @TableField(value = "BADRSN_ID" , el = "fkByBadrsnId.rid")
    private TsSimpleCode fkByBadrsnId;

    @TableField("ANALY_NAME")
    private String analyName;

    @TableField("ITEM_STDVALUE")
    private String itemStdvalue;

    @TableField("XH")
    private String xh;

    @TableField("RMK")
    private String rmk;

    @TableField("IF_OCC_CARCINOGENS")
    private String ifOccCarcinogens;


    public TdZdzybSpecialAnaly(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwWarnPsns;
import com.chis.modules.timer.heth.mapper.TdZwWarnPsnsMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2023-11-10,TdZwWarnPsnsService
 */
@Service
public class TdZwWarnPsnsService extends ZwxBaseServiceImpl<TdZwWarnPsnsMapper, TdZwWarnPsns> {


    /**
     * <p>方法描述：通过主表rid删除子表 </p>
     * pw 2023/11/11
     **/
    @Transactional(rollbackFor = Exception.class)
    public void delWarnPsnByMainRids (List<Integer> mainRidList) {
        if (CollectionUtils.isEmpty(mainRidList)) {
            return;
        }
        List<List<Integer>> groupList = StringUtils.splitListProxy(mainRidList, 1000);
        for (List<Integer> curList : groupList) {
            this.baseMapper.delWarnPsnByMainRids(curList);
        }
    }
}

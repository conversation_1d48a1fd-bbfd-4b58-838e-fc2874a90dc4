package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人, 2022-09-02,TdZwcfKfCfOper
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWCF_KF_CF_OPER")
public class TdZwcfKfCfOper extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID", el = "fkByMainId.rid")
    private TdZwcfKfCf fkByMainId;

    @TableField("OPER_DATE")
    private Date operDate;

    @TableField("OPER_PDF_PATH")
    private String operPdfPath;


    public TdZwcfKfCfOper(Integer rid) {
        super(rid);
    }


}

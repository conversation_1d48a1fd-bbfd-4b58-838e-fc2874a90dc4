package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-30,TdTjResultJudgeItem
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_TJ_RESULT_JUDGE_ITEM")
public class TdTjResultJudgeItem extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdTjBhk fkByMainId;

    @TableField("JUDGE_TYPE")
    private String judgeType;

    @TableField(value = "ITEM_ID" , el = "fkByItemId.rid")
    private TdZdzybAnalyItmType fkByItemId;


    public TdTjResultJudgeItem(Integer rid) {
        super(rid);
    }


}

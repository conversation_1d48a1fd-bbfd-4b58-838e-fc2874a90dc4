package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnBadrsns
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("TD_ZWYJ_BSN_BADRSNS")
@KeySequence(value = "TD_ZWYJ_BSN_BADRSNS_SEQ",clazz = Integer.class)
public class TdZwyjBsnBadrsns  implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键：默认自增
     */
    @TableId
    protected Integer rid;

    @TableField(value = "BHK_ID" , el = "fkByBhkId.rid")
    private TdZwyjBsnBhk fkByBhkId;

    @TableField(value = "BADRSN_ID" , el = "fkByBadrsnId.rid")
    private TsSimpleCode fkByBadrsnId;

    @TableField(value = "EXAM_CONCLUSION_ID" , el = "fkByExamConclusionId.rid")
    private TsSimpleCode fkByExamConclusionId;

    @TableField("QTJB_NAME")
    private String qtjbName;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    protected Integer createManid;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    protected Date createDate;

    public TdZwyjBsnBadrsns(Integer rid) {
        this.rid = rid;
    }


}

package com.chis.modules.timer.heth.logic;

import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 异步导出 个案审核 查询条件对象
 *
 * @ClassAuthor pw,2021年12月23日,BhkAuditQueryPO
 */
@Data
public class BhkAuditQueryPO implements Serializable {
    private static final long serialVersionUID = -2613459060639292512L;
    private Integer checkLevel;
    private Integer zoneType;
    private List<Integer> searchUnitIdList;
    private String searchZoneCode;
    private String searchCrptName;
    private String searchCreditCode;
    private String searchPersonName;
    private String searchIdc;
    private String searchBhkBdate;
    private String searchBhkEdate;
    private List<Integer> selectOnGuardIds;
    private List<Integer> selectBadRsnIds;
    private List<Integer> jcTypeList;
    /**体检类型*/
    private List<Integer> bhkTypeList;
    private Integer ifRhk;
    private Integer ifAbnormal;
    private List<Integer> stateList;
    private List<Integer> searchStateList;
    private String searchRcvBdate;
    private String searchRcvEdate;
    private String startCreateDate;
    private String endCreateDate;
    private String startRptPrintDate;
    private String endRptPrintDate;

    /** 分页查询的开始行 与endRow同时使用 少一个分页条件都忽略*/
    private Integer startRow;
    /** 分页查询的结束行 与startRow同时使用 少一个分页条件都忽略*/
    private Integer endRow;

    /** 状态是否包含3 */
    private Integer ifConThree;
    /** 状态是否包含2 */
    private Integer ifConTwo;
    /** 状态是否包含1 */
    private Integer ifConOne;
    /** 状态是否包含0 */
    private Integer ifConZero;

    //用工单位地区
    private String searchZoneCodeEmp;
    //用工单位名称
    private String searchCrptNameEmp;
    //用工单位社会信用代码
    private String searchCreditCodeEmp;
    /** 是否需要脱敏 true是*/
    private Boolean ifNeedEnctryInfo;
    /** 异常情况*/
    private String searchAbnormals;
    /**选择的单危害因素结论*/
    private List<Integer> searchSelBhkrstIds;

    /**
     * 质控编号
     */
    private String zkBhkCode;

    public Integer getIfConThree() {
        if(!CollectionUtils.isEmpty(stateList) && stateList.contains(3)){
            return 1;
        }
        return ifConThree;
    }

    public Integer getIfConTwo() {
        if(new Integer(3).equals(checkLevel) && !CollectionUtils.isEmpty(stateList) && stateList.contains(2)){
            return 1;
        }
        return ifConTwo;
    }

    public Integer getIfConOne() {
        if(new Integer(3).equals(checkLevel) && !CollectionUtils.isEmpty(stateList) && stateList.contains(1)){
            return 1;
        }
        return ifConOne;
    }

    public Integer getIfConZero() {
        if(!CollectionUtils.isEmpty(stateList) && stateList.contains(0)){
            return 1;
        }
        return ifConZero;
    }
}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsUnit;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-19,TdZwyjOtrWarn
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_OTR_WARN")
@KeySequence(value = "TD_ZWYJ_OTR_WARN_SEQ",clazz = Integer.class)
public class TdZwyjOtr<PERSON>arn extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "CITY_ZONE_ID" , el = "fkByCityZoneId.rid")
    private TsZone fkByCityZoneId;

    @TableField("WARN_TYPE")
    private String warnType;

    @TableField(value = "BHKORG_ID" , el = "fkByBhkorgId.rid")
    private TsUnit fkByBhkorgId;

    @TableField(value = "BHKORG_ZONE_ID" , el = "fkByBhkorgZoneId.rid")
    private TsZone fkByBhkorgZoneId;

    @TableField("WARN_CONT")
    private String warnCont;

    @TableField("HAPPEN_DATE")
    private Date happenDate;

    @TableField("OTR_PSNS")
    private String otrPsns;

    @TableField(value = "DEAL_ORG_ID" , el = "fkByDealOrgId.rid")
    private TsUnit fkByDealOrgId;

    @TableField("IF_OUT_RANGE")
    private String ifOutRange;

    @TableField("DEAL_CONT")
    private String dealCont;

    @TableField("DEAL_DATE")
    private Date dealDate;

    @TableField(value = "CHECK_ORG_ID" , el = "fkByCheckOrgId.rid")
    private TsUnit fkByCheckOrgId;

    @TableField("CHECK_RST")
    private String checkRst;

    @TableField("CHECK_CONT")
    private String checkCont;

    @TableField("CHECK_DATE")
    private Date checkDate;

    @TableField("STATE_MARK")
    private String stateMark;


    public TdZwyjOtrWarn(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdZyUnitbasicRcd
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZY_UNITBASIC_RCD")
@KeySequence(value = "TD_ZY_UNITBASIC_RCD_SEQ",clazz = Integer.class)
public class TdZyUnitbasicRcd extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("DOWN_MESSAGE")
    private String downMessage;

    @TableField("DEAL_MARK")
    private Integer dealMark;

    @TableField("DEAL_RSN")
    private String dealRsn;


    public TdZyUnitbasicRcd(Integer rid) {
        super(rid);
    }


}

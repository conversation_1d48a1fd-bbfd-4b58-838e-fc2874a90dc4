package com.chis.modules.timer.heth.enums;

import lombok.Getter;

/**
 * 预警异常枚举
 */
@Getter
public enum WarnExceptEnums {
    FILING_NEW("1", "新备案"),
    FILING_RENEWAL("2", "备案续展"),
    PSN_REQUIREMENTS("3", "人员配置要求预警"),
    PSN_RE_EDUCATION("4", "人员培训再教育预警"),
    PSN_IN_MULTIPLE_LOCATIONS("5", "人员所在机构情况预警"),
    PSN_TITLE_LEVEL("6", "人员职称级别预警"),
    INST_REQUIREMENTS("7", "设备配置要求预警"),
    INST_VERIFICATION("8", "设备检定要求预警");

    private final String extents1;
    private final String name;

    WarnExceptEnums(String extents1, String name) {
        this.extents1 = extents1;
        this.name = name;
    }
}

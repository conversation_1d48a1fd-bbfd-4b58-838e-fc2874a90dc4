package com.chis.modules.timer.heth.logic.vo;

import com.chis.modules.webmvc.api.pojo.ReturnDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>类描述：专项治理数据下载 请求返回数据对象 </p>
 * pw 2023/9/21
 **/
@Data
public class SpecialRectficationReturnVo extends ReturnDTO implements Serializable {
    private static final long serialVersionUID = 2033988458667158923L;
    private Date ms;
    private Integer total;
    private List<SpecialRectficationBaseInfoVo> rectificationListExpList;
}

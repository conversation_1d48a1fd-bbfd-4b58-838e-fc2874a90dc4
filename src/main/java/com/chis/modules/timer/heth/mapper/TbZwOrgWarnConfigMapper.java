package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TbZwOrgWarnConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * <AUTHOR> 2024-07-11,TbZwOrgWarnConfigMapper
 */
@Repository
public interface TbZwOrgWarnConfigMapper extends ZwxBaseMapper<TbZwOrgWarnConfig> {
    /**
     * 通过资质类型查询资质机构预警配置
     *
     * @param busType 资质类型
     * @return 资质机构预警配置
     */
    List<TbZwOrgWarnConfig> dataAndSubList(@Param("busType") Integer busType);
}

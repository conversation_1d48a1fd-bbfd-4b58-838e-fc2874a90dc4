package com.chis.modules.timer.heth.job.zw.warn;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.service.TsSimpleCodeService;
import com.chis.modules.sys.service.TsSysHolidayService;
import com.chis.modules.timer.heth.entity.TbZwWarnModel;
import com.chis.modules.timer.heth.entity.TdZwWarnInfo;
import com.chis.modules.timer.heth.entity.TdZwWarnPsns;
import com.chis.modules.timer.heth.pojo.IdcAndDiseIdPojo;
import com.chis.modules.timer.heth.pojo.OccDisCaseLatePojo;
import com.chis.modules.timer.heth.service.OccDisCaseLateTimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 疑似职业病诊断不及时
 */
@Slf4j
@Component
public class OccDisCaseLateTimeJob extends AbstractNotInTimeWarn{
    /**预警天数*/
    @Value("${warn-timer.zwWarn.occDisCase-late-days}")
    private Integer lateDays;
    @Autowired
    private OccDisCaseLateTimeService occDisCaseLateTimService;

    @Autowired
    private TsSimpleCodeService simpleCodeService;

    @Autowired
    private TsSysHolidayService holidayService;

    /**对码表5026进行缓存 key:code_no value: rid*/
    protected Map<String, Integer> simpCode5026Map;

    @Scheduled(cron = "${warn-timer.zwWarn.cron.occDisCase-late}")
    public void start() throws Exception {
        //初始化
        init();

        //1、获取需要处理的体检数据
        List<OccDisCaseLatePojo> bhkList = occDisCaseLateTimService.selectBhkInfo(beginDate,zoneCode);
        if (CollectionUtils.isEmpty(bhkList)) {
            return;
        }

        //计算报告打印日期+lateDays后的日期，缓存备用
        Map<String,Date> warnDateMap=new HashMap<>();
        //key:企业rid  value：体检数据
        Map<Integer, List<OccDisCaseLatePojo>> countryBhkMap = new HashMap<>();
        Map<Integer, List<OccDisCaseLatePojo>> cityBhkMap = new HashMap<>();
        Map<Integer, List<OccDisCaseLatePojo>> provincialBhkMap = new HashMap<>();
        Set<String> bhkNoRepeatSet = new HashSet<>();
        //2、拼装身份证+病种list,匹配查询诊断数据
        List<IdcAndDiseIdPojo> idcAndDiseIdPOList = getIdcAndDiseIdPojos(bhkList, warnDateMap, countryBhkMap, cityBhkMap,provincialBhkMap, bhkNoRepeatSet);

        //3、根据身份证和病种rid去重匹配出来的全量的诊断记录
        Map<String, List<Date>> idcADiseIdMap = getOccDisCaseByIdcADis(idcAndDiseIdPOList);
        //分别循环 区县体检记录 ，市级体检记录 与map匹配
        Map<Integer, List<OccDisCaseLatePojo>> countryWarnMap = new HashMap<>();
        Map<Integer, List<OccDisCaseLatePojo>> cityWarnMap = new HashMap<>();
        Map<Integer, List<OccDisCaseLatePojo>> provincialWarnMap = new HashMap<>();
        getWarnMap(warnDateMap, countryBhkMap, cityBhkMap,provincialBhkMap, idcADiseIdMap, countryWarnMap, cityWarnMap,provincialWarnMap);

        //4、区县、市分别匹配模型 按单位计算预警例数
        List<TdZwWarnInfo> warnInfoList = getTdZwWarnInfos(countryWarnMap, cityWarnMap,provincialWarnMap);
        if(CollectionUtils.isEmpty(warnInfoList)){
            return;
        }
        //5、保存逻辑
        saveWarnInfo(warnInfoList);
    }

    
    /**
    * <p>Description：合并处理预警消息 </p>
    * <p>Author： yzz 2023-11-11 </p>
    */
    private List<TdZwWarnInfo> getTdZwWarnInfos(Map<Integer, List<OccDisCaseLatePojo>> countryWarnMap, Map<Integer, List<OccDisCaseLatePojo>> cityWarnMap,Map<Integer, List<OccDisCaseLatePojo>> provincialWarnMap) {
        List<TdZwWarnInfo> countryWarnInfoList = dealCycleWarnData(warnModelMap, countryWarnMap, 1);
        List<TdZwWarnInfo> cityWarnInfoList = dealCycleWarnData(warnModelMap, cityWarnMap, 2);
        List<TdZwWarnInfo> provincialWarnInfoList = dealCycleWarnData(warnModelMap, provincialWarnMap, 3);
        //区县预警 市级预警合并处理
        List<TdZwWarnInfo> warnInfoList=new ArrayList<>();
        warnInfoList.addAll(countryWarnInfoList);
        warnInfoList.addAll(cityWarnInfoList);
        warnInfoList.addAll(provincialWarnInfoList);
        return warnInfoList;
    }

    /**
    * <p>Description：分别处理区县，市级预警记录 </p>
    * <p>Author： yzz 2023-11-11 </p>
    */
    private void getWarnMap(Map<String, Date> warnDateMap, Map<Integer, List<OccDisCaseLatePojo>> countryBhkMap,
                                   Map<Integer, List<OccDisCaseLatePojo>> cityBhkMap,
                                   Map<Integer, List<OccDisCaseLatePojo>> provincialBhkMap,
                                   Map<String, List<Date>> idcADiseIdMap,
                                   Map<Integer, List<OccDisCaseLatePojo>> countryWarnMap,
                                   Map<Integer, List<OccDisCaseLatePojo>> cityWarnMap,
                                   Map<Integer, List<OccDisCaseLatePojo>> provincialWarnMap) {
        getCrptWarnRecords(countryBhkMap, idcADiseIdMap, countryWarnMap, warnDateMap);
        getCrptWarnRecords(cityBhkMap, idcADiseIdMap, cityWarnMap, warnDateMap);
        getCrptWarnRecords(provincialBhkMap, idcADiseIdMap, provincialWarnMap, warnDateMap);
    }

    /**
    * <p>Description：通过idc+病种查询诊断记录 </p>
    * <p>Author： yzz 2023-11-11 </p>
    */
    private Map<String, List<Date>> getOccDisCaseByIdcADis(List<IdcAndDiseIdPojo> idcAndDiseIdPOList) {
        List<OccDisCaseLatePojo> occDisCaseList = occDisCaseLateTimService.selectOccDisCaseByBhkRids(idcAndDiseIdPOList);
        //循环处理诊断记录，缓存map key:idc+病种  value:诊断日期-lateDays
        Map<String, List<Date>> idcADiseIdMap = new HashMap<>();
        if (CollectionUtils.isEmpty(occDisCaseList)) {
            return idcADiseIdMap;
        }
        for (OccDisCaseLatePojo occDisCasePo : occDisCaseList) {
            String key=occDisCasePo.getIdc() + "&" + occDisCasePo.getOccDiseid();
            if(idcADiseIdMap.containsKey(key)){
                idcADiseIdMap.get(key).add(occDisCasePo.getApplyDate());
            }else {
                List<Date> dateList=new ArrayList<>();
                dateList.add(occDisCasePo.getApplyDate());
                idcADiseIdMap.put(key,dateList);
            }
        }
        return idcADiseIdMap;
    }

    /**
    * <p>Description：处理体检主表 返回idc+病种 </p>
    * <p>Author： yzz 2023-11-11 </p>
    */
    private List<IdcAndDiseIdPojo> getIdcAndDiseIdPojos(List<OccDisCaseLatePojo> bhkList, Map<String, Date> warnDateMap,
                                                        Map<Integer, List<OccDisCaseLatePojo>> countryBhkMap,
                                                        Map<Integer, List<OccDisCaseLatePojo>> cityBhkMap,
                                                        Map<Integer, List<OccDisCaseLatePojo>> provincialBhkMap,Set<String> bhkNoRepeatSet) {
        //区县预警、市级预警 体检数据区分
        bhkList.forEach(pojo -> {
            //区县Map
            if ("1".equals(pojo.getWarnUnit())) {
                if (countryBhkMap.containsKey(pojo.getCrptId())) {

                    countryBhkMap.get(pojo.getCrptId()).add(pojo);
                } else {
                    List<OccDisCaseLatePojo> countryBhkList = new ArrayList<>();
                    countryBhkList.add(pojo);
                    countryBhkMap.put(pojo.getCrptId(), countryBhkList);
                }
            }
            //市级Map
            if ("2".equals(pojo.getWarnUnit())) {
                if (cityBhkMap.containsKey(pojo.getCrptId())) {
                    cityBhkMap.get(pojo.getCrptId()).add(pojo);
                } else {
                    List<OccDisCaseLatePojo> cityBhkList = new ArrayList<>();
                    cityBhkList.add(pojo);
                    cityBhkMap.put(pojo.getCrptId(), cityBhkList);
                }
            }
            //省级Map
            if ("3".equals(pojo.getWarnUnit())) {
                if (provincialBhkMap.containsKey(pojo.getCrptId())) {
                    provincialBhkMap.get(pojo.getCrptId()).add(pojo);
                } else {
                    List<OccDisCaseLatePojo> provincialBhkList = new ArrayList<>();
                    provincialBhkList.add(pojo);
                    provincialBhkMap.put(pojo.getCrptId(), provincialBhkList);
                }
            }
            //根据身份证和病种去重 后续过滤诊断记录用
            if(StringUtils.isNotBlank(pojo.getCodeNo())) {
                if (pojo.getCodeNo().contains(",")) {
                    //5010码表extends3对应的职业病诊断病种5026编码，多个英文逗号分隔
                    for (String codeNo : pojo.getCodeNo().split(",")) {
                        if (simpCode5026Map.containsKey(codeNo)) {
                            bhkNoRepeatSet.add(pojo.getIdc() + "&" + simpCode5026Map.get(codeNo));
                        }
                    }
                } else {
                    if(simpCode5026Map.containsKey(pojo.getCodeNo())){
                        bhkNoRepeatSet.add(pojo.getIdc() + "&" + simpCode5026Map.get(pojo.getCodeNo()));
                    }
                }
            }
            //计算报告打印日期+lateDays后的日期，缓存备用
            if (!warnDateMap.containsKey(DateUtils.formatDate(pojo.getRptPrintDate()))) {
                warnDateMap.put(DateUtils.formatDate(pojo.getRptPrintDate()), holidayService.calcuteWorkDayDate(pojo.getRptPrintDate(), this.lateDays));
            }
        });
        //组装成list<身份证，病种> 循环查询诊断记录使用
        List<IdcAndDiseIdPojo> idcAndDiseIdPOList = new ArrayList<>();
        if (bhkNoRepeatSet.isEmpty()) {
            return idcAndDiseIdPOList;
        }
        bhkNoRepeatSet.forEach(po -> {
            IdcAndDiseIdPojo idPO = new IdcAndDiseIdPojo();
            idPO.setIdc(po.split("&")[0]);
            idPO.setDiseId(Integer.parseInt(po.split("&")[1]));
            idcAndDiseIdPOList.add(idPO);
        });
        return idcAndDiseIdPOList;
    }


    /**
    * <p>Description：码表缓存 </p>
    * <p>Author： yzz 2023-11-11 </p>
    */
    @Override
    public void init() {
        super.init();
        if (lateDays == null) {
            throw new RuntimeException("配置的预警天数(warn-timer.zwWarn.occDisCase-late-days)不能为空！");
        }
        //对码表5026进行缓存 key:code_no value: rid
        simpCode5026Map = new HashMap<>();
        List<TsSimpleCode> allTsSimpleCodeList = simpleCodeService.findAllTsSimpleCodeList("5026");
        if (CollectionUtils.isEmpty(allTsSimpleCodeList)) {
            return;
        }
        allTsSimpleCodeList.forEach(simp -> {
            simpCode5026Map.put(simp.getCodeNo(), simp.getRid());
        });
    }

    /**
    * <p>Description：处理，封装预警数据 </p>
    * <p>Author： yzz 2023-11-11 </p>
    */
    private List<TdZwWarnInfo> dealCycleWarnData(Map<Integer, TbZwWarnModel> warnModelMap, Map<Integer, List<OccDisCaseLatePojo>> countryWarnMap,Integer level) {
        if (countryWarnMap.isEmpty()) {
            return new ArrayList<>();
        }
        List<TdZwWarnInfo> warnInfo=new ArrayList<>();
        for (Map.Entry<Integer, List<OccDisCaseLatePojo>> entry : countryWarnMap.entrySet()) {
            boolean fail = calRuleIfFail(warnModelMap.get(level), entry.getValue().size());
            if (fail) {
                log.info(entry.getValue().get(0).getCrptName() + "的" + getWarnName() + "为" + entry.getValue().size()
                        + "例，不在预警模型范围内！");
            } else {
                warnInfo.add(packageWarnInfo(entry.getValue(), warnModelMap.get(level), level));
            }
        }
        return warnInfo;
    }

    /**
    * <p>Description：封装预警信息 </p>
    * <p>Author： yzz 2023-11-11 </p>
    */
    private TdZwWarnInfo packageWarnInfo(List<OccDisCaseLatePojo> occDisCaseLatePojos,TbZwWarnModel warnModel,Integer viewLevel) {
        if(CollectionUtils.isEmpty(occDisCaseLatePojos)){
            return null;
        }
        //取最小报告打印日期
        OccDisCaseLatePojo minLatePojo=null;
        Optional<OccDisCaseLatePojo> LateOptional = occDisCaseLatePojos.stream()
                .min(Comparator.comparing(OccDisCaseLatePojo::getRptPrintDate));
        if(LateOptional.isPresent() && LateOptional.get().getRptPrintDate()!=null){
            minLatePojo = LateOptional.get();
        }
        //封装主表信息
        TdZwWarnInfo tdZwWarnInfo=new TdZwWarnInfo();
        tdZwWarnInfo.setFkByModelId(warnModel);
        tdZwWarnInfo.setFkByWarnZone(new TsZone(minLatePojo.getZoneId()));
        tdZwWarnInfo.setBusType(getBusType());
        tdZwWarnInfo.setBusId(minLatePojo.getCrptId());
        tdZwWarnInfo.setHappenDate(new Date());
        tdZwWarnInfo.setJcBeginDate(minLatePojo.getRptPrintDate());
        tdZwWarnInfo.setJcEndDate(new Date());
        tdZwWarnInfo.setHappenNum(occDisCaseLatePojos.size());
        tdZwWarnInfo.setWarnCont(getWarnCont(warnModel,minLatePojo.getZoneName(),tdZwWarnInfo.getJcBeginDate(),tdZwWarnInfo.getJcEndDate(),minLatePojo.getCrptName(),occDisCaseLatePojos.size()));
        tdZwWarnInfo.setStateMark(0);
        tdZwWarnInfo.setViewLevel(viewLevel);
        //封装人员信息
        List<TdZwWarnPsns> warnPsnList=new ArrayList<>();
        occDisCaseLatePojos.forEach(pojo->{
            TdZwWarnPsns warnPsns=new TdZwWarnPsns();
            warnPsns.setFkByMainId(tdZwWarnInfo);
            warnPsns.setBusId(pojo.getRid());
            warnPsns.setRcvDate(pojo.getRptPrintDate());
            warnPsns.setDealDate(pojo.getApplyDate());
            warnPsns.setFkByDisId(new TsSimpleCode(pojo.getOccDiseid()));
            warnPsnList.add(warnPsns);
        });
        if(!CollectionUtils.isEmpty(warnPsnList)){
            tdZwWarnInfo.setPsnList(warnPsnList);
        }
        return tdZwWarnInfo;
    }


    /**
    * <p>Description：区县，市级分开计算 预警的记录 </p>
    * <p>Author： yzz 2023-11-10 </p>
    */
    private void getCrptWarnRecords(Map<Integer, List<OccDisCaseLatePojo>> bhkMap,Map<String, List<Date>> idcADiseIdMap,
                                    Map<Integer, List<OccDisCaseLatePojo>> warnMap, Map<String, Date> warnDateMap) {
        if (bhkMap.isEmpty()) {
            return;
        }
        for (Map.Entry<Integer, List<OccDisCaseLatePojo>> entry : bhkMap.entrySet()) {
            entry.getValue().forEach(pojo -> {
                boolean ifWarn=false;
                List<String> keyList=new ArrayList<>();
                if(StringUtils.isBlank(pojo.getCodeNo())){
                    return;
                }
                if (pojo.getCodeNo().contains(",")) {
                    //5010码表extends3对应的职业病诊断病种5026编码，多个英文逗号分隔
                    for (String codeNo : pojo.getCodeNo().split(",")) {
                        if (simpCode5026Map.containsKey(codeNo)) {
                            keyList.add(pojo.getIdc() + "&" + simpCode5026Map.get(codeNo));
                        }
                    }
                } else {
                    if(simpCode5026Map.containsKey(pojo.getCodeNo())){
                        keyList.add(pojo.getIdc() + "&" + simpCode5026Map.get(pojo.getCodeNo()));
                    }
                }
                if(CollectionUtils.isEmpty(keyList)){
                    return;
                }
                for (String key : keyList) {
                    if (idcADiseIdMap.containsKey(key)) {
                        List<Date> dateList = idcADiseIdMap.get(key);
                        Collections.sort(dateList);
                        Date minDate=null;
                        for (Date date : dateList) {
                            if(!date.before(pojo.getRptPrintDate())){
                                minDate=date;
                                break;
                            }
                        }
                        if(minDate==null){
                            //根据idc和病种没匹配到诊断记录，则比较预警
                            if(warnDateMap.get(DateUtils.formatDate(pojo.getRptPrintDate())).before(new Date())){
                                ifWarn=true;
                                //此处没有匹配到诊断记录， 处理日期 赋值为null
                                pojo.setApplyDate(null);
                                break;
                            }
                        }else if (warnDateMap.get(DateUtils.formatDate(pojo.getRptPrintDate())).before(minDate)){
                            ifWarn=true;
                            //如果预警则赋值 处理日期 即登记日期
                            pojo.setApplyDate(minDate);
                            break;
                        }
                    } else {
                        //根据idc和病种没匹配到诊断记录，则比较预警
                        if(warnDateMap.get(DateUtils.formatDate(pojo.getRptPrintDate())).before(new Date())){
                            ifWarn=true;
                            //此处没有匹配到诊断记录， 处理日期 赋值为null
                            pojo.setApplyDate(null);
                            break;
                        }
                    }
                }
                if(ifWarn){
                    if (warnMap.containsKey(pojo.getCrptId())) {
                        warnMap.get(pojo.getCrptId()).add(pojo);
                    } else {
                        List<OccDisCaseLatePojo> countryBhkList = new ArrayList<>();
                        countryBhkList.add(pojo);
                        warnMap.put(pojo.getCrptId(), countryBhkList);
                    }
                }
            });
        }
    }


    @Override
    public Integer getWarnType() {
        return 3;
    }

    @Override
    public String getWarnName() {
        return "疑似职业病诊断不及时";
    }

    @Override
    public Integer getBusType() {
        return 1;
    }
}

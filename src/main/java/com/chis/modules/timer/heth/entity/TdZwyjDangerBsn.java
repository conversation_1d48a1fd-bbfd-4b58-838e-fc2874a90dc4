package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.chis.modules.sys.entity.TsSimpleCode;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-21,TdZwyjDangerBsn
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("TD_ZWYJ_DANGER_BSN")
@KeySequence(value = "TD_ZWYJ_DANGER_BSN_SEQ",clazz = Integer.class)
public class TdZwyjDangerBsn implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField(value = "BHK_ID" , el = "fkByBhkId.rid")
    private TdZwyjDangerBhk fkByBhkId;

    @TableField("BADRSN_ID")
    private String badrsnId;

    @TableField(value = "EXAM_CONCLUSION_ID" , el = "fkByExamConclusionId.rid")
    private TsSimpleCode fkByExamConclusionId;

    @TableField("QTJB_NAME")
    private String qtjbName;

    /**
     * 主键：默认自增
     */
    @TableId
    protected Integer rid;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    protected Integer createManid;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    protected Date createDate;


    public TdZwyjDangerBsn(Integer rid) {
        this.rid = rid;
    }


}

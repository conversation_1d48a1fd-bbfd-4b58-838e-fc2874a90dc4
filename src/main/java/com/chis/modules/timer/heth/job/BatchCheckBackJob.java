package com.chis.modules.timer.heth.job;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.entity.TdTjCheckTask;
import com.chis.modules.timer.heth.listener.BatchCheckListener;
import com.chis.modules.timer.heth.logic.vo.CheckDataVo;
import com.chis.modules.timer.heth.service.BhkCheckBackService;
import com.chis.modules.timer.heth.service.TdTjBhkService;
import com.chis.modules.timer.heth.service.TdTjCheckTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.io.File;
import java.util.concurrent.*;

/**
 * 批量审核退回定时任务
 * 定时解析指定路径下的Excel文件并处理数据
 */
@Slf4j
@Component
public class BatchCheckBackJob {
    @Autowired
    private TdTjCheckTaskService checkTaskService;

    @Autowired
    private TdTjBhkService tdTjBhkService;

    @Autowired
    private BhkCheckBackService bhkCheckBackService;

    @Value("${task.back.thread-pool.core-pool-size}")
    private Integer corePoolSize;

    @Value("${task.back.thread-pool.maximum-pool-size}")
    private Integer maximumPoolSize;

    @Value("${heth-timer.virtual.directory}")
    private String virtualDir;
    /**
     * 存储错误记录的集合
     */
    private List<CheckDataVo> errorRecords = new ArrayList<>();

    /**
     * 定时任务入口方法
     * 查询待处理的任务并执行处理
     */
    @Scheduled(cron = "${task.back.cron}")
    public void start() {
        List<TdTjCheckTask> tasks = checkTaskService.getPendingTasks();
        if (tasks.isEmpty()) {
            return;
        }
        // 创建线程池
        ExecutorService executor = createThreadPool();
        try {
            // 并行处理任务
            CompletableFuture<?>[] futures = tasks.stream()
                    .map(task -> CompletableFuture.runAsync(() -> {
                        try {
                            processTask(task);
                        } catch (Exception e) {
                            updateTaskFailed(task, "处理任务异常：" + e.getMessage());
                        }
                    }, executor))
                    .toArray(CompletableFuture[]::new);

            // 等待所有任务完成
            CompletableFuture.allOf(futures).get(5, TimeUnit.MINUTES);

        } catch (TimeoutException e) {
            throw new RuntimeException("任务执行超时", e);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("任务执行被中断", e);
        } catch (ExecutionException e) {
            throw new RuntimeException("任务执行异常", e.getCause());
        } finally {
            shutdownThreadPool(executor);
        }
    }

    /**
     *  <p>方法描述：生成错误信息的文件地址</p>
     * @MethodAuthor hsj 2024-09-23 17:33
     */
    private String generateErrorInfoFile(String fileType,String uuid) {
        if (!this.virtualDir.endsWith(File.separator) && !this.virtualDir.endsWith("/")) {
            this.virtualDir += "/";
        }
        String filePath = this.virtualDir + fileType + "/";
        File fileDir = new File(filePath);
        if (!fileDir.exists()) {
            boolean ignore = fileDir.mkdirs();
        }
        return filePath + uuid + ".xlsx";
    }


    /**
     * 安全关闭线程池
     *
     * @param executor 线程池对象
     */
    private void shutdownThreadPool(ExecutorService executor) {
        if (executor == null) {
            return;
        }

        try {
            // 停止接收新任务
            executor.shutdown();

            // 等待现有任务完成
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                // 强制关闭未完成的任务
                List<Runnable> droppedTasks = executor.shutdownNow();
                log.warn("线程池强制关闭，丢弃{}个未完成的任务", droppedTasks.size());

                // 再次等待任务终止
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.error("线程池未能正常关闭");
                }
            }
        } catch (InterruptedException e) {
            // 如果等待过程中被中断，强制关闭线程池
            executor.shutdownNow();
            Thread.currentThread().interrupt();
            log.error("线程池关闭过程被中断", e);
        }
    }

    /**
     * 创建线程池
     *
     * @return 线程池对象
     */
    private ThreadPoolExecutor createThreadPool() {
        // 创建线程池
        return new ThreadPoolExecutor(
                corePoolSize, maximumPoolSize, 100L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());
    }

    /**
     * 处理单个任务
     *
     * @param task 待处理的任务对象
     */
    private void processTask(TdTjCheckTask task) {
        String exportCondition = task.getExportCondition();
        Map<String, Object> condition = JSON.parseObject(exportCondition);
        if (!this.virtualDir.endsWith(File.separator) && !this.virtualDir.endsWith("/")) {
            this.virtualDir += File.separator;
        }
        String filePath =  this.virtualDir+ condition.get("filePath");
        // 验证文件是否存在
        File file = new File(filePath);
        if (!file.exists()) {
            updateTaskFailed(task, "文件不存在");
            return;
        }
        String url = "heth/comm/module/bhkcheck";
        String uuid= StringUtils.uuid();
        String fileName = generateErrorInfoFile(url,uuid);
        try {
            this.errorRecords=new ArrayList<>();
            // 使用EasyExcel读取文件
            EasyExcel.read(file, CheckDataVo.class, new BatchCheckListener(errorRecords, tdTjBhkService, bhkCheckBackService, task,fileName))
                    .sheet()
                    .doRead();
            if (errorRecords.isEmpty()) {
                task.setState(1); // 成功
                task.setErrorMsg(null);
                task.setModifyDate(new Date());
                task.setModifyManid(0);
                checkTaskService.updateTask(task);
            }else{
                task.setState(2); // 失败
                task.setErrorFilePath(url+"/"+uuid+".xlsx");
                task.setErrorMsg(null);
                task.setModifyDate(new Date());
                task.setModifyManid(0);
                checkTaskService.updateTask(task);
            }
        } catch (RuntimeException e) {
            // 处理表头验证异常
            if (e.getMessage().contains("表头缺失")) {
                updateTaskFailed(task, "表头缺失，无法解析数据");
            } else {
                updateTaskFailed(task, "文件解析异常");
            }
        } catch (Exception e) {
            updateTaskFailed(task, "文件解析异常");
        }
    }

    /**
     * 更新任务失败状态
     *
     * @param task     任务对象
     * @param errorMsg 错误信息
     */
    private void updateTaskFailed(TdTjCheckTask task, String errorMsg) {
        task.setState(2);
        task.setErrorMsg(errorMsg);
        task.setModifyDate(new Date());
        checkTaskService.updateTask(task);
    }

} 
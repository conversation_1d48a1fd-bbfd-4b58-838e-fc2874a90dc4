package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwyjDangerItems;
import com.chis.modules.timer.heth.mapper.TdZwyjDangerItemsMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-11-21,TdZwyjDangerItemsService
 */
@Service
public class TdZwyjDangerItemsService extends ZwxBaseServiceImpl<TdZwyjDangerItemsMapper, TdZwyjDangerItems> {

    public List<TdZwyjDangerItems> selectListByItemIds(List<Integer> itemIdList){
        if(null == itemIdList || itemIdList.size() == 0){
            return null;
        }
        return this.baseMapper.selectListByItemIds(itemIdList);
    }
}

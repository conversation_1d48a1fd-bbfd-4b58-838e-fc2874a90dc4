package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 曼荼罗返回的数据对象
 *
 * @ClassAuthor pw,2022年12月28日,MtlResponseVo
 */
@Data
public class MtlResponseVo implements Serializable {
    private static final long serialVersionUID = -5594988475389196738L;
    private Integer type;
    private String mess;
    private Integer totalCount;
    private Long timeStampe;
    private List<MtlDataVo> data;
}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;

import com.chis.modules.sys.entity.TsUnit;
import com.chis.modules.sys.entity.TsUserInfo;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2024-10-14,TdTjCheckTask
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_TJ_CHECK_TASK")
public class TdTjCheckTask extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "ZONE_ID" , el = "fkByZoneId.rid")
    private TsZone fkByZoneId;

    @TableField("EXPORT_CONDITION")
    private String exportCondition;

    @TableField("CHECK_RST")
    private Integer checkRst;

    @TableField("CHECK_ADV")
    private String checkAdv;

    @TableField(value = "CHECK_UNIT_ID" , el = "fkByCheckUnitId.rid")
    private TsUnit fkByCheckUnitId;

    @TableField(value = "CHECK_RSN_ID" , el = "fkByCheckRsnId.rid")
    private TsUserInfo fkByCheckRsnId;

    @TableField("CHECK_DATE")
    private Date checkDate;

    @TableField("TOTAL_NUM")
    private BigDecimal totalNum;

    @TableField("STATE")
    private Integer state;

    @TableField("ERROR_MSG")
    private String errorMsg;

    @TableField("TASK_TYPE")
    private Integer taskType;

    @TableField("ERROR_FILE_PATH")
    private String errorFilePath;

    @TableField(exist = false)
    private String zoneGb;


    public TdTjCheckTask(Integer rid) {
        super(rid);
    }


}

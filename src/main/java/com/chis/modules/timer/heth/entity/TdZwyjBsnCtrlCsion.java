package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnCtrlCsion
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_BSN_CTRL_CSION")
@KeySequence(value = "TD_ZWYJ_BSN_CTRL_CSION_SEQ",clazz = Integer.class)
public class TdZwyjBsnCtrlCsion extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "BHK_ID" , el = "fkByBhkId.rid")
    private TdZwyjBsnBhk fkByBhkId;

    @TableField(value = "CRTL_ID" , el = "fkByCrtlId.rid")
    private TdZwyjBsnCsionNoSub fkByCrtlId;


    public TdZwyjBsnCtrlCsion(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-19,TdZwyjOtrPsnList
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_OTR_PSN_LIST")
@KeySequence(value = "TD_ZWYJ_OTR_PSN_LIST_SEQ",clazz = Integer.class)
public class TdZwyjOtrPsnList extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwyjOtrCrptList fkByMainId;

    @TableField(value = "CRPT_ID" , el = "fkByCrptId.rid")
    private TdZwyjOtrBhk fkByCrptId;


    public TdZwyjOtrPsnList(Integer rid) {
        super(rid);
    }


}

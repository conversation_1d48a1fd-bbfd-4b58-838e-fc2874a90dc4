package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-30,TdZwyjBsnCsionItems
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_BSN_CSION_ITEMS")
public class TdZwyjBsnCsionItems extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwyjBsnCsionItemSub fkByMainId;

    @TableField(value = "ITEM_ID" , el = "fkByItemId.rid")
    private TbTjItems fkByItemId;

    @TableField("SEX")
    private String sex;

    @TableField("JDGPTN")
    private String jdgptn;

    @TableField(value = "MSRUNT_ID" , el = "fkByMsruntId.rid")
    private TsSimpleCode fkByMsruntId;

    @TableField("GE")
    private String ge;

    @TableField("GT")
    private String gt;

    @TableField("LE")
    private String le;

    @TableField("LT")
    private String lt;

    @TableField("HG_FLAG")
    private String hgFlag;


    public TdZwyjBsnCsionItems(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TbTjItems
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("TB_TJ_ITEMS")
public class TbTjItems extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("NUM")
    private String num;

    @TableField("ITEM_CODE")
    private String itemCode;

    @TableField("ITEM_NAME")
    private String itemName;

    @TableField("PYXNAM")
    private String pyxnam;

    @TableField("MSRUNT")
    private String msrunt;

    @TableField(value = "ITEM_SORTID" , el = "fkByItemSortid.rid")
    private TsSimpleCode fkByItemSortid;

    @TableField("JDGPTN")
    private String jdgptn;

    @TableField("MINVAL")
    private String minval;

    @TableField("MAXVAL")
    private String maxval;

    @TableField("ITEM_STDVALUE")
    private String itemStdvalue;

    @TableField("DFLT")
    private String dflt;

    @TableField("STOP_TAG")
    private String stopTag;

    @TableField("SEX")
    private String sex;

    @TableField("ITEM_TAG")
    private String itemTag;

    @TableField("MSRUNT_ID")
    private String msruntId;


    public TbTjItems(Integer rid) {
        super(rid);
    }


}

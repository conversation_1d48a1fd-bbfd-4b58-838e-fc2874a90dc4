package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZwWarnPsns;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2023-11-10,TdZwWarnPsnsMapper
 */
@Repository
public interface TdZwWarnPsnsMapper extends ZwxBaseMapper<TdZwWarnPsns> {

    /**
     * <p>方法描述：通过主表rid删除子表 </p>
     * pw 2023/11/11
     **/
    void delWarnPsnByMainRids (@Param("mainRidList") List<Integer> mainRidList);
}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.TbTjSrvorg;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.sql.Clob;
import java.util.Date;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnBhk
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_BSN_BHK")
@KeySequence(value = "TD_ZWYJ_BSN_BHK_SEQ",clazz = Integer.class)
public class TdZwyjBsnBhk extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("BHK_CODE")
    private String bhkCode;

    @TableField(value = "BHKORG_ID" , el = "fkByBhkorgId.rid")
    private TbTjSrvorg fkByBhkorgId;

    @TableField(value = "CRPT_ID" , el = "fkByCrptId.rid")
    private TbTjCrpt fkByCrptId;

    @TableField("CRPT_NAME")
    private String crptName;

    @TableField("PERSON_NAME")
    private String personName;

    @TableField("SEX")
    private String sex;

    @TableField(value = "CARD_TYPE_ID" , el = "fkByCardTypeId.rid")
    private TsSimpleCode fkByCardTypeId;

    @TableField("IDC")
    private String idc;

    @TableField("PSN_TYPE")
    private String psnType;

    @TableField("BRTH")
    private Date brth;

    @TableField("AGE")
    private String age;

    @TableField("ISXMRD")
    private String isxmrd;

    @TableField("LNKTEL")
    private String lnktel;

    @TableField("DPT")
    private String dpt;

    @TableField("WRKNUM")
    private String wrknum;

    @TableField("WRKLNT")
    private String wrklnt;

    @TableField("WRKLNTMONTH")
    private String wrklntmonth;

    @TableField("TCHBADRSNTIM")
    private String tchbadrsntim;

    @TableField("TCHBADRSNMONTH")
    private String tchbadrsnmonth;

    @TableField("WORK_NAME")
    private String workName;

    @TableField("BHK_TYPE")
    private String bhkType;

    @TableField(value = "ONGUARD_STATEID" , el = "fkByOnguardStateid.rid")
    private TsSimpleCode fkByOnguardStateid;

    @TableField("BHK_DATE")
    private Date bhkDate;

    @TableField("BHKRST")
    private String bhkrst;

    @TableField("MHKADV")
    private String mhkadv;

    @TableField("OCP_BHKRSTDES")
    private String ocpBhkrstdes;

    @TableField("JDGDAT")
    private Date jdgdat;

    @TableField("BADRSN")
    private String badrsn;

    @TableField("RPT_PRINT_DATE")
    private Date rptPrintDate;

    @TableField("IF_RHK")
    private String ifRhk;

    @TableField("LAST_BHK_CODE")
    private String lastBhkCode;

    @TableField("LAST_FST_BHK_CODE")
    private String lastFstBhkCode;

    @TableField("DATA_UP_DATE")
    private Date dataUpDate;

    @TableField("UUID")
    private String uuid;

    @TableField(value = "ENTRUST_CRPT_ID" , el = "entrustId.rid")
    private TbTjCrpt entrustId;


    public TdZwyjBsnBhk(Integer rid) {
        super(rid);
    }


}

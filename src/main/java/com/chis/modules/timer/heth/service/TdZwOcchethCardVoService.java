package com.chis.modules.timer.heth.service;


import com.chis.modules.timer.heth.enums.CardUploadCountryBusTypeEnum;
import com.chis.modules.timer.heth.logic.vo.TdZwOcchethCardPsnVo;
import com.chis.modules.timer.heth.logic.vo.TdZwOcchethCardVo;
import com.chis.modules.timer.heth.logic.vo.TdZwUploadAddressVo;
import com.chis.modules.timer.heth.mapper.TdZwOcchethCardVoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2022-12-21,TdZwOcchethCardService
 */
@Service
public class TdZwOcchethCardVoService {

    @Autowired
    private TdZwOcchethCardVoMapper tdZwOcchethCardVoMapper;

    /**
     * <p>方法描述：获取 职业卫生技术服务报送卡 数据</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-21
     **/
    public List<TdZwOcchethCardVo> findOcchethCard(Integer dataSize, String startDate) {
        return tdZwOcchethCardVoMapper.findOcchethCard(dataSize, startDate);
    }

    /**
     * <p>方法描述：获取 职业卫生技术服务信息报送卡参与人员子表 数据</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-23
     **/
    public List<TdZwOcchethCardPsnVo> findOcchethCardPsn(Integer dataSize){
        return tdZwOcchethCardVoMapper.findOcchethCardPsn(dataSize);
    }


    /**
     * <p>方法描述：获取 职业卫生技术服务信息报送卡服务地址子表 数据</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-23
     **/
    public List<TdZwUploadAddressVo> findOcchethCardAddress(Integer dataSize){
        return tdZwOcchethCardVoMapper.findOcchethCardAddress(dataSize);
    }

    /**
     * 查询主表和子表关联日志记录状态全为1且创建日期+15天（配置文件）<=当天的数据
     *
     * @param dataSize 查询最大条数
     * @param lastDate 职业/放射卫生技术服务信息报送卡创建后可附件上传&提交天数
     * @return 查询到的报送卡的第三方唯一标识、统一社会信用代码、附件地址
     */
    public List<Map<String, Object>> findNeedUpdateFileAndSubmitData(Integer dataSize, Date lastDate) {
        return tdZwOcchethCardVoMapper.selectNeedUpdateFileAndSubmitDataForOccHealth(
                CardUploadCountryBusTypeEnum.OCCCARD.getCode(),
                CardUploadCountryBusTypeEnum.OCCPSN.getCode(),
                CardUploadCountryBusTypeEnum.OCCADDR.getCode(),
                dataSize,
                lastDate
        );
    }
}

package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdTjBhk;
import com.chis.modules.timer.heth.logic.vo.TjPersonSearchConditionQueryPO;
import com.chis.modules.timer.heth.pojo.BhkCheckCondition;
import com.chis.modules.timer.heth.pojo.BhkCheckPojo;
import com.chis.modules.timer.heth.pojo.json.BhkBatchUpdateDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人, 2020-09-11,TdTjBhkMapper
 */
@Repository
public interface TdTjBhkMapper extends ZwxBaseMapper<TdTjBhk> {
    /**
     * <p>方法描述：查询待处理数据</p>
     *
     * @MethodAuthor qrr, 2020-10-29,selectBhks
     */
    public List<TdTjBhk> selectBhks(@Param("dataSize") Integer dataSize, @Param("startDate") String startDate);

    /**
     * @MethodName: selectOutRangeBhks
     * @Description: 超范围服务工具
     * @Param: [dataSize, startDate]
     * @Return: java.util.List<com.chis.modules.timer.heth.entity.TdTjBhk>
     * @Author: maox
     * @Date: 2020-11-12
     **/
    public List<TdTjBhk> selectOutRangeBhks(@Param("dataSize") Integer dataSize, @Param("startDate") String startDate);


    /**
     * <p>
     * 方法描述：危急值工具
     * 查询体检主表TD_TJ_BHK 条件是体检类型BHK_TYPE为3，4
     * 并且对应的企业TB_TJ_CRPT中新规范数据标记INTER_PRC_TAG为1
     * 并且在危急值处理日志TD_ZWYJ_DANGER_LOG中没有对应记录
     * </p>
     *
     * @MethodAuthor pw, 2020年11月23日
     */
    List<TdTjBhk> selectDangerValToolBhks(@Param("dataSize") Integer dataSize, @Param("startDate") String startDate);


    /**
     * @Description: 获取个案审核工具准备处理的体检信息
     * @MethodAuthor pw, 2021年05月11日
     */
    List<TdTjBhk> selectAuditCalculationBhks(@Param("dataSize") Integer dataSize, @Param("startDate") String startDate,
                                             @Param("ifNotCheckFsBadRsn") Integer ifNotCheckFsBadRsn);

    /**
     * <p>描述：分页获取个案查询条件查询的体检信息</p>
     *
     * @param searchUnitId 查询人单位id
     * @param searchBhkNum 档案份数
     * @param conditionPO  查询条件
     * @param ifAdmin      是否质控员
     * @param startRow     分页查询的开始行 与endRow同时使用 少一个分页条件都忽略
     * @param endRow       分页查询的结束行 与startRow同时使用 少一个分页条件都忽略
     * @return {@link List< Integer>}
     * @Author: 龚哲, 2021/12/21 15:36,findTjPersonSearch
     */
    List<Integer> findTjPersonSearch(@Param("searchUnitId") Integer searchUnitId, @Param("searchBhkNum") Integer searchBhkNum
            , @Param("property") TjPersonSearchConditionQueryPO conditionPO, @Param("ifAdmin") Boolean ifAdmin
            , @Param("startRow") Integer startRow, @Param("endRow") Integer endRow);

    /**
     * <p>描述：获取个案查询条件查询的总数</p>
     *
     * @param searchUnitId 查询人单位id
     * @param searchBhkNum 档案份数
     * @param conditionPO  查询条件
     * @param ifAdmin      是否质控员
     * @return {@link Integer}
     * @Author: 龚哲, 2021/12/28 9:17,findTjPersonSearchCounts
     */
    Integer findTjPersonSearchCounts(@Param("searchUnitId") Integer searchUnitId, @Param("searchBhkNum") Integer searchBhkNum
            , @Param("property") TjPersonSearchConditionQueryPO conditionPO, @Param("ifAdmin") Boolean ifAdmin);

    /**
     * <p>方法描述：通过初检rid集合 获取初检对应的最新的复检rid </p>
     * 结果Map key 定义的列名 value 对应值
     * 注意数据库的Integer类型返回的是BigDecimal
     * 返回的初检rid key FATHERID
     * 返回的复检rid key RID
     *
     * @MethodAuthor： pw 2022/7/2
     **/
    List<Map<String, BigDecimal>> findLastestRhkRidByFstRid(@Param("mainRidList") List<Integer> mainRidList);

    void updateChestRstBatch(@Param("ridList") List<Integer> ridList, @Param("rst") Integer rst);

    void updateHearingRstBatch(@Param("ridList") List<Integer> ridList, @Param("rst") Integer rst);

    List<TdTjBhk> queryCheckByCondition(BhkCheckCondition bhkCheckCondition);

    int updateBhkBytype(@Param("pojo") BhkCheckPojo bhkCheckPojo, @Param("list") List<Integer> rids,@Param("psnId") Integer psnId);

     /**
     * 根据条件批量查询体检记录
     * @param conditions 查询条件列表，每个条件包含checkNo和checkOrg
     * @return 体检记录列表
     */
    List<TdTjBhk> selectBhksByConditions(List<Map<String, String>> conditions);

    int updateBhkBackBytype(@Param("dto") BhkBatchUpdateDTO dto);
}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-08-25,TdTjBhkAbnomal
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_TJ_BHK_ABNOMAL")
public class TdTjBhkAbnomal extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("BHK_ID")
    private Integer bhkId;

    @TableField("TYPE")
    private Integer type;

    @TableField("ABNOMAL_INFO")
    private String abnomalInfo;


    public TdTjBhkAbnomal(Integer rid) {
        super(rid);
    }


}

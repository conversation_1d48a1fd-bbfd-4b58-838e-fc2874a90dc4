package com.chis.modules.timer.heth.job.country;

import com.chis.modules.timer.heth.enums.CardUploadCountryBusTypeEnum;
import com.chis.modules.timer.heth.logic.vo.TdZwOcchethCardPsnVo;
import com.chis.modules.timer.heth.logic.vo.TdZwUploadAddressVo;
import com.chis.modules.timer.heth.service.SrvorgCardUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>类描述： 放射卫生技术服务信息报送参与人员以及服务地址上传任务 </p>
 * @ClassAuthor： pw 2022/12/24
 **/
@Slf4j
@Component
public class SrvorgPsnAndAddrUploadCountJob extends PsnAndAddrUploadCountAbstract {

    @Resource
    private SrvorgCardUploadService uploadService;

    @Scheduled(cron = "${heth-timer.country.sche-cron.srvorgPsnCron}")
    public void startPsn() {
        this.uploadPsnData();
    }

    @Scheduled(cron = "${heth-timer.country.sche-cron.srvorgAddrCron}")
    public void startAddr(){
        this.uploadAddrData();
    }

    @Override
    public void fillPsnBusType() {
        this.busPsnType = CardUploadCountryBusTypeEnum.SRVORGPSN.getCode();
    }

    @Override
    public List<TdZwOcchethCardPsnVo> queryPsnDataList(Integer dataSize) {
        return this.uploadService.findExecutePsnDataList(dataSize);
    }

    @Override
    public void fillAddrBusType() {
        this.busAddrType = CardUploadCountryBusTypeEnum.SRVORGADDR.getCode();
    }

    @Override
    public List<TdZwUploadAddressVo> queryAddrDataList(Integer dataSize) {
        return this.uploadService.findExecuteAddrDataList(dataSize);
    }
}

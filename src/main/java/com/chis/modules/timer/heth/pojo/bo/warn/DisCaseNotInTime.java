package com.chis.modules.timer.heth.pojo.bo.warn;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class DisCaseNotInTime implements Serializable {
    private static final long serialVersionUID = 1450316264110325475L;
    /**
     * 预警人员名单业务主键ID
     */
    private Integer id;
    /**
     * 业务机构ID
     */
    private Integer orgId;
    /**
     * 业务名称
     */
    private String orgName;
    /**
     * 业务机构地区ID
     */
    private Integer zoneId;
    /**
     * 地区名称 原始fullName不去除省以及下划线
     */
    private String zoneName;
    /**
     * 接收日期
     */
    private Date rcvDate;
    /**
     * 处置日期
     * */
    private Date dealDate;
    /**
     * 病种ID
     */
    private Integer disId;
    /**
     * 预警级别
     * */
    private Integer warnLevel;

    /**预警人员名单业务主键ID + 病种ID*/
    private String idAndDisId;

    /**
     * 与传入日期比较
     *
     * @param date 传入日期
     * @return 如果参数的 Date 等于此 Date，则值为 0;如果此 Date 位于 Date 参数之前，则值小于 0;如果此 Date 位于 Date 参数之后，则值大于 0。
     */
    public int compareDate(Date date) {
        if (date == null) {
            return 1;
        }
        if (this.rcvDate == null) {
            return -1;
        }
        return this.rcvDate.compareTo(date);
    }
}

package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
/**
 * <p>类描述：企业在线申报 职业病危害因素种类及接触人数 </p>
 * @ClassAuthor： pw 2022/9/7
 **/
@Data
public class CrptOnLineDistributionVo implements Serializable {
    private static final long serialVersionUID = -4075267600963513904L;
    private Boolean existsDust;
    private Integer dustContacts;
    private Boolean existsChemical;
    private Integer chemicalContacts;
    private Boolean existsPhysical;
    private Integer physicalContacts;
    private Boolean existsRadioactivity;
    private Integer radioactivityContacts;
    private Boolean existsBiotic;
    private Integer bioticContacts;
    private Boolean existsOther;
    private Integer otherContacts;
}

package com.chis.modules.timer.heth.enums;
/**
 * <p>类描述： 职业/放射卫生技术服务信息报送卡上传国家 业务类型枚举 </p>
 * 11：职业卫生技术服务机构
 * 12：职业卫生报送卡
 * 13：职业卫生报送卡-参与人员
 * 14：职业卫生报送卡-服务地址
 * 21：放射卫生技术服务机构
 * 22：放射卫生报送卡
 * 23：放射卫生报送卡-参与人员
 * 24：放射卫生报送卡-服务地址
 * @ClassAuthor： pw 2022/12/23
 **/
public enum CardUploadCountryBusTypeEnum {
    OCCORG(11,"职业卫生技术服务机构"),
    OCCCARD(12,"职业卫生报送卡"),
    OCCPSN(13,"职业卫生报送卡-参与人员"),
    OCCADDR(14,"职业卫生报送卡-服务地址"),
    SRVORGORG(21,"放射卫生技术服务机构"),
    SRVORGCARD(22,"放射卫生报送卡"),
    SRVORGPSN(23,"放射卫生报送卡-参与人员"),
    SRVORGADDR(24,"放射卫生报送卡-服务地址");
    private Integer code;
    private String desc;
    CardUploadCountryBusTypeEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}

package com.chis.modules.timer.heth.service;

import com.chis.modules.timer.heth.mapper.DisCaseJobMapper;
import com.chis.modules.timer.heth.pojo.bo.warn.DisCaseBusDataBO;
import com.chis.modules.timer.heth.pojo.bo.warn.DisCaseNotInTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class DisCaseJobService {
    @Autowired
    protected DisCaseJobMapper disCaseJobMapper;

    public List<DisCaseBusDataBO> findOccDisCaseDataList(String zoneGbMax, Date beginDate) {
        return this.disCaseJobMapper.findOccDisCaseDataList(zoneGbMax, beginDate);
    }

    /**
     * <p>方法描述： 查询疑似职业病例数数据 </p>
     * pw 2023/11/11
     **/
    public List<DisCaseBusDataBO> findSuspectedOccDisCaseDataList (Date beginDate, Integer warnLevel, String zoneGb) {
        return this.disCaseJobMapper.findSuspectedOccDisCaseDataList(beginDate, warnLevel, zoneGb);
    }

    /**
     *  <p>方法描述：疑似职业病报告不及时-体检数据</p>
     * @MethodAuthor hsj 2023-11-12 14:33
     */
    public List<DisCaseNotInTime> findNotTimeBhkDatas(Date limitDate, Date beginDate, String zoneCode ,Integer warnUnit) {
        return this.disCaseJobMapper.findNotTimeBhkDatas(limitDate,beginDate,zoneCode,warnUnit);
    }

    /**
     *  <p>方法描述：疑似职业病报告不及时-疑似职业病子表</p>
     * @MethodAuthor hsj 2023-11-12 14:34
     */
    public List<DisCaseNotInTime> findSupoccdiseList(List<Integer> bhkRids) {
        return this.disCaseJobMapper.findSupoccdiseList(bhkRids);
    }
    /**
     *  <p>方法描述：疑似职业病报告不及时-疑似职业病报告卡</p>
     * @MethodAuthor hsj 2023-11-12 15:11
     */
    public List<DisCaseNotInTime> findYszybRptList(List<Integer> bhkRids) {
        return this.disCaseJobMapper.findYszybRptList(bhkRids);
    }

    /**
     * <p>方法描述： 查询职业病报告不及时数据 </p>
     * pw 2023/11/12
     **/
    public List<DisCaseNotInTime> findDisCaseNotInTimeDataList(Date limitDate, Date beginDate, Integer warnLevel,
                                                               Integer warnDays, String zoneGb){
        boolean ifMissParam = null == limitDate || null == warnDays || null == beginDate || null == warnLevel;
        if (ifMissParam) {
            return Collections.emptyList();
        }
        return this.disCaseJobMapper.findDisCaseNotInTimeDataList(limitDate, beginDate, warnLevel, warnDays, zoneGb);
    }
}

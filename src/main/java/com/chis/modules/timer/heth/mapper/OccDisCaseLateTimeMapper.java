package com.chis.modules.timer.heth.mapper;

import com.chis.modules.timer.heth.pojo.IdcAndDiseIdPojo;
import com.chis.modules.timer.heth.pojo.OccDisCaseLatePojo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Repository
public interface OccDisCaseLateTimeMapper {
    List<OccDisCaseLatePojo> selectBhkInfo(@Param("beginDate") String beginDate,@Param("zoneCode") String zoneCode);

    List<OccDisCaseLatePojo> selectOccDisCaseByBhkRids(@Param("idcAndDiseIds") List<IdcAndDiseIdPojo> idcAndDiseIds);

}

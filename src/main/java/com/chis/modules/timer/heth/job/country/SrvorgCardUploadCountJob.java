package com.chis.modules.timer.heth.job.country;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.enums.CardUploadCountryBusTypeEnum;
import com.chis.modules.timer.heth.logic.CountSrvorgCardJsonPO;
import com.chis.modules.timer.heth.service.SrvorgCardUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>类描述： 放射卫生技术服务信息报告卡上传任务 </p>
 * @ClassAuthor： pw 2022/12/22
 **/
@Slf4j
@Component
public class SrvorgCardUploadCountJob extends OcchethCardAbstract<CountSrvorgCardJsonPO>{

    @Resource
    private SrvorgCardUploadService uploadService;

    @Scheduled(cron = "${heth-timer.country.sche-cron.srvorgCardCron}")
    @Override
    public void start() {
        super.uploadReportInfo();
    }

    @Override
    public List<CountSrvorgCardJsonPO> findDealData(Integer dataSize, String startDate) {
        return this.uploadService.findExecuteCardDataList(dataSize, startDate);
    }

    @Override
    public List<CountSrvorgCardJsonPO> dealDate(List<CountSrvorgCardJsonPO> list,Map<String, String> errorMsgMap) {
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        List<CountSrvorgCardJsonPO> newList = new ArrayList<>(list.size());
        StringBuffer buffer = new StringBuffer();
        for(CountSrvorgCardJsonPO jsonPO : list){
            buffer.setLength(0);
            buffer.append(jsonPO.getRid());
            Integer rcdRid = jsonPO.getRcdRid();
            if(null != rcdRid){
                buffer.append("&").append(rcdRid);
            }
            StringJoiner joiner = new StringJoiner(";");
            //地区编码
            String sssxbm = jsonPO.getSssxbm();
            if(StringUtils.isNotBlank(sssxbm)){
                sssxbm = this.contraSubExchange("15","1", sssxbm);
                if(StringUtils.isBlank(sssxbm)){
                    //地区编码对照不上
                    joiner.add("服务的用人单位注册地址-行政区划码："+jsonPO.getSssxbm()+"对照不上");
                }else{
                    jsonPO.setSssxbm(sssxbm);
                }
            }

            //单位类型
            String stype = jsonPO.getStype();
            if(StringUtils.isNotBlank(stype)){
                stype = this.contraSubExchange("15","3", stype);
                if(StringUtils.isBlank(stype)){
                    //地区编码对照不上
                    joiner.add("服务的用人单位企业类型："+jsonPO.getStype()+"对照不上");
                }else{
                    jsonPO.setStype(stype);
                }
            }

            //字符串长度控制 以及 数值位数限制
            jsonPO.setCname(this.executeCheckStr(jsonPO.getCname(), 16));
            jsonPO.setSoname(this.executeCheckStr(jsonPO.getSoname(), 64));
            if(StringUtils.isNotBlank(jsonPO.getSocode()) && jsonPO.getSocode().length() > 32){
                joiner.add("服务的用人单位统一社会信用代码长度异常："+jsonPO.getSocode());
            }
            if (1 == jsonPO.getSisz() || 1 == jsonPO.getSiswfszl() || 1 == jsonPO.getSiswhyx() || 1 == jsonPO.getSiswjrfsx() || 1 == jsonPO.getSiswxsxyxzd() || 1 == jsonPO.getSisq()) {
                if(StringUtils.isBlank(jsonPO.getSfintimes())){
                    joiner.add("服务的用人单位现场调查开始时间不能为空");
                }
                if(StringUtils.isBlank(jsonPO.getSfintimen())){
                    joiner.add("服务的用人单位现场调查结束时间不能为空");
                }
            }
            if(null == jsonPO.getSwsexnums()){
                jsonPO.setSwsexnums(0);
            }
            if(null == jsonPO.getSwszexnums()){
                jsonPO.setSwszexnums(0);
            }
            if(null == jsonPO.getSzsexnums()){
                jsonPO.setSzsexnums(0);
            }
            if(null == jsonPO.getSksexnums()){
                jsonPO.setSksexnums(0);
            }
            if(null == jsonPO.getSgsnumsa()){
                jsonPO.setSgsnumsa(0);
            }
            if(null == jsonPO.getSgsnumsb()){
                jsonPO.setSgsnumsb(0);
            }
            if(null == jsonPO.getSqsexnums()){
                jsonPO.setSqsexnums(0);
            }
            if(null == jsonPO.getScsexnums()){
                jsonPO.setScsexnums(0);
            }
            jsonPO.setSaddress(this.executeCheckStr(jsonPO.getSaddress(), 128));
            jsonPO.setScname(this.executeCheckStr(jsonPO.getScname(), 16));
            if(!this.validateInteger(jsonPO.getSwsnums(), 1, 99999)){
                joiner.add("开展放射诊疗工作场所放射防护检测点位数异常："+jsonPO.getSwsnums());
            }
            if(!this.validateInteger(jsonPO.getSwsexnums(), 0, 99999)){
                joiner.add("开展放射诊疗工作场所放射防护检测点位超标数异常："+jsonPO.getSwsexnums());
            }
            if(!this.validateInteger(jsonPO.getSwsznums(), 1, 99999)){
                joiner.add("开展放射诊疗设备质量控制检测设备数异常："+jsonPO.getSwsznums());
            }
            if(!this.validateInteger(jsonPO.getSwszexnums(), 0, 99999)){
                joiner.add("开展放射诊疗设备质量控制检测设备不合格数异常："+jsonPO.getSwszexnums());
            }
            if(!this.validateInteger(jsonPO.getSzsexnums(), 0, 99999)){
                joiner.add("预评价-剂量估算超标点位数异常："+jsonPO.getSzsexnums());
            }
            if(!this.validateInteger(jsonPO.getSksnums(), 0, 99999)){
                joiner.add("控制效果评价现场共检测点位数异常："+jsonPO.getSksnums());
            }
            if(!this.validateInteger(jsonPO.getSksexnums(), 0, 99999)){
                joiner.add("控制效果评价现场共检测超标点位数异常："+jsonPO.getSksexnums());
            }
            if(!this.validateInteger(jsonPO.getSgsnums(), 0, 99999)){
                joiner.add("个人剂量监测人数异常："+jsonPO.getSgsnums());
            }
            if(!this.validateInteger(jsonPO.getSgsnumsa(), 0, 99999)){
                joiner.add("个人剂量监测-5~20mSv人数异常："+jsonPO.getSgsnumsa());
            }
            if(!this.validateInteger(jsonPO.getSgsnumsb(), 0, 99999)){
                joiner.add("超过20mSv人数异常："+jsonPO.getSgsnumsb());
            }

            if(!this.validateInteger(jsonPO.getSqsnums(), 1, 99999)){
                joiner.add("开展放射防护器材检测样品数异常："+jsonPO.getSqsnums());
            }
            if(!this.validateInteger(jsonPO.getSqsexnums(), 0, 99999)){
                joiner.add("开展放射防护器材检测不合格样品数异常："+jsonPO.getSqsexnums());
            }
            jsonPO.setSqsexnames(this.executeCheckStr(jsonPO.getSqsexnames(), 128));
            if(!this.validateInteger(jsonPO.getScsnums(), 1, 99999)){
                joiner.add("开展含放射性产品检测样品数异常："+jsonPO.getScsnums());
            }
            if(!this.validateInteger(jsonPO.getScsexnums(), 0, 99999)){
                joiner.add("开展含放射性产品检测不合格样品数异常："+jsonPO.getScsexnums());
            }
            jsonPO.setScsexnames(this.executeCheckStr(jsonPO.getScsexnames(), 128));
            String phone = jsonPO.getCphone();
            if(StringUtils.isNotBlank(phone)){
                phone = this.fixTelphone(phone);
                jsonPO.setCphone(phone);
            }

            phone = jsonPO.getScphone();
            if(StringUtils.isNotBlank(phone)){
                phone = this.fixTelphone(phone);
                jsonPO.setScphone(phone);
            }
            String errorMsg = joiner.toString();
            if(StringUtils.isNotBlank(errorMsg)){
                errorMsgMap.put(buffer.toString(), errorMsg);
            }else{
                newList.add(jsonPO);
            }
        }
        return newList;
    }

    @Override
    public Integer getBusType() {
        return CardUploadCountryBusTypeEnum.SRVORGCARD.getCode();
    }
}

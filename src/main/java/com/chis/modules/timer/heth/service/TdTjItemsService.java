package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TbTjItems;
import com.chis.modules.timer.heth.entity.TdTjBhkItemStd;
import com.chis.modules.timer.heth.mapper.TbTjItemsMapper;
import com.chis.modules.timer.heth.mapper.TdTjBhkItemStdMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2021-05-12,TdTjBhkItemStdService
 */
@Service
public class TdTjItemsService extends ZwxBaseServiceImpl<TbTjItemsMapper, TbTjItems> {

    /**
     * <p>描述：根据体检状态查询体检项目</p>
     * @param state 1为启用 0为停用
     *
     * @return {@link List< TbTjItems>}
     * @Author: 龚哲,2021/12/23 17:27,findTjItemListByState
     */
    public List<TbTjItems> findTjItemListByState(Short state){
        return this.baseMapper.findTjItemListByState(state);
    }
}

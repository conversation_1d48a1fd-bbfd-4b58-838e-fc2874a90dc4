package com.chis.modules.timer.heth.logic.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 获取患者康复处方-返回结果-data-执行情况
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GetPatientKffaCarryoutVo {
    /**
     * 处方执行时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date carryoutTime;
    /**
     * 处方每次执行情况的唯一标识
     */
    private String carryoutid;
    /**
     * 展示处方执行情况PDF的URL
     */
    private String carryout_url;
}

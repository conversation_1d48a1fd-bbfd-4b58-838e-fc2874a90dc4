package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2023-09-20,TdRectificationScheme
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_RECTIFICATION_SCHEME")
public class TdRectificationScheme extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdRectificationList fkByMainId;

    @TableField("RECTIFICATION_MEASURE")
    private String rectificationMeasure;

    @TableField("CAPITAL_INVESTMENT")
    private BigDecimal capitalInvestment;

    @TableField("START_DATE")
    private Date startDate;

    @TableField("END_DATE")
    private Date endDate;

    @TableField("PRINCIPAL")
    private String principal;

    @TableField("CONTACT")
    private String contact;


    public TdRectificationScheme(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.job.zw.warn;

import com.chis.modules.timer.heth.pojo.bo.warn.DisCaseNotInTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>类描述： 职业病报告不及时定时任务 </p>
 * pw 2023/11/11
 **/
@Slf4j
@Component
public class OccDisCaseNotInTimeJob extends NotInTimeWarnBase {

    @Value("${warn-timer.zwWarn.occ-discase-notInTime-days}")
    private Integer notInTimeDays;

    @Scheduled(cron = "${warn-timer.zwWarn.cron.occ-discase-notInTime}")
    //@Scheduled(initialDelay = 3000,fixedDelay = 60000)
    public void start() {
        this.executeData();
    }

    @Override
    public void initJobParam() {
        this.warnDays = this.notInTimeDays;
        this.warnType = 5;
        this.warnName = "职业病报告不及时";
        this.busType = 3;
    }

    @Override
    public void queryDataList() {
        this.dataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(this.warnModelMap)) {
            return;
        }
        for (Integer warnLevel : this.warnModelMap.keySet()) {
            List<DisCaseNotInTime> queryResultList = this.disCaseJobService.findDisCaseNotInTimeDataList(this.limitDate, this.beginDate, warnLevel,this.warnDays, this.zoneCode);
            if (CollectionUtils.isEmpty(queryResultList)) {
                continue;
            }
            for (DisCaseNotInTime entity : queryResultList) {
                entity.setWarnLevel(warnLevel);
                this.dataList.add(entity);
            }
        }
    }
}

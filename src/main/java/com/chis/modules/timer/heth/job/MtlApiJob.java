package com.chis.modules.timer.heth.job;

import com.alibaba.fastjson.JSONObject;
import com.chis.comm.utils.AesEncryptUtils;
import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.HttpRequestUtil;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsContraSub;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.service.TsContraSubService;
import com.chis.modules.sys.service.TsZoneService;
import com.chis.modules.timer.heth.entity.TdCfkfRecoveryRst;
import com.chis.modules.timer.heth.enums.BadRsnCaffeineKeyEnum;
import com.chis.modules.timer.heth.logic.vo.MtlDataVo;
import com.chis.modules.timer.heth.logic.vo.MtlResponseVo;
import com.chis.modules.timer.heth.service.TdCfkfRecoveryRstService;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 定时调用曼荼罗接口
 *
 * @ClassAuthor pw,2022年12月28日,MtlApiJob
 */
@Slf4j
@Component
public class MtlApiJob {
    @Value("${heth-timer.mtl.apiRoot}")
    private String apiRoot;
    @Value("${heth-timer.mtl.tokenUrl}")
    private String tokenUrl;
    @Value("${heth-timer.mtl.kftjUrl}")
    private String kftjUrl;
    @Value("${heth-timer.mtl.unitCode}")
    private String unitCode;
    @Value("${heth-timer.mtl.password}")
    private String password;
    @Value("${heth-timer.mtl.statisticalYear}")
    private Integer statisticalYear;
    @Value("${heth-timer.mtl.encrypt.key}")
    private String aesKey;
    @Value("${heth-timer.mtl.encrypt.debug}")
    private String debugStr;

    private final String MTL_API_TOKEN_TIME_KEY = "MTL_API_TOKEN_TIME_KEY";
    private final String MTL_API_TOKEN_KEY = "MTL_API_TOKEN_KEY";

    @Autowired
    private TdCfkfRecoveryRstService recoveryRstService;
    @Autowired
    private TsZoneService zoneService;
    @Autowired
    private TsContraSubService contraSubService;
    /** 地区Map key 地区编码 value 地区对象 */
    private Map<String, TsZone> zoneMap;
    /** key对照leftCode value 对照对象 */
    private Map<String, TsContraSub> contraSubMap;

    @Scheduled(cron = "${heth-timer.sche-cron.mtlCron}")
    //@Scheduled(initialDelay = 3000,fixedDelay = 600000)
    public void start(){
        Date date = new Date();
        log.info("定时调用曼荼罗接口 任务启动 程序版本{} 当前时间{}","20220108",date);
        StringBuffer errorBuffer = new StringBuffer();
        if(StringUtils.isBlank(this.apiRoot)){
            errorBuffer.append("；").append("参数apiRoot为空");
        }
        if(StringUtils.isBlank(this.tokenUrl)){
            errorBuffer.append("；").append("参数tokenUrl为空");
        }
        if(StringUtils.isBlank(this.kftjUrl)){
            errorBuffer.append("；").append("参数kftjUrl为空");
        }
        if(StringUtils.isBlank(this.unitCode)){
            errorBuffer.append("；").append("参数unitCode为空");
        }
        if(StringUtils.isBlank(this.password)){
            errorBuffer.append("；").append("参数password为空");
        }
        if(StringUtils.isBlank(this.debugStr) || !this.debugStr.trim().equals("true")){
            if(StringUtils.isBlank(this.aesKey)){
                errorBuffer.append("；").append("参数aesKey为空");
            }
        }
        String errorStr = errorBuffer.toString();
        if(StringUtils.isNotBlank(errorStr)){
            log.error(errorStr.substring(1));
            return;
        }
        String tokenId = invokeGetTokenId();
        if(null == tokenId){
            log.error("未获取到tokenId");
            return;
        }
        //初始化zoneMap
        initZoneMap();
        List<TdCfkfRecoveryRst> list = invokeRecoveryConclusion(tokenId);
        try{
            if(!CollectionUtils.isEmpty(list)){
                if(list.stream().anyMatch(v -> null == v.getRid())){
                    List<TdCfkfRecoveryRst> tmpList = list.stream().filter(v -> null == v.getRid())
                            .collect(Collectors.toList());
                    for(List<TdCfkfRecoveryRst> saveList : StringUtils.splitList(tmpList, 500)){
                        this.recoveryRstService.insertBatch(saveList);
                    }
                }
                if(list.stream().anyMatch(v -> null != v.getRid())){
                    List<TdCfkfRecoveryRst> tmpList = list.stream().filter(v -> null != v.getRid())
                            .collect(Collectors.toList());
                    for(List<TdCfkfRecoveryRst> updateList : StringUtils.splitList(tmpList, 500)){
                        this.recoveryRstService.updateFullBatchById(updateList);
                    }
                }
                log.info("定时调用曼荼罗接口 共处理{}条数据", list.size());
            }
        }catch(Exception e){
            e.printStackTrace();
            log.error("定时调用曼荼罗接口，存储或者更新数据异常：{}",e.getMessage());
        }
        Date endDate = new Date();
        log.info("定时调用曼荼罗接口完成，开始时间：{} 结束时间：{} 用时:{}ms",date,endDate,(endDate.getTime()- date.getTime()));
    }

    /**
     * @Description: 调用接口 获取尘肺病康复统计结果
     *
     * @MethodAuthor pw,2022年12月28日
     */
    private List<TdCfkfRecoveryRst> invokeRecoveryConclusion(String tokenId){
        List<TdCfkfRecoveryRst> resultList = new ArrayList<>();
        if(null == this.statisticalYear){
            this.statisticalYear = DateUtils.getYearInt();
        }
        String url = this.apiRoot+this.kftjUrl;
        JSONObject object = new JSONObject();
        object.put("tokenId",tokenId);
        object.put("statisticalYear", this.statisticalYear);
        Header[] headers = {
                new BasicHeader("tokenId",tokenId)
        };
        String msg = JSONObject.toJSONString(object);
        try{
            log.info("调用曼荼罗获取尘肺病康复统计结果接口，URL:{} 明文参数:{} ",url, msg);
            msg = encryptParam(msg);
            String result = HttpRequestUtil.httpRequestByRaw(url, msg, headers);
            if(StringUtils.isBlank(this.debugStr) || !this.debugStr.trim().equals("true")){
                log.info("调用曼荼罗获取尘肺病康复统计结果接口 加密返回:{} ",result);
            }else{
                log.info("调用曼荼罗获取尘肺病康复统计结果接口 非加密返回:{} ",result);
            }
            result = decryptResult(result);
            if(StringUtils.isNotBlank(result)){
                MtlResponseVo responseVo = JSONObject.parseObject(result, MtlResponseVo.class);
                if(null != responseVo && !CollectionUtils.isEmpty(responseVo.getData())){
                    for(MtlDataVo dataVo : responseVo.getData()){
                        TdCfkfRecoveryRst recoveryRst = new TdCfkfRecoveryRst();
                        recoveryRst.setAnalyYear(this.statisticalYear);
                        recoveryRst.setPsnNo(dataVo.getPsnNo());
                        recoveryRst.setPsnName(dataVo.getPsnName());
                        recoveryRst.setIdc(dataVo.getIdc());
                        //获取对照表信息
                        TsContraSub contraSub = (null != contraSubMap && StringUtils.isNotBlank(dataVo.getCounty())) ?
                                contraSubMap.get(dataVo.getCounty()) : null;
                        TsZone tsZone = (null != contraSub && StringUtils.isNotBlank(contraSub.getRightCode())) ?
                                zoneMap.get(contraSub.getRightCode()) : null;
                        recoveryRst.setFkByZoneId(tsZone);
                        recoveryRst.setSiteName(dataVo.getSiteName());
                        recoveryRst.setFstRecovrDate(dataVo.getFirstRehabilitaionDateThisYear());
                        recoveryRst.setLstRecovrDate(dataVo.getLastRehabilitaionDateThisYear());
                        recoveryRst.setHasInstRevocery(null != dataVo.getHasInstrumentRehabilitation() &&
                                dataVo.getHasInstrumentRehabilitation() ? 1 : 0);
                        recoveryRst.setHasMedRevocery(null != dataVo.getHasRehabilitationOfTraditionalChineseMedicine() &&
                                dataVo.getHasRehabilitationOfTraditionalChineseMedicine() ? 1 : 0);
                        recoveryRst.setHasNutriRevocery(null != dataVo.getHasNutritionalRehabilitation() &&
                                dataVo.getHasNutritionalRehabilitation() ? 1 : 0);
                        recoveryRst.setHasPsyRevocery(null != dataVo.getHasPsychologicalRehabilitation() &&
                                dataVo.getHasPsychologicalRehabilitation() ? 1 : 0);
                        recoveryRst.setHasOtherRevocery(1);//与对方开发确认接收到的都是有康复训练的
                        recoveryRst.setRehabiNum(dataVo.getRehabilitationNum());
                        recoveryRst.setTotalTime(dataVo.getTotalTime());
                        recoveryRst.setAvgTime(dataVo.getAverageDuration());
                        if(null != dataVo.getBloodOxygenSaturation()){
                            recoveryRst.setFstBloodOxygen(dataVo.getBloodOxygenSaturation().getFirstDataThisYear());
                            recoveryRst.setLstBloodOxygen(dataVo.getBloodOxygenSaturation().getLastDataThisYear());
                        }
                        if(null != dataVo.getADLScore()){
                            recoveryRst.setFstAdl(dataVo.getADLScore().getFirstDataThisYear());
                            recoveryRst.setLstAdl(dataVo.getADLScore().getLastDataThisYear());
                        }
                        if(null != dataVo.getAnxietyScore()){
                            recoveryRst.setFstAnxiety(dataVo.getAnxietyScore().getFirstDataThisYear());
                            recoveryRst.setLstAnxiety(dataVo.getAnxietyScore().getLastDataThisYear());
                        }
                        if(null != dataVo.getDepressionScore()){
                            recoveryRst.setFstDepression(dataVo.getDepressionScore().getFirstDataThisYear());
                            recoveryRst.setLstDepression(dataVo.getDepressionScore().getLastDataThisYear());
                        }
                        recoveryRst.setCreateDate(new Date());
                        recoveryRst.setCreateManid(0);
                        resultList.add(recoveryRst);
                    }
                }
            }
        }catch(Exception e){
            e.printStackTrace();
            log.error("调用曼荼罗获取尘肺病康复统计结果接口 异常：{}", e.getMessage());
        }
        if(!CollectionUtils.isEmpty(resultList)){
            TdCfkfRecoveryRst queryRst = new TdCfkfRecoveryRst();
            queryRst.setAnalyYear(this.statisticalYear);
            List<TdCfkfRecoveryRst> existList = this.recoveryRstService.selectListByEntity(queryRst);
            if(CollectionUtils.isEmpty(existList)){
               return resultList;
            }
            for(TdCfkfRecoveryRst curRst : resultList){
                if(StringUtils.isBlank(curRst.getPsnNo())){
                    continue;
                }
                Optional<TdCfkfRecoveryRst> optional = existList.stream()
                        .filter(v -> StringUtils.isNotBlank(v.getPsnNo()) && v.getPsnNo().trim().equals(curRst.getPsnNo().trim()))
                        .findFirst();
                if(optional.isPresent()){
                    TdCfkfRecoveryRst existRst = optional.get();
                    curRst.setRid(existRst.getRid());
                    curRst.setCreateDate(existRst.getCreateDate());
                    curRst.setCreateManid(existRst.getCreateManid());
                }
            }
        }else{
            log.info("年份：{} 未查询到尘肺病康复统计结果",this.statisticalYear);
        }
        return resultList;
    }

    /**
     * @Description: 调用接口 获取tokenId
     *
     * @MethodAuthor pw,2022年12月28日
     */
    private String invokeGetTokenId(){
        String tokenId = null;
        Object cacheObj = CaffeineUtil.getIfPresent(BadRsnCaffeineKeyEnum.MIT_API, this.MTL_API_TOKEN_TIME_KEY);
        if(null != cacheObj){
            Date date = (Date) cacheObj;
            date = DateUtils.addDays(date,1);
            if(!DateUtils.isAfter( date,new Date())){
                cacheObj = CaffeineUtil.getIfPresent(BadRsnCaffeineKeyEnum.MIT_API, this.MTL_API_TOKEN_KEY);
                if(null != cacheObj){
                    tokenId = cacheObj.toString();
                }
            }
        }
        if(null != tokenId){
            return tokenId;
        }
        String url = this.apiRoot+this.tokenUrl;
        JSONObject object = new JSONObject();
        object.put("unitCode",this.unitCode);
        object.put("password", this.password);
        String msg = JSONObject.toJSONString(object);
        try{
            log.info("调用曼荼罗获取tokenId接口，URL:{} 明文参数:{} ",url, msg);
            msg = encryptParam(msg);
            String result = HttpRequestUtil.httpRequestByRaw(url, msg);
            if(StringUtils.isBlank(this.debugStr) || !this.debugStr.trim().equals("true")){
                log.info("调用曼荼罗获取tokenId接口 加密返回：{}", result);
            }else{
                log.info("调用曼荼罗获取tokenId接口 非加密返回：{}", result);
            }
            result = decryptResult(result);
            if(StringUtils.isNotBlank(result)){
                JSONObject resultObj = JSONObject.parseObject(result);
                if(null != resultObj){
                    tokenId = null == resultObj.get(this.unitCode) ? null : resultObj.get(this.unitCode).toString();
                }
            }
        }catch(Exception e){
            e.printStackTrace();
            log.error("调用曼荼罗获取tokenId接口 异常：{}", e.getMessage());
        }
        if(null != tokenId){
            CaffeineUtil.put(BadRsnCaffeineKeyEnum.MIT_API,this.MTL_API_TOKEN_TIME_KEY,new Date());
            CaffeineUtil.put(BadRsnCaffeineKeyEnum.MIT_API,this.MTL_API_TOKEN_KEY,tokenId);
        }
        return tokenId;
    }


    /**
     * @Description: 加密请求参数
     *
     * @MethodAuthor pw,2022年12月30日
     */
    private String encryptParam(String param){
        if(StringUtils.isBlank(this.debugStr) || !this.debugStr.trim().equals("true")){
            try{
                String encryptResult = AesEncryptUtils.aesEncrypt(param, this.aesKey);
                log.info("AES加密请求参数 key：{} 明文：{} 密文：{}", this.aesKey, param, encryptResult);
                param = encryptResult;
            }catch(Exception e){
                e.printStackTrace();
                log.error("AES加密参数异常：{} key：{} 明文：{}", e.getMessage(),this.aesKey, param);
                param = null;
            }
        }
        return param;
    }

    /**
     * @Description: 解密响应结果
     *
     * @MethodAuthor pw,2022年12月30日
     */
    private String decryptResult(String result){
        if(StringUtils.isBlank(this.debugStr) || !this.debugStr.trim().equals("true")){
            try{
                String decryptResult = AesEncryptUtils.aesDecrypt(result, this.aesKey);
                log.info("AES解密返回结果 key：{} 密文：{} 明文：{}", this.aesKey, result, decryptResult);
                result = decryptResult;
            }catch(Exception e){
                e.printStackTrace();
                log.error("AES解密返回结果异常：{} key：{} 密文：{}", e.getMessage(),this.aesKey, result);
                result = null;
            }
        }
        return result;
    }

    /**
     * @Description: 初始化地区Map
     *
     * @MethodAuthor pw,2022年12月29日
     */
    private void initZoneMap(){
        //初始化对照
        contraSubMap = contraSubService.findTsContraSub("11", "1");
        if(CollectionUtils.isEmpty(zoneMap)){
            zoneMap = new HashMap<>();
            if(CollectionUtils.isEmpty(contraSubMap)){
                log.error("对照曼荼罗地区信息失败，未找到对照表信息");
                return;
            }
            TsZone queryZone = new TsZone();
            queryZone.setIfReveal(1);
            List<TsZone> zoneList = zoneService.selectListByEntity(queryZone);
            if(!CollectionUtils.isEmpty(zoneList)){
                for(TsZone tsZone : zoneList){
                    zoneMap.put(tsZone.getZoneGb(), tsZone);
                }
            }
        }
    }
}

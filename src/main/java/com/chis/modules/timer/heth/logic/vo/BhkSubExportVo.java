package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
/**
 * @Description: 异步导出体检子表Vo
 *
 * @ClassAuthor pw,2021年12月21日,BhkSubVo
 */
@Data
public class BhkSubExportVo implements Serializable {
    private static final long serialVersionUID = -7605006583578315232L;
    /** 体检主表rid */
    private Integer bhkRid;
    /** 体检项目rid */
    private Integer itemRid;
    /** 体检项目名称 */
    private String itemName;
    /** 体检结果 */
    private String itemRst;
    /** 计量单位 */
    private String msrunt;
    /** 合格标记 */
    private String rglTag;
    private Integer rglTagVal;
    /** 结果判定标记 */
    private String rstFlag;
    private Integer rstFlagVal;
    /** 项目标记 30代表胸片 30时需要导出结果判定 */
    private Integer itemTag;

    public String getRglTag() {
        if(null != this.rglTagVal && 1 == this.rglTagVal){
            return "合格";
        }
        return "不合格";
    }

    public String getRstFlag() {
        String result = "";
        if(null == this.rstFlagVal){
            return result;
        }
        if(0 == this.rstFlagVal){
            result = "未见异常";
        }else if(1 == this.rstFlagVal){
            result = "尘肺样改变";
        }else if(2 == this.rstFlagVal){
            result = "其他异常";
        }else if(3 == this.rstFlagVal){
            result = "未检查";
        }
        return result;
    }
}

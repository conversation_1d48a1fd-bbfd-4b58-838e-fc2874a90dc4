package com.chis.modules.timer.heth.job;
import com.alibaba.fastjson.JSON;
import com.chis.comm.utils.AesEncryptUtils;
import com.chis.comm.utils.HttpRequestUtil;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.controller.SxFsTokenController;
import com.chis.modules.timer.heth.entity.TdTjRadPsnTrain;
import com.chis.modules.timer.heth.entity.TdTjRadhethPsn;
import com.chis.modules.timer.heth.pojo.*;
import com.chis.modules.timer.heth.service.TdTjRadPsnTrainService;
import com.chis.modules.timer.heth.service.TdTjRadhethPsnService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 *  <p>类描述：放射工作人员培训结果同步功能-更新相关培训信息</p>
 * @ClassAuthor hsj 2022-09-06 14:25
 */
@Slf4j
@Component
public class SxFsPsnTrainJob {
    @Autowired
    private TdTjRadhethPsnService tdTjRadhethPsnService;
    @Autowired
    private TdTjRadPsnTrainService tdTjRadPsnTrainService;
    @Autowired
    private SxFsTokenController sxFsTokenController;

    @Value("${heth-timer.train.dataSize}")
    private Integer dataSize;
    @Value("${heth-timer.train.orgName}")
    private String orgName;

    /**
     *  <p>方法描述：查询所有同步培训信息状态为0的数据，进行更新相关培训信息</p>
     * @MethodAuthor hsj 2022-09-06 14:32
     */
    @Scheduled(cron = "${heth-timer.sche-cron.sxFsPsnTrainCron}")
    public void start(){
        log.info("定时调用放射人员更新相关培训信息接口 任务启动 程序版本{} 当前时间{}", "1.0", new Date());
        TokenReturnPojo tokenReturnPojo = sxFsTokenController.gainToken();
        if(!"1".equals(tokenReturnPojo.getType())){
            log.error(tokenReturnPojo.getMess());
            return;
        }
        Map<String, String>  tsSystemParamMap = tokenReturnPojo.getTsSystemParamMap();
        String fsPsnurl = tsSystemParamMap.get("fsPsnurl");
        String fsPsnKey = tsSystemParamMap.get("fsPsnKey");
        String fsPsnVipara = tsSystemParamMap.get("fsPsnVipara");
        String tokenId = tokenReturnPojo.getTokenId();
        for(;;){
            //查询同步培训信息状态为0
            List<TdTjRadhethPsn> psns = tdTjRadhethPsnService.selectRadhethPsnByState(dataSize);
            if(CollectionUtils.isEmpty(psns)){
                return;
            }
            //key : 身份证号 一个身份证号可能存在多个单位下
            Map<String,List<TdTjRadhethPsn>> idcMap =new HashMap<>();
            for(TdTjRadhethPsn psn : psns){
                if(idcMap.containsKey(psn.getIdcCard())){
                    idcMap.get(psn.getIdcCard()).add(psn);
                }else {
                    List<TdTjRadhethPsn> psnList = new ArrayList<>();
                    psnList.add(psn);
                    idcMap.put(psn.getIdcCard(),psnList);
                }
            }
            List<Integer> mainIds = psns.stream().map(TdTjRadhethPsn::getRid).collect(Collectors.toList());
            List<TdTjRadPsnTrain> tdTjRadPsnTrains = this.tdTjRadPsnTrainService.selectTdTjRadPsnTrains(mainIds) ;
            Map<String,TdTjRadPsnTrain> trainMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(tdTjRadPsnTrains)){
                for (TdTjRadPsnTrain train: tdTjRadPsnTrains){
                    trainMap.put(train.getUuid()+"_"+train.getFkByMainId().getRid(),train);
                }
            }
            List<String> idcList = psns.stream().map(TdTjRadhethPsn::getIdcCard).collect(Collectors.toList());
            IdcListPojo idcListPojo =new IdcListPojo();
            idcListPojo.setIdcList(idcList);
            try {
                //根据身份证获取信息
                 String requestJson = JSON.toJSONString(idcListPojo);
                 String encodeJson = AesEncryptUtils.encrypt(requestJson, fsPsnKey,fsPsnVipara);
                Header[] headers = {
                        new BasicHeader("tokenId",tokenId),
                };
                List<TrainPojo> trainPojos =new ArrayList<>();
                String reposeJson = HttpRequestUtil.httpRequestByRaw(fsPsnurl+"/apiv2/getUsersTrain",encodeJson,headers);
                if(StringUtils.isBlank(reposeJson)){
                    this.tdTjRadhethPsnService.updateRadhethPsns(idcList,"2","请求失败！");
                    log.info("获取放射人员相关培训信息失败 失败数{} 当前时间{}", idcList.size(), new Date());
                    return;
                }
                String returnJson = AesEncryptUtils.desEncrypt(reposeJson, fsPsnKey,fsPsnVipara);
                if(StringUtils.isBlank(returnJson)){
                    this.tdTjRadhethPsnService.updateRadhethPsns(idcList,"2","解析失败！");
                    log.info("获取放射人员相关培训信息失败 失败数{} 当前时间{}", idcList.size(), new Date());
                    return;
                }
                TrainListPojo trainListPojo = JSON.parseObject(returnJson, TrainListPojo.class);
                if(null == trainListPojo || !"1".equals(trainListPojo.getType())){
                    log.error("获取放射人员培训失败！",new Date());
                    //状态更新为2 同步培训信息日期置为当前日期；同步培训失败原因存储失败原因
                    this.tdTjRadhethPsnService.updateRadhethPsns(idcList,"2",trainListPojo.getMess());
                    log.info("获取放射人员相关培训信息失败 失败数{} 当前时间{}", idcList.size(), new Date());
                    return;
                }
                trainPojos = trainListPojo.getData();
                if(CollectionUtils.isNotEmpty(trainPojos)){
                    //根据放射卫生培训表第三方标识（TD_TJ_RAD_PSN_TRAIN.UUID）查询需要同步的数据
                    log.info("更新放射人员相关培训信息 更新数{} 当前时间{}", idcList.size(), new Date());
                    try {
                       boolean flag =  this.tdTjRadPsnTrainService.saveOrUpdate(trainPojos,idcMap,trainMap,orgName);
                       if(flag){
                           this.tdTjRadhethPsnService.updateRadhethPsns(idcList,"1","");
                           log.info("更新放射人员相关培训信息成功 成功数{} 当前时间{}", idcList.size(), new Date());
                       }else {
                           this.tdTjRadhethPsnService.updateRadhethPsns(idcList,"2","更新放射人员培训失败!");
                           log.info("更新放射人员相关培训信息失败 失败数{} 当前时间{}", idcList.size(), new Date());
                       }
                    }catch (Exception e){
                        e.printStackTrace();
                        //状态更新为2 同步培训信息日期置为当前日期；同步培训失败原因存储失败原因
                        this.tdTjRadhethPsnService.updateRadhethPsns(idcList,"2", e.getMessage().substring(0,e.getMessage().length() > 1000 ? 1000 : e.getMessage().length() ));
                        log.info("更新放射人员相关培训信息失败 失败数{} 当前时间{}", idcList.size(), new Date());
                    }

                }else {
                    //无返回数据算成功
                    this.tdTjRadhethPsnService.updateRadhethPsns(idcList,"1","");
                    log.info("更新放射人员相关培训信息成功 成功数{} 当前时间{}", idcList.size(), new Date());
                }
            }catch (Exception e){
                e.printStackTrace();
                //状态更新为2 同步培训信息日期置为当前日期；同步培训失败原因存储失败原因
                this.tdTjRadhethPsnService.updateRadhethPsns(idcList,"2",e.getMessage().substring(0,e.getMessage().length() > 1000 ? 1000 : e.getMessage().length() ));
                log.info("获取放射人员相关培训信息失败 失败数{} 当前时间{}", idcList.size(), new Date());

            }
        }
    }
}

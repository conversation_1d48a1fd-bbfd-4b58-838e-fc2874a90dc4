package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwyjBsnClusion;
import com.chis.modules.timer.heth.entity.TdZwyjBsnClusionAdv;
import com.chis.modules.timer.heth.entity.TdZwyjBsnClusionRsn;
import com.chis.modules.timer.heth.entity.TdZwyjBsnCsionItemSub;
import com.chis.modules.timer.heth.enums.BadRsnCaffeineKeyEnum;
import com.chis.modules.timer.heth.logic.BsnClusionPO;
import com.chis.modules.timer.heth.mapper.TdZwyjBsnClusionMapper;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnClusionService
 */
@Service
public class TdZwyjBsnClusionService extends ZwxBaseServiceImpl<TdZwyjBsnClusionMapper, TdZwyjBsnClusion> {
    @Autowired
    private TdZwyjBsnClusionRsnService rsnService;
    @Autowired
    private TdZwyjBsnClusionAdvService advService;
    @Autowired
    private TdZwyjBsnCsionItemSubService itemSubService;
    /**
     * <p>方法描述：查询危害因素配置规则</p>
     * @MethodAuthor qrr,2020-10-29,findTdZwyjBsnClusion
     * */
    public Map<String,List<BsnClusionPO>> findTdZwyjBsnClusionRsn(){
        Map<String,List<BsnClusionPO>> map = (Map<String,List<BsnClusionPO>>) CaffeineUtil.getIfPresent(BadRsnCaffeineKeyEnum.BAD_RSN_CAFFEINE_KEY, null);
        if(null==map){
            List<TdZwyjBsnClusion> clusions = this.selectListByEntity(null);
            // key:在岗状态Id,value:List<BsnClusionPO>
            map = new HashMap<>();
            if(!CollectionUtils.isEmpty(clusions)){
                Map<Integer,List<TdZwyjBsnClusionRsn>> rsnMap = rsnService.findClusionRsnMap();
                Map<Integer,List<TdZwyjBsnClusionAdv>> advMap = advService.findClusionAdvMap();
                Map<Integer,List<TdZwyjBsnCsionItemSub>> itemSubMap = itemSubService.findCsionItemSubMap();
                for(TdZwyjBsnClusion t:clusions){
                    if(null==t.getFkByOnguardStateid()){
                        continue;
                    }
                    Integer onguardStateid = t.getFkByOnguardStateid().getRid();
                    List<TdZwyjBsnClusionRsn> rsnList = rsnMap.get(t.getRid());
                    for(TdZwyjBsnClusionRsn rsn:rsnList){
                        if(null==rsn.getFkByBadrsnId()){
                            continue;
                        }
                        BsnClusionPO po = new BsnClusionPO();
                        po.setClusion(t);
                        po.setAdvList(advMap.get(t.getRid()));
                        po.setItemSubList(itemSubMap.get(t.getRid()));

                        String key = onguardStateid+"&"+rsn.getFkByBadrsnId().getRid();
                        if(null==map.get(key)){
                            List<BsnClusionPO> poList = new ArrayList<>();
                            poList.add(po);
                            map.put(key,poList);
                        }else {
                            List<BsnClusionPO> poList = map.get(key);
                            poList.add(po);
                        }
                    }
                }
                CaffeineUtil.put(BadRsnCaffeineKeyEnum.BAD_RSN_CAFFEINE_KEY,null,map);
            }
        }
        return map;
    }
}

package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
/**
 * <p>类描述：企业在线申报 主要产品</p>
 * @ClassAuthor： pw 2022/9/7
 **/
@Data
public class CrptOnLineProductVo implements Serializable {
    private static final long serialVersionUID = 3370729828170378099L;
    /** 产品名称 */
    private String productName;
    /** 年产量 */
    private Double annualOutput;
    /** 单位 */
    private String unit;
}

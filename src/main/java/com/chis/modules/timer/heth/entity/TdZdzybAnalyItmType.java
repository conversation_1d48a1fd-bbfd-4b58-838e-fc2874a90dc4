package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.beans.Transient;
import java.util.List;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZdzybAnalyItmType
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZDZYB_ANALY_ITM_TYPE")
public class TdZdzybAnalyItmType extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZdzybSpecialAnaly fkByMainId;

    @TableField("ITEM_NAME")
    private String itemName;

    @TableField("SHOW_NAME")
    private String showName;

    @TableField("XH")
    private String xh;

    @TableField("RULE_LEVEL")
    private String ruleLevel;

    @TableField("ITEM_CODE")
    private String itemCode;

    @TableField("IF_JUDGE_RESULT")
    private String ifJudgeResult;

    private List<TdZdzybAnalyPgItem> itemList;
    @Transient
    public List<TdZdzybAnalyPgItem> getItemList() {
        return itemList;
    }

    public void setItemList(List<TdZdzybAnalyPgItem> itemList) {
        this.itemList = itemList;
    }

    public TdZdzybAnalyItmType(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
/**
 * <p>类描述：企业在线申报 检测情况明细</p>
 * @ClassAuthor： pw 2022/9/7
 **/
@Data
public class CrptOnLineDetectionItemVo implements Serializable {
    private static final long serialVersionUID = 255301815670441860L;
    private Integer hazardsSort;
    private String hazardsName;
    private String codeNo;
    private Boolean supervisionRequirement;
    private Integer checkPoints;
    private Integer overproofPoints;
}

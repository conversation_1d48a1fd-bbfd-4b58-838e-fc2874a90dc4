package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2023-11-10,TbZwBeginDateRcd
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TB_ZW_BEGIN_DATE_RCD")
public class TbZwBeginDateRcd extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("WARN_TYPE")
    private Integer warnType;

    @TableField("BEGIN_DATE")
    private Date beginDate;

    @TableField("BUS_TYPE")
    private Integer busType;

    @TableField("BUS_ID")
    private Integer busId;

    @TableField("WARN_UNIT")
    private Integer warnUnit;


    public TbZwBeginDateRcd(Integer rid) {
        super(rid);
    }


}

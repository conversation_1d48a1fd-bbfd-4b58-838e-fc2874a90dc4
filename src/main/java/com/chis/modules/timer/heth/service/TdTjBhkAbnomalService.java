package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdTjBhkAbnomal;
import com.chis.modules.timer.heth.mapper.TdTjBhkAbnomalMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2022-08-25,TdTjBhkAbnomalService
 */
@Service
public class TdTjBhkAbnomalService extends ZwxBaseServiceImpl<TdTjBhkAbnomalMapper, TdTjBhkAbnomal> {

    /**
     * <p>方法描述：通过BHK_ID删除异常信息 </p>
     * @MethodAuthor： pw 2022/8/25
     **/
    public void deleteBhkAbnomalByBhkIdList(List<Integer> bhkRidList){
        if(CollectionUtils.isEmpty(bhkRidList)){
            return;
        }
        List<List<Integer>> bhkRidGroupList = StringUtils.splitListProxy(bhkRidList, 1000);
        for(List<Integer> ridList : bhkRidGroupList){
            this.baseMapper.deleteBhkAbnomalByBhkIdList(ridList);
        }
    }

    public List<TdTjBhkAbnomal> findBhkAbnomals(List<Integer> ridList) {
        return this.baseMapper.findBhkAbnomals(ridList);
    }
}

package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.mapper.TdZwyjBsnCsionPdjlMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnCsionPdjlService
 */
@Service
public class TdZwyjBsnCsionPdjlService extends ZwxBaseServiceImpl<TdZwyjBsnCsionPdjlMapper, TdZwyjBsnCsionPdjl> {
    @Autowired
    private TdZdzybSpecialAnalyService analyService;
    @Autowired
    private TdZdzybAnalyPgItemService itemService;
    /**
     * <p>方法描述：查询判定结论</p>
     * @MethodAuthor qrr,2020-10-30,findCsionPdjlByMainId
     * */
    public List<TdZwyjBsnCsionPdjl>  findCsionPdjlByMainId(Integer mainId){
        TdZwyjBsnCsionPdjl csionPdjl = new TdZwyjBsnCsionPdjl();
        csionPdjl.setFkByMainId(new TdZwyjBsnCsionItemSub(mainId));
        List<TdZwyjBsnCsionPdjl> csionPdjlList = this.baseMapper.selectListByEntity(csionPdjl);
        if(!CollectionUtils.isEmpty(csionPdjlList)){
            for(TdZwyjBsnCsionPdjl t:csionPdjlList){
                TdZdzybAnalyItmType itmType = t.getFkByPdClusionId();
                TdZdzybSpecialAnaly analy = analyService.selectByEntity(new TdZdzybSpecialAnaly(itmType.getFkByMainId().getRid()));
                itmType.setFkByMainId(analy);
                List<TdZdzybAnalyPgItem> pgItemList = itemService.findPgItemsByMainId(itmType.getRid());
                if (!CollectionUtils.isEmpty(pgItemList)){
                    itmType.setItemList(pgItemList);
                }
            }
        }
        return csionPdjlList;
    }
}

package com.chis.modules.timer.heth.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chis.comm.utils.*;
import com.chis.modules.sys.entity.TsContraSub;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.service.TsContraSubService;
import com.chis.modules.sys.service.TsSimpleCodeService;
import com.chis.modules.sys.service.TsZoneService;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.enums.BadRsnCaffeineKeyEnum;
import com.chis.modules.timer.heth.enums.RespCodeEnum;
import com.chis.modules.timer.heth.logic.vo.*;
import com.chis.modules.timer.heth.pojo.json.CrptUploadRepDTO;
import com.chis.modules.timer.heth.pojo.json.CrptUploadRepSingleDTO;
import com.chis.modules.timer.heth.pojo.json.CrptValueJson;
import com.chis.modules.timer.heth.service.TdZyUnitbasicRcdService;
import com.chis.modules.timer.heth.service.TdZyUnitbasicinfoService;
import com.chis.modules.webmvc.api.enums.ReturnType;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;


/**
* <p>Description： 申报系统数据对接下载</p>
* <p>Author： yzz 2025/8/8 </p>
*/
@Slf4j
@Component
public class CrptOnlineReportJob {

    @Value("${heth-timer.crpt-online.url-host}")
    private String urlHost;
    @Value("${heth-timer.crpt-online.token-url}")
    private String tokenUrl;
    @Value("${heth-timer.crpt-online.declaration-url}")
    private String declarationUrl;
    @Value("${heth-timer.crpt-online.secret-key}")
    private String secretKey;
    @Value("${spring.encrypt.key}")
    private String key;

    @Value("${spring.encrypt.debug}")
    private String debug;
    /**
     * 同步用人单位信息接口
     */
    @Value("${heth-timer.del.url.saveOrUpdateCrpt}")
    private String delSaveOrUpdateCrptUrl;
    @Resource
    private TdZyUnitbasicinfoService basicinfoService;
    @Resource
    private TsContraSubService contraSubService;
    @Resource
    private TsSimpleCodeService codeService;
    @Resource
    private TsZoneService zoneService;
    @Resource
    private TdZyUnitbasicRcdService rcdService;

    /** 是否https请求 */
    private boolean ifSSL;
    /** 缓存对照 key 对照类型 value-key leftCode */
    private Map<Integer,Map<String, TsContraSub>> contraSubTypeMap;
    private Map<String,Map<String,TsSimpleCode>> simpleCodeTypeMap;
    private Map<String,TsZone> tsZoneMap;

    @Scheduled(cron = "${heth-timer.sche-cron.crptOnline}")
    //@Scheduled(initialDelay = 3000,fixedDelay = 60000)
    public void start(){
        if(StringUtils.isBlank(this.urlHost) ||
                StringUtils.isBlank(this.tokenUrl) ||
                StringUtils.isBlank(this.declarationUrl) ||
                StringUtils.isBlank(this.secretKey)){
            log.error("企业在线申报参数配置异常，Job退出！");
            return;
        }
        this.ifSSL = this.urlHost.trim().startsWith("https");
        long start = System.currentTimeMillis();
        this.initMap();
        log.info("企业在线申报初始化码表与对照用时：{}ms",(System.currentTimeMillis() - start));
        while(true){
            start = System.currentTimeMillis();
            String returnJson = this.requestDeclaration();
            log.info("企业在线申报请求接口用时：{}ms 接口返回：{}",(System.currentTimeMillis() - start), returnJson);
            if(StringUtils.isBlank(returnJson)){
                return;
            }
            TdZyUnitbasicRcd rcd = new TdZyUnitbasicRcd();
            rcd.setDealMark(0);
            rcd.setDownMessage(returnJson);
            //去除长度为1的数组里的空对象
            returnJson = returnJson.replaceAll("\\[\\{\\}\\]","[]");
            try{
                CrptOnLineDeclarationVo declarationVo = JSON.parseObject(returnJson, CrptOnLineDeclarationVo.class);
                List<TdZyUnitbasicRcdSub> rcdSubList = new ArrayList<>();
                if(null != declarationVo && RespCodeEnum.SUCCESS.getCode().equals(declarationVo.getType())){
                    rcd.setDealMark(1);
                    List<CrptOnLineResultVo> result = declarationVo.getResult();
                    //无数据 退出
                    if(CollectionUtils.isEmpty(result)){
                        return;
                    }
                    for(CrptOnLineResultVo resultVo : result){
                        this.executeResultVo(rcd, resultVo, rcdSubList);
                    }
                }
                boolean flag = !CollectionUtils.isEmpty(rcdSubList) &&
                        rcdSubList.stream().anyMatch(v -> null != v.getDealMark() && 0 == v.getDealMark());
                if(flag){
                    rcd.setDealMark(0);
                    rcd.setDealRsn("申报记录存在异常！");
                }
                if(0 == rcd.getDealMark()){
                    if(StringUtils.isBlank(rcd.getDealRsn()) && StringUtils.isNotBlank(declarationVo.getMess())){
                        rcd.setDealRsn(declarationVo.getMess().length()>1000?declarationVo.getMess().substring(0,1000):declarationVo.getMess());
                    }
                }
                //保存日志
                rcdService.saveTdZyUnitbasicRcd(rcd,rcdSubList);
                long end = System.currentTimeMillis();
                log.info("企业在线申报请求接口一次完成存储总耗时：{} ms",(end-start));
            }catch(Exception e){
                log.error(e.getMessage(),new Throwable(e));
            }
        }
    }

    /**
     * <p>方法描述：单条申报单位存储 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    private void executeResultVo(TdZyUnitbasicRcd rcd, CrptOnLineResultVo resultVo, List<TdZyUnitbasicRcdSub> rcdSubList){
        TdZyUnitbasicRcdSub rcdSub = new TdZyUnitbasicRcdSub();
        rcdSub.setFkByMainId(rcd);
        rcdSub.setUuid(resultVo.getUuid());
        rcdSub.setUnitName(resultVo.getUnitName());
        int dealMark = 1;
        String msg = null;
        try{
            StringBuffer buffer = new StringBuffer();
            TdZyUnitbasicinfo info = this.generateTdZyUnitbasicinfo(resultVo, buffer);
            List<TdZyUnitfactorcDetail> factorcDetailList = this.generateTdZyUnitfactorcDetail(resultVo, info, buffer);
            List<TdZyUnitharmfactDetail> harmFactDetailList = this.generateTdZyUnitharmfactDetail(resultVo, info, buffer);
            List<TdZyUnithealthDetail> healthDetailList = this.generateTdZyUnithealthDetail(resultVo, info, buffer);
            if(buffer.length() > 0){
                dealMark = 0;
                msg = buffer.toString();
            }else{
                TdZyTrainSituation situation = this.generateTdZyTrainSituation(resultVo, info);
                List<TdZyUnitmainprod> prodList = this.generateTdZyUnitmainprod(resultVo, info);
                TdZyUnitfactorcrowd crowd = this.generateTdZyUnitfactorcrowd(resultVo, info);
                TdZyUnitharmfactorcheck check = this.generateTdZyUnitharmfactorcheck(resultVo, info);
                List<TdZyJcOrg> jcOrgList = this.generateTdZyJcOrg(resultVo, info);
                TdZyUnithealthcustody custody = this.generateTdZyUnithealthcustody(resultVo, info);
                List<TdZyHethOrg> hethOrgList = this.generateTdZyHethOrg(resultVo, info);

                //封装同步用人单位信息接口入参json
                List<CrptValueJson> crptList = new ArrayList<>();
                crptList.add(pakCrptInfo(info, factorcDetailList));
                Map<String, List<CrptValueJson>> crptValueListMap = new HashMap<>();
                crptValueListMap.put("crptList", crptList);
                String crptValueJsonStr = JSONObject.toJSONString(crptValueListMap);
                //请求同步用人单位信息接口
                info.setCrptId(saveOrUpdateCrpt(crptValueJsonStr));
                this.basicinfoService.saveOrUpdateTdZyUnitbasicinfo(info, factorcDetailList, harmFactDetailList,
                        healthDetailList, situation, prodList, crowd, check, jcOrgList, custody, hethOrgList);
            }
        }catch (Exception e) {
            log.error(e.getMessage(),e);
            dealMark = 0;
            msg = e.getMessage();
            if(StringUtils.isBlank(msg)){
                msg = e.toString();
            }

        }
        rcdSub.setDealMark(dealMark);
        rcdSub.setDealRsn(StringUtils.isNotBlank(msg) && msg.length()>1000?msg.substring(0,1000):msg);
        rcdSubList.add(rcdSub);
    }

    private CrptValueJson pakCrptInfo(TdZyUnitbasicinfo info, List<TdZyUnitfactorcDetail> factorcDetailList) {
        CrptValueJson crptValueJson = new CrptValueJson();
        if (info.getFkByZoneId() != null) {
            crptValueJson.setZoneId(info.getFkByZoneId().getRid());
        }
        crptValueJson.setCrptName(info.getUnitName());
        crptValueJson.setIfSubPrg(info.getIfBranch());
        crptValueJson.setInstitutionCode(info.getCreditCode());
        //是否分支机构为“是”时传参
        if (new Integer(1).equals(info.getIfBranch())) {
            crptValueJson.setUpperCrptName(info.getParentUnitName());
            crptValueJson.setUpperInstitutionCode(info.getParentUnitCredit());
            if (info.getFkByParentZoneId() != null) {
                crptValueJson.setUpperZoneId(info.getFkByParentZoneId().getRid());
            }
        }
        crptValueJson.setAddress(info.getRegAddr());
        crptValueJson.setEnrolAddress(info.getWorkAddr());
        if (info.getFkByEnterpriseScaleId() != null) {
            crptValueJson.setCrptSizeId(info.getFkByEnterpriseScaleId().getRid());
        }
        if (info.getFkByEconomicId() != null) {
            crptValueJson.setEconomyId(info.getFkByEconomicId().getRid());
        }
        if (info.getFkByIndustryCateId() != null) {
            crptValueJson.setIndusTypeId(info.getFkByIndustryCateId().getRid());
        }
        crptValueJson.setOccManaOffice(info.getLinkManager());
        crptValueJson.setLinkPhone2(info.getLinkPhone());
        crptValueJson.setWorkForce(info.getEmpNum());
        crptValueJson.setOutsourceNum(info.getExternalNum());
        crptValueJson.setHoldCardMan(info.getVictimsNum());
        crptValueJson.setOccupationalDiseasesNum(info.getOccupationalDiseasesNum());
        crptValueJson.setOperationStatus(info.getOperationStatus());
        crptValueJson.setSourceCode("1003");
        crptValueJson.setUuid("1");
        if (CollectionUtils.isEmpty(factorcDetailList)) {
            return crptValueJson;
        }
        List<Integer> badRsnList = new ArrayList<>();
        for (TdZyUnitfactorcDetail unitfactorcDetail : factorcDetailList) {
            //接触人数小于等于0的危害因素不要
            if (unitfactorcDetail.getFkByHazardsId() == null
                    || unitfactorcDetail.getContactNumber() == null
                    || unitfactorcDetail.getContactNumber().compareTo(0) <= 0) {
                continue;
            }
            badRsnList.add(unitfactorcDetail.getFkByHazardsId().getRid());
        }
        crptValueJson.setBadRsnList(badRsnList);
        return crptValueJson;
    }

    private Integer saveOrUpdateCrpt(String crptValueJsonStr) throws Exception {
        String url = this.delSaveOrUpdateCrptUrl;
        log.info("同步用人单位信息接口请求信息：{}", crptValueJsonStr);
        if (!"true".equals(debug)) {
            //加密
            crptValueJsonStr = AesEncryptUtils.aesEncrypt(crptValueJsonStr, key);
        }
        String responseBody = OkHttpUtils.postJsonParams(url, crptValueJsonStr, null);
        if (StringUtils.isNotBlank(responseBody)) {
            //解密
            if (!"true".equals(debug)) {
                responseBody = AesEncryptUtils.aesDecrypt(responseBody, key);
            }
            log.info("同步用人单位信息接口返回信息：{}", responseBody);

            CrptUploadRepDTO repDTO = JSONObject.parseObject(responseBody, CrptUploadRepDTO.class);
            if (repDTO == null) {
                throw new Exception("同步用人单位信息接口失败！返回信息为空！");
            }
            if (!ReturnType.SUCCESS_PROCESS.getTypeNo().equals(repDTO.getType())
                    || CollectionUtils.isEmpty(repDTO.getRidList())) {
                if (repDTO.getMess() == null) {
                    throw new Exception("同步用人单位信息接口失败！失败返回信息mess为空！");
                }
                throw new Exception("同步用人单位信息接口失败！" + repDTO.getMess());
            }
            List<CrptUploadRepSingleDTO> singleDTOList = repDTO.getRidList();
            for (CrptUploadRepSingleDTO singleDTO : singleDTOList) {
                if (singleDTO == null) {
                    throw new Exception("同步用人单位信息接口失败！部分返回信息为空！");
                }
                if (!ReturnType.SUCCESS_PROCESS.getTypeNo().equals(singleDTO.getType())
                        || singleDTO.getRid() == null) {
                    if (singleDTO.getMess() == null) {
                        throw new Exception("同步用人单位信息接口失败！部分失败返回信息mess为空！");
                    }
                    throw new Exception("同步用人单位信息接口失败！" + singleDTO.getMess());
                }
                return singleDTO.getRid();
            }
        }
        throw new Exception("同步用人单位信息接口失败！");
    }

    /**
     * <p>方法描述：创建申报单位基本信息 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    private TdZyUnitbasicinfo generateTdZyUnitbasicinfo(CrptOnLineResultVo resultVo, StringBuffer buffer){
        TdZyUnitbasicinfo info = new TdZyUnitbasicinfo();
        info.setUuid(resultVo.getUuid());
        List<TdZyUnitbasicinfo> queryList = basicinfoService.selectListByEntity(info);
        if(!CollectionUtils.isEmpty(queryList)){
            info = queryList.get(0);
        }
        info.setRecordUuid(resultVo.getEmployerUuid());
        info.setDeclareYear(resultVo.getDeclareYear());
        info.setDeclareType(resultVo.getDeclareType());
        info.setDeclareStatus(resultVo.getDeclareStatus());
        info.setDeclareDate(resultVo.getDeclareDate());
        info.setApproveDate(resultVo.getApproveDate());

        //定义的对照表
        TsContraSub contraSub = null;
        //使用的对照表map
        Map<String, TsContraSub> contraSubMap = null;

        //变更原因
        String reasonNo = resultVo.getReasonNo();
        if(StringUtils.isNotBlank(reasonNo)){
            contraSubMap = this.contraSubTypeMap.get(5);
            contraSub = contraSubMap.get(reasonNo);
            if(null == contraSub){
                buffer.append("变更原因：").append(reasonNo).append("对照失败！");
            }else{
                Map<String, TsSimpleCode> map = this.simpleCodeTypeMap.get("5508");
                if(!CollectionUtils.isEmpty(map)){
                    TsSimpleCode t = map.get(contraSub.getRightCode());
                    if(null==t){
                        buffer.append("变更原因：").append(reasonNo).append("码表不存在！");
                    }else {
                        info.setFkByReasonId(t);
                    }
                }
            }
        }else{
            info.setFkByReasonId(null);
        }
        info.setRemark(resultVo.getRemark());

        //所属地区
        String businessZone = resultVo.getBusinessZone();
        if(StringUtils.isNotBlank(businessZone)){
            contraSubMap = this.contraSubTypeMap.get(1);
            contraSub = contraSubMap.get(businessZone);
            if(null == contraSub){
                buffer.append("地区：").append(businessZone).append("对照失败！");
            }else {
                if(!CollectionUtils.isEmpty(this.tsZoneMap)){
                    TsZone t = this.tsZoneMap.get(contraSub.getRightCode());
                    if(null==t){
                        buffer.append("地区：").append(businessZone).append("地区不存在！");
                    }else {
                        info.setFkByZoneId(t);
                    }
                }
            }
        }else{
            info.setFkByZoneId(null);
        }

        info.setUnitName(resultVo.getUnitName());
        info.setCreditCode(resultVo.getCreditCode());
        info.setRegAddr(resultVo.getRegAddr());
        info.setWorkAddr(resultVo.getWorkAddr());

        //企业规模
        String enterpriseScale = resultVo.getEnterpriseScale();
        if(StringUtils.isNotBlank(enterpriseScale)){
            contraSubMap  = this.contraSubTypeMap.get(2);
            contraSub = contraSubMap.get(enterpriseScale);
            if(null == contraSub){
                buffer.append("企业规模：").append(enterpriseScale).append("对照失败！");
            }else {
                Map<String,TsSimpleCode> map = this.simpleCodeTypeMap.get("5004");
                if(!CollectionUtils.isEmpty(map)){
                    TsSimpleCode t = map.get(contraSub.getRightCode());
                    if(null==t){
                        buffer.append("企业规模：").append(enterpriseScale).append("码表不存在！");
                    }else {
                        info.setFkByEnterpriseScaleId(t);
                    }
                }
            }
        }else{
            info.setFkByEnterpriseScaleId(null);
        }

        //行业类别
        String industryCateNo = resultVo.getIndustryCateNo();
        if(StringUtils.isNotBlank(industryCateNo)){
            contraSubMap = this.contraSubTypeMap.get(3);
            contraSub = contraSubMap.get(industryCateNo);
            if(null == contraSub){
                buffer.append("行业分类：").append(industryCateNo).append("对照失败！");
            }else {
                Map<String,TsSimpleCode> map = this.simpleCodeTypeMap.get("5002");
                if(!CollectionUtils.isEmpty(map)){
                    TsSimpleCode t = map.get(contraSub.getRightCode());
                    if(null==t){
                        buffer.append("行业分类：").append(industryCateNo).append("码表不存在！");
                    }else {
                        info.setFkByIndustryCateId(t);
                    }
                }
            }
        }else{
            info.setFkByIndustryCateId(null);
        }

        //经济类型
        String economicNo = resultVo.getEconomicNo();
        if(StringUtils.isNotBlank(economicNo)){
            contraSubMap = this.contraSubTypeMap.get(4);
            contraSub = contraSubMap.get(economicNo);
            if(null == contraSub){
                buffer.append("经济类型：").append(economicNo).append("对照失败！");
            }else {
                Map<String,TsSimpleCode> map = this.simpleCodeTypeMap.get("5003");
                if(!CollectionUtils.isEmpty(map)){
                    TsSimpleCode t = map.get(contraSub.getRightCode());
                    if(null==t){
                        buffer.append("经济类型：").append(economicNo).append("码表不存在！");
                    }else {
                        info.setFkByEconomicId(t);
                    }
                }
            }
        }else{
            info.setFkByEconomicId(null);
        }

        info.setFillMan(resultVo.getFillMan());
        info.setFillPhone(resultVo.getFillPhone());
        info.setLegalPerson(resultVo.getLegalPerson());
        info.setLegalPersonPhone(resultVo.getLegalPersonPhone());
        info.setLinkManager(resultVo.getLinkManager());
        info.setLinkPhone(resultVo.getLinkPhone());
        info.setEmpNum(resultVo.getEmpNum());
        info.setExternalNum(resultVo.getExternalNum());
        info.setVictimsNum(resultVo.getVictimsNum());
        info.setOccupationalDiseasesNum(resultVo.getOccupationalDiseasesNum());
        if(null != resultVo.getIfBranch()){
            info.setIfBranch(resultVo.getIfBranch() ? 1 : 0);
        }else{
            info.setIfBranch(null);
        }
        info.setParentUnitUuid(resultVo.getParentUuid());
        info.setParentUnitName(resultVo.getParentUnitName());
        info.setParentUnitCredit(resultVo.getParentCreditCode());

        //上级单位所属地区编码
        String parentBusinessZone = resultVo.getParentBusinessZone();
        if (StringUtils.isNotBlank(parentBusinessZone)) {
            contraSubMap = this.contraSubTypeMap.get(1);
            contraSub = contraSubMap.get(parentBusinessZone);
            if (null == contraSub) {
                buffer.append("上级单位所属地区编码：").append(parentBusinessZone).append("对照失败！");
            } else {
                if (!CollectionUtils.isEmpty(this.tsZoneMap)) {
                    TsZone t = this.tsZoneMap.get(contraSub.getRightCode());
                    if (null == t) {
                        buffer.append("上级单位所属地区编码：").append(parentBusinessZone).append("地区不存在！");
                    } else {
                        info.setFkByParentZoneId(t);
                    }
                }
            }
        } else {
            info.setFkByParentZoneId(null);
        }

        if(null != resultVo.getIfWarProduct()){
            info.setIfWarProduct(resultVo.getIfWarProduct() ? 1 : 0);
        }else{
            info.setIfWarProduct(null);
        }
        info.setOperationStatus(resultVo.getOperationStatus());
        if(null == info.getRid()){
            info.setCreateDate(new Date());
            info.setCreateManid(1);
        }else{
            info.setModifyDate(new Date());
            info.setModifyManid(1);
        }
        return info;
    }

    /**
     * <p>方法描述：创建职业卫生培训情况 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    private TdZyTrainSituation generateTdZyTrainSituation(CrptOnLineResultVo resultVo, TdZyUnitbasicinfo basicinfo){
        Boolean existsLeaderTrain = resultVo.getExistsLeaderTrain();
        Boolean existsManagersTrain = resultVo.getExistsManagersTrain();
        Integer trainSum = resultVo.getTrainSum();
        if(null == existsLeaderTrain && null == existsManagersTrain && null == trainSum){
            return null;
        }
        TdZyTrainSituation ation = new TdZyTrainSituation();
        ation.setFkByMainId(basicinfo);
        if(null != existsLeaderTrain){
            ation.setIfLeadersTrain(existsLeaderTrain ? 1 : 0);
        }
        if(null != existsManagersTrain){
            ation.setIfManagersTrain(existsManagersTrain ? 1 : 0);
        }
        ation.setTrainSum(trainSum);
        ation.setCreateDate(new Date());
        ation.setCreateManid(1);
        return ation;
    }

    /**
     * <p>方法描述：创建申报单位主要产品 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    private List<TdZyUnitmainprod> generateTdZyUnitmainprod(CrptOnLineResultVo resultVo, TdZyUnitbasicinfo basicinfo){
        List<CrptOnLineProductVo> productList = resultVo.getProductList();
        if(CollectionUtils.isEmpty(productList)){
            return Collections.EMPTY_LIST;
        }
        List<TdZyUnitmainprod> resultList = new ArrayList<>();
        for(CrptOnLineProductVo productVo : productList){
            String prodName = productVo.getProductName();
            Double annualOutput = productVo.getAnnualOutput();;
            String unit = productVo.getUnit();
            if(null == annualOutput && StringUtils.isBlank(prodName) && StringUtils.isBlank(unit)){
                continue;
            }
            TdZyUnitmainprod tmpProd = new TdZyUnitmainprod();
            tmpProd.setFkByMainId(basicinfo);
            tmpProd.setCreateDate(new Date());
            tmpProd.setCreateManid(1);
            tmpProd.setProdName(prodName);
            tmpProd.setAnnualOutput(annualOutput);
            tmpProd.setUnitName(unit);
            resultList.add(tmpProd);
        }
        return resultList;
    }
    /**
     * <p>方法描述：创建职业病危害因素种类及接触人数 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    private TdZyUnitfactorcrowd generateTdZyUnitfactorcrowd(CrptOnLineResultVo resultVo, TdZyUnitbasicinfo basicinfo){
        CrptOnLineDistributionVo distribution = resultVo.getDistribution();
        if(null == distribution){
            return null;
        }
        Boolean existsDust = distribution.getExistsDust();
        Integer dustContacts = distribution.getDustContacts();
        Boolean existsChemical = distribution.getExistsChemical();
        Integer chemicalContacts = distribution.getChemicalContacts();
        Boolean existsPhysical = distribution.getExistsPhysical();
        Integer physicalContacts = distribution.getPhysicalContacts();
        Boolean existsRadioactivity = distribution.getExistsRadioactivity();
        Integer radioactivityContacts = distribution.getRadioactivityContacts();
        Boolean existsBiotic = distribution.getExistsBiotic();
        Integer bioticContacts = distribution.getBioticContacts();
        Boolean existsOther = distribution.getExistsOther();
        Integer otherContacts = distribution.getOtherContacts();
        if(null == existsDust && null == dustContacts && null == existsChemical && null == chemicalContacts &&
                null == existsPhysical && null == physicalContacts && null == existsRadioactivity &&
                null == radioactivityContacts && null == existsBiotic && null == bioticContacts &&
                null == existsOther && null == otherContacts){
            return null;
        }
        TdZyUnitfactorcrowd crowd = new TdZyUnitfactorcrowd();
        crowd.setFkByMainId(basicinfo);
        crowd.setCreateDate(new Date());
        crowd.setCreateManid(1);
        if(null != existsDust){
            crowd.setIfhfDust(existsDust ? 1 : 0);
        }
        crowd.setHfDustPeoples(dustContacts);
        if(null != existsChemical){
            crowd.setIfhfChemistry(existsChemical ? 1 : 0);
        }
        crowd.setHfChemistryPeoples(chemicalContacts);
        if(null != existsPhysical){
            crowd.setIfhfPhysics(existsPhysical ? 1 : 0);
        }
        crowd.setHfPhysicsPeoples(physicalContacts);
        if(null != existsRadioactivity){
            crowd.setIfhfRadioactivity(existsRadioactivity ? 1 : 0);
        }
        crowd.setHfRadioactivityPeoples(radioactivityContacts);
        if(null != existsBiotic){
            crowd.setIfhfBiology(existsBiotic ? 1 : 0);
        }
        crowd.setHfBiologyPeoples(bioticContacts);
        if(null != existsOther){
            crowd.setIfhfOther(existsOther ? 1 : 0);
        }
        crowd.setHfOtherPeoples(otherContacts);
        return crowd;
    }
    /**
     * <p>方法描述：创建危害因素接触情况明细 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    private List<TdZyUnitfactorcDetail> generateTdZyUnitfactorcDetail(CrptOnLineResultVo resultVo, TdZyUnitbasicinfo basicinfo,
                                                                      StringBuffer buffer){
        List<CrptOnLineDistributionItemVo> distributionItemList = resultVo.getDistributionItemList();
        if(CollectionUtils.isEmpty(distributionItemList)){
            return Collections.EMPTY_LIST;
        }
        //定义的对照表
        TsContraSub contraSub = null;
        //使用的对照表map
        Map<String, TsContraSub> contraSubMap = this.contraSubTypeMap.get(6);
        Map<String,TsSimpleCode> simpleCodeMap = this.simpleCodeTypeMap.get("5007");
        List<TdZyUnitfactorcDetail> resultList = new ArrayList<>();
        for(CrptOnLineDistributionItemVo itemVo : distributionItemList){
            Integer hazardsSort = itemVo.getHazardsSort();
            String hazardsName = itemVo.getHazardsName();
            String codeNo = itemVo.getCodeNo();
            Boolean supervisionRequirement = itemVo.getSupervisionRequirement();
            Integer contactNumber = itemVo.getContactNumber();
            if(null == hazardsSort && null == hazardsName && null == codeNo && null == supervisionRequirement &&
                    null == contactNumber) {
                continue;
            }
            contraSub = contraSubMap.get(codeNo);
            if(null == contraSub){
                buffer.append("危害因素：").append(codeNo).append("对照失败！");
                continue;
            }
            TdZyUnitfactorcDetail detail = new TdZyUnitfactorcDetail();
            detail.setFkByMainId(basicinfo);
            detail.setCreateDate(new Date());
            detail.setCreateManid(1);
            if(!CollectionUtils.isEmpty(simpleCodeMap)){
                TsSimpleCode t = simpleCodeMap.get(contraSub.getRightCode());
                if(null==t){
                    buffer.append("危害因素：").append(codeNo).append("码表不存在！");
                    continue;
                }else {
                    detail.setFkByHazardsId(t);
                }
            }
            detail.setHazardsSort(hazardsSort);
            detail.setHazardsName(hazardsName);
            if(null != supervisionRequirement){
                detail.setSupervisionRequirement(supervisionRequirement ? 1 : 0);
            }
            detail.setContactNumber(contactNumber);
            resultList.add(detail);
        }
        return resultList;
    }
    /**
     * <p>方法描述：创建职业病危害因素检测情况 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    private TdZyUnitharmfactorcheck generateTdZyUnitharmfactorcheck(CrptOnLineResultVo resultVo, TdZyUnitbasicinfo basicinfo){
        CrptOnLineDetectionVo detection = resultVo.getDetection();
        if(null == detection){
            return null;
        }
        Boolean existsDetection = detection.getExistsDetection();
        Boolean existsDust = detection.getExistsDust();
        Integer dustCheckPoints = detection.getDustCheckPoints();
        Integer dustOverproofPoints = detection.getDustOverproofPoints();
        Boolean existsChemical = detection.getExistsChemical();
        Integer chemicalCheckPoints = detection.getChemicalCheckPoints();
        Integer chemicalOverproofPoints = detection.getChemicalOverproofPoints();
        Boolean existsPhysical = detection.getExistsPhysical();
        Integer physicalCheckPoints = detection.getPhysicalCheckPoints();
        Integer physicalOverproofPoints = detection.getPhysicalOverproofPoints();
        Boolean existsRadioactivity = detection.getExistsRadioactivity();
        Integer radioactivityCheckPoints = detection.getRadioactivityCheckPoints();
        Integer radioactivityOverproofPoints = detection.getRadioactivityOverproofPoints();
        Boolean existsBioticOther = detection.getExistsBioticOther();
        Integer bioticOtherCheckPoints = detection.getBioticOtherCheckPoints();
        Integer bioticOtherOverproofPoints = detection.getBioticOtherOverproofPoints();
        if(null == existsDetection && null == existsDust && null == dustCheckPoints && null == dustOverproofPoints &&
                null == existsChemical && null == chemicalCheckPoints && null == chemicalOverproofPoints &&
                null == existsPhysical && null == physicalCheckPoints && null == physicalOverproofPoints &&
                null == existsRadioactivity && null == radioactivityCheckPoints && null == radioactivityOverproofPoints &&
                null == existsBioticOther && null == bioticOtherCheckPoints && null == bioticOtherOverproofPoints){
            return null;
        }
        TdZyUnitharmfactorcheck check = new TdZyUnitharmfactorcheck();
        check.setFkByMainId(basicinfo);
        check.setCreateDate(new Date());
        check.setCreateManid(1);
        if(null != existsDetection){
            check.setIfat(existsDetection ? 1 : 0);
        }
        if(null != existsDust){
            check.setIfatDust(existsDust ? 1 : 0);
        }
        check.setIfatDustAllChecknum(dustCheckPoints);
        check.setIfatDustAllExcessnum(dustOverproofPoints);
        if(null != existsChemical){
            check.setIfatChemistry(existsChemical ? 1 : 0);
        }
        check.setIfatChemistryAllChecknum(chemicalCheckPoints);
        check.setIfatChemistryAllExcessnum(chemicalOverproofPoints);
        if(null != existsPhysical){
            check.setIfatPhysics(existsPhysical ? 1 : 0);
        }
        check.setIfatPhysicsAllChecknum(physicalCheckPoints);
        check.setIfatPhysicsAllExcessnum(physicalOverproofPoints);
        if(null != existsRadioactivity){
            check.setIfatRadioactivity(existsRadioactivity ? 1 : 0);
        }
        check.setIfatRadioactivityChecknum(radioactivityCheckPoints);
        check.setIfatRadioactivityExcessnum(radioactivityOverproofPoints);
        if(null != existsBioticOther){
            check.setIfatBiologyother(existsBioticOther ? 1 : 0);
        }
        check.setIfatBiologyotherChecknum(bioticOtherCheckPoints);
        check.setIfatBiologyotherExcessnum(bioticOtherOverproofPoints);
        return check;
    }
    /**
     * <p>方法描述：创建检测情况明细 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    private List<TdZyUnitharmfactDetail> generateTdZyUnitharmfactDetail(CrptOnLineResultVo resultVo, TdZyUnitbasicinfo basicinfo,
                                                                        StringBuffer buffer){
        List<CrptOnLineDetectionItemVo> detectionItemList = resultVo.getDetectionItemList();
        if(CollectionUtils.isEmpty(detectionItemList)){
            return Collections.EMPTY_LIST;
        }
        //定义的对照表
        TsContraSub contraSub = null;
        //使用的对照表map
        Map<String, TsContraSub> contraSubMap = this.contraSubTypeMap.get(6);
        Map<String,TsSimpleCode> simpleCodeMap = this.simpleCodeTypeMap.get("5007");
        List<TdZyUnitharmfactDetail> resultList = new ArrayList<>();
        for(CrptOnLineDetectionItemVo itemVo : detectionItemList){
            Integer hazardsSort = itemVo.getHazardsSort();
            String hazardsName = itemVo.getHazardsName();
            String codeNo = itemVo.getCodeNo();
            Boolean supervisionRequirement = itemVo.getSupervisionRequirement();
            Integer checkPoints = itemVo.getCheckPoints();
            Integer overproofPoints = itemVo.getOverproofPoints();
            if(null == hazardsSort && null == hazardsName && null == codeNo && null == supervisionRequirement &&
                    null == checkPoints && null == overproofPoints){
                continue;
            }
            contraSub = contraSubMap.get(codeNo);
            if(null == contraSub){
                buffer.append("危害因素：").append(codeNo).append("对照失败！");
                continue;
            }
            TdZyUnitharmfactDetail detail = new TdZyUnitharmfactDetail();
            detail.setFkByMainId(basicinfo);
            detail.setCreateDate(new Date());
            detail.setCreateManid(1);
            if(!CollectionUtils.isEmpty(simpleCodeMap)){
                TsSimpleCode t = simpleCodeMap.get(contraSub.getRightCode());
                if(null==t){
                    buffer.append("危害因素：").append(codeNo).append("码表不存在！");
                    continue;
                }else {
                    detail.setFkByHazardsId(t);
                }
            }
            detail.setHazardsSort(hazardsSort);
            detail.setHazardsName(hazardsName);
            if(null != supervisionRequirement){
                detail.setSupervisionRequirement(supervisionRequirement ? 1 : 0);
            }
            detail.setCheckPoints(checkPoints);
            detail.setOverproofPoints(overproofPoints);
            resultList.add(detail);
        }
        return resultList;
    }
    /**
     * <p>方法描述：创建检测机构 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    private List<TdZyJcOrg> generateTdZyJcOrg(CrptOnLineResultVo resultVo, TdZyUnitbasicinfo basicinfo){
        List<CrptOnLineDetectionOrgVo> detectionOrgList = resultVo.getDetectionOrgList();
        if(CollectionUtils.isEmpty(detectionOrgList)){
            return Collections.EMPTY_LIST;
        }
        List<TdZyJcOrg> resultList = new ArrayList<>();
        for(CrptOnLineDetectionOrgVo orgVo : detectionOrgList){
            String unitName = orgVo.getUnitName();
            String reportNo = orgVo.getReportNo();
            String creditCode = orgVo.getCreditCode();
            String contactInfo = orgVo.getContactInfo();
            if(null == unitName && null == reportNo && null == creditCode && null == contactInfo){
                continue;
            }
            TdZyJcOrg jcOrg = new TdZyJcOrg();
            jcOrg.setFkByMainId(basicinfo);
            jcOrg.setCreateDate(new Date());
            jcOrg.setCreateManid(1);
            jcOrg.setUnitName(null == unitName ? "" : unitName);
            jcOrg.setReportNo(null == reportNo ? "" : reportNo);
            jcOrg.setCreditCode(null == creditCode ? "" : creditCode);
            jcOrg.setContactInfo(null == contactInfo ? "" : contactInfo);
            resultList.add(jcOrg);
        }
        return resultList;
    }
    /**
     * <p>方法描述：创建职业健康监护开展情况 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    private TdZyUnithealthcustody generateTdZyUnithealthcustody(CrptOnLineResultVo resultVo, TdZyUnitbasicinfo basicinfo){
        CrptOnLineSupervisionVo supervision = resultVo.getSupervision();
        if(null == supervision){
            return null;
        }
        Boolean existsSupervision = supervision.getExistsSupervision();
        Boolean existsDust = supervision.getExistsDust();
        Integer dustNum = supervision.getDustNum();
        Boolean existsChemical = supervision.getExistsChemical();
        Integer chemicalNum = supervision.getChemicalNum();
        Boolean existsPhysical = supervision.getExistsPhysical();
        Integer physicalNum = supervision.getPhysicalNum();
        Boolean existsRadioactivity = supervision.getExistsRadioactivity();
        Integer radioactivityNum = supervision.getRadioactivityNum();
        if(null == existsSupervision && null == existsDust && null == dustNum && null == existsChemical &&
                null == chemicalNum && null == existsPhysical && null == physicalNum && null == existsRadioactivity &&
                null == radioactivityNum){
            return null;
        }
        TdZyUnithealthcustody custody = new TdZyUnithealthcustody();
        custody.setFkByMainId(basicinfo);
        custody.setCreateDate(new Date());
        custody.setCreateManid(1);
        if(null != existsSupervision){
            custody.setIfhea(existsSupervision ? 1 : 0);
        }
        if(null != existsDust){
            custody.setIfheaDust(existsDust ? 1 : 0);
        }
        custody.setHeaDustPeoples(dustNum);
        if(null != existsChemical){
            custody.setIfheaChemistry(existsChemical ? 1 : 0);
        }
        custody.setHeaChemistryPeoples(chemicalNum);
        if(null != existsPhysical){
            custody.setIfheaPhysics(existsPhysical ? 1 : 0);
        }
        custody.setHeaPhysicsPeoples(physicalNum);
        if(null != existsRadioactivity){
            custody.setIfheaRadioactivity(existsRadioactivity ? 1 : 0);
        }
        custody.setHeaRadioactivityPeoples(radioactivityNum);
        return custody;
    }
    /**
     * <p>方法描述：创建职业健康监护开展情况明细 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    private List<TdZyUnithealthDetail> generateTdZyUnithealthDetail(CrptOnLineResultVo resultVo, TdZyUnitbasicinfo basicinfo,
                                                                    StringBuffer buffer){
        List<CrptOnLineSupervisionItemVo> supervisionItemList = resultVo.getSupervisionItemList();
        if(CollectionUtils.isEmpty(supervisionItemList)){
            return Collections.EMPTY_LIST;
        }
        //定义的对照表
        TsContraSub contraSub = null;
        //使用的对照表map
        Map<String, TsContraSub> contraSubMap = this.contraSubTypeMap.get(6);
        Map<String,TsSimpleCode> simpleCodeMap = this.simpleCodeTypeMap.get("5007");
        List<TdZyUnithealthDetail> resultList = new ArrayList<>();
        for(CrptOnLineSupervisionItemVo itemVo : supervisionItemList){
            Integer hazardsSort = itemVo.getHazardsSort();
            String hazardsName = itemVo.getHazardsName();
            String codeNo = itemVo.getCodeNo();
            Boolean supervisionRequirement = itemVo.getSupervisionRequirement();
            Integer peNum = itemVo.getPeNum();
            if(null == hazardsSort && null == hazardsName && null == codeNo && null == supervisionRequirement &&
                    null == peNum){
                continue;
            }
            contraSub = contraSubMap.get(codeNo);
            if(null == contraSub){
                buffer.append("危害因素：").append(codeNo).append("对照失败！");
                continue;
            }
            TdZyUnithealthDetail detail = new TdZyUnithealthDetail();
            detail.setFkByMainId(basicinfo);
            detail.setCreateDate(new Date());
            detail.setCreateManid(1);
            if(!CollectionUtils.isEmpty(simpleCodeMap)){
                TsSimpleCode t = simpleCodeMap.get(contraSub.getRightCode());
                if(null==t){
                    buffer.append("危害因素：").append(codeNo).append("码表不存在！");
                    continue;
                }else {
                    detail.setFkByHazardsId(t);
                }
            }
            detail.setHazardsSort(hazardsSort);
            detail.setHazardsName(hazardsName);
            detail.setPeNum(peNum);
            if(null != supervisionRequirement){
                detail.setSupervisionRequirement(supervisionRequirement ? 1 : 0);
            }
            resultList.add(detail);
        }
        return resultList;
    }
    /**
     * <p>方法描述：创建职业健康监护机构 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    private List<TdZyHethOrg> generateTdZyHethOrg(CrptOnLineResultVo resultVo, TdZyUnitbasicinfo basicinfo){
        List<CrptOnLineSupervisionOrgVo> supervisionOrgList = resultVo.getSupervisionOrgList();
        if(CollectionUtils.isEmpty(supervisionOrgList)){
            return Collections.EMPTY_LIST;
        }
        List<TdZyHethOrg> resultList = new ArrayList<>();
        for(CrptOnLineSupervisionOrgVo orgVo : supervisionOrgList){
            String unitName = orgVo.getUnitName();
            String reportNo = orgVo.getReportNo();
            String creditCode = orgVo.getCreditCode();
            String contactInfo = orgVo.getContactInfo();
            if(null == unitName && null == reportNo && null == creditCode && null == contactInfo){
                continue;
            }
            TdZyHethOrg hethOrg = new TdZyHethOrg();
            hethOrg.setFkByMainId(basicinfo);
            hethOrg.setCreateDate(new Date());
            hethOrg.setCreateManid(1);
            hethOrg.setUnitName(null == unitName ? "" : unitName);
            hethOrg.setReportNo(null == reportNo ? "" : reportNo);
            hethOrg.setCreditCode(null == creditCode ? "" : creditCode);
            hethOrg.setContactInfo(null == contactInfo ? "" : contactInfo);
            resultList.add(hethOrg);
        }
        return resultList;
    }


    /**
     * <p>方法描述：请求用人单位申报数据下载接口 </p>
     * @MethodAuthor： pw 2022/9/6
     **/
    private String requestDeclaration(){
        String tokenId = this.requestToken();
        if(null == tokenId){
            return null;
        }
        log.info("tokenId:{}"+tokenId);
        String requestUrl = this.urlHost + this.declarationUrl;
        try{
            String returnJson;
            Map<String,String> headerMap = new HashMap<>();
            headerMap.put("Content-type","application/json; charset=UTF-8");
            headerMap.put("tokenId",tokenId);
            if(this.ifSSL){
                returnJson = HttpRequestUtil.httpSSLRequest(requestUrl, "GET",null, headerMap);
            }else{
                returnJson = HttpRequestUtil.httpGetRequestByRaw(requestUrl,tokenId);
            }
            return returnJson;
        }catch(Exception e){
            log.error(e.getMessage(),new Throwable(e));
        }
        return null;
    }
    
    /**
     * <p>方法描述：请求token </p>
     * @MethodAuthor： pw 2022/9/6
     **/
    private String requestToken(){
        try {
            String tokenKey = "token";
            CrptOnLineTokenVo token = (CrptOnLineTokenVo) CaffeineUtil.getIfPresent(BadRsnCaffeineKeyEnum.CRPT_ONLINE_REPORT, tokenKey);
            //有效期
            if(null!=token){
                if(token.getEndDate().after(new Date())){//有效
                    return token.getTokenId();
                }
            }
            String requestUrl = this.urlHost + this.tokenUrl+"?secretKey="+this.secretKey;
            String returnJson;
            if(ifSSL){
                returnJson = HttpRequestUtil.httpSSLRequest(requestUrl, "GET",null, null);
            }else{
                returnJson = HttpRequestUtil.httpRequest(requestUrl, "GET",null);
            }
            if(StringUtils.isBlank(returnJson)){
                return null;
            }
            log.info("请求tokenId返回：{}", returnJson);
            token = JSON.parseObject(returnJson,CrptOnLineTokenVo.class);
            if(null!=token){
                if(!RespCodeEnum.SUCCESS.getCode().equals(token.getType())){
                    throw new Exception("请求token失败！");
                }
                int hour = 23;
                if(null != token.getValidateTime()){
                    hour = token.getValidateTime()-1;
                }
                if(null == token.getMs()){
                    token.setMs(new Date());
                }
                Date endTime = DateUtils.addHours(token.getMs(),hour);
                token.setEndDate(endTime);
            }
            CaffeineUtil.put(BadRsnCaffeineKeyEnum.CRPT_ONLINE_REPORT,tokenKey,token);
            return token.getTokenId();
        } catch (Exception e) {
            log.error(e.getMessage(),new Throwable(e));
        }
        return null;
    }

    /**
     * <p>方法描述：初始化码表以及对照 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    private void initMap(){
        this.contraSubTypeMap = new HashMap<>();
        this.contraSubTypeMap.put(1, contraSubService.findTsContraSub("9", "1"));
        this.contraSubTypeMap.put(2, contraSubService.findTsContraSub("9", "2"));
        this.contraSubTypeMap.put(3, contraSubService.findTsContraSub("9", "3"));
        this.contraSubTypeMap.put(4, contraSubService.findTsContraSub("9", "4"));
        this.contraSubTypeMap.put(5, contraSubService.findTsContraSub("9", "5"));
        this.contraSubTypeMap.put(6, contraSubService.findTsContraSub("9", "6"));
        this.simpleCodeTypeMap = new HashMap<>();
        this.simpleCodeTypeMap.put("5508", this.codeService.findTsSimpleCodeMap("5508"));
        this.simpleCodeTypeMap.put("5004",this.codeService.findTsSimpleCodeMap("5004"));
        this.simpleCodeTypeMap.put("5002",this.codeService.findTsSimpleCodeMap("5002"));
        this.simpleCodeTypeMap.put("5003",this.codeService.findTsSimpleCodeMap("5003"));
        this.simpleCodeTypeMap.put("5007",this.codeService.findTsSimpleCodeMap("5007"));
        this.tsZoneMap = zoneService.findTsZoneMap();
    }
}

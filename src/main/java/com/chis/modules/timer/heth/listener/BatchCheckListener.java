package com.chis.modules.timer.heth.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.entity.TdTjBhk;
import com.chis.modules.timer.heth.entity.TdTjCheckTask;
import com.chis.modules.timer.heth.handler.ErrorFileCustomCellWriteHandler;
import com.chis.modules.timer.heth.logic.vo.CheckDataVo;
import com.chis.modules.timer.heth.service.BhkCheckBackService;
import com.chis.modules.timer.heth.service.TdTjBhkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel数据读取监听器
 * 负责数据的验证和批量处理
 */
@Slf4j
public class BatchCheckListener extends AnalysisEventListener<CheckDataVo> {
    /**
     * 临时存储待处理的数据
     */
    private List<CheckDataVo> dataList = new ArrayList<>();
    /**
     * 存储错误记录的集合
     */
    private List<CheckDataVo> errorRecords;

    private boolean isHeaderValid = false;

    /**
     * 用于记录已处理的体检编号和体检机构组合
     */
    private Map<String,List<CheckDataVo>> processedRecords = new HashMap<>();
    private TdTjBhkService tdTjBhkService;
    private BhkCheckBackService bhkCheckBackService;
    private TdTjCheckTask task;

    /**
     * 条数
     */
    private Integer rowCount = 0;

    /**
     * 用于生成错误文件
     */
    private ExcelWriter excelWriter;

    /**
     * 用于生成错误文件
     */
    private WriteSheet writeSheet;

    /**
     * Excel列名；key:列下标，value:列名
     */
    private Map<Integer, String> headTitleMap;
    /**
     * 是否已生成错误文件
     */
    private Boolean hasFileGen;

    /**错误文件下载路径*/
    private String errorFilePath;
    /**
     * 构造函数
     *
     * @param errorRecords 错误记录集合
     */
    public BatchCheckListener(List<CheckDataVo> errorRecords, TdTjBhkService tdTjBhkService, BhkCheckBackService bhkCheckBackService, TdTjCheckTask task,String fileName) {
        this.errorRecords = errorRecords;
        this.tdTjBhkService = tdTjBhkService;
        this.bhkCheckBackService = bhkCheckBackService;
        this.task = task;
        this.hasFileGen=false;
        this.errorFilePath=fileName;
    }

    /**
     * 处理每一行数据
     *
     * @param data    行数据
     * @param context 解析上下文
     */
    @Override
    public void invoke(CheckDataVo data, AnalysisContext context) {
        rowCount++;
        //清除数据的前后空格
        clearSpaces(data);

        dataList.add(data);

        validateData(data);
    }

    /**
    * <p>Description：清除数据的前后空格 </p>
    * <p>Author： yzz 2025/4/11 </p>
    */
    private void clearSpaces(CheckDataVo data) {
        if(StringUtils.isNotBlank(data.getName())){
            data.setName(StrUtil.trim(data.getName()));
        }
        if(StringUtils.isNotBlank(data.getCheckNo())){
            data.setCheckNo(StrUtil.trim(data.getCheckNo()));
        }
        if(StringUtils.isNotBlank(data.getCheckOrg())){
            data.setCheckOrg(StrUtil.trim(data.getCheckOrg()));
        }
        if(StringUtils.isNotBlank(data.getRejectReason())){
            data.setRejectReason(StrUtil.trim(data.getRejectReason()));
        }
    }

    /**
     * 处理批次数据
     * 验证通过的数据进行保存，验证失败的数据添加到错误记录
     */
    private void processBatch() {
        //无要处理的数据时，返回
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }

        // 获取验证通过的集合
        List<CheckDataVo> validRecords = dataList.stream()
                .filter(data -> data.getErrorMsg() == null || data.getErrorMsg().isEmpty())
                .collect(Collectors.toList());
        // 批次验证数据
        validateBatch(validRecords);
        validRecords = validRecords.stream()
                .filter(data -> data.getErrorMsg() == null || data.getErrorMsg().isEmpty())
                .collect(Collectors.toList());

        if (!validRecords.isEmpty()) {
            try {
                this.bhkCheckBackService.saveBatchBackData(validRecords, task);
            } catch (Exception e) {
                e.printStackTrace();
                if(validRecords.size()==1){
                    return;
                }
                // 批量保存失败，转为单条保存
                for (CheckDataVo data : validRecords) {
                    try {
                        this.bhkCheckBackService.saveSingleRecord(data,task);
                    } catch (Exception ex) {
                        data.setErrorMsg("保存失败");
                    }
                }
            }
        }

        //重新赋值 有重复数据
        this.errorRecords.addAll(dataList.stream()
                .filter(data -> StringUtils.isNotBlank(data.getErrorMsg()))
                .collect(Collectors.toList()));
        dataList.clear();
    }

    /**
     * 验证批次数据
     *
     * @param batch 待验证的批次数据
     * @return 验证通过的数据列表
     */
    private void validateBatch(List<CheckDataVo> batch) {
        if (batch == null || batch.isEmpty()) {
            return;
        }
        // 构建查询条件列表
        List<Map<String, String>> conditions = batch.stream()
                .filter(data ->
                        data.getCheckNo() != null && !data.getCheckNo().isEmpty() &&
                                data.getCheckOrg() != null && !data.getCheckOrg().isEmpty() && StringUtils.isBlank(data.getErrorMsg())
                )
                .map(data -> {
                    Map<String, String> condition = new HashMap<>();
                    condition.put("checkNo", data.getCheckNo());
                    condition.put("checkOrg", data.getCheckOrg());
                    return condition;
                })
                .collect(Collectors.toList());

        // 批量查询体检记录
        List<TdTjBhk> bhkList = tdTjBhkService.selectBhksByConditions(conditions);

        // 构建体检编号和机构的组合key的Map,用于快速查找
        Map<String, TdTjBhk> bhkMap = bhkList.stream()
                .collect(Collectors.toMap(
                        bhk -> bhk.getBhkCode() + "_" + bhk.getFkByBhkorgId().getUnitName(),
                        bhk -> bhk,
                        (existing, replacement) -> existing
                ));

        // 解析导出条件
        Map<String, Object> condition = JSON.parseObject(task.getExportCondition());
        String zoneType = condition.get("zoneType").toString();
        String checkLevel = condition.get("checkLevel").toString();
        String taskZoneGb = task.getFkByZoneId().getZoneGb();

        // 验证每条数据
        for (CheckDataVo data : batch) {
            String key = data.getCheckNo() + "_" + data.getCheckOrg();
            StringBuilder errorMsg = new StringBuilder();

            // 检查记录是否存在
            if (!bhkMap.containsKey(key)) {
                errorMsg.append("未找到对应的体检记录;");
            } else {
                TdTjBhk bhk = bhkMap.get(key);
                // 验证管辖地区
                if (!validateZone(zoneType, taskZoneGb, bhk.getZoneGb())) {
                    errorMsg.append("该体检记录不在管辖地区内，无法审核;");
                }
                // 验证审核状态
                if (!validateState(checkLevel, zoneType, bhk.getState(),bhk.getIfCityDirect())) {
                    errorMsg.append("该体检记录状态非待审核，无法审核;");
                }
            }

            // 如果有错误信息，添加到错误记录
            if (errorMsg.length() > 0) {
                if (data.getErrorMsg() != null && !data.getErrorMsg().isEmpty()) {
                    data.setErrorMsg(data.getErrorMsg() + errorMsg);
                } else {
                    data.setErrorMsg(errorMsg.toString());
                }
            } else {
                data.setBhk(bhkMap.get(key));
            }
        }
    }

    /**
     * 验证审核状态
     *
     * @param checkLevel 审核级别（2-二级审核，3-三级审核）
     * @param zoneType   区划类型（2-省级，3-市级，4-县级）
     * @param state      体检记录状态（1-待审核，3-市级待审核，5/6-省级待审核）
     * @return 验证结果
     */
    private boolean
    validateState(String checkLevel, String zoneType, String state,Integer ifCityDirect) {
        // 参数校验
        if (checkLevel == null || zoneType == null || state == null) {
            log.warn("审核状态验证参数为空：checkLevel={}, zoneType={}, state={}",
                    checkLevel, zoneType, state);
            return false;
        }

        try {
            // 二级审核
            if ("2".equals(checkLevel)) {
                switch (zoneType) {
                    case "4":  // 县级
                        return "1".equals(state);
                    case "2":  // 省级
                        return "5".equals(state) || "6".equals(state);
                    default:
                        log.warn("二级审核不支持的区划类型：{}", zoneType);
                        return false;
                }
            }
            // 三级审核
            else if ("3".equals(checkLevel)) {
                switch (zoneType) {
                    case "4":  // 县级
                        return "1".equals(state);
                    case "3":  // 市级
                        return "3".equals(state) || (new Integer("1").equals(ifCityDirect) && "1".equals(state));
                    case "2":  // 省级
                        return "5".equals(state) || "6".equals(state);
                    default:
                        log.warn("三级审核不支持的区划类型：{}", zoneType);
                        return false;
                }
            } else {
                log.warn("不支持的审核级别：{}", checkLevel);
                return false;
            }
        } catch (Exception e) {
            log.error("验证审核状态时发生异常", e);
            return false;
        }
    }

    /**
     * 验证管辖地区
     *
     * @param zoneType   区划类型（2-省级，3-市级，4-县级）
     * @param taskZoneGb 任务区划编码
     * @param bhkZoneGb  体检记录区划编码
     * @return 验证结果
     */
    private boolean validateZone(String zoneType, String taskZoneGb, String bhkZoneGb) {
        // 参数校验
        if (taskZoneGb == null || bhkZoneGb == null || zoneType == null) {
            return false;
        }

        try {
            // 根据区划类型判断需要比较的区划编码长度
            int compareLength;
            switch (zoneType) {
                case "2":  // 省级
                    compareLength = 2;
                    break;
                case "3":  // 市级
                    compareLength = 4;
                    break;
                case "4":  // 县级
                    compareLength = 6;
                    break;
                default:
                    log.warn("未知的区划类型：{}", zoneType);
                    return false;
            }

            // 确保体检记录的区划编码长度足够
            if (bhkZoneGb.length() < compareLength) {
                log.warn("体检记录区划编码[{}]长度不足", bhkZoneGb);
                return false;
            }

            // 确保任务的区划编码长度足够
            if (taskZoneGb.length() < compareLength) {
                log.warn("任务区划编码[{}]长度不足", taskZoneGb);
                return false;
            }

            // 比较区划编码前N位是否相同
            String bhkZonePrefix = bhkZoneGb.substring(0, compareLength);
            String taskZonePrefix = taskZoneGb.substring(0, compareLength);

            return bhkZonePrefix.equals(taskZonePrefix);

        } catch (Exception e) {
            log.error("验证管辖地区时发生异常", e);
            return false;
        }
    }

    /**
    * <p>Description：验证表头是否缺失 </p>
    * <p>Author： yzz 2025/4/11 </p>
    */
    private boolean isValidHeadData(Map<Integer, String> headMap){
        boolean isValid = true;
        Field[] fields = CheckDataVo.class.getDeclaredFields();
        List<String> headNames = new ArrayList<>();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ExcelProperty.class)) {
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                // 获取 value 值（可能是多个表头）
                String[] headers = excelProperty.value();
                if(headers.length > 0){
                    if("姓名".equals(headers[0].trim())){
                        continue;
                    }
                    headNames.add(headers[0].trim());
                }
            }
        }
        List<String> headNameList = new ArrayList<>();
        for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
            headNameList.add(entry.getValue().trim());
        }
        headNames.removeAll(headNameList);
        if(!headNames.isEmpty()){
            return false;
        }
        return isValid;
    }

    /**
     * 所有数据解析完成后的操作
     *
     * @param context 解析上下文
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        processBatch();
        if (!CollectionUtils.isEmpty(this.errorRecords)) {
            writeToErrorFile();
        }
        if (!ObjectUtils.isEmpty(this.excelWriter)) {
            this.excelWriter.finish();
        }
        // 存储完成清理
        this.errorRecords = new ArrayList<>();
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        headTitleMap = headMap;
        isHeaderValid = isValidHeadData(headMap);
        if (!isHeaderValid) {
            throw new RuntimeException("表头缺失，请确保包含列：体检编号、体检机构名称、退回原因");
        }
    }
    /**
     * 错误数据写入错误文件
     */
    private void writeToErrorFile() {
        List<List<String>> datasList = new ArrayList<>();
        if(CollectionUtils.isEmpty(errorRecords)){
            return;
        }
        for (CheckDataVo errorRecord : errorRecords) {
            List<String> dataList = new ArrayList<>();
            for (Map.Entry<Integer, String> entry : headTitleMap.entrySet()) {
                String value = entry.getValue().trim();
                if("姓名".equals(value)) {
                    dataList.add(errorRecord.getName());
                }else if("体检编号".equals(value)) {
                    dataList.add(errorRecord.getCheckNo());
                }else if("体检机构名称".equals(value)) {
                    dataList.add(errorRecord.getCheckOrg());
                }else if("退回原因".equals(value)) {
                    dataList.add(errorRecord.getRejectReason());
                }
            }
            dataList.add(errorRecord.getErrorMsg());
            datasList.add(dataList);
        }

        if (CollectionUtils.isEmpty(datasList)) {
            return;
        }
        if (!this.hasFileGen) {
            genErrorFile();
        }
        this.excelWriter.write(datasList, this.writeSheet);
    }

    /**
     * 生成错误文件
     */
    private void genErrorFile() {
        if (ObjectUtils.isEmpty(this.writeSheet) && !ObjectUtils.isEmpty(this.excelWriter)) {
            this.excelWriter.finish();
            this.excelWriter = null;
        }
        if (ObjectUtils.isEmpty(this.excelWriter)) {
            this.excelWriter = EasyExcel.write(this.errorFilePath).registerWriteHandler(new ErrorFileCustomCellWriteHandler()).build();
            this.writeSheet = EasyExcel.writerSheet("Sheet").build();
        }
        List<List<String>> headsList = new ArrayList<>();
        List<String> headList = new ArrayList<>();
        this.headTitleMap.forEach(headList::add);
        headList.add("失败信息");
        headsList.add(headList);
        this.excelWriter.write(headsList, this.writeSheet);
        this.hasFileGen = true;
    }

    /**
     * 验证数据有效性
     *
     * @param data 待验证的数据
     * @return 验证结果
     */
    private boolean validateData(CheckDataVo data) {
        StringBuilder errorMsg = new StringBuilder();

        // 验证体检编号
        validateField(errorMsg, data.getCheckNo(), "体检编号", 50);

        // 验证体检机构
        validateField(errorMsg, data.getCheckOrg(), "体检机构名称", 100);

        // 验证退回原因
        validateField(errorMsg, data.getRejectReason(), "退回原因", 100);

        //验证重复
        validateRepeat(data, errorMsg);

        // 如果有错误信息，设置到对象中并返回false
        if (errorMsg.length() > 0) {
            // 如果已有错误信息，追加新的错误信息
            if (data.getErrorMsg() != null && !data.getErrorMsg().isEmpty()) {
                data.setErrorMsg(data.getErrorMsg() + errorMsg);
            } else {
                data.setErrorMsg(errorMsg.toString());
            }
            return false;
        }
        return true;
    }
    
    /**
    * <p>Description：验证重复 </p>
    * <p>Author： yzz 2025/4/16 </p>
    */
    private void validateRepeat(CheckDataVo data, StringBuilder errorMsg) {
        // 验证体检编号和体检机构的唯一性
        if(StringUtils.isNotBlank(data.getCheckNo()) && StringUtils.isNotBlank(data.getCheckOrg())){
            String key = data.getCheckNo() + "_" + data.getCheckOrg();
            if (processedRecords.containsKey(key)) {
                for (CheckDataVo checkDataVo : processedRecords.get(key)) {
                    if (checkDataVo.getErrorMsg() != null && !checkDataVo.getErrorMsg().isEmpty()) {
                        checkDataVo.setErrorMsg(checkDataVo.getErrorMsg().contains("体检机构名称、体检编号不能重复")?checkDataVo.getErrorMsg() : "体检机构、体检编号不能重复;");
                    } else {
                        checkDataVo.setErrorMsg("体检机构名称、体检编号不能重复;");
                    }
                }
                errorMsg.append("体检机构名称、体检编号不能重复;");
            } else {
                List<CheckDataVo> list=new ArrayList<>();
                list.add(data);
                processedRecords.put(key,list);
            }
        }
    }

    /**
     * 验证字段的有效性
     *
     * @param errorMsg  错误信息收集器
     * @param value     字段值
     * @param fieldName 字段名称
     * @param maxLength 最大长度限制
     */
    private void validateField(StringBuilder errorMsg, String value, String fieldName, int maxLength) {
        if (value == null || value.trim().isEmpty()) {
            errorMsg.append(fieldName).append("不能为空;");
        } else if (value.length() > maxLength) {
            errorMsg.append(fieldName).append("长度不能超过").append(maxLength).append(";");
        }
    }

}
package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZwyjDangerItems;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2020-11-21,TdZwyjDangerItemsMapper
 */
@Repository
public interface TdZwyjDangerItemsMapper extends ZwxBaseMapper<TdZwyjDangerItems> {

    /** 通过项目rid集合获取危急值项目维护 */
    List<TdZwyjDangerItems> selectListByItemIds(@Param("itemIdList") List<Integer> itemIdList);
}

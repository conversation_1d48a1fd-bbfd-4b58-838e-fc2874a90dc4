package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdZyUnitbasicRcdSub
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZY_UNITBASIC_RCD_SUB")
@KeySequence(value = "TD_ZY_UNITBASIC_RCD_SUB_SEQ",clazz = Integer.class)
public class TdZyUnitbasicRcdSub extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZyUnitbasicRcd fkByMainId;

    @TableField("UUID")
    private String uuid;

    @TableField("UNIT_NAME")
    private String unitName;

    @TableField("DEAL_MARK")
    private Integer dealMark;

    @TableField("DEAL_RSN")
    private String dealRsn;


    public TdZyUnitbasicRcdSub(Integer rid) {
        super(rid);
    }


}

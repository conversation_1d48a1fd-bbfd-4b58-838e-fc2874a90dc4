package com.chis.modules.timer.heth.service;


import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2022-12-21,TdZwOcchethCardService
 */
@Service
public abstract class TdZwOcchethCardService<T>{


    /**
     * <p>方法描述：获取需要上传的报送卡数据</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-21
     **/
    public abstract List<T> findCardList(Integer dataSize);
}

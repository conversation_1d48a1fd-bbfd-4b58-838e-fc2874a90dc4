package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * <AUTHOR> 2024-07-11,TbZwOrgWarnSub
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TB_ZW_ORG_WARN_SUB")
public class TbZwOrgWarnSub extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID", el = "fkByMainId.rid")
    private TbZwOrgWarnConfig fkByMainId;

    @TableField(value = "WARN_TYPE_ID", el = "fkByWarnTypeId.rid")
    private TsSimpleCode fkByWarnTypeId;

    @TableField("XH")
    private String xh;


    public TbZwOrgWarnSub(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.job.zw.warn;

import com.chis.modules.timer.heth.pojo.bo.warn.DisCaseBusDataBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

/**
* <p>Description：职业病例数 </p>
* <p>Author： cc 2023-11-10 </p>
*/
@Slf4j
@Component
public class OccDisCaseJob extends DisCaseJob {

    @Scheduled(cron = "${warn-timer.zwWarn.cron.occDisCase}")
    public void start() {
        super.warnStart();
    }

    /**
     * 设置预警类型及业务类型
     */
    public void setWarnTypeAndBusType() {
        super.setWarnType(2);
        super.setBusType(1);
    }

    /**
     * 查询业务数据(业务日期正序)
     *
     * @return 业务数据
     */
    public List<DisCaseBusDataBO> findBusDataList() {
        return super.disCaseJobService.findOccDisCaseDataList(super.zoneGbMax, super.beginDate);
    }

}

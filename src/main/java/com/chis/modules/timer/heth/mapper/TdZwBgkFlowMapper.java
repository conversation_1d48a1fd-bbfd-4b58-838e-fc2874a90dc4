package com.chis.modules.timer.heth.mapper;


import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZwBgkFlow;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人, 2024-10-15,TdZwBgkFlowMapper
 */
@Repository
public interface TdZwBgkFlowMapper extends ZwxBaseMapper<TdZwBgkFlow> {

    int batchInsertBgkFlow(@Param("operationFlag") Integer operationFlag, @Param("psnId") Integer psnId, @Param("smtPsnId") Integer smtPsnId, @Param("auditAdv") String auditAdv, @Param("auditMan") String auditMan, @Param("rids") List<Integer> rids);

    List<TdZwBgkFlow> selectBgkFlowByLastMark(@Param("lastMark") Integer lastMark, @Param("rids") List<Integer> rids);

    int updateBgkFlow(@Param("checkRsnId") Integer checkRsnId, @Param("timely") Integer timely,@Param("psnId") Integer psnId, @Param("rids") List<Integer> rids);

    int batchInsertBgkFlowBack(@Param("operationFlag") Integer operationFlag, @Param("psnId") Integer psnId, @Param("smtPsnId") Integer smtPsnId, @Param("auditMan") String auditMan,@Param("params") List<Map<String, String>> params);
}

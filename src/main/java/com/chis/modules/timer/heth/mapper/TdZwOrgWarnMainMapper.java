package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZwOrgWarnMain;
import com.chis.modules.timer.heth.pojo.bo.warn.CheckOrgWarnPsnBO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * <AUTHOR> 2024-07-12,TdZwOrgWarnMainMapper
 */
@Repository
public interface TdZwOrgWarnMainMapper extends ZwxBaseMapper<TdZwOrgWarnMain> {
    List<TdZwOrgWarnMain> selectCheckOrgWarnMainListByOrgId(@Param("orgIdList") List<Integer> orgIdList);

    List<CheckOrgWarnPsnBO> selectCheckOrgWarnPsnListByOrgId(@Param("orgId") String orgId);
}

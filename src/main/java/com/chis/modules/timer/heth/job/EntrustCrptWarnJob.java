package com.chis.modules.timer.heth.job;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.service.TsSimpleCodeService;
import com.chis.modules.timer.heth.entity.TbTjCrpt;
import com.chis.modules.timer.heth.entity.TbTjCrptWarn;
import com.chis.modules.timer.heth.logic.vo.CrptWarnOcchethCardInfoVo;
import com.chis.modules.timer.heth.logic.vo.CrptWarnUnitbasicInfoVo;
import com.chis.modules.timer.heth.logic.vo.CrptWarnZxjcUnitInfoVo;
import com.chis.modules.timer.heth.service.EntrustCrptWarnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>类描述： 用人单位预警计算工具 </p>
 * pw 2023/9/27
 **/
@Slf4j
@Component
public class EntrustCrptWarnJob {
    @Value("${heth-timer.crpt-warn.zone-code}")
    private String queryZoneGb;
    @Value("${heth-timer.crpt-warn.days}")
    private Integer warnDays;
    @Value("${heth-timer.crpt-warn.query-date}")
    private String queryDateStr;
    @Value("${heth-timer.crpt-warn.query-size}")
    private Integer querySize;

    @Resource
    private TsSimpleCodeService codeService;
    @Resource
    private EntrustCrptWarnService warnService;

    /** 预警类型Map key 扩展字段1 */
    private Map<String, TsSimpleCode> warnMap;
    /** 系统确认的处理结果码表 */
    private TsSimpleCode resultSimpleCode;

    @Scheduled(cron = "${heth-timer.crpt-warn.sche-cron}")
    //@Scheduled(initialDelay = 3000,fixedDelay = 60000)
    public void start(){
        if(!this.ymlParamCheck() || !this.validateSimpleCode()){
            return;
        }
        log.info("用人单位预警计算工具版本{} 启动时间 {}", 20230928,new Date());
        for(String zoneGb : this.queryZoneGb.split(",")){
            this.execute(zoneGb);
        }
        log.info("用人单位预警计算工具完成");
    }

    /**
     * <p>方法描述：单地区处理 </p>
     * pw 2023/9/27
     **/
    private void execute(String zoneGb){
        if(StringUtils.isBlank(zoneGb)){
            return;
        }
        log.info("用人单位预警计算工具执行中 处理地区{}", zoneGb);
        long time = System.currentTimeMillis();
        List<CrptWarnOcchethCardInfoVo> crptInfoList = this.warnService.findPrepareWarnCrptIds(zoneGb.trim(), this.querySize);
        while(!CollectionUtils.isEmpty(crptInfoList)){
            this.singleExecute(crptInfoList);
            crptInfoList = this.warnService.findPrepareWarnCrptIds(zoneGb.trim(), this.querySize);
        }
        log.info("用人单位预警计算工具 处理地区{}完成 用时{}ms", zoneGb, (System.currentTimeMillis()-time));
    }

    /**
     * <p>方法描述：处理一组数据 </p>
     * pw 2023/9/28
     **/
    private void singleExecute(List<CrptWarnOcchethCardInfoVo> crptInfoList){
        if(CollectionUtils.isEmpty(crptInfoList)){
            return;
        }
        List<Integer> crptRidList = crptInfoList.stream().filter(v -> null != v.getCrptId())
                .mapToInt(CrptWarnOcchethCardInfoVo::getCrptId).distinct()
                .boxed().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(crptRidList)){
            return;
        }
        //最新的预警信息
        Map<String, TbTjCrptWarn> crptWarnMap = this.warnService.selectLastestWarnByCrptIds(crptRidList);
        //最新的在线申报相关信息
        Map<Integer, CrptWarnUnitbasicInfoVo> unitbasicInfoMap = this.warnService.findLastestUnitBasicInfoMapByCrptIds(crptRidList);
        //最新的在线监测相关信息
        Map<Integer, CrptWarnZxjcUnitInfoVo> zxjcUnitInfoMap = this.warnService.findLastestZxjcBasicInfoMapByCrptIds(crptRidList);
        //最新体检日期
        Map<Integer,Date> bhkDateMap = this.warnService.findLastestBhkDateMapByEntrustCrptIds(crptRidList);
        //最新体检创建日期
        Map<Integer, Date> createDateMap = this.warnService.findZybLastestCreateDateMapByEntrustCrptIds(crptRidList, this.queryDateStr);
        //最新职业卫生技术服务信息报送卡出具技术报告日期
        List<Integer> queryRptCrptIdList = crptInfoList.stream()
                .filter(v -> null != v.getCrptId() && ("1".equals(v.getCodeDesc()) || "3".equals(v.getCodeDesc())))
                .mapToInt(CrptWarnOcchethCardInfoVo::getCrptId).distinct()
                .boxed().collect(Collectors.toList());
        Map<Integer, Date> rptDateMap = this.warnService.findOcchethCardInfoMapByCrptRids(queryRptCrptIdList);
        List<TbTjCrptWarn> crptWarnList = new ArrayList<>();
        //企业未进行申报
        this.fillCrptUnitBasicInfoNotExistWarn(crptRidList, crptWarnMap, unitbasicInfoMap, crptWarnList);
        //企业未按时进行申报年度更新
        this.fillCrptUnitBasicInfoNotRightTimeWarn(crptRidList, crptWarnMap, unitbasicInfoMap, crptWarnList);
        //企业本年度不存在职业健康检查体检档案信息
        this.fillCurYearBhkDataUnExistWarn(crptRidList, crptWarnMap, bhkDateMap, crptWarnList);
        //企业未存在职业卫生技术服务报告信息
        this.fillOccRptUnExistWarn(crptInfoList, crptWarnMap, rptDateMap, crptWarnList);
        //企业未按要求进行职业卫生管理相关人员培训
        this.fillManagerWithoutTrainWarn(crptRidList, crptWarnMap, unitbasicInfoMap, zxjcUnitInfoMap, crptWarnList);
        //企业未按要求开展定期检测
        this.fillUnRegularCheckWarn(crptRidList, crptWarnMap, unitbasicInfoMap, zxjcUnitInfoMap, crptWarnList);
        //企业未按要求开展劳动者职业健康检查
        this.fillUnWorkerHethCheckWarn(crptRidList, crptWarnMap, unitbasicInfoMap, zxjcUnitInfoMap, crptWarnList);
        //企业职业健康检查人数不匹配
        this.fillHethCheckPersonNumUnMatchWarn(crptRidList, crptWarnMap, unitbasicInfoMap, zxjcUnitInfoMap, crptWarnList);
        //企业出现检出的疑似职业病病例
        this.fillCheckoutZybWarn(crptRidList, crptWarnMap, createDateMap, crptWarnList);
        try{
            this.warnService.saveOrUpdateCrptWarn(crptRidList, crptWarnList, this.resultSimpleCode);
        }catch(Exception e){
            log.error("用人单位预警计算工具存储异常", e);
        }
    }

    /**
     * <p>方法描述：企业出现检出的疑似职业病病例 </p>
     * pw 2023/9/28
     **/
    private void fillCheckoutZybWarn(List<Integer> crptRidList,Map<String, TbTjCrptWarn> crptWarnMap,
                                     Map<Integer, Date> createDateMap, List<TbTjCrptWarn> crptWarnList){
        String warnExt1 = "8";
        for(Integer crptRid : crptRidList){
            Date createDate = createDateMap.get(crptRid);
            TbTjCrptWarn crptWarn = crptWarnMap.get(crptRid+"@"+warnExt1);
            Integer state = null == crptWarn ? null : crptWarn.getState();
            Date dealDate = null == crptWarn ? null : crptWarn.getDealDate();
            //无预警记录 并且 无疑似职业病病例
            //未处置的预警记录
            //有已处置的预警记录 并且 最新疑似职业病体检创建日期不大于预警处理时间   不需要处理
            boolean ifContinue = (null == state && null == createDate) || (null != state && 0 == state)
                    || (null != state && 1 == state && (null == createDate || (null != dealDate && null != createDate && !this.compareDateAfter(createDate,dealDate))));
            if(ifContinue){
                continue;
            }
            crptWarn = new TbTjCrptWarn();
            crptWarn.setFkByCrptId(new TbTjCrpt(crptRid));
            crptWarn.setFkByWarnId(this.warnMap.get(warnExt1));
            crptWarnList.add(crptWarn);
        }
    }

    /**
     * <p>方法描述：企业职业健康检查人数不匹配 </p>
     * pw 2023/9/28
     **/
    private void fillHethCheckPersonNumUnMatchWarn(List<Integer> crptRidList,Map<String, TbTjCrptWarn> crptWarnMap,
                                                   Map<Integer, CrptWarnUnitbasicInfoVo> unitbasicInfoMap,
                                                   Map<Integer, CrptWarnZxjcUnitInfoVo> zxjcUnitInfoMap,
                                                   List<TbTjCrptWarn> crptWarnList){
        String warnExt1 = "6";
        for(Integer crptRid : crptRidList){
            CrptWarnUnitbasicInfoVo basicInfo = unitbasicInfoMap.get(crptRid);
            CrptWarnZxjcUnitInfoVo zxjcInfo = zxjcUnitInfoMap.get(crptRid);
            if(null == basicInfo && null == zxjcInfo){
                continue;
            }
            boolean ifBasic = false;
            if(null != basicInfo){
                Integer dustNum = basicInfo.getDustNum();
                Integer chemistryNum = basicInfo.getChemistryNum();
                Integer physicsNum = basicInfo.getPhysicsNum();
                Integer radioNum = basicInfo.getRadioNum();
                Integer victimsNum = basicInfo.getVictimsNum();
                ifBasic = null != victimsNum && (null != dustNum || null != chemistryNum || null != physicsNum || null != radioNum);
                if(ifBasic){
                    Integer total = (null == dustNum ? 0 : dustNum)
                            +(null == chemistryNum ? 0 : chemistryNum)
                            +(null == physicsNum ? 0 : physicsNum)
                            +(null == radioNum ? 0 : radioNum);
                    //体检人数之和 小于接害总人数*80%
                    ifBasic = total*100 < victimsNum*80;
                }
            }
            boolean ifZxjc = null != zxjcInfo && null != zxjcInfo.getCheckTotalPeoples()
                    && null != zxjcInfo.getContactTotalPeoples() &&
                    (zxjcInfo.getCheckTotalPeoples()*100 < zxjcInfo.getContactTotalPeoples()*80);
            TbTjCrptWarn crptWarn = crptWarnMap.get(crptRid+"@"+warnExt1);
            Integer state = null == crptWarn ? null : crptWarn.getState();
            Date dealDate = null == crptWarn ? null : crptWarn.getDealDate();
            //无预警信息 并且 无申报记录或体检人数之和大于等于接害总人数的80% 并且 无监测记录或体检总人数大于等于接触职业病危害因素总人数的80%
            //有未处置的预警信息 并且  有申报记录并且体检人数之和小于接害总人数的80% 或者 有监测记录并且体检总人数小于接触职业病危害因素总人数的80%
            //有已处置的预警信息 并且
            //                   无申报记录 或 体检人数之和大于等于接害总人数的80% 或 申报创建日期小于等于最新预警处置时间
            //                并且
            //                   无监测记录 或 体检总人数大于等于接触职业病危害因素总人数的80% 或 监测创建日期小于等于最新预警处置时间 不需要处理
            boolean ifContinue = (null == state && !ifBasic && !ifZxjc) || (null != state && 0 == state && (ifBasic || ifZxjc))
                    || (null != state && 1 == state && null != dealDate &&
                    (!(ifBasic && this.compareDateAfter(basicInfo.getCreateDate(),dealDate)) &&
                            !(ifZxjc && this.compareDateAfter(zxjcInfo.getCreateDate(),dealDate))));
            if(ifContinue){
                continue;
            }
            if(null == state || 1 == state){
                crptWarn = new TbTjCrptWarn();
                crptWarn.setFkByCrptId(new TbTjCrpt(crptRid));
                crptWarn.setFkByWarnId(this.warnMap.get(warnExt1));
            }
            crptWarnList.add(crptWarn);
        }
    }

    /**
     * <p>方法描述：企业未按要求开展劳动者职业健康检查 </p>
     * pw 2023/9/28
     **/
    private void fillUnWorkerHethCheckWarn(List<Integer> crptRidList,Map<String, TbTjCrptWarn> crptWarnMap,
                                           Map<Integer, CrptWarnUnitbasicInfoVo> unitbasicInfoMap,
                                           Map<Integer, CrptWarnZxjcUnitInfoVo> zxjcUnitInfoMap,
                                           List<TbTjCrptWarn> crptWarnList){
        String warnExt1 = "5";
        for(Integer crptRid : crptRidList){
            CrptWarnUnitbasicInfoVo basicInfo = unitbasicInfoMap.get(crptRid);
            CrptWarnZxjcUnitInfoVo zxjcInfo = zxjcUnitInfoMap.get(crptRid);
            if(null == basicInfo && null == zxjcInfo){
                continue;
            }
            boolean ifBasic = null != basicInfo && null != basicInfo.getIfhea() && 0 == basicInfo.getIfhea();
            boolean ifZxjc = null != zxjcInfo && null != zxjcInfo.getIfhea() && 0 == zxjcInfo.getIfhea();
            TbTjCrptWarn crptWarn = crptWarnMap.get(crptRid+"@"+warnExt1);
            Integer state = null == crptWarn ? null : crptWarn.getState();
            Date dealDate = null == crptWarn ? null : crptWarn.getDealDate();
            //无预警记录 并且 无申报记录或已体检 并且 无监测记录或已体检
            //有未处置的预警记录 并且     有申报记录并且无体检 或者 有监测记录并且无体检
            //有已处置的预警记录 并且
            //                   无申报记录 或 有体检 或 申报创建日期小于等于最新预警处置时间
            //                并且
            //                   无监测记录 或 有体检 或 监测创建日期小于等于最新预警处置时间     不需要处理
            boolean ifContinue = (null == state && !ifBasic && !ifZxjc) || (null != state && 0 == state && (ifBasic || ifZxjc))
                    || (null != state && 1 == state && null != dealDate &&
                    (!(ifBasic && this.compareDateAfter(basicInfo.getCreateDate(), dealDate)) &&
                            !(ifZxjc && this.compareDateAfter(zxjcInfo.getCreateDate(), dealDate))));
            if(ifContinue){
                continue;
            }
            if(null == state || 1 == state){
                crptWarn = new TbTjCrptWarn();
                crptWarn.setFkByCrptId(new TbTjCrpt(crptRid));
                crptWarn.setFkByWarnId(this.warnMap.get(warnExt1));
            }
            crptWarnList.add(crptWarn);
        }
    }
    /**
     * <p>方法描述：企业未按要求开展定期检测 </p>
     * pw 2023/9/28
     **/
    private void fillUnRegularCheckWarn(List<Integer> crptRidList,Map<String, TbTjCrptWarn> crptWarnMap,
                                        Map<Integer, CrptWarnUnitbasicInfoVo> unitbasicInfoMap,
                                        Map<Integer, CrptWarnZxjcUnitInfoVo> zxjcUnitInfoMap,
                                        List<TbTjCrptWarn> crptWarnList){
        String warnExt1 = "4";
        for(Integer crptRid : crptRidList){
            CrptWarnUnitbasicInfoVo basicInfo = unitbasicInfoMap.get(crptRid);
            CrptWarnZxjcUnitInfoVo zxjcInfo = zxjcUnitInfoMap.get(crptRid);
            if(null == basicInfo && null == zxjcInfo){
                continue;
            }
            boolean ifBasic = null != basicInfo && null != basicInfo.getIfat() && 0 == basicInfo.getIfat();
            boolean ifZxjc = null != zxjcInfo && null != zxjcInfo.getIfat() && 0 == zxjcInfo.getIfat();
            TbTjCrptWarn crptWarn = crptWarnMap.get(crptRid+"@"+warnExt1);
            Integer state = null == crptWarn ? null : crptWarn.getState();
            Date dealDate = null == crptWarn ? null : crptWarn.getDealDate();
            //无预警记录 并且 无申报记录或已检测 并且 无监测记录或已检测
            //有未处置的预警记录 并且     有申报记录并且无检测 或者 有监测记录并且无检测
            //有已处置的预警记录 并且
            //                   无申报记录 或 有检测 或 申报创建日期小于等于最新预警处置时间
            //                并且
            //                   无监测记录 或 有检测 或 监测创建日期小于等于最新预警处置时间     不需要处理
            boolean ifContinue = (null == state && !ifBasic && !ifZxjc) || (null != state && 0 == state && (ifBasic || ifZxjc))
                    || (null != state && 1 == state && null != dealDate &&
                    (!(ifBasic && this.compareDateAfter(basicInfo.getCreateDate(), dealDate)) &&
                            !(ifZxjc && this.compareDateAfter(zxjcInfo.getCreateDate(), dealDate))));
            if(ifContinue){
                continue;
            }
            if(null == state || 1 == state){
                crptWarn = new TbTjCrptWarn();
                crptWarn.setFkByCrptId(new TbTjCrpt(crptRid));
                crptWarn.setFkByWarnId(this.warnMap.get(warnExt1));
            }
            crptWarnList.add(crptWarn);
        }
    }

    /**
     * <p>方法描述：企业未按要求进行职业卫生管理相关人员培训 </p>
     * pw 2023/9/28
     **/
    private void fillManagerWithoutTrainWarn(List<Integer> crptRidList,Map<String, TbTjCrptWarn> crptWarnMap,
                                             Map<Integer, CrptWarnUnitbasicInfoVo> unitbasicInfoMap,
                                             Map<Integer, CrptWarnZxjcUnitInfoVo> zxjcUnitInfoMap,
                                             List<TbTjCrptWarn> crptWarnList){
        String warnExt1 = "3";
        for(Integer crptRid : crptRidList){
            CrptWarnUnitbasicInfoVo basicInfo = unitbasicInfoMap.get(crptRid);
            CrptWarnZxjcUnitInfoVo zxjcInfo = zxjcUnitInfoMap.get(crptRid);
            if(null == basicInfo && null == zxjcInfo){
                continue;
            }
            boolean ifBasic = null != basicInfo && ((null != basicInfo.getIfLeadersTrain() && 0 == basicInfo.getIfLeadersTrain()) ||
                    (null != basicInfo.getIfManagersTrain() && 0 == basicInfo.getIfManagersTrain()));
            boolean ifZxjc = null != zxjcInfo && ((null != zxjcInfo.getIfLeadersTrain() && 0 == zxjcInfo.getIfLeadersTrain()) ||
                    (null != zxjcInfo.getIfManagersTrain() && 0 == zxjcInfo.getIfManagersTrain()));
            TbTjCrptWarn crptWarn = crptWarnMap.get(crptRid+"@"+warnExt1);
            Integer state = null == crptWarn ? null : crptWarn.getState();
            Date dealDate = null == crptWarn ? null : crptWarn.getDealDate();
            //无预警记录 并且 无申报记录或负责人与管理人员都有培训 并且 无监测记录或负责人与管理人员都有培训
            //有未处置的预警记录 并且     申报记录负责人或管理人员无培训 或者 监测记录负责人或管理人员无培训
            //有已处置的预警记录 并且
            //                   无申报记录 或 申报记录负责人与管理人员有培训 或 申报创建日期小于等于最新预警处置时间
            //                并且
            //                   无监测记录 或 监测记录负责人与管理人员有培训 或 监测创建日期小于等于最新预警处置时间   不需要处理
            boolean ifContinue = (null == state && !ifBasic && !ifZxjc) || (null != state && 0 == state && (ifBasic || ifZxjc))
                    || (null != state && 1 == state && null != dealDate &&
                          (!(ifBasic && this.compareDateAfter(basicInfo.getCreateDate(), dealDate)) &&
                                  !(ifZxjc && this.compareDateAfter(zxjcInfo.getCreateDate(), dealDate))));
            if(ifContinue){
                continue;
            }
            if(null == state || 1 == state){
                crptWarn = new TbTjCrptWarn();
                crptWarn.setFkByCrptId(new TbTjCrpt(crptRid));
                crptWarn.setFkByWarnId(this.warnMap.get(warnExt1));
            }
            crptWarnList.add(crptWarn);
        }
    }

    private boolean compareDateAfter(Date pre,Date after){
        if(null == pre || null == after){
            return false;
        }
        return pre.after(after);
    }

    /**
     * <p>方法描述：企业未存在职业卫生技术服务报告信息 </p>
     * pw 2023/9/28
     **/
    private void fillOccRptUnExistWarn(List<CrptWarnOcchethCardInfoVo> crptInfoList,Map<String, TbTjCrptWarn> crptWarnMap,
                                       Map<Integer, Date> rptDateMap, List<TbTjCrptWarn> crptWarnList){
        String warnExt1 = "9";
        Date today = new Date();
        for(CrptWarnOcchethCardInfoVo infoVo : crptInfoList){
            String codeDesc = infoVo.getCodeDesc();
            //企业职业风险分类非一般和严重时 不需要预警
            if(!"1".equals(codeDesc) && !"3".equals(codeDesc)){
                continue;
            }
            Integer crptRid = infoVo.getCrptId();
            TbTjCrptWarn crptWarn = crptWarnMap.get(crptRid+"@"+warnExt1);
            Integer state = null == crptWarn ? null : crptWarn.getState();
            Date rptDate = rptDateMap.get(crptRid);
            if(null != rptDate){
                rptDate = DateUtils.addMonths(rptDate, "1".equals(codeDesc) ? 13 : 37);
            }
            //没有预警记录 并且 企业风险分类严重时存在出具技术报告日期+13个月大于等于当天或者企业风险分类一般时存在出具技术报告日期+37个月大于等于当天
            //有状态未处置的预警记录 并且 企业风险分类严重时不存在出具技术报告日期+13个月大于等于当天或者企业风险分类一般时不存在出具技术报告日期+37个月大于等于当天
            //有状态已处置的预警记录 并且 企业风险分类严重时存在出具技术报告日期+13个月大于等于当天或者企业风险分类一般时存在出具技术报告日期+37个月大于等于当天 不需要处理
            boolean ifContinue = (null == state && null != rptDate && !this.compareOnlyDateBeforeToday(rptDate, today))
                    || (null != state && 0 == state && (null == rptDate || this.compareOnlyDateBeforeToday(rptDate, today)))
                    || (null != state && 1 == state && null != rptDate && !this.compareOnlyDateBeforeToday(rptDate, today));
            if(ifContinue){
                continue;
            }
            if((null == rptDate || this.compareOnlyDateBeforeToday(rptDate, today)) && (null == state || 1 == state)){
                crptWarn = new TbTjCrptWarn();
                crptWarn.setFkByCrptId(new TbTjCrpt(crptRid));
                crptWarn.setFkByWarnId(this.warnMap.get(warnExt1));
            }
            crptWarnList.add(crptWarn);
        }
    }

    /**
     * <p>方法描述：企业本年度不存在职业健康检查体检档案信息 </p>
     * pw 2023/9/28
     **/
    private void fillCurYearBhkDataUnExistWarn(List<Integer> crptRidList,Map<String, TbTjCrptWarn> crptWarnMap,
                                               Map<Integer,Date> bhkDateMap, List<TbTjCrptWarn> crptWarnList){
        String warnExt1 = "7";
        Date today = new Date();
        for(Integer crptRid : crptRidList){
            TbTjCrptWarn crptWarn = crptWarnMap.get(crptRid+"@"+warnExt1);
            Integer state = null == crptWarn ? null : crptWarn.getState();
            Date bhkDate = bhkDateMap.get(crptRid);
            if(null != bhkDate){
                bhkDate = DateUtils.addMonths(bhkDate, 13);
            }
            //没有预警记录 并且 存在最新体检日期+13个月大于等于当天记录
            //预警状态未处置 并且 不存在体检日期+13个月大于等于当天记录
            //预警状态已处置 并且 存在体检日期+13个月大于等于当天记录 不需要处理
            boolean ifContinue = (null == state && null != bhkDate && !this.compareOnlyDateBeforeToday(bhkDate, today))
                    || (null != state && 0 == state && (null == bhkDate || this.compareOnlyDateBeforeToday(bhkDate, today)))
                    || (null != state && 1 == state && null != bhkDate && !this.compareOnlyDateBeforeToday(bhkDate, today));
            if(ifContinue){
                continue;
            }
            if((null == bhkDate || this.compareOnlyDateBeforeToday(bhkDate, today)) && (null == state || 1 == state)){
                crptWarn = new TbTjCrptWarn();
                crptWarn.setFkByCrptId(new TbTjCrpt(crptRid));
                crptWarn.setFkByWarnId(this.warnMap.get(warnExt1));
            }
            crptWarnList.add(crptWarn);
        }
    }

    /**
     * <p>方法描述：企业未按时进行申报年度更新预警 </p>
     * pw 2023/9/28
     **/
    private void fillCrptUnitBasicInfoNotRightTimeWarn(List<Integer> crptRidList,Map<String, TbTjCrptWarn> crptWarnMap,
                                                       Map<Integer, CrptWarnUnitbasicInfoVo> unitbasicInfoMap,
                                                       List<TbTjCrptWarn> crptWarnList){
        String warnExt1 = "2";
        Date today = new Date();
        for(Integer crptRid : crptRidList){
            TbTjCrptWarn crptWarn = crptWarnMap.get(crptRid+"@"+warnExt1);
            Integer state = null == crptWarn ? null : crptWarn.getState();
            CrptWarnUnitbasicInfoVo basicInfo = unitbasicInfoMap.get(crptRid);
            Date declareDate = null == basicInfo ? null : DateUtils.addMonths(basicInfo.getDeclareDate(), 13);
            //没有申报数据
            //或者 没有预警信息并且最新申报日期+13个月不小于当天
            //或者 有未处置的预警信息并且最新申报日期+13个月小于当天
            //或者 有已处置的预警信息并且最新申报日期+13个月不小于当天  不需要处理
            boolean ifContinue = null == declareDate || (null == state && !this.compareOnlyDateBeforeToday(declareDate, today))
                    || (null != state && 0 == state && this.compareOnlyDateBeforeToday(declareDate, today))
                    || (null != state && 1 == state && !this.compareOnlyDateBeforeToday(declareDate, today));
            if(ifContinue){
                continue;
            }
            if(this.compareOnlyDateBeforeToday(declareDate, today) && (null == state || 1 == state)){
                crptWarn = new TbTjCrptWarn();
                crptWarn.setFkByCrptId(new TbTjCrpt(crptRid));
                crptWarn.setFkByWarnId(this.warnMap.get(warnExt1));
            }
            crptWarnList.add(crptWarn);
        }
    }

    /**
     * <p>方法描述：企业未进行申报预警 </p>
     * pw 2023/9/28
     **/
    private void fillCrptUnitBasicInfoNotExistWarn(List<Integer> crptRidList,Map<String, TbTjCrptWarn> crptWarnMap,
                                                   Map<Integer, CrptWarnUnitbasicInfoVo> unitbasicInfoMap,
                                                   List<TbTjCrptWarn> crptWarnList){
        String warnExt1 = "1";
        for(Integer crptRid : crptRidList){
            TbTjCrptWarn crptWarn = crptWarnMap.get(crptRid+"@"+warnExt1);
            Integer state = null == crptWarn ? null : crptWarn.getState();
            CrptWarnUnitbasicInfoVo basicInfo = unitbasicInfoMap.get(crptRid);
            //有申报数据预警信息不存在 或者 无申报数据并且存在状态为未处置的预警信息 或者 有申报数据预警信息状态已处置 不需要处理
            boolean ifContinue = (null == basicInfo && null != state && 0 == state) ||
                    (null != basicInfo && (null == state || 1 == state));
            if(ifContinue){
                continue;
            }
            if(null == basicInfo && (null == state || 1 == state)){
                crptWarn = new TbTjCrptWarn();
                crptWarn.setFkByCrptId(new TbTjCrpt(crptRid));
                crptWarn.setFkByWarnId(this.warnMap.get(warnExt1));
            }
            crptWarnList.add(crptWarn);
        }
    }

    /**
     * <p>方法描述：前边的时间是否在后边的时间前 仅比较年月日 </p>
     * pw 2023/9/28
     **/
    private boolean compareOnlyDateBeforeToday(Date date, Date after){
        return DateUtils.getDateOnly(date).before(DateUtils.getDateOnly(after));
    }

    /**
     * <p>方法描述：参数自检 </p>
     * pw 2023/9/27
     **/
    private boolean ymlParamCheck(){
        StringJoiner joiner = new StringJoiner("；");
        if(StringUtils.isBlank(this.queryZoneGb)){
            joiner.add("参数heth-timer.crpt-warn.zone-code不允许为空");
        }
        if(null == this.warnDays){
            joiner.add("参数heth-timer.crpt-warn.days不允许为空");
        }
        if(StringUtils.isNotBlank(this.queryDateStr)){
            try{
                Date queryDate = DateUtils.parseDate(this.queryDateStr, "yyyy-MM-dd");
                this.queryDateStr = DateUtils.formatDate(queryDate);
            }catch(Exception e){
                joiner.add("参数heth-timer.crpt-warn.query-date配置有误");
            }
        }
        if(StringUtils.isNotBlank(this.queryZoneGb)){
            String[] zoneGbArr = this.queryZoneGb.split(",");
            boolean flag = false;
            for(String zoneGb : zoneGbArr){
                if(StringUtils.isBlank(zoneGb)){
                    continue;
                }
                int length = zoneGb.trim().length();
                if(10 != length && 12 != length){
                    flag = true;
                }
            }
            if(flag){
                joiner.add("参数heth-timer.crpt-warn.zone-code配置有误");
            }
        }
        String errMsg = joiner.toString();
        if(StringUtils.isNotBlank(errMsg)){
            log.error("用人单位预警计算工具配置参数自检失败，错误信息：{}，请检查chiscdc-crpt-warning.yml配置文件！", errMsg);
            return false;
        }
        return true;
    }

    /**
     * <p>方法描述：初始化码表 </p>
     * pw 2023/9/28
     **/
    private void initCache(){
        if(null != this.warnMap){
            return;
        }
        this.warnMap = new HashMap<>();
        List<TsSimpleCode> simpleCodeList = this.codeService.findTsSimpleCodeListOrderby("5602");
        if(!CollectionUtils.isEmpty(simpleCodeList)){
            for(TsSimpleCode simpleCode : simpleCodeList){
                String ext1 = simpleCode.getExtends1();
                if(StringUtils.isBlank(ext1)){
                    continue;
                }
                this.warnMap.put(ext1, simpleCode);
            }
        }
        simpleCodeList = this.codeService.findTsSimpleCodeListOrderby("5603");
        if(!CollectionUtils.isEmpty(simpleCodeList)){
            Optional<TsSimpleCode> optional = simpleCodeList.stream().filter(v -> "4".equals(v.getExtends1())).findFirst();
            this.resultSimpleCode = optional.isPresent() ? optional.get() : null;
        }
    }

    /**
     * <p>方法描述：校验码表信息 </p>
     * pw 2023/9/28
     **/
    private boolean validateSimpleCode(){
        this.initCache();
        StringJoiner joiner = new StringJoiner("；");
        Set<String> warnKeySet = new HashSet<>();
        if(!CollectionUtils.isEmpty(this.warnMap)){
            warnKeySet = this.warnMap.keySet();
        }
        if(!warnKeySet.contains("1")){
            joiner.add("企业预警信息码表5602，缺少扩展字段1为1的启用的码表");
        }
        if(!warnKeySet.contains("2")){
            joiner.add("企业预警信息码表5602，缺少扩展字段1为2的启用的码表");
        }
        if(!warnKeySet.contains("3")){
            joiner.add("企业预警信息码表5602，缺少扩展字段1为3的启用的码表");
        }
        if(!warnKeySet.contains("4")){
            joiner.add("企业预警信息码表5602，缺少扩展字段1为4的启用的码表");
        }
        if(!warnKeySet.contains("5")){
            joiner.add("企业预警信息码表5602，缺少扩展字段1为5的启用的码表");
        }
        if(!warnKeySet.contains("6")){
            joiner.add("企业预警信息码表5602，缺少扩展字段1为6的启用的码表");
        }
        if(!warnKeySet.contains("7")){
            joiner.add("企业预警信息码表5602，缺少扩展字段1为7的启用的码表");
        }
        if(!warnKeySet.contains("8")){
            joiner.add("企业预警信息码表5602，缺少扩展字段1为8的启用的码表");
        }
        if(!warnKeySet.contains("9")){
            joiner.add("企业预警信息码表5602，缺少扩展字段1为9的启用的码表");
        }
        if(null == this.resultSimpleCode){
            joiner.add("预警处置结果码表5603，缺少扩展字段1为4的启用的码表");
        }
        String errMsg = joiner.toString();
        if(StringUtils.isNotBlank(errMsg)){
            log.error("用人单位预警计算工具码表维护异常，异常信息：{}，请停止程序维护正确后再启动程序！", errMsg);
            return false;
        }
        return true;
    }
}

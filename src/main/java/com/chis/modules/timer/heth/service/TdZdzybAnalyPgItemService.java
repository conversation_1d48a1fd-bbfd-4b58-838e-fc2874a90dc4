package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZdzybAnalyItmType;
import com.chis.modules.timer.heth.entity.TdZdzybAnalyPgItem;
import com.chis.modules.timer.heth.mapper.TdZdzybAnalyPgItemMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-30,TdZdzybAnalyPgItemService
 */
@Service
public class TdZdzybAnalyPgItemService extends ZwxBaseServiceImpl<TdZdzybAnalyPgItemMapper, TdZdzybAnalyPgItem> {
    public List<TdZdzybAnalyPgItem> findPgItemsByMainId(Integer mainId){
        TdZdzybAnalyPgItem pgItem = new TdZdzybAnalyPgItem();
        pgItem.setFkByMainId(new TdZdzybAnalyItmType(mainId));
        return this.baseMapper.selectListByEntity(pgItem);
    }
}

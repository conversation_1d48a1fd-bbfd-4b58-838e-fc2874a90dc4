package com.chis.modules.timer.heth.logic;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 异步导出 个案审核 从导出条件解析出的参数对象
 *
 * @ClassAuthor pw,2021年12月23日,BhkCalculationAuditQueryPO
 */
@Data
public class BhkCalculationAuditQueryPO implements Serializable {
    private static final long serialVersionUID = -4953380157785351278L;
    private String zonecode;//searchZoneCode
    private String crptName;//searchCrptName
    private String creditCode;//searchCreditCode
    private String personIdc;//searchIdc
    private String personName;//searchPersonName
    private String searchBhkBdate;//searchBhkBdate
    private String searchBhkEdate;//searchBhkEdate
    private String selectOnGuardIds;//selectOnGuardIds ,
    private String selectBadRsnIds;//selectBadRsnIds ,
    private String searchRcvBdate;//searchRcvBdate
    private String searchRcvEdate;//searchRcvEdate
    private String jcTypes;//jcTypes ,
    private String ifRhks;//ifRhks , 长度为2直接忽略
    private String ifAbnormals;//ifAbnormals , 长度2直接忽略
    private String searchUnitId;//searchUnitId ,
    private String states;//states stateList ,
    private String checkLevel;//checkLevel
    private Integer zoneType;//zoneType
    private String exportItems;//exportItems ,
    private String bhkTypes;//体检类型
    private String startRptPrintDate;//报告出具日期
    private String endRptPrintDate;//报告出具日期
    private String startCreateDate;//报告日期
    private String endCreateDate;//报告日期

    //用工单位地区
    private String searchZoneCodeEmp;
    //用工单位名称
    private String searchCrptNameEmp;
    //用工单位社会信用代码
    private String searchCreditCodeEmp;
    /** 是否需要脱敏 true是*/
    private Boolean ifNeedEnctryInfo;
    /** 异常情况*/
    private String searchAbnormals;
    /**选择的单危害因素结论*/
    private String searchSelBhkrstIds;

    /**
     * 质控编号
     */
    private String zkBhkCode;
}

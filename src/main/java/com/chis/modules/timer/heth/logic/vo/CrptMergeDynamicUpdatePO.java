package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>方法描述：修订企业信息 动态表 </p>
 * pw 2023/8/10
 **/
@Data
public class CrptMergeDynamicUpdatePO implements Serializable {
    private static final long serialVersionUID = 8235754666011889885L;
    /** 表名 */
    private String tableName;

    private List<CrptMergeDynamicColumnPO> columnPOList;
    /** 条件集合 */
    private List<CrptMergeDynamicColumnPO> paramList;
    public CrptMergeDynamicUpdatePO(String tableName){
        this.tableName = tableName;
    }
}

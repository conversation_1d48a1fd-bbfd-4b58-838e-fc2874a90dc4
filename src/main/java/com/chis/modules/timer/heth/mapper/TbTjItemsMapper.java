package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TbTjItems;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TbTjItemsMapper
 */
@Repository
public interface TbTjItemsMapper extends ZwxBaseMapper<TbTjItems> {

    /**
     * <p>描述：根据体检状态查询体检项目</p>
     * @param state 1为启用 0为停用
     *
     * @return {@link List< TbTjItems>}
     * @Author: 龚哲,2021/12/23 17:27,findTjItemListByState
     */
    public List<TbTjItems> findTjItemListByState(@Param("state") Short state);
}

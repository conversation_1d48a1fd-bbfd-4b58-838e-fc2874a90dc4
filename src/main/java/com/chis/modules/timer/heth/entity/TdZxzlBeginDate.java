package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2023-09-20,TdZxzlBeginDate
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZXZL_BEGIN_DATE")
public class TdZxzlBeginDate extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("UUID")
    private String uuid;

    @TableField("BEGIN_DATE")
    private Date beginDate;


    public TdZxzlBeginDate(Integer rid) {
        super(rid);
    }


}

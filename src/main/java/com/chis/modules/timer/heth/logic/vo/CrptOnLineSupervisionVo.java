package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
/**
 * <p>类描述：企业在线申报 职业健康监护开展情况</p>
 * @ClassAuthor： pw 2022/9/7
 **/

@Data
public class CrptOnLineSupervisionVo implements Serializable {
    private static final long serialVersionUID = 354620336601332789L;
    private Boolean existsSupervision;
    private Boolean existsDust;
    private Integer dustNum;
    private Boolean existsChemical;
    private Integer chemicalNum;
    private Boolean existsPhysical;
    private Integer physicalNum;
    private Boolean existsRadioactivity;
    private Integer radioactivityNum;
}

package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZyUnitbasicRcd;
import com.chis.modules.timer.heth.entity.TdZyUnitbasicRcdSub;
import com.chis.modules.timer.heth.mapper.TdZyUnitbasicRcdMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdZyUnitbasicRcdService
 */
@Service
public class TdZyUnitbasicRcdService extends ZwxBaseServiceImpl<TdZyUnitbasicRcdMapper, TdZyUnitbasicRcd> {
    @Autowired
    private TdZyUnitbasicRcdSubService subService;
    /**
     * <p>方法描述：存储下载记录 </p>
     * @MethodAuthor： pw 2022/9/7
     **/
    public void saveTdZyUnitbasicRcd(TdZyUnitbasicRcd rcd, List<TdZyUnitbasicRcdSub> rcdSubList){
        //实体如果无KeySequence 会因rid为null报错
        this.save(rcd);
        if(!CollectionUtils.isEmpty(rcdSubList)){
            this.subService.saveBatch(rcdSubList);
        }
    }

}

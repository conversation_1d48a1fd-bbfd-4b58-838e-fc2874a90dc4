package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.sql.Clob;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnAdvCsion
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_BSN_ADV_CSION")
@KeySequence(value = "TD_ZWYJ_BSN_ADV_CSION_SEQ",clazz = Integer.class)
public class TdZwyjBsnAdvCsion extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "BHK_ID" , el = "fkByBhkId.rid")
    private TdZwyjBsnBhk fkByBhkId;

    @TableField(value = "ADV_ID" , el = "fkByAdvId.rid")
    private TdZwyjBsnCsionItems fkByAdvId;

    @TableField("ITEM_RST")
    private String itemRst;

    @TableField("MSRUNT")
    private String msrunt;

    @TableField("ITEM_STDVALUE")
    private String itemStdvalue;

    @TableField("JDGPTN")
    private String jdgptn;

    @TableField("MAXVAL")
    private String maxval;


    public TdZwyjBsnAdvCsion(Integer rid) {
        super(rid);
    }


}

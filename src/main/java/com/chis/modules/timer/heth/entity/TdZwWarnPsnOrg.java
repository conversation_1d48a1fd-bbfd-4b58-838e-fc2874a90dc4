package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsUnit;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * <AUTHOR> 2024-07-12,TdZwWarnPsnOrg
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_WARN_PSN_ORG")
@KeySequence(value = "TD_ZW_WARN_PSN_ORG_SEQ", clazz = Integer.class)
public class TdZwWarnPsnOrg extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID", el = "fkByMainId.rid")
    private TdZwOrgWarnPsns fkByMainId;

    @TableField(value = "ORG_ID", el = "fkByOrgId.rid")
    private TsUnit fkByOrgId;


    public TdZwWarnPsnOrg(Integer rid) {
        super(rid);
    }


}

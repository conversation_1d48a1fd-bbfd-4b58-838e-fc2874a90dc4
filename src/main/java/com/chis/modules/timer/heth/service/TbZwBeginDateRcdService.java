package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TbZwBeginDateRcd;
import com.chis.modules.timer.heth.mapper.TbZwBeginDateRcdMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人, 2023-11-10,TbZwBeginDateRcdService
 */
@Service
public class TbZwBeginDateRcdService extends ZwxBaseServiceImpl<TbZwBeginDateRcdMapper, TbZwBeginDateRcd> {

    public List<TbZwBeginDateRcd> selectListByWarnTypeAndBusType(Integer warnType, Integer busType) {
        return this.baseMapper.selectListByWarnTypeAndBusType(warnType, busType);
    }
}

package com.chis.modules.timer.heth.job;

import com.chis.comm.utils.ObjectCopyUtil;
import com.chis.comm.utils.ObjectUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.logic.BsnClusionPO;
import com.chis.modules.timer.heth.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>类描述：重点危害因素结论判定</p>
 * @ClassAuthor qrr,2020-10-29,BadRsnToRstJob
 */
@Slf4j
@Component
public class BadRsnToRstJob {
    @Autowired
    private TdTjBhkService bhkService;
    @Autowired
    private TdZwyjBsnClusionService clusionService;
    @Autowired
    private TdZwyjBsnCsionNoSubService noSubService;
    @Autowired
    private TdTjBadrsnsService badrsnsService;
    @Autowired
    private TdTjResultJudgeItemService judgeItemService;
    @Autowired
    private TdTjBhksubService bhksubService;
    @Autowired
    private TdZwyjBsnBhkService bsnBhkService;
    @Autowired
    private TdZwyjBsnAdvCsionService advCsionService;
    @Autowired
    private TdZwyjBsnCtrlCsionService ctrlCsionService;
    @Autowired
    private TdZwyjBsnRcdService bsnRcdService;
    @Value("${heth-timer.dataSize}")
    private Integer dataSize;
    @Value("${heth-timer.start-date}")
    private String startDate;


    @Scheduled(cron = "${heth-timer.sche-cron.badrsn-rst}")
    public void start() {
        for (;;){
            try{
                List<TdTjBhk> selectBhks = bhkService.selectBhks(dataSize, startDate);
                if(CollectionUtils.isEmpty(selectBhks)){
                    return;
                }
                //初始化匹配规则
                Map<String,List<BsnClusionPO>> clusionMap = clusionService.findTdZwyjBsnClusionRsn();
                Map<Integer, List<TdZwyjBsnCsionNoSub>> noRstMap = noSubService.findCsionNoRstMap();
                for (TdTjBhk t:selectBhks) {
                    long start = System.currentTimeMillis();
                    try{
                        StringBuffer errorSb = new StringBuffer();
                        //查询体检子表、接触危害因素+结论
                        if(null==t.getFkByOnguardStateid()){
                            //存日志
                            errorSb.append("在岗状态为空！");
                            continue;
                        }
                        //体检结果子表
                        List<TdTjBhksub> bhksubList = bhksubService.findTdTjBhksubByBhkId(t.getRid());
                        //key:项目Id,value:TdTjBhksub
                        Map<Integer,TdTjBhksub> bhkItemMap = getBhkItemMap(bhksubList);
                        //接触危害因素
                        List<TdTjBadrsns> badrsnList = badrsnsService.findTdTjBadrsnsByBhkId(t.getRid());

                        //重点职业病判定结果
                        String ifIntoZdzybAnaly = t.getIfIntoZdzybAnaly();
                        String checkState = t.getCheckState();
                        String ifRhk = t.getIfRhk();
                        List<TdTjResultJudgeItem> judgeItemList = null;
                        if("1".equals(ifRhk)){//复检
                            String frstIfIntoZdzybAnaly = t.getFrstIfIntoZdzybAnaly();
                            String frstCheckState = t.getFrstCheckState();
                            if("1".equals(frstIfIntoZdzybAnaly) && "8".equals(frstCheckState)){//初检纳入重点职业病且审核通过
                                judgeItemList = judgeItemService.findRhkZdzybJudgeItem(t.getLastFstBhkCode(),t.getFkByBhkorgId().getRid());
                            }
                        }else {
                            if("1".equals(ifIntoZdzybAnaly) && "8".equals(checkState)){//纳入且审核通过
                                judgeItemList = judgeItemService.findZdzybJudgeItem(t.getRid());
                            }
                        }
                        //key:评估结果Id,value:TdZdzybAnalyItmType
                        Map<Integer,TdTjResultJudgeItem> judgeItemMap = getZdzybJudgeItemMap(judgeItemList);

                        List<TdZwyjBsnCtrlCsion> ctrlCsionList = new ArrayList<>();
                        List<TdZwyjBsnAdvCsion> advCsionList = new ArrayList<>();
                        List<TdZwyjBsnBadrsns> bsnBadrsnsList = new ArrayList<>();
                        if(!CollectionUtils.isEmpty(badrsnList)){
                            //异常结论
                            List<TdZwyjBsnCsionNoSub> noRstList = noRstMap.get(t.getFkByOnguardStateid().getRid());
                            Map<Integer,TdZwyjBsnCsionNoSub> noSubMap = getCsionNoSubMap(noRstList);

                            for(TdTjBadrsns rsn: badrsnList){
                                if(null==rsn.getFkByBadrsnId()){
                                    errorSb.append("接触危害因素主表Id：").append(rsn.getRid()).append("的危害因素Id为空！");
                                    continue;
                                }
                                if(null==rsn.getFkByExamConclusionId()){
                                    errorSb.append("接触危害因素主表Id：").append(rsn.getRid()).append("的体检结论Id为空！");
                                    continue;
                                }
                                //接触危害因素
                                TdZwyjBsnBadrsns badrsns = new TdZwyjBsnBadrsns();
                                badrsns.setFkByBadrsnId(rsn.getFkByBadrsnId());
                                badrsns.setFkByExamConclusionId(rsn.getFkByExamConclusionId());
                                badrsns.setQtjbName(rsn.getQtjbName());
                                bsnBadrsnsList.add(badrsns);
                                //通用规则：在岗状态有异常结论
                                if(null!=noSubMap.get(rsn.getFkByExamConclusionId().getRid())){
                                    TdZwyjBsnCsionNoSub noSub = noSubMap.get(rsn.getFkByExamConclusionId().getRid());
                                    TdZwyjBsnCtrlCsion csion = new TdZwyjBsnCtrlCsion();
                                    csion.setFkByCrtlId(noSub);
                                    ctrlCsionList.add(csion);
                                }

                                String key = t.getFkByOnguardStateid().getRid()+"&"+rsn.getFkByBadrsnId().getRid();
                                List<BsnClusionPO> poList = clusionMap.get(key);
                                //判定方式
                                if(CollectionUtils.isEmpty(poList)){
                                    continue;
                                }
                                judgeBsnClusion(t,rsn,poList,judgeItemMap,bhkItemMap,advCsionList,errorSb);
                            }
                            if(!CollectionUtils.isEmpty(ctrlCsionList)
                                    ||!CollectionUtils.isEmpty(advCsionList)){
                                //去重
                                distictCsionItems(advCsionList);
                                distictCtrlCsion(ctrlCsionList);
                                //体检主表
                                TdZwyjBsnBhk bsnBhk = new TdZwyjBsnBhk();
                                //根据体检编号、体检机构Id查询历史数据
                                List<TdZwyjBsnBhk> bsnBhkList = bsnBhkService.findTdZwyjBsnBhk(t.getBhkCode(), t.getFkByBhkorgId().getRid());
                                boolean ifAdvCsionSame = false;
                                boolean ifCtrlCsionSame = false;
                                if(!CollectionUtils.isEmpty(bsnBhkList)){
                                    bsnBhk = bsnBhkList.get(0);
                                    //比对
                                    ifAdvCsionSame = compareBsnAdvCsion(bsnBhk.getRid(),advCsionList);
                                    ifCtrlCsionSame = compareBsnCtrlCsion(bsnBhk.getRid(),ctrlCsionList);
                                    if(ifAdvCsionSame  && ifCtrlCsionSame){
                                        bsnRcdService.saveTdZwyjBsnRcd(t.getRid(),"1","更新的体检数据相关匹配规则无变化！");
                                        continue;
                                    }
                                }
                                ObjectCopyUtil.copyPropertiesInclude(t,bsnBhk,new String[]{"bhkCode","fkByBhkorgId","fkByCrptId"
                                        ,"crptName","personName","sex","fkByCardTypeId","idc","psnType","brth","age","isxmrd"
                                        ,"lnktel","dpt","wrknum","wrklnt","wrklntmonth","tchbadrsntim","tchbadrsnmonth","workName"
                                        ,"bhkType","fkByOnguardStateid","bhkDate","bhkrst","mhkadv","ocpBhkrstdes","jdgdat","badrsn"
                                        ,"rptPrintDate","ifRhk","lastBhkCode","lastFstBhkCode","uuid","entrustId"});
                                if(ifAdvCsionSame){
                                    if(!CollectionUtils.isEmpty(advCsionList)){
                                        advCsionList.clear();
                                    }
                                }
                                if(ifCtrlCsionSame){
                                    if(!CollectionUtils.isEmpty(ctrlCsionList)){
                                        ctrlCsionList.clear();
                                    }
                                }
                                bsnBhkService.saveOrUpdateBsnBhk(bsnBhk,bsnBadrsnsList,ctrlCsionList,advCsionList);
                            }else {
                                //数据全部正常
                                //删除所有判定信息
                                //根据体检编号、体检机构Id查询历史数据
                                List<TdZwyjBsnBhk> bsnBhkList = bsnBhkService.findTdZwyjBsnBhk(t.getBhkCode(), t.getFkByBhkorgId().getRid());
                                if(!CollectionUtils.isEmpty(bsnBhkList)){
                                    bsnBhkService.deleteBsnBhk(bsnBhkList.get(0).getRid());
                                }
                            }
                        }
                        //日志
                        if(errorSb.length()>0){
                            bsnRcdService.saveTdZwyjBsnRcd(t.getRid(),"2",errorSb.toString());
                        }else {
                            if(CollectionUtils.isEmpty(ctrlCsionList)
                                    && CollectionUtils.isEmpty(advCsionList)){
                                bsnRcdService.saveTdZwyjBsnRcd(t.getRid(),"1","未匹配到规则！");
                            }else {
                                bsnRcdService.saveTdZwyjBsnRcd(t.getRid(),"1",null);
                            }
                        }
                    }catch (Exception e){
                        String msg = e.getMessage();
                        if(StringUtils.isBlank(msg)){
                            msg = e.toString();
                        }
                        if(msg.length()>1000){
                            msg = msg.substring(1000);
                        }
                        bsnRcdService.saveTdZwyjBsnRcd(t.getRid(),"2",msg);
                        log.error(e.getMessage(),new Throwable(e));
                    }
                    long end = System.currentTimeMillis();
                    log.info("一条体检记录总耗时："+(end-start)/1000);
                }
            }catch (Exception e){
                log.error(e.getMessage(),new Throwable(e));
            }
        }
    }
    /**
     * <p>方法描述：根据在岗状态，获取异常结论Map</p>
     * @MethodAuthor qrr,2020-10-29,getCsionNoSubMap
     * */
    private Map<Integer,TdZwyjBsnCsionNoSub> getCsionNoSubMap(List<TdZwyjBsnCsionNoSub> noRstList){
        //key:异常结论Id,value:TdZwyjBsnCsionNoSub
        Map<Integer,TdZwyjBsnCsionNoSub> noSubMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(noRstList)) {
            for (TdZwyjBsnCsionNoSub noSub : noRstList) {
                noSubMap.put(noSub.getFkByBhkrstId().getRid(),noSub);
            }
        }
        return noSubMap;
    }
    /**
     * <p>方法描述：获取重点职业病判定结果</p>
     * @MethodAuthor qrr,2020-10-29,getPdjlMap
     * */
    private Map<Integer,TdZwyjBsnClusionAdv> getClusionAdvMap(List<TdZwyjBsnClusionAdv> advList){
        Map<Integer,TdZwyjBsnClusionAdv> map = new HashMap<>();
        if(!CollectionUtils.isEmpty(advList)){
            for (TdZwyjBsnClusionAdv t : advList) {
                map.put(t.getFkByBhkrstId().getRid(),t);
            }
        }
        return map;
    }
    /**
     * <p>方法描述：获取体检结果项目Map</p>
     * @MethodAuthor qrr,2020-10-29,getPdjlMap
     * */
    private Map<Integer,TdTjBhksub> getBhkItemMap(List<TdTjBhksub> bhksubList){
        Map<Integer,TdTjBhksub> map = new HashMap<>();
        if(!CollectionUtils.isEmpty(bhksubList)){
            for (TdTjBhksub t : bhksubList) {
                map.put(t.getFkByItemId().getRid(),t);
            }
        }
        return map;
    }
    /**
     * <p>方法描述：获取重点职业病评估结果Map</p>
     * @MethodAuthor qrr,2020-10-29,getPdjlMap
     * */
    private Map<Integer,TdTjResultJudgeItem> getZdzybJudgeItemMap(List<TdTjResultJudgeItem> judgeItemList){
        Map<Integer,TdTjResultJudgeItem> map = new HashMap<>();
        if(!CollectionUtils.isEmpty(judgeItemList)){
            for (TdTjResultJudgeItem t : judgeItemList) {
                map.put(t.getFkByItemId().getRid(),t);
            }
        }
        return map;
    }
    /**
     * <p>方法描述：获取约束Map</p>
     * @MethodAuthor qrr,2020-10-29,getAdvCsionMap
     * */
    private Map<Integer,TdZwyjBsnAdvCsion> getAdvCsionMap(List<TdZwyjBsnAdvCsion> advCsionList){
        Map<Integer,TdZwyjBsnAdvCsion> map = new HashMap<>();
        if(!CollectionUtils.isEmpty(advCsionList)){
            for (TdZwyjBsnAdvCsion t : advCsionList) {
                map.put(t.getFkByAdvId().getRid(),t);
            }
        }
        return map;
    }
    /**
     * <p>方法描述：危害因素结论判定</p>
     * @MethodAuthor qrr,2020-11-03,judgeBsnClusion
     * */
    private void judgeBsnClusion(TdTjBhk t,TdTjBadrsns rsn,List<BsnClusionPO> poList
            ,Map<Integer,TdTjResultJudgeItem> judgeItemMap
            ,Map<Integer, TdTjBhksub> bhkItemMap
            ,List<TdZwyjBsnAdvCsion> advCsionList,StringBuffer errorSb){
        for(BsnClusionPO po:poList){
            //key:结论Id,value:TdZwyjBsnClusionAdv
            Map<Integer,TdZwyjBsnClusionAdv> advMap =getClusionAdvMap(po.getAdvList());

            List<TdZwyjBsnCsionItemSub> csionItemSubList = po.getItemSubList();
            if(!CollectionUtils.isEmpty(csionItemSubList)){
                List<TdZwyjBsnAdvCsion> currAdvCsionList = new ArrayList<>();//当前危害因素匹配的项目规则
                boolean ifFail = false;//存在判定项目存在匹配失败
                for(TdZwyjBsnCsionItemSub csionItemSub:csionItemSubList){
                    //匹配项目结果规则
                    boolean ifItemSuccess = calCsionItems(csionItemSub,advMap,rsn,bhkItemMap,t.getSex(),currAdvCsionList,errorSb);
                    ifFail = !ifItemSuccess;
                }
                String deterWay = po.getClusion().getDeterWay();
                if("1".equals(deterWay)) {//同时满足
                    if(!ifFail){//全部判定项目匹配成功
                        if(!CollectionUtils.isEmpty(currAdvCsionList)){
                            advCsionList.addAll(currAdvCsionList);
                        }
                    }
                }else {
                    if(!CollectionUtils.isEmpty(currAdvCsionList)){
                        advCsionList.addAll(currAdvCsionList);
                    }
                }
            }
        }
    }
    /**
     * <p>方法描述：计算项目规则</p>
     * @MethodAuthor qrr,2020-10-30,calCsionItems
     * */
    private boolean calCsionItems(TdZwyjBsnCsionItemSub csionItemSub,Map<Integer,TdZwyjBsnClusionAdv> advMap,
                                  TdTjBadrsns rsn,Map<Integer, TdTjBhksub> bhkItemMap,String bhkSex,
                               List<TdZwyjBsnAdvCsion> advCsionList,StringBuffer errorSb) {
        boolean ifSuccess = false;//匹配规则成功
        List<TdZwyjBsnCsionItems> csionItemsList = csionItemSub.getItemsList();
        if(!CollectionUtils.isEmpty(csionItemsList)){
            for(TdZwyjBsnCsionItems csionItems:csionItemsList){
                TdTjBhksub bhksub = bhkItemMap.get(csionItems.getFkByItemId().getRid());
                if(null!=bhksub){
                    if(StringUtils.isNotBlank(csionItems.getSex())){
                        String sex = "男".equals(bhkSex)?"1":"2";
                        if(!sex.equals(csionItems.getSex())){
                            continue;
                        }
                    }
                    String jdgptn = csionItems.getJdgptn();
                    if("1".equals(jdgptn)){//定性
                        if (StringUtils.isNotBlank(bhksub.getRgltag()) && bhksub.getRgltag().equals(csionItems.getHgFlag())) {
                            ifSuccess = true;
                            if(null==advMap.get(rsn.getFkByExamConclusionId().getRid())) {//危害因素对应体检结论和建议结论不一致
                                TdZwyjBsnAdvCsion advCsion = initTdZwyjBsnAdvCsion(csionItems,bhksub);
                                advCsionList.add(advCsion);
                            }
                        }
                    }else if("2".equals(jdgptn)||"4".equals(jdgptn)){
                        if(!"2".equals(bhksub.getJdgptn())){
                            continue;
                        }
                        if(null==csionItems.getFkByMsruntId()){
                            errorSb.append("重点危害因素项目规则（TD_ZWYJ_BSN_CSION_ITEMS）Id:"+csionItems.getRid()+"的计量单位为空！");
                            continue;
                        }
                        if(StringUtils.isBlank(bhksub.getMsruntId())){
                            errorSb.append("体检结果子表（TD_TJ_BHKSUB）Id："+bhksub.getRid()+"的计量单位为空！");
                            continue;
                        }
                        Integer msruntId = new Integer(bhksub.getMsruntId());
                        if (msruntId.intValue() != csionItems.getFkByMsruntId().getRid().intValue()) {
                            continue;
                        }
                        String maxval = null;
                        if("4".equals(jdgptn)) {//定量-倍数
                            maxval = bhksub.getMaxval();
                            if(StringUtils.isBlank(maxval)){
                                errorSb.append("体检结果子表（TD_TJ_BHKSUB）Id："+bhksub.getRid()+"的最大值为空！");
                                continue;
                            }
                        }
                        String itemRst = bhksub.getItemRst();
                        if(StringUtils.isBlank(itemRst)){
                            errorSb.append("体检结果子表（TD_TJ_BHKSUB）Id："+bhksub.getRid()+"的结果为空！");
                            continue;
                        }
                        BigDecimal val1 = new BigDecimal(itemRst);
                        BigDecimal val2 = null;
                        boolean ifNotRange = false;//不在范围内
                        if(StringUtils.isNotBlank(csionItems.getGe())){
                            if("2".equals(jdgptn)){//定量
                                val2 = new BigDecimal(csionItems.getGe());
                            }
                            if("4".equals(jdgptn)) {//定量-倍数
                                BigDecimal ge = new BigDecimal(csionItems.getGe());
                                val2 = ge.multiply(new BigDecimal(maxval));
                            }
                            if(null!=val2 && val1.compareTo(val2)<0){
                                ifNotRange = true;
                            }
                        }
                        if(StringUtils.isNotBlank(csionItems.getGt())){
                            if("2".equals(jdgptn)) {//定量
                                val2 = new BigDecimal(csionItems.getGt());
                            }
                            if("4".equals(jdgptn)) {//定量-倍数
                                BigDecimal gt = new BigDecimal(csionItems.getGt());
                                val2 = gt.multiply(new BigDecimal(maxval));
                            }
                            if(null!=val2 && val1.compareTo(val2)<=0){
                                ifNotRange = true;
                            }
                        }
                        if(StringUtils.isNotBlank(csionItems.getLe())){
                            if("2".equals(jdgptn)) {//定量
                                val2 = new BigDecimal(csionItems.getLe());
                            }
                            if("4".equals(jdgptn)) {//定量-倍数
                                BigDecimal le = new BigDecimal(csionItems.getLe());
                                val2 = le.multiply(new BigDecimal(maxval));
                            }
                            if(null!=val2 && val1.compareTo(val2)>0){
                                ifNotRange = true;
                            }
                        }
                        if(StringUtils.isNotBlank(csionItems.getLt())){
                            if("2".equals(jdgptn)) {//定量
                                val2 = new BigDecimal(csionItems.getLt());
                            }
                            if("4".equals(jdgptn)) {//定量-倍数
                                BigDecimal lt = new BigDecimal(csionItems.getLt());
                                val2 = lt.multiply(new BigDecimal(maxval));
                            }
                            if(null!=val2 && val1.compareTo(val2)>=0){
                                ifNotRange = true;
                            }
                        }
                        if (!ifNotRange){//在范围内
                            ifSuccess = true;
                            if(null==advMap.get(rsn.getFkByExamConclusionId().getRid())) {//危害因素对应体检结论和建议结论不一致
                                TdZwyjBsnAdvCsion advCsion = initTdZwyjBsnAdvCsion(csionItems,bhksub);
                                advCsionList.add(advCsion);
                            }
                            break;
                        }
                    }else if("3".equals(jdgptn)){
                        //胸片
                        if (ObjectUtils.isNotNull(bhksub.getRstFlag()) && bhksub.getRstFlag().toString().equals(csionItems.getHgFlag())) {
                            ifSuccess = true;
                            if(null==advMap.get(rsn.getFkByExamConclusionId().getRid())) {//危害因素对应体检结论和建议结论不一致
                                String result = "";
                                switch (bhksub.getRstFlag()) {
                                    case 0:
                                        result = "未见异常";
                                        break;
                                    case 1:
                                        result = "尘肺样改变";
                                        break;
                                    case 2:
                                        result = "其他异常";
                                        break;
                                    case 3:
                                        result = "未检查";
                                        break;
                                    default:
                                        result = bhksub.getItemRst();
                                        break;
                                }
                                bhksub.setItemRst(result);
                                TdZwyjBsnAdvCsion advCsion = initTdZwyjBsnAdvCsion(csionItems,bhksub);
                                advCsionList.add(advCsion);
                            }
                        }
                    }
                }
            }
        }
        return ifSuccess;
    }
    /**
     * <p>方法描述：初始化匹配项目规则</p>
     * @MethodAuthor qrr,2020-10-30,initTdZwyjBsnAdvCsion
     * */
    private TdZwyjBsnAdvCsion initTdZwyjBsnAdvCsion(TdZwyjBsnCsionItems csionItems, TdTjBhksub bhksub) {
        TdZwyjBsnAdvCsion advCsion = new TdZwyjBsnAdvCsion();
        advCsion.setFkByAdvId(csionItems);
        advCsion.setItemRst(bhksub.getItemRst());
        advCsion.setJdgptn(bhksub.getJdgptn());
        advCsion.setMsrunt(bhksub.getMsrunt());
        advCsion.setMaxval(bhksub.getMaxval());
        advCsion.setItemStdvalue(bhksub.getItemStdvalue());
        return advCsion;
    }
    /**
     * <p>方法描述：比对项目约束关系</p>
     * @MethodAuthor qrr,2020-10-30,compareBsnAdvCsion
     * */
    private boolean compareBsnAdvCsion(Integer mainId,List<TdZwyjBsnAdvCsion> advCsionList){
        boolean ifAdvCsionSame = false;
        List<TdZwyjBsnAdvCsion> advCsions = advCsionService.findBsnAdvCsions(mainId);
        if(CollectionUtils.isEmpty(advCsions) && CollectionUtils.isEmpty(advCsionList)){
            ifAdvCsionSame = true;
        }
        if(!CollectionUtils.isEmpty(advCsions) && !CollectionUtils.isEmpty(advCsionList)){
            if(advCsionList.size()==advCsions.size()){
                Map<Integer,TdZwyjBsnAdvCsion> advCsionMap =  getAdvCsionMap(advCsionList);
                boolean ifSame = true;
                for(TdZwyjBsnAdvCsion advCsion:advCsions){
                    if(null==advCsionMap.get(advCsion.getFkByAdvId().getRid())){
                        ifSame = false;
                        break;
                    }
                }
                if(ifSame){
                    ifAdvCsionSame = true;
                }
            }
        }
        if(!ifAdvCsionSame) {
            advCsionService.deleteByMainId(mainId);
        }
        return ifAdvCsionSame;
    }
    /**
     * <p>方法描述：比对项目约束关系</p>
     * @MethodAuthor qrr,2020-10-30,compareBsnAdvCsion
     * */
    private boolean compareBsnCtrlCsion(Integer mainId,List<TdZwyjBsnCtrlCsion> ctrlCsionList){
        boolean ifCtrlCsionSame = false;
        List<TdZwyjBsnCtrlCsion> ctrlCsions = ctrlCsionService.findBsnCtrlCsion(mainId);
        if(CollectionUtils.isEmpty(ctrlCsions) && CollectionUtils.isEmpty(ctrlCsionList)){
            ifCtrlCsionSame = true;
        }
        if(!CollectionUtils.isEmpty(ctrlCsions) && !CollectionUtils.isEmpty(ctrlCsionList)){
            if(ctrlCsionList.size()==ctrlCsions.size()){
                Map<Integer,TdZwyjBsnCtrlCsion> map =  getCtrlCsionMap(ctrlCsionList);
                boolean ifSame = true;
                for(TdZwyjBsnCtrlCsion t:ctrlCsions){
                    if(null==map.get(t.getFkByCrtlId().getRid())){
                        ifSame = false;
                        break;
                    }
                }
                if(ifSame){
                    ifCtrlCsionSame = true;
                }
            }
        }
        if(!ifCtrlCsionSame){
            ctrlCsionService.deleteByMainId(mainId);
        }
        return ifCtrlCsionSame;
    }
    /**
     * <p>方法描述：获取约束Map</p>
     * @MethodAuthor qrr,2020-10-29,getCtrlCsionMap
     * */
    private Map<Integer,TdZwyjBsnCtrlCsion> getCtrlCsionMap(List<TdZwyjBsnCtrlCsion> ctrlCsionList){
        Map<Integer,TdZwyjBsnCtrlCsion> map = new HashMap<>();
        if(!CollectionUtils.isEmpty(ctrlCsionList)){
            for (TdZwyjBsnCtrlCsion t : ctrlCsionList) {
                map.put(t.getFkByCrtlId().getRid(),t);
            }
        }
        return map;
    }
    /**
     * <p>方法描述：项目约束去重</p>
     * @MethodAuthor qrr,2020-11-05,distictCsionItems
     * */
    private void distictCsionItems(List<TdZwyjBsnAdvCsion> advCsionList){
        List<TdZwyjBsnAdvCsion> list = new ArrayList<>();
        if(!CollectionUtils.isEmpty(advCsionList)){
            Map<Integer,TdZwyjBsnCsionItems> map = new HashMap<>();
            for(TdZwyjBsnAdvCsion t:advCsionList){
                if(null==map.get(t.getFkByAdvId().getRid())){
                    list.add(t);
                    map.put(t.getFkByAdvId().getRid(),t.getFkByAdvId());
                }
            }
            advCsionList.clear();
            advCsionList.addAll(list);
        }
    }
    /**
     * <p>方法描述：异常结论去重</p>
     * @MethodAuthor qrr,2020-11-05,distictCtrlCsion
     * */
    private void distictCtrlCsion(List<TdZwyjBsnCtrlCsion> ctrlCsionList){
        List<TdZwyjBsnCtrlCsion> list = new ArrayList<>();
        if(!CollectionUtils.isEmpty(ctrlCsionList)){
            Map<Integer,TdZwyjBsnCsionNoSub> map = new HashMap<>();
            for(TdZwyjBsnCtrlCsion t:ctrlCsionList){
                if(null==map.get(t.getFkByCrtlId().getRid())){
                    list.add(t);
                    map.put(t.getFkByCrtlId().getRid(),t.getFkByCrtlId());
                }
            }
            ctrlCsionList.clear();
            ctrlCsionList.addAll(list);
        }
    }
}

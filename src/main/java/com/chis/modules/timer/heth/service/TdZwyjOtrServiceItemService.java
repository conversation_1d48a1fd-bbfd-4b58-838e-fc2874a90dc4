package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwyjOtrServiceItem;
import com.chis.modules.timer.heth.mapper.TdZwyjOtrServiceItemMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-11-12,TdZwyjOtrServiceItemService
 */
@Service
public class TdZwyjOtrServiceItemService extends ZwxBaseServiceImpl<TdZwyjOtrServiceItemMapper, TdZwyjOtrServiceItem> {

    public List<TdZwyjOtrServiceItem> findOtrServiceItemListByMainIds(List<Integer> mainIds){
        if(null == mainIds || mainIds.size() == 0){
            return null;
        }
        return this.baseMapper.findOtrServiceItemListByMainIds(mainIds);
    }

}

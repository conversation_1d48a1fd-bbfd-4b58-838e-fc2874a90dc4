package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>类描述：同步业务数据异步任务查询结果对象 </p>
 * pw 2023/8/9
 **/
@Data
public class TdTjCrptTaskVo implements Serializable {
    private static final long serialVersionUID = -5567921912890124354L;
    private Integer rid;
    /** 最新企业ID */
    private Integer newestCrptId;
    /** 单位名称 */
    private String crptName;
    /** 所属地区 */
    private Integer zoneId;
    /** 社会信用代码 */
    private String creditCode;
    /** 单位地址 */
    private String address;
    /** 作业场所地址 */
    private String enrolAddress;
    /** 行业类别 */
    private Integer indusTypeId;
    /** 经济性质 */
    private Integer economyId;
    /** 企业规模 */
    private Integer crptSizeId;
    /** 联系人 */
    private String linkMan;
    /** 联系电话 */
    private String linkPhone;
    /** 操作人 */
    private Integer opePsnId;

    /** 停用的企业rid集合 */
    private List<Integer> stopCrptIdList;
}

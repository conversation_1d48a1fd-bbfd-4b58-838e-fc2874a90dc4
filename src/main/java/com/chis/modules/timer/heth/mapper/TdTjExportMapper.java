package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdTjExport;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2021-12-18,TdTjExportMapper
 */
@Repository
public interface TdTjExportMapper extends ZwxBaseMapper<TdTjExport> {

    /**
     * @Description: 按创建日期排序的分页查询
     * 注意 这边的pageSize不是每页显示的条数 而是结束的数据下标
     * between first and end 包含first与end的数据
     * @MethodAuthor pw,2021年12月18日
     */
    List<TdTjExport> pageListOrderByCreateTime(@Param("first") Integer first, @Param("pageSize") Integer end,
                                               @Param("property") TdTjExport entity);

    /**
     *  <p>方法描述：查询创建时间为时间点前且状态为已下载且删除状态为空或者0,按照创建日期倒叙</p>
     * @MethodAuthor hsj 2025-03-06 8:34
     */
    List<TdTjExport> queryCleanTdExportList(@Param("dataSize") Integer dataSize,@Param("cutoffTime") String cutoffTime);
}

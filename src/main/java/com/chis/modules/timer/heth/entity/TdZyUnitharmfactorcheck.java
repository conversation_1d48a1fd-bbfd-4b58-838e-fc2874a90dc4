package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdZyUnitharmfactorcheck
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZY_UNITHARMFACTORCHECK")
@KeySequence(value = "TD_ZY_UNITHARMFACTORCHECK_SEQ",clazz = Integer.class)
public class TdZyUnitharmfactorcheck extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZyUnitbasicinfo fkByMainId;

    @TableField("IFAT")
    private Integer ifat;

    @TableField("TEST_UNIT_NAMES")
    private String testUnitNames;

    @TableField("TEST_REPORT_NOS")
    private String testReportNos;

    @TableField("IFAT_DUST")
    private Integer ifatDust;

    @TableField("IFAT_DUST_ALL_CHECKNUM")
    private Integer ifatDustAllChecknum;

    @TableField("IFAT_DUST_ALL_EXCESSNUM")
    private Integer ifatDustAllExcessnum;

    @TableField("IFAT_DUST_CHECKNUM")
    private Integer ifatDustChecknum;

    @TableField("IFAT_DUST_EXCESSNUM")
    private Integer ifatDustExcessnum;

    @TableField("IFAT_DUST_COAL_CHECKNUM")
    private Integer ifatDustCoalChecknum;

    @TableField("IFAT_DUST_COAL_EXCESSNUM")
    private Integer ifatDustCoalExcessnum;

    @TableField("IFAT_DUST_ASBESTOS_CHECKNUM")
    private Integer ifatDustAsbestosChecknum;

    @TableField("IFAT_DUST_ASBESTOS_EXCESSNUM")
    private Integer ifatDustAsbestosExcessnum;

    @TableField("IFAT_CHEMISTRY")
    private Integer ifatChemistry;

    @TableField("IFAT_CHEMISTRY_ALL_CHECKNUM")
    private Integer ifatChemistryAllChecknum;

    @TableField("IFAT_CHEMISTRY_ALL_EXCESSNUM")
    private Integer ifatChemistryAllExcessnum;

    @TableField("IFAT_CHEMISTRY_CHECKNUM")
    private Integer ifatChemistryChecknum;

    @TableField("IFAT_CHEMISTRY_EXCESSNUM")
    private Integer ifatChemistryExcessnum;

    @TableField("IFAT_CHEMISTRY_BENZENE_CHECKNU")
    private Integer ifatChemistryBenzeneChecknu;

    @TableField("IFAT_CHEMISTRY_BENZENE_EXCESSN")
    private Integer ifatChemistryBenzeneExcessn;

    @TableField("IFAT_PHYSICS")
    private Integer ifatPhysics;

    @TableField("IFAT_PHYSICS_ALL_CHECKNUM")
    private Integer ifatPhysicsAllChecknum;

    @TableField("IFAT_PHYSICS_ALL_EXCESSNUM")
    private Integer ifatPhysicsAllExcessnum;

    @TableField("IFAT_PHYSICS_CHECKNUM")
    private Integer ifatPhysicsChecknum;

    @TableField("IFAT_PHYSICS_EXCESSNUM")
    private Integer ifatPhysicsExcessnum;

    @TableField("IFAT_RADIOACTIVITY")
    private Integer ifatRadioactivity;

    @TableField("IFAT_RADIOACTIVITY_CHECKNUM")
    private Integer ifatRadioactivityChecknum;

    @TableField("IFAT_RADIOACTIVITY_EXCESSNUM")
    private Integer ifatRadioactivityExcessnum;

    @TableField("IFAT_BIOLOGYOTHER")
    private Integer ifatBiologyother;

    @TableField("IFAT_BIOLOGYOTHER_CHECKNUM")
    private Integer ifatBiologyotherChecknum;

    @TableField("IFAT_BIOLOGYOTHER_EXCESSNUM")
    private Integer ifatBiologyotherExcessnum;


    public TdZyUnitharmfactorcheck(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.mapper;

import com.chis.modules.timer.heth.logic.BhkAuditQueryPO;
import com.chis.modules.timer.heth.logic.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description: 异步导出Mapper
 *
 * @ClassAuthor pw,2021年12月21日,BhkCalulationFileExportMapper
 */
@Repository
public interface BhkCalulationFileExportMapper {

    /**
     * @Description: 通过体检主表rid集合获取体检子表信息
     *
     * @MethodAuthor pw,2021年12月21日
     */
    List<BhkSubExportVo> findSubExportVoListByBhkRidList(@Param("bhkRidList") List<Integer> bhkRidList);

    /**
     * @Description: 通过体检主表rid集合获取问诊(非)放射职业史信息（放射与非放射的）
     *
     * @MethodAuthor pw,2021年12月21日
     */
    List<EmhistoryExportVo> findEmhistoryExportVoListByBhkRidList(@Param("bhkRidList")  List<Integer> bhkRidList);

    /**
     * @Description: 通过体检主表rid集合获取问诊既往病史
     *
     * @MethodAuthor pw,2021年12月21日
     */
    List<AnamnesisExportVo> findAnamnesisExportVoListByBhkRidList(@Param("bhkRidList")  List<Integer> bhkRidList);

    /**
     * @Description: 通过体检主表rid集合获取问诊项目与问诊症状信息
     *
     * @MethodAuthor pw,2021年12月21日
     */
    List<ExmsAndSymptomExportVo> findExmsAndSymExportVoListByBhkRidList(@Param("bhkRidList")  List<Integer> bhkRidList);

    /**
     * @Description: 通过体检主表rid集合获取个案审核部分的危害因素异常
     *
     * @MethodAuthor pw,2021年12月22日
     */
    List<AbnormalBadrsnExportVo> findAbnormalBadrsnExportVoListByBhkRidList(@Param("bhkRidList")  List<Integer> bhkRidList);

    /**
     * @Description: 通过体检主表rid集合获取异步导出数据的基础信息
     *
     * @MethodAuthor pw,2021年12月22日
     */
    List<BaseInfoExportVo> findBaseInfoExportVoListByRidList(@Param("bhkRidList")  List<Integer> bhkRidList);

    /**
     * @Description: 通过平台传过来的参数 获取个案审核的主表rid集合
     *
     * @MethodAuthor pw,2021年12月23日
     */
    List<Integer> findBhkRidListByQueryPO(BhkAuditQueryPO queryPO);

    /**
     * @Description: 通过平台传过来的参数 获取个案审核的主表rid个数
     *
     * @MethodAuthor pw,2022年12月27日
     */
    Integer findBhkRidCountByQueryPO(BhkAuditQueryPO queryPO);

    /**
     * <p>方法描述：通过传过来的参数获取体检最新档案初检rid集合</p>
     * @MethodAuthor： pw 2022/7/2
     **/
    List<Integer> findTjNewestRecRidList(TjPersonSearchConditionPO queryPO);

    /**
     * <p>方法描述：通过传过来的参数获取体检最新档案初检数量</p>
     * @MethodAuthor： pw 2022/7/2
     **/
    Integer findTjNewestRecCount(TjPersonSearchConditionPO queryPO);

    Integer findAuditGenerateQueryCount(TjPersonAuditConditionPO  queryPO);

    List<Integer> findAuditGenerateQueryList(TjPersonAuditConditionPO  queryPO);
}

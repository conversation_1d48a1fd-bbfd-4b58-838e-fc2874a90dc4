package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TbTjSrvorg;
import com.chis.modules.sys.entity.TsUserInfo;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2024-10-15,TdZwBgkFlow
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_BGK_FLOW")
public class TdZwBgkFlow extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("CART_TYPE")
    private Integer cartType;

    @TableField("BUS_ID")
    private Integer busId;

    @TableField("OPER_FLAG")
    private Integer operFlag;

    @TableField("RCV_DATE")
    private Date rcvDate;

    @TableField(value = "SMT_PSN_ID" , el = "fkBySmtPsnId.rid")
    private TsUserInfo fkBySmtPsnId;

    @TableField("OPER_DATE")
    private Date operDate;

    @TableField(value = "OPER_PSN_ID" , el = "fkByOperPsnId.rid")
    private TsUserInfo fkByOperPsnId;

    @TableField("BACK_RSN")
    private String backRsn;

    @TableField("IF_IN_TIME")
    private Integer ifInTime;

    @TableField("AUDIT_ADV")
    private String auditAdv;

    @TableField("AUDIT_MAN")
    private String auditMan;

    @TableField("BHK_CODE")
    private String bhkCode;

    @TableField(value = "BHKORG_ID" , el = "fkByBhkorgId.rid")
    private TbTjSrvorg fkByBhkorgId;


    public TdZwBgkFlow(Integer rid) {
        super(rid);
    }


}

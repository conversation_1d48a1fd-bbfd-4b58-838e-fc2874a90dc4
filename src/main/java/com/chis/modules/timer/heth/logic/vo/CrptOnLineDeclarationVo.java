package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>类描述：企业在线申报 用人单位申报数据 </p>
 * @ClassAuthor： pw 2022/9/7
 **/
@Data
public class CrptOnLineDeclarationVo extends CrptOnLineReturnVo implements Serializable {
    private static final long serialVersionUID = -7068092911911258676L;
    private List<CrptOnLineResultVo> result;
}

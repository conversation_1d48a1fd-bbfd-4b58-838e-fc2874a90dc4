package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import com.chis.modules.timer.heth.entity.TbTjRadheth;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdTjRadhethPsn
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_TJ_RADHETH_PSN")
@KeySequence(value = "TD_TJ_RADHETH_PSN_SEQ",clazz = Integer.class)
public class TdTjRadhethPsn extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "UNIT_ID" , el = "fkByUnitId.rid")
    private TbTjRadheth fkByUnitId;

    @TableField("PSN_NAME")
    private String psnName;

    @TableField("PSN_SEX")
    private Integer psnSex;

    @TableField("IDC_CARD")
    private String idcCard;

    @TableField("BIRTH_DATE")
    private LocalDateTime birthDate;

    @TableField("PSN_CARD_NO")
    private String psnCardNo;

    @TableField("DOSE_NO")
    private String doseNo;

    @TableField("WORK_TYPE_ID")
    private Integer workTypeId;

    @TableField("ON_DUTY")
    private Integer onDuty;

    @TableField("NEW_CHECK_UNIT_ID")
    private Integer newCheckUnitId;


    @TableField("TRAIN_END_DATE")
    private Date trainEndDate;

    @TableField("UUID")
    private String uuid;

    @TableField("UPDATETAG")
    private Integer updatetag;

    @TableField("ERROR_MSG")
    private String errorMsg;

    @TableField("ON_OFFICE")
    private String onOffice;

    @TableField("ON_DUTY_DATE")
    private Date onDutyDate;

    @TableField("OUT_DUTY_DATE")
    private Date outDutyDate;

    @TableField("TRAIN_HG_TAG")
    private Integer trainHgTag;

    @TableField("BELONG_AREA")
    private String belongArea;

    @TableField(value = "JOB_ID" , el = "fkByJobId.rid")
    private TsSimpleCode fkByJobId;

    @TableField(value = "TITLE_LEVEL_ID" , el = "fkByTitleLevelId.rid")
    private TsSimpleCode fkByTitleLevelId;

    @TableField("OTHER_JOB")
    private String otherJob;

    @TableField(value = "EDUCATION_ID" , el = "fkByEducationId.rid")
    private TsSimpleCode fkByEducationId;

    @TableField("RAD_WORK_YEAR")
    private Integer radWorkYear;

    @TableField(value = "CARD_TYPE_ID" , el = "fkByCardTypeId.rid")
    private TsSimpleCode fkByCardTypeId;

    @TableField("SYNC_TRAIN_STATE")
    private Integer syncTrainState;

    @TableField("SYNC_TRAIN_DATE")
    private Date syncTrainDate;

    @TableField("SYNC_TRAIN_ERR_MSG")
    private String syncTrainErrMsg;


    public TdTjRadhethPsn(Integer rid) {
        super(rid);
    }


}

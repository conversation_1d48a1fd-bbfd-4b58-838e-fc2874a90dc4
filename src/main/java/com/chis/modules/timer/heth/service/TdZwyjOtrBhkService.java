package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.comm.utils.ZoneUtil;
import com.chis.modules.sys.entity.TbTjSrvorg;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsUnit;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.mapper.TdZwyjOtrBhkMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-11-12,TdZwyjOtrBhkService
 */
@Service
public class TdZwyjOtrBhkService extends ZwxBaseServiceImpl<TdZwyjOtrBhkMapper, TdZwyjOtrBhk> {
    @Autowired
    private TdZwyjOtrWarnService otrWarnService;
    @Autowired
    private TdZwyjOtrPsnListService psnListService;
    @Autowired
    private TdZwyjOtrSerItemCrptService itemCrptService;
    @Autowired
    private TdZwyjOtrCrptListService otrCrptListService;
    @Autowired
    private TdZwyjOtrSmyRcdService smyRcdService;
    /**
     * <p>
     *     方法描述：查询不在超范围服务预警统计日志中的超范围服务体检记录列表
     * </p>
     *
     * @MethodAuthor pw,2020年11月19日,
     */
    public List<TdZwyjOtrBhk> findTdZwyjOtrBhkExcludeSmyRCD(Integer dataSize){
        return this.baseMapper.findTdZwyjOtrBhkExcludeSmyRCD(dataSize);
    }

    /** 超服务范围预警统计 超备案地区 与 超项目范围 */
    public boolean areaExcute(List<TdZwyjOtrBhk> areaList, boolean isArea, Map<String, TsZone> cityZoneMap,
                              Map<Integer,TsZone> companyZoneMap,
                              Map<Integer,TsUnit> tsUnitMap,
                              Map<Integer,List<Integer>> itemListMap,
                              Map<Integer,TsSimpleCode> simpleCodeMap,
                              Map<Integer,TbTjSrvorg> tjSrvorgMap,
                              Map<Integer,TsZone> allZoneMap,
                              Map<Integer,TbTjCrpt> crptMap){
        boolean reFlag = true;
        if(null == areaList || areaList.size() == 0){
            return reFlag;
        }
        Map<Integer,List<TdZwyjOtrBhk>> areaMap = new HashMap<>();
        for(TdZwyjOtrBhk otrBhk : areaList){
            List<TdZwyjOtrBhk> tmpList = areaMap.get(otrBhk.getFkByBhkorgId().getRid());
            if(null == tmpList){
                tmpList = new ArrayList<>();
            }
            tmpList.add(otrBhk);
            areaMap.put(otrBhk.getFkByBhkorgId().getRid(), tmpList);
        }
        for(Map.Entry<Integer,List<TdZwyjOtrBhk>> tmpMap : areaMap.entrySet()){
            List<TdZwyjOtrBhk> tmpList = tmpMap.getValue();
            Map<Integer,List<TdZwyjOtrBhk>> cityMap = new HashMap<>();
            for(TdZwyjOtrBhk areaOtrBhk : tmpList){
                Integer rid = cityZoneMap.get(ZoneUtil.getParentCode(
                        companyZoneMap.get(areaOtrBhk.getFkByCrptZoneId().getRid().intValue())
                                .getZoneGb(),4)).getRid();
                List<TdZwyjOtrBhk> bhkList = cityMap.get(rid.intValue());
                if(null == bhkList){
                    bhkList = new ArrayList<>();
                }
                bhkList.add(areaOtrBhk);
                cityMap.put(rid.intValue(), bhkList);
            }
            for(Map.Entry<Integer,List<TdZwyjOtrBhk>> map : cityMap.entrySet()){
                List<TdZwyjOtrBhk> list = map.getValue();
                List<TdZwyjOtrWarn> otrWarnList = findOtrWarnList(list.get(0).getOtrType().trim(),
                        tsUnitMap.get(list.get(0).getFkByBhkorgId().getRid().intValue()).getRid(), cityZoneMap.get(ZoneUtil.getParentCode(
                                companyZoneMap.get(list.get(0).getFkByCrptZoneId().getRid().intValue())
                                        .getZoneGb(),4)));

                boolean flag = false;
                //状态不为省级通过的预警记录
                TdZwyjOtrWarn unAccessOtrWarn = null;
                //状态为省级通过的预警记录列表
                List<TdZwyjOtrWarn> accessOtrWarnList = null;
                // 验证有无对应的超范围服务预警主表
                if(null != otrWarnList && otrWarnList.size() > 0){
                    flag = true;
                    for(TdZwyjOtrWarn tmpOtrWarn : otrWarnList){
                        if(null != tmpOtrWarn.getStateMark() && tmpOtrWarn.getStateMark().trim().equals("3")){
                            if(null == accessOtrWarnList){
                                accessOtrWarnList = new ArrayList<>();
                            }
                            accessOtrWarnList.add(tmpOtrWarn);
                        }else{
                            unAccessOtrWarn = tmpOtrWarn;
                        }
                    }
                }
                try{
                    if(!flag){
                        reFlag = stepOneSave(list, isArea, cityZoneMap,companyZoneMap,
                                tsUnitMap,itemListMap,simpleCodeMap,tjSrvorgMap,allZoneMap,crptMap);
                    }else{
                        if(null != unAccessOtrWarn){//存在状态不为省级通过的预警记录
                            reFlag = stepTwoSave(list, isArea, unAccessOtrWarn,
                                    itemListMap, simpleCodeMap, tjSrvorgMap, allZoneMap,crptMap,cityZoneMap,companyZoneMap);
                        }else{//状态都为省级通过的预警记录
                            //reFlag = stepThreeSave(list, isArea, accessOtrWarnList);
                            reFlag = stepOneSave(list, isArea,cityZoneMap,companyZoneMap,
                                    tsUnitMap,itemListMap,simpleCodeMap,tjSrvorgMap,allZoneMap,crptMap);
                        }
                    }
                }catch (Exception e){
                    String message = e.getMessage();
                    List<TdZwyjOtrSmyRcd> smyRcdList = new ArrayList<>();
                    for(TdZwyjOtrBhk otBhk : list){
                        smyRcdList.add(createSmyRcd(otBhk,"0",message));
                    }
                    if(null != smyRcdList && smyRcdList.size() > 0){
                        smyRcdService.saveBatch(smyRcdList);
                    }
                }
            }
        }
        return reFlag;
    }

    /**
     * 存在状态不为省级通过的预警记录
     * @param list
     * @param isArea
     * @param unAccessOtrWarn
     */
    @Transactional(readOnly = false)
    public boolean stepTwoSave(List<TdZwyjOtrBhk> list, boolean isArea, TdZwyjOtrWarn unAccessOtrWarn,
                                Map<Integer,List<Integer>> itemListMap,
                                Map<Integer,TsSimpleCode> simpleCodeMap,
                                Map<Integer,TbTjSrvorg> tjSrvorgMap,
                                Map<Integer,TsZone> allZoneMap,
                                Map<Integer,TbTjCrpt> crptMap,
                               Map<String, TsZone> cityZoneMap,
                               Map<Integer,TsZone> companyZoneMap){
        boolean reFlag = true;
        if(null == list || list.size() == 0){
            return reFlag;
        }

        // 按企业id分组
        Map<Integer, List<TdZwyjOtrBhk>> bhkCrptMap = new HashMap<>();
        for(TdZwyjOtrBhk otrBhk : list){
            List<TdZwyjOtrBhk> tmpList = bhkCrptMap.get(otrBhk.getFkByCrptId().getRid().intValue());
            if(null == tmpList){
                tmpList = new ArrayList<>();
            }
            tmpList.add(otrBhk);
            bhkCrptMap.put(otrBhk.getFkByCrptId().getRid().intValue() , tmpList);
        }

        TdZwyjOtrCrptList queryOtrCrpt = new TdZwyjOtrCrptList();
        queryOtrCrpt.setFkByMainId(unAccessOtrWarn);
        List<TdZwyjOtrCrptList> otrCrptList = otrCrptListService.selectListByEntity(queryOtrCrpt);
        //按企业id分组
        Map<Integer, TdZwyjOtrCrptList> warnCrptMap = new HashMap<>();
        if(null != otrCrptList && otrCrptList.size() > 0){
            for(TdZwyjOtrCrptList t : otrCrptList){
                warnCrptMap.put(t.getFkByCrptId().getRid().intValue(), t);
            }
        }

        //预警企业名单rid 与 超服务项目范围_企业中服务项目rid列表
        Map<Integer,List<Integer>> itemMaps = new HashMap<>();
        if(!isArea){
            itemMaps = itemCrptServiceIdListMap(unAccessOtrWarn.getRid().intValue());
        }

        List<TdZwyjOtrSmyRcd> smyRcdList = new ArrayList<>();
        List<TdZwyjOtrCrptList> updateCrptList = new ArrayList<>();
        List<TdZwyjOtrSerItemCrpt> saveSerItemCrptList = new ArrayList<>();
        boolean updateContent = false;
        for(Map.Entry<Integer, List<TdZwyjOtrBhk>> bhkMap : bhkCrptMap.entrySet()){
            List<TdZwyjOtrBhk> excuteList = bhkMap.getValue();
            //预警日志
            for(TdZwyjOtrBhk areaOtrBhk : excuteList){
                smyRcdList.add(createSmyRcd(areaOtrBhk,"1",""));
            }
            if(null == warnCrptMap || warnCrptMap.isEmpty() || null == warnCrptMap.get(bhkMap.getKey().intValue())){
                // 2.1 不存在同企业ID的 预警企业名单 记录  新增预警企业名单 预警人员名单
                reFlag = saveOtrCrpt(excuteList, unAccessOtrWarn , isArea, crptMap,itemListMap, simpleCodeMap);
                if(!isArea){
                    //更新预警主表的预警内容
                    updateContent = true;
                }
            }else{
                // 2.2 存在同企业ID的 预警企业名单 记录
                TdZwyjOtrCrptList otrCrpt = warnCrptMap.get(bhkMap.getKey().intValue());
                //新增预警人员名单
                reFlag = savePsnList(otrCrpt, excuteList);
                if(!isArea){
                    //itemMaps
                    List<Integer> existItems = itemMaps.get(otrCrpt.getRid());
                    for(TdZwyjOtrBhk areaOtrBhk : excuteList){
                        List<Integer> itemIds = (null == itemListMap || null == itemListMap.get(areaOtrBhk.getRid())) ? null : itemListMap.get(areaOtrBhk.getRid()) ;
                        if(null != itemIds && itemIds.size() > 0){
                            for(Integer itm : itemIds){
                                if(null == existItems || existItems.size() == 0 || !existItems.contains(itm)){
                                    //需要更新预警主表的预警内容
                                    updateContent = true;
                                    saveSerItemCrptList.add(createSerItemCrpt(itm, otrCrpt, simpleCodeMap));
                                    if(null == existItems){
                                        existItems = new ArrayList<>();
                                    }
                                    //避免重复
                                    existItems.add(itm.intValue());
                                }
                            }
                        }
                    }
                }

                int num = StringUtils.isNotBlank(otrCrpt.getOtrPsns()) ? Integer.parseInt(otrCrpt.getOtrPsns()) : 0;
                otrCrpt.setOtrPsns(String.valueOf(excuteList.size() + num));
                //更新预警企业名单中的开始与结束体检日期
                Date begin = otrCrpt.getBeginBhkDate();
                Date end = otrCrpt.getEndBhkDate();
                for(TdZwyjOtrBhk otrBhk : excuteList){
                    if(DateUtils.isAfter(otrBhk.getBhkDate(),begin)){
                        begin = otrBhk.getBhkDate();
                    }
                    if(DateUtils.isAfter(end,otrBhk.getBhkDate())){
                        end = otrBhk.getBhkDate();
                    }
                }
                otrCrpt.setBeginBhkDate(begin);
                otrCrpt.setEndBhkDate(end);
                otrCrpt.setModifyDate(new Date());
                otrCrpt.setModifyManid(1);
                updateCrptList.add(otrCrpt);
            }
        }
        if(null != saveSerItemCrptList && saveSerItemCrptList.size() > 0){
            itemCrptService.saveBatch(saveSerItemCrptList);
        }

        if(null != updateCrptList && updateCrptList.size() > 0){
            otrCrptListService.updateBatchById(updateCrptList);
        }

        if(updateContent){
            List<TdZwyjOtrSerItemCrpt> serItems = itemCrptService.selectListByWarnId(unAccessOtrWarn.getRid().intValue());
            //缓存项目 避免重复
            if(null != serItems && serItems.size() > 0){
                Map<Integer, TsSimpleCode> itmMap = new HashMap<>();
                for(TdZwyjOtrSerItemCrpt serItemCrpt : serItems){
                    itmMap.put(serItemCrpt.getFkByServiceId().getRid().intValue(),
                            simpleCodeMap.get(serItemCrpt.getFkByServiceId().getRid().intValue()));
                }

                StringBuilder derl = new StringBuilder("");
                if(null != itmMap && !itmMap.isEmpty()){
                    for(TsSimpleCode tmpSimpleCode : itmMap.values()){
                        derl.append("、");
                        derl.append(tmpSimpleCode.getCodeName());
                        derl.append("职业健康检查");
                    }
                }
                TbTjSrvorg tjSrvorg = tjSrvorgMap.get(list.get(0).getFkByBhkorgId().getRid().intValue());
                TsZone tsZone = cityZoneMap.get(ZoneUtil.getParentCode(
                        companyZoneMap.get(list.get(0).getFkByCrptZoneId().getRid().intValue())
                                .getZoneGb(),4));
                StringBuilder builder = new StringBuilder("");
                builder.append("超服务项目：");
                builder.append("发现");
                builder.append(tjSrvorg.getUnitName());
                builder.append("，在");
                builder.append(tsZone.getZoneName());
                builder.append("进行");
                builder.append(derl.substring(1));
                builder.append("，请确认！");
                String warnCont = builder.toString();
                if(warnCont.length() > 500){
                    warnCont = warnCont.substring(0,490);
                }
                unAccessOtrWarn.setWarnCont(warnCont);
            }

        }

        //修改 unAccessOtrWarn
        int num = StringUtils.isNotBlank(unAccessOtrWarn.getOtrPsns()) ? Integer.parseInt(unAccessOtrWarn.getOtrPsns()) : 0;
        unAccessOtrWarn.setOtrPsns(String.valueOf(list.size()+num));
        unAccessOtrWarn.setModifyDate(new Date());
        unAccessOtrWarn.setModifyManid(1);
        otrWarnService.updateById(unAccessOtrWarn);

        if(null != smyRcdList && smyRcdList.size() > 0){
            // 批量新增超范围服务预警统计日志
            smyRcdService.saveBatch(smyRcdList);
        }

        return reFlag;
    }

    /**
     * 新增预警主表 预警企业名单 预警人员名单 超范围服务预警统计日志
     * 超服务项目 新增 超服务项目范围_企业
     * @param list
     * @param isArea
     * @return
     */
    @Transactional(readOnly = false)
    public boolean stepOneSave(List<TdZwyjOtrBhk> list, boolean isArea,
                                Map<String, TsZone> cityZoneMap,
                                Map<Integer,TsZone> companyZoneMap,
                                Map<Integer,TsUnit> tsUnitMap,
                                Map<Integer,List<Integer>> itemListMap,
                                Map<Integer,TsSimpleCode> simpleCodeMap,
                                Map<Integer,TbTjSrvorg> tjSrvorgMap,
                                Map<Integer,TsZone> allZoneMap,
                                Map<Integer,TbTjCrpt> crptMap){
        boolean reFlag = true;
        if(null == list || list.size() == 0){
            return reFlag;
        }
        List<TdZwyjOtrSmyRcd> smyRcdList = new ArrayList<>();

        TsUnit bhkOrg = tsUnitMap.get(list.get(0).getFkByBhkorgId().getRid().intValue());
        TbTjSrvorg tjSrvorg = tjSrvorgMap.get(list.get(0).getFkByBhkorgId().getRid().intValue());
        TsZone tsZone = allZoneMap.get(Integer.parseInt(tjSrvorg.getZoneId()));
        StringBuilder derl = new StringBuilder("");
        if(!isArea){
            Map<Integer,Integer> itemIdMap = new HashMap<>();
            for(TdZwyjOtrBhk areaOtrBhk : list){
                List<Integer> itemIds = (null == itemListMap || null == itemListMap.get(areaOtrBhk.getRid())) ? null : itemListMap.get(areaOtrBhk.getRid()) ;
                if(null != itemIds && itemIds.size() > 0){
                    for(Integer itemId : itemIds){
                        itemIdMap.put(itemId,itemId);
                    }
                }
            }
            if(null != itemIdMap && !itemIdMap.isEmpty()){
                for(Integer itemId : itemIdMap.keySet()){
                    TsSimpleCode tmpSimpleCode = simpleCodeMap.get(itemId.intValue());
                    derl.append("、");
                    derl.append(tmpSimpleCode.getCodeName());
                    derl.append("职业健康检查");
                }
            }
        }

        // 新增预警主表
        TdZwyjOtrWarn tdZwyjOtrWarn = new TdZwyjOtrWarn();
        tdZwyjOtrWarn.setFkByCityZoneId(cityZoneMap.get(ZoneUtil.getParentCode(
                companyZoneMap.get(list.get(0).getFkByCrptZoneId().getRid().intValue())
                        .getZoneGb(),4)));
        tdZwyjOtrWarn.setWarnType(list.get(0).getOtrType());

        tdZwyjOtrWarn.setFkByBhkorgId(bhkOrg);

        tdZwyjOtrWarn.setFkByBhkorgZoneId(tsZone);
        //预警内容 length 500
        StringBuilder builder = new StringBuilder("");
        if(isArea){
            builder.append("超备案地区：");
            builder.append("发现");
            builder.append(tjSrvorg.getUnitName());
            builder.append("，在");
            builder.append(tdZwyjOtrWarn.getFkByCityZoneId().getZoneName());
            builder.append("进行职业健康检查，请确认！");
        }else{
            builder.append("超服务项目：");
            builder.append("发现");
            builder.append(tjSrvorg.getUnitName());
            builder.append("，在");
            builder.append(tdZwyjOtrWarn.getFkByCityZoneId().getZoneName());
            builder.append("进行");
            builder.append(derl.substring(1));
            builder.append("，请确认！");
        }
        String warnCont = builder.toString();
        if(null != warnCont && warnCont.length() >= 500){
            warnCont = warnCont.substring(0, 490);
        }
        tdZwyjOtrWarn.setWarnCont(warnCont);
        tdZwyjOtrWarn.setHappenDate(new Date());
        tdZwyjOtrWarn.setOtrPsns(String.valueOf(list.size()));
        tdZwyjOtrWarn.setStateMark("0");
        tdZwyjOtrWarn.setCreateDate(new Date());
        tdZwyjOtrWarn.setCreateManid(1);
        otrWarnService.save(tdZwyjOtrWarn);

        //按企业分组
        Map<Integer,List<TdZwyjOtrBhk>> crptBhkMap = new HashMap<>();
        for(TdZwyjOtrBhk areaOtrBhk : list){
            List<TdZwyjOtrBhk> tmpList = crptBhkMap.get(areaOtrBhk.getFkByCrptId().getRid().intValue());
            if(null == tmpList){
                tmpList = new ArrayList<>();
            }
            tmpList.add(areaOtrBhk);
            crptBhkMap.put(areaOtrBhk.getFkByCrptId().getRid().intValue(), tmpList);
        }

        if(null != crptBhkMap && !crptBhkMap.isEmpty()){
            for(Map.Entry<Integer,List<TdZwyjOtrBhk>> crptBhk : crptBhkMap.entrySet()){
                List<TdZwyjOtrBhk> bhkCrptList = crptBhk.getValue();
                reFlag = saveOtrCrpt(bhkCrptList, tdZwyjOtrWarn , isArea , crptMap, itemListMap, simpleCodeMap);
                for(TdZwyjOtrBhk areaOtrBhk : bhkCrptList){
                    smyRcdList.add(createSmyRcd(areaOtrBhk,"1",""));
                }
            }
        }

        if(null != smyRcdList && smyRcdList.size() > 0){
            // 批量新增超范围服务预警统计日志
            smyRcdService.saveBatch(smyRcdList);
        }
        return reFlag;
    }

    /**
     * 状态都为省级通过的预警记录
     * @param list
     * @param isArea
     * @param accessOtrWarnList
     */
    /*private boolean stepThreeSave(List<TdZwyjOtrBhk> list, boolean isArea, List<TdZwyjOtrWarn> accessOtrWarnList){
        boolean reFlag = true;
        if(null == list || list.size() == 0 || null == accessOtrWarnList || accessOtrWarnList.size() == 0 ){
            return reFlag;
        }
        Date checkDate = accessOtrWarnList.get(0).getCheckDate();
        for(TdZwyjOtrWarn warn : accessOtrWarnList){
            if(DateUtils.isAfter(checkDate, warn.getCheckDate())){
                checkDate = warn.getCheckDate();
            }
        }

        List<Integer> existItems = new ArrayList<>();
        if(!isArea){
            for(TdZwyjOtrWarn otrWarn : accessOtrWarnList){
                List<TdZwyjOtrSerItemCrpt> serItems = itemCrptService.selectListByWarnId(otrWarn.getRid().intValue());
                if(null != serItems && serItems.size() > 0){
                    for(TdZwyjOtrSerItemCrpt serItemObj : serItems){
                        existItems.add(serItemObj.getFkByServiceId().getRid().intValue());
                    }
                }
            }
        }
        List<TdZwyjOtrBhk> reList = new ArrayList<>();
        List<TdZwyjOtrSmyRcd> smyRcdList = new ArrayList<>();
        for(TdZwyjOtrBhk otrBhk : list){
            if(DateUtils.isAfter(checkDate, otrBhk.getBhkDate())){
                //3.1 体检日期> 最大审核日期 同1 重新预警
                reList.add(otrBhk);
            }else{
                if(isArea){
                    //3.2 新增超范围服务预警统计日志
                    smyRcdList.add(createSmyRcd(otrBhk,"1",""));
                }else{
                    List<Integer> itemIds = (null == itemListMap || null == itemListMap.get(otrBhk.getRid()))
                            ? null : itemListMap.get(otrBhk.getRid()) ;
                    boolean flag = false;
                    if(null != itemIds && itemIds.size() > 0){
                        for(Integer itm : itemIds){
                            if(null == existItems || existItems.size() == 0 || !existItems.contains(itm)){
                                flag = true;
                                break;
                            }
                        }
                    }
                    if(flag){
                        //3.3.2 超服务项目不在审核通过记录的超服务项目内 同1 重新预警
                        reList.add(otrBhk);
                    }else{
                        //3.3.1 超服务项目在审核通过记录的超服务项目内 新增超范围服务预警统计日志
                        smyRcdList.add(createSmyRcd(otrBhk,"1",""));
                    }
                }
            }
        }
        if(null != smyRcdList && smyRcdList.size() > 0){
            // 批量新增超范围服务预警统计日志
            smyRcdService.saveBatch(smyRcdList);
        }
        if(null != reList && reList.size() > 0){
            reFlag = stepOneSave(reList, isArea);
        }

        return reFlag;
    }*/

    /**
     * 保存预警企业名单 预警人员名单
     * 超服务项目还需要新增炒股无项目范围_企业
     * 注意 使用此方法 excuteList中所有对象 必须同一个企业
     * @param excuteList
     * @param unAccessOtrWarn
     * @param isArea
     * @return
     */
    private boolean saveOtrCrpt(List<TdZwyjOtrBhk> excuteList, TdZwyjOtrWarn unAccessOtrWarn, boolean isArea,
                                Map<Integer,TbTjCrpt> crptMap,Map<Integer,List<Integer>> itemListMap,
                                Map<Integer,TsSimpleCode> simpleCodeMap){
        boolean reFlag = true;
        TbTjCrpt tjCrpt  = crptMap.get(excuteList.get(0).getFkByCrptId().getRid().intValue());
        Date begin = excuteList.get(0).getBhkDate();
        Date end = excuteList.get(0).getBhkDate();
        for(TdZwyjOtrBhk otrBhk : excuteList){
            if(DateUtils.isAfter(otrBhk.getBhkDate(),begin)){
                begin = otrBhk.getBhkDate();
            }
            if(DateUtils.isAfter(end,otrBhk.getBhkDate())){
                end = otrBhk.getBhkDate();
            }
        }
        TdZwyjOtrCrptList otrCrptObj = new TdZwyjOtrCrptList();
        otrCrptObj.setFkByMainId(unAccessOtrWarn);
        otrCrptObj.setFkByCrptId(tjCrpt);
        otrCrptObj.setFkByCrptZoneId(excuteList.get(0).getFkByCrptZoneId());
        otrCrptObj.setCrptName(tjCrpt.getCrptName());
        otrCrptObj.setCreditCode(tjCrpt.getInstitutionCode());
        otrCrptObj.setBeginBhkDate(begin);
        otrCrptObj.setEndBhkDate(end);
        otrCrptObj.setOtrPsns(String.valueOf(excuteList.size()));
        otrCrptObj.setCreateDate(new Date());
        otrCrptObj.setCreateManid(1);
        otrCrptListService.save(otrCrptObj);

        reFlag = savePsnList(otrCrptObj, excuteList);

        if(!isArea){
            //预警企业名单rid 与 超服务项目范围_企业中服务项目rid列表
            Map<Integer,List<Integer>> itemMaps = itemCrptServiceIdListMap(unAccessOtrWarn.getRid().intValue());
            List<TdZwyjOtrSerItemCrpt> saveItemCrpt = new ArrayList<>();
            List<Integer> existItems = itemMaps.get(otrCrptObj.getRid());//解决重复的问题
            for(TdZwyjOtrBhk areaOtrBhk : excuteList){
                List<Integer> itemIds = (null == itemListMap || null == itemListMap.get(areaOtrBhk.getRid())) ? null : itemListMap.get(areaOtrBhk.getRid()) ;
                for(Integer itemId : itemIds){
                    if(null == existItems || existItems.size() == 0 || !existItems.contains(itemId)){

                        saveItemCrpt.add(createSerItemCrpt(itemId, otrCrptObj,simpleCodeMap));
                        if(null == existItems){
                            existItems = new ArrayList<>();
                        }
                        //避免重复
                        existItems.add(itemId.intValue());
                    }
                }
            }
            if(null != saveItemCrpt && saveItemCrpt.size() != 0){
                // 批量新增 超服务项目范围_企业
                itemCrptService.saveBatch(saveItemCrpt);
            }
        }
        return reFlag;
    }

    private Map<Integer,List<Integer>> itemCrptServiceIdListMap(Integer rid){
        Map<Integer,List<Integer>> itemMaps = new HashMap<>();
        List<TdZwyjOtrSerItemCrpt> serItems = itemCrptService.selectListByWarnId(rid);
        if(null != serItems && serItems.size() > 0){
            for(TdZwyjOtrSerItemCrpt item : serItems){
                List<Integer> itmRidList =  itemMaps.get(item.getFkByMainId().getRid().intValue());
                if(null == itmRidList){
                    itmRidList = new ArrayList<>();
                }
                if(!itmRidList.contains(item.getFkByServiceId().getRid().intValue())){
                    itmRidList.add(item.getFkByServiceId().getRid().intValue());
                }
                itemMaps.put(item.getFkByMainId().getRid().intValue(), itmRidList);
            }
        }
        return itemMaps;
    }

    /** 新增预警人员名单 */
    private boolean savePsnList(TdZwyjOtrCrptList otrCrptObj, List<TdZwyjOtrBhk> excuteList){
        boolean reFlag = true;
        List<TdZwyjOtrPsnList> savePsns = new ArrayList<>();
        for(TdZwyjOtrBhk areaOtrBhk : excuteList){
            TdZwyjOtrPsnList psnList = new TdZwyjOtrPsnList();
            psnList.setFkByMainId(otrCrptObj);
            psnList.setFkByCrptId(areaOtrBhk);
            psnList.setCreateDate(new Date());
            psnList.setCreateManid(1);
            savePsns.add(psnList);
        }
        if(null != savePsns && savePsns.size() != 0){
            // 批量新增预警人员名单
            psnListService.saveBatch(savePsns);
        }
        return reFlag;
    }

    /** 创建 超服务项目范围_企业 */
    private TdZwyjOtrSerItemCrpt createSerItemCrpt(Integer itemId, TdZwyjOtrCrptList otrCrptObj,
                                                   Map<Integer,TsSimpleCode> simpleCodeMap){
        TsSimpleCode tmpSimpleCode = simpleCodeMap.get(itemId.intValue());
        TdZwyjOtrSerItemCrpt itemCrpt = new TdZwyjOtrSerItemCrpt();
        itemCrpt.setFkByMainId(otrCrptObj);
        itemCrpt.setFkByServiceId(tmpSimpleCode);
        itemCrpt.setCreateDate(new Date());
        itemCrpt.setCreateManid(1);
        return itemCrpt;
    }

    /** 创建超范围服务预警统计日志 */
    private TdZwyjOtrSmyRcd createSmyRcd(TdZwyjOtrBhk areaOtrBhk,String proTag,String proMsg){
        TdZwyjOtrSmyRcd smyRcd = new TdZwyjOtrSmyRcd();
        smyRcd.setFkByBhkId(areaOtrBhk);
        smyRcd.setProTag(proTag);
        if(StringUtils.isNotBlank(proMsg) && proMsg.length() >= 1000){
            proMsg = proMsg.substring(0,990);
        }
        smyRcd.setProMsg(proMsg);
        smyRcd.setCreateDate(new Date());
        smyRcd.setCreateManid(1);
        return smyRcd;
    }

    /** 通过预警类型以及体检机构rid以及市级地区获取 超范围服务预警主表列表 */
    private List<TdZwyjOtrWarn> findOtrWarnList(String warnType, Integer bhkOrgId, TsZone cityZone){
        TdZwyjOtrWarn queryOtrWarn = new TdZwyjOtrWarn();
        queryOtrWarn.setWarnType(warnType);
        queryOtrWarn.setFkByBhkorgId(new TsUnit(bhkOrgId));
        queryOtrWarn.setFkByCityZoneId(cityZone);
        return otrWarnService.selectListByEntity(queryOtrWarn);
    }
}

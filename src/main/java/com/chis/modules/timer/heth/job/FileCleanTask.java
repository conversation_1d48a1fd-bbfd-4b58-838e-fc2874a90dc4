package com.chis.modules.timer.heth.job;

import cn.hutool.core.util.StrUtil;
import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsSystemParam;
import com.chis.modules.sys.service.TsSystemParamService;
import com.chis.modules.timer.heth.entity.TdTjExport;
import com.chis.modules.timer.heth.service.TdTjExportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * <p>类描述：定时删除异步导出文件</p>
 *
 * @ClassAuthor hsj 2025-03-05 19:05
 */
@Slf4j
@Component
public class FileCleanTask {
    @Autowired
    private TsSystemParamService tsSystemParamService;
    @Autowired
    private TdTjExportService exportService;

    @Value("${heth-timer.file-clean-time.sche-cron.dataSize:1000}")
    private Integer dataSize;
    /**
     * 虚拟路径
     */
    @Value("${heth-timer.virtual.directory:}")
    private String virtualDirectory;

    @Scheduled(cron = "${heth-timer.file-clean-time.checkCron:-}")
//    @Scheduled(initialDelay = 3000,fixedDelay = 60000)
    public void start() {
        log.info("删除异步导出文件定时任务启动，当前程序版本：{} 启动时间：{}", "20250305", DateUtils.formatDateTime(new Date()));
        if (StringUtils.isEmpty(virtualDirectory)) {
            log.error("虚拟路径配置文件不存在，本次定时任务结束！");
            return;
        }
        TsSystemParam tsSystemParam = tsSystemParamService.findTsSystemParam("EXPORT_FILE_LIVEMONTH");
        if (tsSystemParam == null || StringUtils.isEmpty(tsSystemParam.getParamValue())) {
            log.error("EXPORT_FILE_LIVEMONTH系统参数不存在，本次定时任务结束！");
            return;
        }
        Integer liveMonth;
        try {
            liveMonth = Integer.parseInt(tsSystemParam.getParamValue());
        } catch (NumberFormatException e) {
            e.printStackTrace();
            log.error("EXPORT_FILE_LIVEMONTH系统参数未配置成大于0且长度小于11位的整数，本次定时任务结束！");
            return;
        }
        if (liveMonth == null || liveMonth <= 0) {
            log.error("EXPORT_FILE_LIVEMONTH系统参数未配置成大于0的整数，本次定时任务结束！");
            return;
        }
        //根据当前时间计算liveMonth（月份）前的时间
        LocalDateTime cutoffTime = LocalDateTime.now().minusMonths(liveMonth);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedCutoffTime = cutoffTime.format(formatter);
        //打印当前时间和liveMonth（月份）和formattedCutoffTime值
        log.info("当前时间：{} 文件保留月份：{} 临界时间：{}", DateUtils.formatDateTime(new Date()), liveMonth, formattedCutoffTime);
        for (; ; ) {
            List<TdTjExport> tjExportList = exportService.queryCleanTdExportList(dataSize, formattedCutoffTime);
            if (CollectionUtils.isEmpty(tjExportList)) {
                log.info("无需要删除的数据，本次定时任务结束！");
                return;
            }
            for (TdTjExport tdTjExport : tjExportList) {
                executeExport(tdTjExport);
            }
        }
    }

    /**
     * <p>方法描述：附件删除</p>
     * <p>根据导出任务对象中的文件路径，删除对应的物理文件并更新任务状态</p>
     *
     * @param tdTjExport 导出任务对象，需包含导出文件路径等元数据信息
     * @MethodAuthor hsj 2025-03-06 8:59
     */
    private void executeExport(TdTjExport tdTjExport) {
        try {
            // 验证文件路径有效性
            Path virtualDirPath = Paths.get(virtualDirectory).normalize();
            String exportFilePath = tdTjExport.getExportFilePath();
            if (StringUtils.isEmpty(exportFilePath)) {
                updateExportStatus(tdTjExport, 2, "导出文件地址为空！");
                return;
            }
            Path resolvedPath = virtualDirPath.resolve(exportFilePath).normalize();
            if (!resolvedPath.startsWith(virtualDirPath)) {
                updateExportStatus(tdTjExport, 2, "非法文件路径！");
                return;
            }
            // 构建完整物理路径并验证文件存在性
            if (!Files.exists(resolvedPath)) {
                updateExportStatus(tdTjExport, 2, "文件不存在，无法删除！");
                return;
            }
            if (Files.isDirectory(resolvedPath)) {
                updateExportStatus(tdTjExport, 2, "目标为目录，无法删除！");
                return;
            }
            if (!Files.isWritable(resolvedPath)) {
                updateExportStatus(tdTjExport, 2, "无写入权限，无法删除！");
                return;
            }
            if (isFileInUse(resolvedPath.toFile())) {
                updateExportStatus(tdTjExport, 2, "文件正在被使用，无法删除！");
                return;
            }
            // 执行物理文件删除操作
            Files.delete(resolvedPath);
            updateExportStatus(tdTjExport, 1, "");
        } catch (Exception e) {
            // 异常处理及状态更新
            log.error("删除异步导出文件，任务rid：{} 附件删除异常", tdTjExport.getRid(), e);
            updateExportStatus(tdTjExport, 2, e.getMessage());
        }
    }

    // 辅助方法：检查文件是否被占用
    private boolean isFileInUse(File file) {
        try {
            FileChannel channel = FileChannel.open(file.toPath(), StandardOpenOption.WRITE);
            FileLock lock = channel.tryLock();
            if (lock == null) {
                // 文件被占用
                return true;
            }
            lock.release();
            channel.close();
        } catch (IOException e) {
            return true; // 如果发生异常，假设文件被占用
        }
        return false;
    }

    /**
     * <p>方法描述：导出任务重删除信息点赋值</p>
     *
     * <p>功能说明：更新导出任务状态及相关信息，包含以下处理逻辑：
     * 1. 设置文件删除状态和错误信息
     * 2. 删除成功时清空导出文件路径
     * 3. 更新最后修改时间和操作人
     * 4. 持久化更新到数据库</p>
     *
     * @param tdTjExport 待更新的导出任务对象
     * @param state      文件删除状态（1-删除成功，其他值表示失败状态）
     * @param msg        错误信息（自动截断前1000个字符）
     * @MethodAuthor hsj 2025-03-06 9:35
     */
    private void updateExportStatus(TdTjExport tdTjExport, Integer state, String msg) {
        try {
            tdTjExport.setDelFileState(state);
            tdTjExport.setDelFileErrorMsg(StrUtil.sub(msg, 0, 1000));
            //删除成功后清空附件路径
            if (new Integer(1).equals(state)) {
                tdTjExport.setExportFilePath(null);
            }
            tdTjExport.setModifyDate(new Date());
            tdTjExport.setModifyManid(1);
            this.exportService.updateExport(tdTjExport);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("删除异步导出文件，任务rid：{} 更新异常", tdTjExport.getRid(), e);
        }
    }

}

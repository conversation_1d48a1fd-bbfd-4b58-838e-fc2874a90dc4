package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdZdzybAnalyType;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2021-05-11,TdZdzybAnalyTypeMapper
 */
@Repository
public interface TdZdzybAnalyTypeMapper extends ZwxBaseMapper<TdZdzybAnalyType> {
    
    /**
     * @Description: 通过analyType 与 busType 获取重点职业病统计维度对应的统计项ID集合
     * @param analyType 统计类别
     * @param busType 业务类型
     *
     * @MethodAuthor pw,2021年05月11日
     */
    List<Integer> findAnalyItemIdListByAnalyTypeAndBusType(@Param("analyType") Integer analyType,
                                                           @Param("busType") Integer busType);

    /**
     * <p>方法描述：通过analyType 与 busType 获取重点职业病统计维度对应的社会信用代码/单位编码集合 </p>
     * @param analyType 统计类别
     * @param busType 业务类型
     * @MethodAuthor： pw 2022/8/25
     **/
    List<String> findAnalyUnitCodeListByAnalyTypeAndBusType(@Param("analyType") Integer analyType,
                                                            @Param("busType") Integer busType);

}

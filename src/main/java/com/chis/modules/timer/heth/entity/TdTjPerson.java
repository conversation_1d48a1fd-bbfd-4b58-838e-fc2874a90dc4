package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-09-11,TdTjPerson
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_TJ_PERSON")
public class TdTjPerson extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("PERSON_NAME")
    private String personName;

    @TableField("SEX")
    private String sex;

    @TableField("IDC")
    private String idc;

    @TableField("BRTH")
    private Date brth;

    @TableField("AGE")
    private String age;

    @TableField("ISXMRD")
    private String isxmrd;

    @TableField("PYXNAM")
    private String pyxnam;

    @TableField("BRPLACE")
    private String brplace;

    @TableField("JOB")
    private String job;

    @TableField("NATIVEPLACE")
    private String nativeplace;

    @TableField("PSTN")
    private String pstn;

    @TableField("HMXADS")
    private String hmxads;

    @TableField("MLC")
    private String mlc;

    @TableField("FOLK")
    private String folk;

    @TableField("EDCDGR")
    private String edcdgr;

    @TableField("PCT")
    private String pct;

    @TableField("TEL")
    private String tel;

    @TableField("DEPT")
    private String dept;

    @TableField("JOBCOD")
    private String jobcod;

    @TableField("NTNLTY")
    private String ntnlty;

    @TableField("ARCH_NUM")
    private String archNum;

    @TableField(value = "CARD_TYPE_ID" , el = "fkByCardTypeId.rid")
    private TsSimpleCode fkByCardTypeId;


    public TdTjPerson(Integer rid) {
        super(rid);
    }


}

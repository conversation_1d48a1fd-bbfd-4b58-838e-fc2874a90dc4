package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwyjOtrSerItemCrpt;
import com.chis.modules.timer.heth.mapper.TdZwyjOtrSerItemCrptMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-11-19,TdZwyjOtrSerItemCrptService
 */
@Service
public class TdZwyjOtrSerItemCrptService extends ZwxBaseServiceImpl<TdZwyjOtrSerItemCrptMapper, TdZwyjOtrSerItemCrpt> {

    /** 不传递mainId 返回空 */
    public List<TdZwyjOtrSerItemCrpt> selectListByWarnId(Integer mainId){
        if(null == mainId){
            return null;
        }
        return this.baseMapper.selectListByWarnId(mainId);
    }

}

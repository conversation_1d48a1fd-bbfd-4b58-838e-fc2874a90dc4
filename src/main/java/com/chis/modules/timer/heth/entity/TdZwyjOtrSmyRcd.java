package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-19,TdZwyjOtrSmyRcd
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_OTR_SMY_RCD")
@KeySequence(value = "TD_ZWYJ_OTR_SMY_RCD_SEQ",clazz = Integer.class)
public class TdZwyjOtrSmyRcd extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "BHK_ID" , el = "fkByBhkId.rid")
    private TdZwyjOtrBhk fkByBhkId;

    @TableField("PRO_TAG")
    private String proTag;

    @TableField("PRO_MSG")
    private String proMsg;


    public TdZwyjOtrSmyRcd(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsUnit;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2023-11-10,TdZwWarnInfo
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_WARN_INFO")
public class TdZwWarnInfo extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MODEL_ID" , el = "fkByModelId.rid")
    private TbZwWarnModel fkByModelId;

    @TableField(value = "WARN_ZONE" , el = "fkByWarnZone.rid")
    private TsZone fkByWarnZone;

    @TableField("BUS_TYPE")
    private Integer busType;

    @TableField("BUS_ID")
    private Integer busId;

    @TableField("WARN_CONT")
    private String warnCont;

    @TableField("HAPPEN_DATE")
    private Date happenDate;

    @TableField("JC_BEGIN_DATE")
    private Date jcBeginDate;

    @TableField("JC_END_DATE")
    private Date jcEndDate;

    @TableField("DEAL_DATE")
    private Date dealDate;

    @TableField(value = "DEAL_ORGID" , el = "fkByDealOrgid.rid")
    private TsUnit fkByDealOrgid;

    @TableField("HAPPEN_NUM")
    private Integer happenNum;

    @TableField("EXCLUDE_RSN")
    private String excludeRsn;

    @TableField("STATE_MARK")
    private Integer stateMark;

    @TableField("VIEW_LEVEL")
    private Integer viewLevel;

    @TableField("ANNEX_PATH")
    private String annexPath;

    private List<TdZwWarnPsns> psnList;

    public TdZwWarnInfo(Integer rid) {
        super(rid);
    }


}

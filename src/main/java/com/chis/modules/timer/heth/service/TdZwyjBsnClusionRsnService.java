package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwyjBsnClusionRsn;
import com.chis.modules.timer.heth.mapper.TdZwyjBsnClusionRsnMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnClusionRsnService
 */
@Service
public class TdZwyjBsnClusionRsnService extends ZwxBaseServiceImpl<TdZwyjBsnClusionRsnMapper, TdZwyjBsnClusionRsn> {
    /**
     * <p>方法描述：查询危害因素</p>
     * @MethodAuthor qrr,2020-10-29,findClusionRsnMap
     * */
    public Map<Integer,List<TdZwyjBsnClusionRsn>> findClusionRsnMap(){
        List<TdZwyjBsnClusionRsn> rsnList = this.selectListByEntity(null);
        //key:配置主表Id,value:TdZwyjBsnClusionRsn
        Map<Integer,List<TdZwyjBsnClusionRsn>> rsnMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(rsnList)){
            for(TdZwyjBsnClusionRsn t:rsnList){
                if (null==t.getFkByMainId()){
                    continue;
                }
                if(null==rsnMap.get(t.getFkByMainId().getRid())){
                    List<TdZwyjBsnClusionRsn> list = new ArrayList<>();
                    list.add(t);
                    rsnMap.put(t.getFkByMainId().getRid(),list);
                }else {
                    List<TdZwyjBsnClusionRsn> list = rsnMap.get(t.getFkByMainId().getRid());
                    list.add(t);
                }
            }
        }
        return rsnMap;
    }
}

package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdTjBhk;
import com.chis.modules.timer.heth.entity.TdTjResultJudgeItem;
import com.chis.modules.timer.heth.mapper.TdTjResultJudgeItemMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-30,TdTjResultJudgeItemService
 */
@Service
public class TdTjResultJudgeItemService extends ZwxBaseServiceImpl<TdTjResultJudgeItemMapper, TdTjResultJudgeItem> {
    /**
     * <p>方法描述：查询重点职业病判定结果</p>
     * @MethodAuthor qrr,2020-10-29,findZdzybAnalyItmType
     * */
    public List<TdTjResultJudgeItem> findZdzybJudgeItem(Integer bhkId){
        TdTjResultJudgeItem judgeItem = new TdTjResultJudgeItem();
        judgeItem.setFkByMainId(new TdTjBhk(bhkId));
        judgeItem.setJudgeType("1");
        return this.baseMapper.selectListByEntity(judgeItem);
    }
    /**
     * <p>方法描述：查询复检关联的初检的重点职业病判定结论</p>
     * @MethodAuthor qrr,2020-11-03,findRhkZdzybJudgeItem
     * */
    public List<TdTjResultJudgeItem> findRhkZdzybJudgeItem(String firstBhkCode,Integer orgId){
        return this.baseMapper.findRhkZdzybJudgeItem(firstBhkCode,orgId);
    }
}

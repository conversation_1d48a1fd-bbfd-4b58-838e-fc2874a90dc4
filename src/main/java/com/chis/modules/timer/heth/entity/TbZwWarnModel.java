package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2023-11-10,TbZwWarnModel
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TB_ZW_WARN_MODEL")
public class TbZwWarnModel extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("WARN_TYPE")
    private Integer warnType;

    @TableField("WARN_LEVEL")
    private Integer warnLevel;

    @TableField("WARN_INDEX")
    private Integer warnIndex;

    @TableField("GE_IND")
    private Integer geInd;

    @TableField("GT_IND")
    private Integer gtInd;

    @TableField("LE_IND")
    private Integer leInd;

    @TableField("LT_IND")
    private Integer ltInd;

    @TableField("IF_CYCLE_TIME")
    private Integer ifCycleTime;

    @TableField("CYCLE_DAYS")
    private Integer cycleDays;

    @TableField("RMK")
    private String rmk;

    @TableField("STATE_MARK")
    private Integer stateMark;


    public TbZwWarnModel(Integer rid) {
        super(rid);
    }


}

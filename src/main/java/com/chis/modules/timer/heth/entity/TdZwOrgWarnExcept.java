package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * <AUTHOR> 2024-07-12,TdZwOrgWarnExcept
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_ORG_WARN_EXCEPT")
@KeySequence(value = "TD_ZW_ORG_WARN_EXCEPT_SEQ",clazz = Integer.class)
public class TdZwOrgWarnExcept extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID", el = "fkByMainId.rid")
    private TdZwOrgWarnMain fkByMainId;

    @TableField(value = "EXCEPT_ID", el = "fkByExceptId.rid")
    private TsSimpleCode fkByExceptId;

    @TableField("FILING_DATE")
    private Date filingDate;

    @TableField("FILING_YEAR")
    private Integer filingYear;

    @TableField("NEXT_DATE")
    private Date nextDate;

    @TableField(exist = false)
    private List<TdZwOrgWarnDetail> detailList;

    public TdZwOrgWarnExcept(Integer rid) {
        super(rid);
    }


}

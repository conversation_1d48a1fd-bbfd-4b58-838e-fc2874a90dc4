package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.mapper.TdZyUnitbasicinfoMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdZyUnitbasicinfoService
 */
@Service
public class TdZyUnitbasicinfoService extends ZwxBaseServiceImpl<TdZyUnitbasicinfoMapper, TdZyUnitbasicinfo> {
    @Resource
    private TdZyUnitfactorcDetailService unitfactorcDetailService;
    @Resource
    private TdZyUnitharmfactDetailService harmfactDetailService;
    @Resource
    private TdZyUnithealthDetailService healthDetailService;
    @Resource
    private TdZyTrainSituationService situationService;
    @Resource
    private TdZyUnitmainprodService prodService;
    @Resource
    private TdZyUnitfactorcrowdService factorcrowdService;
    @Resource
    private TdZyUnitharmfactorcheckService checkService;
    @Resource
    private TdZyJcOrgService jcOrgService;
    @Resource
    private TdZyUnithealthcustodyService todyService;
    @Resource
    private TdZyHethOrgService orgService;

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateTdZyUnitbasicinfo(TdZyUnitbasicinfo info,
                                              List<TdZyUnitfactorcDetail> factorcDetailList,
                                              List<TdZyUnitharmfactDetail> harmFactDetailList,
                                              List<TdZyUnithealthDetail> healthDetailList,
                                              TdZyTrainSituation situation,
                                              List<TdZyUnitmainprod> prodList,
                                              TdZyUnitfactorcrowd crowd,
                                              TdZyUnitharmfactorcheck check,
                                              List<TdZyJcOrg> jcOrgList,
                                              TdZyUnithealthcustody custody,
                                              List<TdZyHethOrg> hethOrgList){
        if(null == info.getRid()){
            this.insertEntity(info);
        }else{
            this.updateFullById(info);
            //删除子表
            TdZyUnitfactorcDetail detail = new TdZyUnitfactorcDetail();
            detail.setFkByMainId(info);
            this.unitfactorcDetailService.removeByEntity(detail);
            TdZyUnitharmfactDetail factDetail = new TdZyUnitharmfactDetail();
            factDetail.setFkByMainId(info);
            this.harmfactDetailService.removeByEntity(factDetail);
            TdZyUnithealthDetail healthDetail = new TdZyUnithealthDetail();
            healthDetail.setFkByMainId(info);
            this.healthDetailService.removeByEntity(healthDetail);
            TdZyTrainSituation trainSituation = new TdZyTrainSituation();
            trainSituation.setFkByMainId(info);
            this.situationService.removeByEntity(trainSituation);
            TdZyUnitmainprod prod = new TdZyUnitmainprod();
            prod.setFkByMainId(info);
            this.prodService.removeByEntity(prod);
            TdZyUnitfactorcrowd factorcrowd = new TdZyUnitfactorcrowd();
            factorcrowd.setFkByMainId(info);
            this.factorcrowdService.removeByEntity(factorcrowd);
            TdZyUnitharmfactorcheck factorCheck = new TdZyUnitharmfactorcheck();
            factorCheck.setFkByMainId(info);
            this.checkService.removeByEntity(factorCheck);
            TdZyJcOrg jcOrg = new TdZyJcOrg();
            jcOrg.setFkByMainId(info);
            this.jcOrgService.removeByEntity(jcOrg);
            TdZyUnithealthcustody healthcustody = new TdZyUnithealthcustody();
            healthcustody.setFkByMainId(info);
            this.todyService.removeByEntity(healthcustody);
            TdZyHethOrg zyHethOrg = new TdZyHethOrg();
            zyHethOrg.setFkByMainId(info);
            this.orgService.removeByEntity(zyHethOrg);
        }
        if(!CollectionUtils.isEmpty(factorcDetailList)){
            this.unitfactorcDetailService.insertBatch(factorcDetailList);
        }
        if(!CollectionUtils.isEmpty(harmFactDetailList)){
            this.harmfactDetailService.insertBatch(harmFactDetailList);
        }
        if(!CollectionUtils.isEmpty(healthDetailList)){
            this.healthDetailService.insertBatch(healthDetailList);
        }
        if(null != situation){
            this.situationService.insertEntity(situation);
        }
        if(!CollectionUtils.isEmpty(prodList)){
            this.prodService.insertBatch(prodList);
        }
        if(null != crowd){
            this.factorcrowdService.insertEntity(crowd);
        }
        if(null != check){
            this.checkService.insertEntity(check);
        }
        if(!CollectionUtils.isEmpty(jcOrgList)){
            this.jcOrgService.insertBatch(jcOrgList);
        }
        if(null != custody){
            this.todyService.insertEntity(custody);
        }
        if(!CollectionUtils.isEmpty(hethOrgList)){
            this.orgService.insertBatch(hethOrgList);
        }
    }
}

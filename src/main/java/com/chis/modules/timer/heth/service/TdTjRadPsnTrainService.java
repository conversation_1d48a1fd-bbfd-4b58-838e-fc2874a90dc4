package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdTjRadPsnTrain;
import com.chis.modules.timer.heth.entity.TdTjRadhethPsn;
import com.chis.modules.timer.heth.mapper.TdTjRadPsnTrainMapper;
import com.chis.modules.timer.heth.pojo.TrainPojo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdTjRadPsnTrainService
 */
@Service
public class TdTjRadPsnTrainService extends ZwxBaseServiceImpl<TdTjRadPsnTrainMapper, TdTjRadPsnTrain> {
    public List<TdTjRadPsnTrain> selectTdTjRadPsnTrains(List<Integer> mainIds) {
        return baseMapper.selectTdTjRadPsnTrains(mainIds);
    }
    @Transactional(readOnly = false)
    public boolean saveOrUpdate(List<TrainPojo> trainPojos, Map<String, List<TdTjRadhethPsn>> idcMap, Map<String, TdTjRadPsnTrain> trainMap,String orgName) {
        List<TdTjRadPsnTrain> trains = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(trainPojos)){
            for(TrainPojo trainPojo : trainPojos){
                List<TdTjRadhethPsn> psns = idcMap.get(trainPojo.getIdc());
                for(TdTjRadhethPsn psn : psns ){
                    TdTjRadPsnTrain train = new TdTjRadPsnTrain();
                    if(null != trainMap && null != trainMap.get(trainPojo.getUuid()+"_"+psn.getRid()) && null != trainMap.get(trainPojo.getUuid()+"_"+psn.getRid()).getRid()){
                        train.setRid(trainMap.get(trainPojo.getUuid()+"_"+psn.getRid()).getRid());
                        train.setModifyDate(new Date());
                        train.setModifyManid(1);
                    }else {
                        train.setCreateDate(new Date());
                        train.setCreateManid(1);
                    }
                    train.setTrainOrgName(orgName);
                    train.setTrainResult(trainPojo.getTrainResult());
                    train.setTrainDate(trainPojo.getTrainDate());
                    train.setAddUpTime(trainPojo.getTotalTrainTime());
                    train.setScore(trainPojo.getScore());
                    train.setDataSource(3);
                    train.setUuid(trainPojo.getUuid());
                    train.setFkByMainId(psn);
                    trains.add(train);
                }
            }
            return saveOrUpdateBatch(trains);
        }
        return false;
    }
}

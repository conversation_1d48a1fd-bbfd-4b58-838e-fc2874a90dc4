package com.chis.modules.timer.heth.pojo.bo.warn;

import lombok.Data;

import java.util.Date;

/**
 * 业务数据BO(例数)
 */
@Data
public class DisCaseBusDataBO {
    /**
     * 业务ID
     */
    private Integer id;
    /**
     * 业务机构ID
     */
    private Integer orgId;
    /**
     * 单位名称
     */
    private String orgName;
    /**
     * 业务机构地区ID
     */
    private Integer zoneId;
    /**
     * 地区名称 原始fullName不去除省以及下划线
     */
    private String zoneName;
    /**
     * 业务日期
     */
    private Date date;
    /**
     * 疑似职业病病种ID
     */
    private Integer disId;

    public DisCaseBusDataBO() {
    }

    public DisCaseBusDataBO(Integer id, Integer orgId, Integer zoneId, Date date) {
        this.id = id;
        this.orgId = orgId;
        this.zoneId = zoneId;
        this.date = date;
    }

    public DisCaseBusDataBO(Integer id, Integer orgId, Integer zoneId, Date date, Integer disId) {
        this.id = id;
        this.orgId = orgId;
        this.zoneId = zoneId;
        this.date = date;
        this.disId = disId;
    }

    /**
     * 与传入日期比较
     *
     * @param date 传入日期
     * @return 如果参数的 Date 等于此 Date，则值为 0;如果此 Date 位于 Date 参数之前，则值小于 0;如果此 Date 位于 Date 参数之后，则值大于 0。
     */
    public int compareDate(Date date) {
        if (date == null) {
            return 1;
        }
        if (this.date == null) {
            return -1;
        }
        return this.date.compareTo(date);
    }
}

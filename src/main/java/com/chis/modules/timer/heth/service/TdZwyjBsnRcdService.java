package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdTjBhk;
import com.chis.modules.timer.heth.entity.TdZwyjBsnRcd;
import com.chis.modules.timer.heth.mapper.TdZwyjBsnRcdMapper;
import org.springframework.stereotype.Service;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-31,TdZwyjBsnRcdService
 */
@Service
public class TdZwyjBsnRcdService extends ZwxBaseServiceImpl<TdZwyjBsnRcdMapper, TdZwyjBsnRcd> {
    /**
     * <p>方法描述：保存危害因素上传日志</p>
     * @MethodAuthor qrr,2020-10-31,saveTdZwyjBsnRcd
     * */
    public void saveTdZwyjBsnRcd(Integer bhkId,String dataMark,String errMsg){
        TdZwyjBsnRcd rcd = new TdZwyjBsnRcd();
        rcd.setDataMark(dataMark);
        rcd.setFkByBhkId(new TdTjBhk(bhkId));
        rcd.setErrMsg(errMsg);
        this.save(rcd);
    }
}

package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>方法描述：修订企业信息动态列 </p>
 * pw 2023/8/10
 **/
@Data
public class CrptMergeDynamicColumnPO implements Serializable {
    private static final long serialVersionUID = 4858734700826006348L;
    /** 需要修改的列名 */
    private String columnName;
    /** 值 */
    private String columnVal;
    /** 值列（将这一列的值更新到修改的列） */
    private String valColumn;
    public CrptMergeDynamicColumnPO(String columnName, String columnVal, String valColumn){
        this.columnName = columnName;
        this.columnVal = columnVal;
        this.valColumn = valColumn;
    }
}

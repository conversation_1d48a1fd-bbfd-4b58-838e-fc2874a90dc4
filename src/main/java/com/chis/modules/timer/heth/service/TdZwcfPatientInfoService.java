package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.ObjectUtils;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.mapper.TdZwcfPatientInfoMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.Transient;
import java.util.Date;
import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人, 2022-09-02,TdZwcfPatientInfoService
 */
@Service
public class TdZwcfPatientInfoService extends ZwxBaseServiceImpl<TdZwcfPatientInfoMapper, TdZwcfPatientInfo> {
    public int updateSyncKfState() {
        return this.baseMapper.updateSyncKfState0();
    }

    public int updateSyncKfState3IdcNull(Date syncKfDate, String syncKfErrMsg) {
        return this.baseMapper.updateSyncKfState3IdcNull(syncKfDate, syncKfErrMsg);
    }

    public List<TdZwcfPatientInfo> getPatientInfoListByState0() {
        return this.baseMapper.selectPatientInfoListByState0();
    }

    @Transactional(rollbackFor = Exception.class)
    public String updateKfInfo(TdZwcfPatientInfo patientInfo,
                               List<TdZwcfKfPg> patientPdList,
                               List<TdZwcfKfCf> patientKffaList,
                               List<TdZwcfKfRcd> cfbRecoveryRecordList) {
        String msg = "";
        try {
            String year = "0";
            if (!ObjectUtils.isEmpty(cfbRecoveryRecordList)) {
                String rcdYear = cfbRecoveryRecordList.get(0).getYear();
                if (!ObjectUtils.isEmpty(rcdYear)) {
                    year = rcdYear;
                }
            }
            this.baseMapper.deleteKfInfo(patientInfo.getRid(), year);
            for (TdZwcfKfPg kfPg : patientPdList) {
                this.baseMapper.insertTdZwcfKfPg(kfPg);
            }
            for (TdZwcfKfCf kfCf : patientKffaList) {
                this.baseMapper.insertTdZwcfKfCf(kfCf);
                for (TdZwcfKfCfOper kfCfOper : kfCf.getKfCfOperList()) {
                    kfCfOper.setFkByMainId(kfCf);
                    this.baseMapper.insertTdZwcfKfCfOper(kfCfOper);
                }
            }
            for (TdZwcfKfRcd kfRcd : cfbRecoveryRecordList) {
                this.baseMapper.insertTdZwcfKfRcd(kfRcd);
            }
        } catch (Exception e) {
            e.printStackTrace();
            patientInfo.setSyncKfErrMsg(patientInfo.getSyncKfErrMsg() + "；保存尘肺病人相关康复信息 异常：" + e.getMessage());
            msg = e.getMessage();
        }

        if (!ObjectUtils.isEmpty(patientInfo.getSyncKfErrMsg())) {
            if (patientInfo.getSyncKfErrMsg().length() > 1000) {
                patientInfo.setSyncKfErrMsg(patientInfo.getSyncKfErrMsg().substring(1, 1000));
            } else {
                patientInfo.setSyncKfErrMsg(patientInfo.getSyncKfErrMsg().substring(1));
            }
        }
        patientInfo.setSyncKfState(ObjectUtils.isEmpty(patientInfo.getSyncKfErrMsg()) ? 1 : 2);
        patientInfo.setSyncKfDate(new Date());
        this.baseMapper.updateSyncKfSyncKfByRid(patientInfo);

        return msg;
    }
}

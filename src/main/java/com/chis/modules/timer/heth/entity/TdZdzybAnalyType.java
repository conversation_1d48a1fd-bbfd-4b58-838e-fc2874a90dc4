package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-05-11,TdZdzybAnalyType
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZDZYB_ANALY_TYPE")
public class TdZdzybAnalyType extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("ANALY_TYPE")
    private String analyType;

    @TableField("RMK")
    private String rmk;

    @TableField("BUS_TYPE")
    private String busType;


    public TdZdzybAnalyType(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-13,TdZwTjorgRcdItem
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZW_TJORG_RCD_ITEM")
public class TdZwTjorgRcdItem extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwTjorgRecord fkByMainId;

    @TableField(value = "ITEM_ID" , el = "fkByItemId.rid")
    private TsSimpleCode fkByItemId;

    private String codeNo;

    public TdZwTjorgRcdItem(Integer rid) {
        super(rid);
    }


}

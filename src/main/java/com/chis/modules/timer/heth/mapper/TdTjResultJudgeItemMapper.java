package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdTjResultJudgeItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2020-10-30,TdTjResultJudgeItemMapper
 */
@Repository
public interface TdTjResultJudgeItemMapper extends ZwxBaseMapper<TdTjResultJudgeItem> {
    public List<TdTjResultJudgeItem> findRhkZdzybJudgeItem(@Param("firstBhkCode") String firstBhkCode, @Param("orgId") Integer orgId);
}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2023-09-20,TdRectificationList
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_RECTIFICATION_LIST")
public class TdRectificationList extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("UUID")
    private String uuid;

    @TableField("UNIT_NAME")
    private String unitName;

    @TableField(value = "ZONE_ID" , el = "fkByZoneId.rid")
    private TsZone fkByZoneId;

    @TableField("CREDIT_CODE")
    private String creditCode;

    @TableField(value = "INDUSTRY_ID" , el = "fkByIndustryId.rid")
    private TsSimpleCode fkByIndustryId;

    @TableField(value = "ECONOMIC_ID" , el = "fkByEconomicId.rid")
    private TsSimpleCode fkByEconomicId;

    @TableField(value = "ENTERPRISE_SCALE_ID" , el = "fkByEnterpriseScaleId.rid")
    private TsSimpleCode fkByEnterpriseScaleId;

    @TableField("LINK_MAN")
    private String linkMan;

    @TableField("LINK_PHONE")
    private String linkPhone;

    @TableField("ADDRESS")
    private String address;

    @TableField("ENROL_ADDRESS")
    private String enrolAddress;

    @TableField("IF_ABOVE_10_STAFF")
    private Integer ifAbove10Staff;

    @TableField("IF_MAIN_FACTOR_OVER")
    private Integer ifMainFactorOver;

    @TableField("IF_WORK_PLACE_DETECTION")
    private Integer ifWorkPlaceDetection;

    @TableField("IF_OTHER_FACTOR_OVER")
    private Integer ifOtherFactorOver;

    @TableField("DATA_SOURCE")
    private Integer dataSource;

    @TableField("REGULATION_RSN")
    private String regulationRsn;

    @TableField("CONFIRM_DATE")
    private Date confirmDate;

    @TableField("REPORT_DATE")
    private Date reportDate;

    @TableField("COMPLETE_DATE")
    private Date completeDate;

    @TableField("IN_PROGRESS")
    private Integer inProgress;

    @TableField("PRE_TOTAL_HG")
    private Integer preTotalHg;

    @TableField("RESULT_ABNORMAL")
    private Integer resultAbnormal;

    @TableField("RECTIFICATION_RESULT")
    private Integer rectificationResult;

    @TableField("REPORT_STATE")
    private Integer reportState;

    @TableField("CHECK_UNIT")
    private String checkUnit;

    @TableField("CHECK_PSN")
    private String checkPsn;

    @TableField("CHECK_DATE")
    private Date checkDate;

    @TableField("CHECK_ADV")
    private String checkAdv;

    @TableField("SERVICE_ORG_NAME")
    private String serviceOrgName;

    @TableField("UPDATE_DATE")
    private Date updateDate;

    @TableField(value = "CRPT_ID" , el = "fkByCrptId.rid")
    private TbTjCrpt fkByCrptId;

    @TableField(value = "SOURCE_CRPT_ID" , el = "fkBySourceCrptId.rid")
    private TbTjCrpt fkBySourceCrptId;


    public TdRectificationList(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2024-10-15,TbTjJcTaskPsn
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TB_TJ_JC_TASK_PSN")
public class TbTjJcTaskPsn extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private Integer fkByMainId;

    @TableField("PSN_NAME")
    private String psnName;

    @TableField(value = "CARD_TYPE_ID" , el = "fkByCardTypeId.rid")
    private TsSimpleCode fkByCardTypeId;

    @TableField("IDC")
    private String idc;

    @TableField(value = "ONGUARD_STATEID" , el = "fkByOnguardStateid.rid")
    private TsSimpleCode fkByOnguardStateid;

    @TableField(value = "POST_ID" , el = "fkByPostId.rid")
    private TsSimpleCode fkByPostId;

    @TableField("RMK")
    private String rmk;

    @TableField("BHK_CODE")
    private String bhkCode;

    @TableField("WORK_OTHER")
    private String workOther;

    @TableField(value = "PROTECT_EQU_ID" , el = "fkByProtectEquId.rid")
    private TsSimpleCode fkByProtectEquId;

    @TableField("PLAN_TYPE")
    private Integer planType;


    public TbTjJcTaskPsn(Integer rid) {
        super(rid);
    }


}

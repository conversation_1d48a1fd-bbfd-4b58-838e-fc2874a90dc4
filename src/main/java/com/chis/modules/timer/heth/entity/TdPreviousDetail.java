package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2023-09-20,TdPreviousDetail
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_PREVIOUS_DETAIL")
public class TdPreviousDetail extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdRectificationList fkByMainId;

    @TableField(value = "FACTOR_ID" , el = "fkByFactorId.rid")
    private TsSimpleCode fkByFactorId;

    @TableField("DETECTION_STATION")
    private Integer detectionStation;

    @TableField("OVERWEIGHT_STATION")
    private Integer overweightStation;

    @TableField("DETECTION_POINT")
    private Integer detectionPoint;

    @TableField("OVERWEIGHT_POINT")
    private Integer overweightPoint;


    public TdPreviousDetail(Integer rid) {
        super(rid);
    }


}

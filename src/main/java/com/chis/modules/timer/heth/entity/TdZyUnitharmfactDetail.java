package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdZyUnitharmfactDetail
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZY_UNITHARMFACT_DETAIL")
@KeySequence(value = "TD_ZY_UNITHARMFACT_DETAIL_SEQ",clazz = Integer.class)
public class TdZyUnitharmfactDetail extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZyUnitbasicinfo fkByMainId;

    @TableField("SUPERVISION_REQUIREMENT")
    private Integer supervisionRequirement;

    @TableField("HAZARDS_SORT")
    private Integer hazardsSort;

    @TableField(value = "HAZARDS_ID" , el = "fkByHazardsId.rid")
    private TsSimpleCode fkByHazardsId;

    @TableField("HAZARDS_NAME")
    private String hazardsName;

    @TableField("CHECK_POINTS")
    private Integer checkPoints;

    @TableField("OVERPROOF_POINTS")
    private Integer overproofPoints;


    public TdZyUnitharmfactDetail(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.job;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <p>
 *     类描述：危急值工具
 * </p>
 *
 * @ClassAuthor pw,2020年11月21日,DangerValToolJob
 */
@Slf4j
@Component
public class DangerValToolJob {

    @Autowired
    private TdTjBhkService bhkService;
    @Autowired
    private TdTjBhksubService subBhkService;
    @Autowired
    private TdZwyjDangerBhkService dangerBhkService;
    @Autowired
    private TdZwyjDangerItemsService dangerItemsService;
    @Autowired
    private TdTjBadrsnsService badrsnsService;

    @Value("${heth-timer.danger.dataSize}")
    private Integer dataSize;
    @Value("${heth-timer.danger.startDate}")
    private String startDate;

   @Scheduled(cron = "${heth-timer.sche-cron.dangerCron}")
    public void start(){
       Date beginTime = new Date();
       excuteData();
       Date endTime = new Date();
       log.info("危急值工具开始时间：{} 结束时间：{} 耗时: {} 毫秒",DateUtils.formatDateTime(beginTime),DateUtils.formatDateTime(endTime),String.valueOf(endTime.getTime() - beginTime.getTime()));
    }

    private void excuteData(){
        List<TdTjBhk> bhkList = findTdtjBhks();
        //避免死循环
        if(null == bhkList || bhkList.size() == 0){
            return;
        }
        excute(bhkList);

        excuteData();
    }

    /** 处理数据方法 */
    private void excute(List<TdTjBhk> bhkList){
        if(null == bhkList || bhkList.size() == 0){
            return;
        }
        Map<Integer,List<TdTjBhk>> srvorgBhkMap = new HashMap<>();
        for(TdTjBhk bhk : bhkList){
            List<TdTjBhk> list = srvorgBhkMap.get(bhk.getFkByBhkorgId().getRid().intValue());
            if(null == list){
                list = new ArrayList<>();
            }
            list.add(bhk);
            srvorgBhkMap.put(bhk.getFkByBhkorgId().getRid().intValue(), list);
        }

        if(null != srvorgBhkMap && !srvorgBhkMap.isEmpty()){
            for(Map.Entry<Integer,List<TdTjBhk>> bhkMap : srvorgBhkMap.entrySet()){
                groupExcuteByBhkorg(bhkMap.getValue());
            }
        }
    }

    /**
     * 按体检机构分组 处理数据
     * 注意 传递进来的bhkList 都是同一个体检机构的体检数据
     * @param bhkList
     */
    private void groupExcuteByBhkorg(List<TdTjBhk> bhkList){
        try{
            if(null == bhkList || bhkList.size() == 0){
                return;
            }
            List<Integer> bhkIdList = new ArrayList<>();
            List<String> bhkCodeList = new ArrayList<>();
            for(TdTjBhk bhk : bhkList){
                bhkIdList.add(bhk.getRid().intValue());
                bhkCodeList.add(bhk.getBhkCode());
            }

            List<TdZwyjDangerBhk> dangerBhkList = findDangerBhk(bhkList.get(0).getFkByBhkorgId().getRid().intValue(),
                    bhkCodeList);

            List<TdTjBadrsns> badrsnsList = badrsnsService.findTdTjBadrsnsByBhkIdList(bhkIdList);
            List<Integer> itemIdList = new ArrayList<>();
            List<TdTjBhksub> subBhkList = findSubBhks(bhkIdList);
            //都是同一体检机构的数据 key 体检编号 唯一
            Map<String,TdZwyjDangerBhk> dangerBhkMap = null;
            //按体检主表rid 区分体检子表
            Map<Integer, List<TdTjBhksub>> subBhkMap = null;
            //按体检主表rid 区分接触危害因素
            Map<Integer,List<TdTjBadrsns>> badrsnsMap = null;
            if(null != dangerBhkList && dangerBhkList.size() > 0){
                dangerBhkMap = new HashMap<>();
                for(TdZwyjDangerBhk dangerBhk : dangerBhkList){
                    dangerBhkMap.put(dangerBhk.getBhkCode(), dangerBhk);
                }
            }
            if(null != subBhkList && subBhkList.size() > 0){
                subBhkMap = new HashMap<>();
                for(TdTjBhksub tjBhksub : subBhkList){
                    if(null == itemIdList){
                        itemIdList = new ArrayList<>();
                    }
                    if(itemIdList.size() == 0 || !itemIdList.contains(tjBhksub.getFkByItemId().getRid().intValue())){
                        itemIdList.add(tjBhksub.getFkByItemId().getRid().intValue());
                    }
                    List<TdTjBhksub> subList = subBhkMap.get(Integer.parseInt(tjBhksub.getBhkId()));
                    if(null ==subList){
                        subList = new ArrayList<>();
                    }
                    subList.add(tjBhksub);
                    subBhkMap.put(Integer.parseInt(tjBhksub.getBhkId()), subList);
                }
            }
            if(null != badrsnsList && badrsnsList.size() > 0){
                badrsnsMap = new HashMap<>();
                for(TdTjBadrsns badrsns : badrsnsList){
                    List<TdTjBadrsns> rsnList =  badrsnsMap.get(badrsns.getFkByBhkId().getRid().intValue());
                    if(null == rsnList){
                        rsnList = new ArrayList<>();
                    }
                    rsnList.add(badrsns);
                    badrsnsMap.put(badrsns.getFkByBhkId().getRid().intValue(), rsnList);
                }
            }

            List<TdZwyjDangerItems> dangerItemsList = dangerItemsService.selectListByItemIds(itemIdList);
            Map<Integer, List<TdZwyjDangerItems>> itemsMap = null;
            if(null != dangerItemsList && dangerItemsList.size() > 0){
                itemsMap = new HashMap<>();
                for(TdZwyjDangerItems itm : dangerItemsList){
                    List<TdZwyjDangerItems> dangerItmList = itemsMap.get(itm.getFkByItemId().getRid().intValue());
                    if(null == dangerItmList){
                        dangerItmList = new ArrayList<>();
                    }
                    dangerItmList.add(itm);
                    itemsMap.put(itm.getFkByItemId().getRid().intValue(), dangerItmList);
                }
            }

            for(TdTjBhk tjBhk : bhkList){
                dangerBhkService.excuteTdTjBhkData(dangerBhkMap, tjBhk, subBhkMap, itemsMap,
                        null == badrsnsMap ? null : badrsnsMap.get(tjBhk.getRid().intValue()));
            }
        }catch(Exception e){
            log.error(e.getMessage());
        }
    }


    /** 获取危急值体检主表数据 */
    private List<TdZwyjDangerBhk> findDangerBhk(Integer orgId, List<String> bhkCodeList){
        return dangerBhkService.selectListByOrgIdAndbhkCode(orgId, bhkCodeList);
    }

    /** 查询未缺项 定量项目 且危急值项目维护中存在数据的体检子表集合 */
    private List<TdTjBhksub> findSubBhks(List<Integer> bhkIds){
        return subBhkService.selectDangerValSubBhkList(bhkIds);
    }

    /** 查询符合条件的体检主表数据 */
    private List<TdTjBhk> findTdtjBhks(){
        //避免时间转换异常
        if(StringUtils.isNotBlank(startDate)){
            startDate = startDate.trim();
            Date date = DateUtils.parseDate(startDate);
            if(null == date){
                startDate = null;
            }
        }else{
            startDate = null;
        }
        return this.bhkService.selectDangerValToolBhks(dataSize, startDate);
    }
}

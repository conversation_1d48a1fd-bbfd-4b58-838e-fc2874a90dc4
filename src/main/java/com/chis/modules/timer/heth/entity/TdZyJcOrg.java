package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2022-09-06,TdZyJcOrg
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZY_JC_ORG")
@KeySequence(value = "TD_ZY_JC_ORG_SEQ",clazz = Integer.class)
public class TdZyJcOrg extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZyUnitbasicinfo fkByMainId;

    @TableField("UNIT_NAME")
    private String unitName;

    @TableField("REPORT_NO")
    private String reportNo;

    @TableField("CREDIT_CODE")
    private String creditCode;

    @TableField("CONTACT_INFO")
    private String contactInfo;


    public TdZyJcOrg(Integer rid) {
        super(rid);
    }


}

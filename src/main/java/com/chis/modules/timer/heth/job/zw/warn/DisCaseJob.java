package com.chis.modules.timer.heth.job.zw.warn;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.ObjectUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.timer.heth.entity.TbZwBeginDateRcd;
import com.chis.modules.timer.heth.entity.TbZwWarnModel;
import com.chis.modules.timer.heth.entity.TdZwWarnInfo;
import com.chis.modules.timer.heth.entity.TdZwWarnPsns;
import com.chis.modules.timer.heth.pojo.bo.warn.DisCaseBusDataBO;
import com.chis.modules.timer.heth.pojo.bo.warn.DisCaseCrptBO;
import com.chis.modules.timer.heth.pojo.bo.warn.DisCaseWarnModelBO;
import com.chis.modules.timer.heth.service.DisCaseJobService;
import com.chis.modules.timer.heth.service.TbZwBeginDateRcdService;
import com.chis.modules.timer.heth.service.TbZwWarnModelService;
import com.chis.modules.timer.heth.service.TdZwWarnInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.util.*;

@Slf4j
@Component
public abstract class DisCaseJob {

    @Autowired
    protected DisCaseJobService disCaseJobService;

    @Autowired
    protected TbZwWarnModelService warnModelService;

    @Autowired
    protected TbZwBeginDateRcdService beginDateRcdService;

    @Autowired
    protected TdZwWarnInfoService warnInfoService;

    /**
     * 配置的起始日期
     */
    @Value("${warn-timer.zwWarn.beginDate}")
    private String beginDateStr;
    protected Date beginDate;

    /**
     * 配置的最大地区
     */
    @Value("${warn-timer.zwWarn.zoneCode}")
    protected String zoneGbMax;

    /**
     * 预警类型
     * <pre>1: 疑似职业病例数</pre>
     * <pre>2: 职业病例数</pre>
     * <pre>3: 疑似职业病诊断不及时</pre>
     * <pre>4: 疑似职业病报告卡不及时</pre>
     * <pre>5: 职业病报告卡不及时</pre>
     */
    private Integer warnType;
    /**
     * 业务机构类型
     * <pre>1: 用人单位</pre>
     * <pre>2: 检查机构</pre>
     * <pre>3: 诊断机构</pre>
     */
    private Integer busType;
    /**
     * 预警级别及预警规则
     * <pre>key: 预警级别</pre>
     * <pre>value: 预警规则</pre>
     */
    protected Map<Integer, DisCaseWarnModelBO> warnModelBOMap;
    /**
     * 单位&预警级别及起始日期
     * <pre>key: 单位&预警级别</pre>
     * <pre>value: 起始日期</pre>
     */
    protected Map<String, Date> warnStartDateMap;
    /**
     * 所有单位
     */
    protected Map<Integer, DisCaseCrptBO> crptBOMap;

    public void init() {
        if (StringUtils.isBlank(this.beginDateStr)) {
            throw new RuntimeException("配置的起始日期(warn-timer.zwWarn.beginDate)不能为空！");
        }
        try {
            if (StringUtils.isNotBlank(this.beginDateStr)) {
                this.beginDate = DateUtils.parseDate(this.beginDateStr, "yyyy-MM-dd");
            }
        } catch (ParseException e) {
            throw new RuntimeException("配置的起始日期(warn-timer.zwWarn.beginDate)格式错误！");
        }
        setWarnTypeAndBusType();
        loadWarnRule();
        loadWarnStartDate();
        pakCrptBOMap();
    }

    /**
     * 设置预警类型及业务类型
     */
    abstract void setWarnTypeAndBusType();

    /**
     * 加载预警规则
     */
    private void loadWarnRule() {
        this.warnModelBOMap = new HashMap<>();
        List<TbZwWarnModel> zwWarnModelList = findZwWarnModelList();
        if (ObjectUtils.isEmpty(zwWarnModelList)) {
            return;
        }
        for (TbZwWarnModel tbZwWarnModel : zwWarnModelList) {
            Integer rid = tbZwWarnModel.getRid();
            Integer warnLevel = tbZwWarnModel.getWarnLevel();
            Integer geInd = tbZwWarnModel.getGeInd();
            Integer gtInd = tbZwWarnModel.getGtInd();
            if (rid == null || warnLevel == null || (geInd == null && gtInd == null)) {
                continue;
            }
            //下限
            int lowerLimit;
            if (geInd != null) {
                lowerLimit = geInd;
            } else {
                lowerLimit = gtInd + 1;
            }
            //监测周期天数
            Integer ifCycleTime = tbZwWarnModel.getIfCycleTime();
            Integer cycleDays = tbZwWarnModel.getCycleDays();
            if (cycleDays == null) {
                cycleDays = 0;
            }
            if (!new Integer(1).equals(ifCycleTime) || cycleDays <= 0) {
                cycleDays = null;
            }
            this.warnModelBOMap.put(warnLevel, new DisCaseWarnModelBO(rid, warnLevel, lowerLimit, cycleDays));
        }
    }

    /**
     * 查询预警规则
     *
     * @return 预警规则
     */
    private List<TbZwWarnModel> findZwWarnModelList() {
        return this.warnModelService.selectListByWarnType(this.warnType);
    }

    /**
     * 加载预警起始日期
     */
    private void loadWarnStartDate() {
        this.warnStartDateMap = new HashMap<>();
        List<TbZwBeginDateRcd> zwBeginDateRcdList = findZwBeginDateRcdList();
        if (ObjectUtils.isEmpty(zwBeginDateRcdList)) {
            return;
        }
        for (TbZwBeginDateRcd zwBeginDateRcd : zwBeginDateRcdList) {
            Integer busId = zwBeginDateRcd.getBusId();
            Integer warnUnit = zwBeginDateRcd.getWarnUnit();
            Date beginDate = zwBeginDateRcd.getBeginDate();
            if (busId == null || warnUnit == null || beginDate == null) {
                continue;
            }
            this.warnStartDateMap.put(busId + "&" + warnUnit, beginDate);
        }
    }

    /**
     * 查询预警起始日期记录
     *
     * @return 预警起始日期记录
     */
    private List<TbZwBeginDateRcd> findZwBeginDateRcdList() {
        return this.beginDateRcdService.selectListByWarnTypeAndBusType(this.warnType, this.busType);
    }

    /**
     * 封装单位BO
     */
    private void pakCrptBOMap() {
        this.crptBOMap = new HashMap<>();
        List<DisCaseBusDataBO> busDataList = findBusDataList();
        if (ObjectUtils.isEmpty(busDataList)) {
            return;
        }
        for (DisCaseBusDataBO busDataBO : busDataList) {
            Integer id = busDataBO.getId();
            Integer orgId = busDataBO.getOrgId();
            String orgName = busDataBO.getOrgName();
            Integer zoneId = busDataBO.getZoneId();
            String zoneName = busDataBO.getZoneName();
            Date date = busDataBO.getDate();
            if (id == null || orgId == null || zoneId == null || date == null) {
                continue;
            }
            if (!this.crptBOMap.containsKey(orgId)) {
                this.crptBOMap.put(orgId, pakDisCaseCrptBO(orgId, orgName, zoneId, zoneName));
            }
            for (Integer warnType : this.crptBOMap.get(orgId).getBusDataMap().keySet()) {
                Date beginDate = this.crptBOMap.get(orgId).getBeginDateMap().get(warnType);
                if (date.before(beginDate)) {
                    continue;
                }
                this.crptBOMap.get(orgId).getBusDataMap().get(warnType).add(busDataBO);
            }
        }
    }

    /**
     * 查询业务数据(业务日期正序)
     *
     * @return 业务数据
     */
    abstract List<DisCaseBusDataBO> findBusDataList();

    /**
     * 封装新单位BO
     *
     * @param orgId  单位ID
     * @param zoneId 单位地区ID
     * @return 单位BO
     */
    private DisCaseCrptBO pakDisCaseCrptBO(Integer orgId, String orgName, Integer zoneId, String zoneName) {
        DisCaseCrptBO disCaseCrptBO = new DisCaseCrptBO(orgId, orgName, zoneId, zoneName);
        Map<Integer, Date> beginDateMap = disCaseCrptBO.getBeginDateMap();
        Map<Integer, List<DisCaseBusDataBO>> busDataMap = disCaseCrptBO.getBusDataMap();
        Set<Integer> warnTypeSet = this.warnModelBOMap.keySet();
        for (Integer warnType : warnTypeSet) {
            Date beginDate = this.warnStartDateMap.get(orgId + "&" + warnType);
            if (beginDate == null) {
                beginDate = this.beginDate;
            }
            beginDateMap.put(warnType, beginDate);
            busDataMap.put(warnType, new LinkedList<>());
        }
        return disCaseCrptBO;
    }

    /**
     * 开始预警
     */
    public void warnStart() {
        try {
            init();
            List<TdZwWarnInfo> saveOrUpdateList = new LinkedList<>();
            //遍历单位BO及其预警级别对应业务数据Map
            this.crptBOMap.forEach((crptId, crptBO) -> crptBO.getBusDataMap().forEach((warnLevel, busDataList) -> warnSingle(crptBO, warnLevel, busDataList, saveOrUpdateList)));
            //存储
            this.warnInfoService.saveOrUpdateTdZwWarnInfoWithoutCompareSub(saveOrUpdateList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 根据单位以及预警级别
     *
     * @param crptBO      单位BO
     * @param warnLevel   预警级别
     * @param busDataList 业务数据
     */
    private void warnSingle(DisCaseCrptBO crptBO, Integer warnLevel,
                            List<DisCaseBusDataBO> busDataList,
                            List<TdZwWarnInfo> saveOrUpdateList) {
        //避免出现县级查询出存在预警主表 到市级时不重新查询的情况 因pakWarnInfoMap中不为空会return
        crptBO.setZwWarnInfoMap(new HashMap<>());
        DisCaseWarnModelBO warnModelBO = this.warnModelBOMap.get(warnLevel);
        //是否周期性预警
        boolean isCycle = warnModelBO.getCycleDays() != null;
        List<List<DisCaseBusDataBO>> allPossibleList = new LinkedList<>();
        DisCaseBusDataBO lastBusDataBO = null;
        if (isCycle) {
            pakAllPossibleListByCycle(allPossibleList, busDataList, warnModelBO.getCycleDays());
            if (!CollectionUtils.isEmpty(busDataList)) {
                lastBusDataBO = busDataList.get(busDataList.size() - 1);
            }
        } else {
            allPossibleList.add(busDataList);
        }
        Integer lowerLimit = warnModelBO.getLowerLimit();
        Date nowDate = DateUtils.parseDate(DateUtils.getDate());
        for (List<DisCaseBusDataBO> disCaseBusDataBOS : allPossibleList) {
            int num = disCaseBusDataBOS.size();
            if (num < lowerLimit) {
                continue;
            }
            //需要预警
            //监测开始日期
            Date startDate = disCaseBusDataBOS.get(0).getDate();
            //监测结束日期
            Date endDate = null;
            if (isCycle) {
                endDate = DateUtils.addDays(startDate, warnModelBO.getCycleDays());
            }
            if (endDate == null || endDate.after(new Date())) {
                endDate = nowDate;
            }
            String warnCont = pakWarnCont(warnLevel, crptBO.getZoneName(), startDate, endDate, crptBO.getCrptName(), num);

            TdZwWarnInfo warnInfo = new TdZwWarnInfo();
            warnInfo.setFkByModelId(new TbZwWarnModel(warnModelBO.getRid()));
            warnInfo.setFkByWarnZone(new TsZone(crptBO.getZoneId()));
            warnInfo.setBusType(this.busType);
            warnInfo.setBusId(crptBO.getCrptId());
            warnInfo.setWarnCont(warnCont);
            warnInfo.setHappenDate(new Date());
            warnInfo.setJcBeginDate(startDate);
            warnInfo.setJcEndDate(endDate);
            warnInfo.setHappenNum(num);
            warnInfo.setStateMark(0);
            warnInfo.setViewLevel(warnLevel);
            warnInfo.setCreateDate(new Date());
            warnInfo.setCreateManid(1);
            warnInfo.setModifyDate(new Date());
            warnInfo.setModifyManid(1);

            saveOrUpdateList.add(warnInfo);

            pakWarnInfoMap(crptBO, warnModelBO.getRid());
            //根据开始日期查找是否存在有未处置的预警
            String startDateStr = DateUtils.formatDate(startDate, "yyyy-MM-dd");
            //非周期性的 待处置的预警仅会有一条
            if (!isCycle) {
                startDateStr = crptBO.getZwWarnInfoMap().keySet().stream().findFirst().orElse("");
            }
            TdZwWarnInfo warnInfo1 = crptBO.getZwWarnInfoMap().get(startDateStr);
            boolean needFillPsnList = true;
            if (warnInfo1 != null && warnInfo1.getRid() != null) {
                warnInfo.setRid(warnInfo1.getRid());
                //没有差异 不需要更新子表
                if (!this.ifDifference(disCaseBusDataBOS, warnInfo1.getPsnList())) {
                    needFillPsnList = false;
                }
            }
            if (needFillPsnList) {
                this.fillPsnList(warnInfo, disCaseBusDataBOS);
            }

            DisCaseBusDataBO cycleLastBusDataBO = disCaseBusDataBOS.get(num - 1);
            //当前周期最后一条数据为当前单位最后一条数据，则后续周期不再计算
            if (lastBusDataBO != null && lastBusDataBO == cycleLastBusDataBO) {
                return;
            }
        }
    }

    /**
     * <p>方法描述：子表填充 </p>
     * pw 2023/11/11
     **/
    private void fillPsnList(TdZwWarnInfo warnInfo, List<DisCaseBusDataBO> disCaseBusDataBOS) {
        List<TdZwWarnPsns> psnList = new ArrayList<>();
        warnInfo.setPsnList(psnList);
        if (CollectionUtils.isEmpty(disCaseBusDataBOS)) {
            return;
        }
        for (DisCaseBusDataBO dataBO : disCaseBusDataBOS) {
            TdZwWarnPsns warnPsn = new TdZwWarnPsns();
            warnPsn.setFkByMainId(warnInfo);
            warnPsn.setBusId(dataBO.getId());
            warnPsn.setRcvDate(dataBO.getDate());
            TsSimpleCode disSimpleCode = null == dataBO.getDisId() ? null : new TsSimpleCode(dataBO.getDisId());
            warnPsn.setFkByDisId(disSimpleCode);
            warnPsn.setCreateDate(new Date());
            warnPsn.setCreateManid(0);
            psnList.add(warnPsn);
        }

    }

    /**
     * <p>方法描述：判断子表是否有差异 </p>
     * pw 2023/11/11
     **/
    private boolean ifDifference(List<DisCaseBusDataBO> disCaseBusDataBOS, List<TdZwWarnPsns> psnList) {
        int dataSize = CollectionUtils.isEmpty(disCaseBusDataBOS) ? 0 : disCaseBusDataBOS.size();
        int psnSize = CollectionUtils.isEmpty(psnList) ? 0 : psnList.size();
        if (dataSize != psnSize) {
            return true;
        }
        if (CollectionUtils.isEmpty(psnList)) {
            return true;
        }
        Set<String> differenceSet = new HashSet<>();
        for (DisCaseBusDataBO dataBO : disCaseBusDataBOS) {
            Integer id = dataBO.getId();
            Integer disId = dataBO.getDisId();
            if (null == id) {
                continue;
            }
            if (null == disId) {
                disId = 0;
            }
            differenceSet.add(id + "&" + disId);
        }
        for (TdZwWarnPsns warnPsn : psnList) {
            Integer disId = null == warnPsn.getFkByDisId() ? 0 : warnPsn.getFkByDisId().getRid();
            String key = warnPsn.getBusId() + "&" + disId;
            if (!differenceSet.contains(key)) {
                return true;
            }
            differenceSet.remove(key);
        }
        return !CollectionUtils.isEmpty(differenceSet);
    }

    /**
     * 封装所有周期
     *
     * @param allPossibleList 所有周期业务数据集合
     * @param busDataList     业务数据集合
     * @param cycleDays       周期
     */
    private void pakAllPossibleListByCycle(List<List<DisCaseBusDataBO>> allPossibleList,
                                           List<DisCaseBusDataBO> busDataList,
                                           int cycleDays) {
        int index = 0;
        Date startDate;
        List<DisCaseBusDataBO> tempPossibleList = new LinkedList<>();
        for (int i = 0; i < busDataList.size(); i++) {
            startDate = busDataList.get(i).getDate();
            Date endDate = DateUtils.addDays(startDate, cycleDays);
            index = findCombinationsHelper(busDataList, tempPossibleList, endDate, index);
            allPossibleList.add(new LinkedList<>(tempPossibleList));
            i = findCombinationsHelper(tempPossibleList, startDate, i);
        }
    }

    /**
     * 步进周期开始日期
     *
     * @param tempPossibleList 临时周期业务数据集合
     * @param startDate        周期开始日期
     * @param i                业务数据List开始数据下标
     * @return 业务数据List开始数据下标
     */
    public static int findCombinationsHelper(List<DisCaseBusDataBO> tempPossibleList, Date startDate, int i) {
        if (CollectionUtils.isEmpty(tempPossibleList)) {
            return i;
        }
        Iterator<DisCaseBusDataBO> iterator = tempPossibleList.iterator();
        while (iterator.hasNext()) {
            if (iterator.next().compareDate(startDate) != 0) {
                return i - 1;
            }
            i++;
            iterator.remove();
        }
        return i - 1;
    }

    /**
     * 根据周期开始日期以及结束日期放入业务数据
     *
     * @param busDataList  业务数据集合
     * @param tempDateList 临时周期业务数据集合
     * @param endDate      周期结束日期
     * @param j            标记
     * @return 标记
     */
    public static Integer findCombinationsHelper(List<DisCaseBusDataBO> busDataList,
                                                 List<DisCaseBusDataBO> tempDateList,
                                                 Date endDate, Integer j) {
        if (busDataList.size() <= j) {
            return j;
        }
        DisCaseBusDataBO busDataBO = busDataList.get(j);
        if (busDataBO.compareDate(endDate) > 0) {
            return j;
        }
        tempDateList.add(busDataBO);
        return findCombinationsHelper(busDataList, tempDateList, endDate, ++j);
    }

    /**
     * 按监测开始日期封装未处置数据Map
     *
     * @param crptBO  单位BO
     * @param modelId 预警模型ID
     */
    private void pakWarnInfoMap(DisCaseCrptBO crptBO, Integer modelId) {
        if (!CollectionUtils.isEmpty(crptBO.getZwWarnInfoMap())) {
            return;
        }
        Integer crptId = crptBO.getCrptId();
        crptBO.setZwWarnInfoMap(new HashMap<>());
        Map<String, TdZwWarnInfo> warnInfoMap = crptBO.getZwWarnInfoMap();
        List<TdZwWarnInfo> warnInfoList = findZwWarnInfoList(modelId, crptId);
        for (TdZwWarnInfo warnInfo : warnInfoList) {
            Integer rid = warnInfo.getRid();
            Date jcBeginDate = warnInfo.getJcBeginDate();
            if (rid == null || jcBeginDate == null) {
                continue;
            }
            String key = DateUtils.formatDate(jcBeginDate, "yyyy-MM-dd");
            warnInfoMap.put(key, warnInfo);
        }
    }

    /**
     * 查询预警起始日期记录
     *
     * @return 预警起始日期记录
     */
    private List<TdZwWarnInfo> findZwWarnInfoList(Integer modelId, Integer crptId) {
        List<TdZwWarnInfo> warnInfoList = this.warnInfoService.selectNotDisposalList(modelId, this.busType, crptId);
        if (CollectionUtils.isEmpty(warnInfoList)) {
            return new ArrayList<>();
        }
        return warnInfoList;
    }

    private String pakWarnCont(Integer warnLevel, String zoneName, Date startDate, Date endDate, String crptName, Integer num) {
        String title = "职业病病例数";
        if (1 == this.warnType) {
            title = "疑似职业病病例数";
        }
        String msg = "<预警级别>风险预警：<用人单位地区>在<开始日期>到<结束日期>期间内发现<用人单位>的<标题>为<例数>例，请及时关注！";
        if (new Integer(3).equals(warnLevel)){
            msg = msg.replace("<预警级别>", "省级");
        }else if (new Integer(2).equals(warnLevel)) {
            msg = msg.replace("<预警级别>", "市级");
        } else {
            msg = msg.replace("<预警级别>", "区县");
        }
        zoneName = StringUtils.isBlank(zoneName) ? "" : zoneName.substring(zoneName.indexOf("_") + 1).replace("_", "");
        msg = msg.replace("<用人单位地区>", zoneName);
        msg = msg.replace("<开始日期>", DateUtils.formatDate(startDate, "yyyy-MM-dd"));
        msg = msg.replace("<结束日期>", DateUtils.formatDate(endDate, "yyyy-MM-dd"));
        msg = msg.replace("<用人单位>", crptName);
        msg = msg.replace("<例数>", num + "");
        msg = msg.replace("<标题>", title);
        return msg;
    }

    protected void setWarnType(Integer warnType) {
        this.warnType = warnType;
    }

    protected void setBusType(Integer busType) {
        this.busType = busType;
    }
}

package com.chis.modules.timer.heth.job;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.service.TsSimpleCodeService;
import com.chis.modules.timer.heth.logic.HearingRulePO;
import com.chis.modules.timer.heth.service.ChestAndHearingRstService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.*;

/**
 * 计算胸片和电测听的结论
 */
@Slf4j
@Component
public class ChestAndHearingRstJob {

    @Value("${heth-timer.chest-hearing-rst.size}")
    private Integer size;
    @Value("${heth-timer.chest-hearing-rst.bhk-date.start}")
    private String bhkStartDate;
    @Value("${heth-timer.chest-hearing-rst.bhk-date.end}")
    private String bhkEndDate;
    @Value("${heth-timer.chest-hearing-rst.rpt-print-date.start}")
    private String rptPrintStartDate;
    @Value("${heth-timer.chest-hearing-rst.rpt-print-date.end}")
    private String rptPrintEndDate;

    @Autowired
    private TsSimpleCodeService simpleCodeService;
    @Autowired
    private ChestAndHearingRstService chestAndHearingRstService;

    /**
     * 定时任务，用于计算胸片和电测听的结论。
     * 任务执行包括以下步骤：
     * 1. 校验配置的日期格式是否正确。
     * 2. 查询电测听码表，确保配置完整。
     * 3. 使用线程池执行胸片和电测听结论的计算任务。
     */
    @Scheduled(cron = "${heth-timer.chest-hearing-rst.cron}")
    public void start() {
        long startTimeMillis = System.currentTimeMillis();
        log.info("计算胸片&电测听结论-开始");

        // 校验日期格式
        if (!verifyDateFormats()) {
            return;
        }

        // 查询电测听码表5622
        List<TsSimpleCode> simpleCodeList = this.simpleCodeService.findTsSimpleCodeList("5622");
        Map<String, HearingRulePO> hearingRuleMap = new HashMap<>();
        List<Integer> hearingRstRidList = new ArrayList<>();
        if (isConfigIncomplete(simpleCodeList, hearingRuleMap, hearingRstRidList)) {
            return;
        }

        ExecutorService poolExecutor = new ThreadPoolExecutor(
                2, 2, 100L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(1), new ThreadPoolExecutor.CallerRunsPolicy()
        );
        try {
            // 异步执行胸片结论计算任务
            CompletableFuture<Void> chestFuture = CompletableFuture.runAsync(this::dealChestRst, poolExecutor);
            // 异步执行电测听结论计算任务
            Integer finalNormalRid = hearingRstRidList.get(0);
            Integer finalAbnormalRid = hearingRstRidList.get(1);
            CompletableFuture<Void> hearingFuture = CompletableFuture.runAsync(() ->
                    dealHearingRst(hearingRuleMap, finalNormalRid, finalAbnormalRid), poolExecutor
            );
            // 等待所有任务完成
            CompletableFuture.allOf(chestFuture, hearingFuture).join();
        } catch (Exception e) {
            log.error("任务执行过程中发生异常: {}", e.getMessage(), e);
        } finally {
            shutdownExecutorGracefully(poolExecutor);
        }

        long nowTimeMillis = System.currentTimeMillis();
        log.info("计算胸片&电测听结论-已完成, 共耗时: {}毫秒", nowTimeMillis - startTimeMillis);
    }

    /**
     * 验证日期格式是否正确。
     *
     * @return 如果日期格式正确返回true，否则返回false。
     */
    private boolean verifyDateFormats() {
        boolean success = true;
        if (this.size == null) {
            log.error("计算胸片&电测听结论-配置的单次查询条数(heth-timer.chest-hearing-rst.size)不能为空！");
            success = false;
        }
        try {
            if (StringUtils.isNotBlank(this.bhkStartDate)) {
                DateUtils.parseDate(this.bhkStartDate, "yyyy-MM-dd");
            }
        } catch (ParseException e) {
            log.error("计算胸片&电测听结论-配置的体检日期起始日期(heth-timer.chest-hearing-rst.bhk-date.start)格式错误！");
            success = false;
        }
        try {
            if (StringUtils.isNotBlank(this.bhkEndDate)) {
                DateUtils.parseDate(this.bhkEndDate, "yyyy-MM-dd");
            }
        } catch (ParseException e) {
            log.error("计算胸片&电测听结论-配置的体检日期结束日期(heth-timer.chest-hearing-rst.bhk-date.end)格式错误！");
            success = false;
        }
        try {
            if (StringUtils.isNotBlank(this.rptPrintStartDate)) {
                DateUtils.parseDate(this.rptPrintStartDate, "yyyy-MM-dd");
            }
        } catch (ParseException e) {
            log.error("计算胸片&电测听结论-配置的报告打印日期起始日期(heth-timer.chest-hearing-rst.rpt-print-date.start)格式错误！");
            success = false;
        }
        try {
            if (StringUtils.isNotBlank(this.rptPrintEndDate)) {
                DateUtils.parseDate(this.rptPrintEndDate, "yyyy-MM-dd");
            } else {
                this.rptPrintEndDate = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
            }
        } catch (ParseException e) {
            log.error("计算胸片&电测听结论-配置的报告打印日期结束日期(heth-timer.chest-hearing-rst.rpt-print-date.start)格式错误！");
            success = false;
        }
        return success;
    }

    /**
     * 检查电测听码表配置是否完整。
     *
     * @param simpleCodeList 电测听码表
     * @return 如果配置完整返回false，否则返回true。
     */
    private boolean isConfigIncomplete(List<TsSimpleCode> simpleCodeList,
                                       Map<String, HearingRulePO> hearingRuleMap,
                                       List<Integer> hearingRstRidList) {
        if (CollectionUtils.isEmpty(simpleCodeList)) {
            log.error("计算胸片&电测听结论-电测听码表5622未维护，请检查！");
            return true;
        }
        Integer normalRid = null;
        Integer abnormalRid = null;
        String extends3 = "";
        for (TsSimpleCode simpleCode : simpleCodeList) {
            if (normalRid == null && "1".equals(simpleCode.getExtends1())) {
                normalRid = simpleCode.getRid();
            }
            if (abnormalRid == null && "2".equals(simpleCode.getExtends1())) {
                abnormalRid = simpleCode.getRid();
                extends3 = StringUtils.objectToString(simpleCode.getExtends3());
            }
            if (normalRid != null && abnormalRid != null) {
                break;
            }
        }
        if (normalRid == null || abnormalRid == null) {
            log.error("计算胸片&电测听结论-电测听结论码表5622扩展字段1未找到1（正常）、2（异常）标记，请检查！");
            return true;
        }
        hearingRstRidList.add(normalRid);
        hearingRstRidList.add(abnormalRid);
        // 异常扩展字段3为空无需处理
        if (StringUtils.isBlank(extends3)) {
            log.error("计算胸片&电测听结论-电测听码表5622扩展字段3对应的异常项目结果未配置，请检查！");
            return true;
        }
        
        // 判断extends3是否包含@符号，决定解析模式
        if (extends3.contains("@")) {
            // 模式1：包含@符号，使用原来的解析逻辑
            return parseExtends3WithAtSymbol(extends3, hearingRuleMap);
        } else {
            // 模式2：不包含@符号，按英文逗号分隔解析项目编码
            return parseExtends3WithComma(extends3, hearingRuleMap);
        }
    }
    
    /**
     * 解析包含@符号的extends3（原有模式）
     * extends3的格式为：体检项目编码@符号@结果；体检项目编码@符号@结果，例如11244@>=@40；11242@>@25
     */
    private boolean parseExtends3WithAtSymbol(String extends3, Map<String, HearingRulePO> hearingRuleMap) {
        String error = "计算胸片&电测听结论-电测听码表5622异常项目配置的扩展字段3格式异常，请检查！";
        try {
            List<String> allList = StringUtils.string2list(extends3, "；");
            for (String s : allList) {
                List<String> list = StringUtils.string2list(s, "@");
                if (CollectionUtils.isEmpty(list) || list.size() != 3) {
                    log.error(error);
                    return true;
                }
                // 体检项目编码为空
                String itemCode = StringUtils.objectToString(list.get(0));
                // 未知符号
                String sign = StringUtils.objectToString(list.get(1));
                // 结果不是数字
                String rst = StringUtils.objectToString(list.get(2));
                if (StringUtils.isBlank(itemCode) || StringUtils.isBlank(sign) || StringUtils.isBlank(rst)) {
                    log.error(error);
                    return true;
                }
                boolean misconfiguration = !">".equals(sign) && !">=".equals(sign) && !"=".equals(sign)
                        && !"<".equals(sign) && !"<=".equals(sign);
                if (misconfiguration) {
                    log.error(error);
                    return true;
                }
                BigDecimal bigDecimal = new BigDecimal(StringUtils.objectToString(list.get(2)));
                HearingRulePO hearingRulePO = new HearingRulePO();
                hearingRulePO.setItemCode(StringUtils.objectToString(list.get(0)));
                hearingRulePO.setSign(StringUtils.objectToString(list.get(1)));
                hearingRulePO.setRst(bigDecimal);
                hearingRulePO.setUseRgltag(false); // 标记使用原有模式
                hearingRuleMap.put(hearingRulePO.getItemCode(), hearingRulePO);
            }
        } catch (Exception e) {
            log.error(error);
            return true;
        }
        return false;
    }
    
    /**
     * 解析不包含@符号的extends3（新模式）
     * extends3的格式为：体检项目编码,体检项目编码，例如11244,11242
     */
    private boolean parseExtends3WithComma(String extends3, Map<String, HearingRulePO> hearingRuleMap) {
        String error = "计算胸片&电测听结论-电测听码表5622异常项目配置的扩展字段3格式异常，请检查！";
        try {
            List<String> itemCodeList = StringUtils.string2list(extends3, "；");
            if (CollectionUtils.isEmpty(itemCodeList)) {
                log.error(error);
                return true;
            }
            for (String itemCode : itemCodeList) {
                String code = StringUtils.objectToString(itemCode).trim();
                if (StringUtils.isBlank(code)) {
                    log.error(error);
                    return true;
                }
                HearingRulePO hearingRulePO = new HearingRulePO();
                hearingRulePO.setItemCode(code);
                hearingRulePO.setUseRgltag(true); // 标记使用新模式（根据RGLTAG判断）
                hearingRuleMap.put(hearingRulePO.getItemCode(), hearingRulePO);
            }
        } catch (Exception e) {
            log.error(error);
            return true;
        }
        return false;
    }

    /**
     * 处理胸片结果的逻辑方法。
     */
    private void dealChestRst() {
        try {
            this.chestAndHearingRstService.dealChestRst(
                    this.bhkStartDate, this.bhkEndDate, this.rptPrintStartDate, this.rptPrintEndDate
            );
        } catch (Exception e) {
            e.printStackTrace();
            log.error("计算胸片结论-失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理电测听结果的逻辑方法。
     *
     * @param hearingRuleMap   电测听规则映射，键为体检项目编码，值为电测听规则对象
     * @param finalNormalRid   正常结果的标识RID
     * @param finalAbnormalRid 异常结果的标识RID
     */
    private void dealHearingRst(Map<String, HearingRulePO> hearingRuleMap,
                                Integer finalNormalRid,
                                Integer finalAbnormalRid) {
        try {
            this.chestAndHearingRstService.dealHearingRst(
                    hearingRuleMap, finalNormalRid, finalAbnormalRid,
                    this.bhkStartDate, this.bhkEndDate, this.rptPrintStartDate, this.rptPrintEndDate
            );
        } catch (Exception e) {
            e.printStackTrace();
            log.error("计算电测听结论-失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 优雅地关闭ExecutorService。
     * 这个方法旨在安全地关闭ExecutorService，确保所有已提交的任务有机会完成，
     * 同时在超时或中断的情况下能够强制关闭。
     *
     * @param executor 要关闭的ExecutorService实例。
     */
    private void shutdownExecutorGracefully(ExecutorService executor) {
        // 请求ExecutorService关闭，已提交的任务将有机会完成。
        executor.shutdown();
        try {
            // 如果在100毫秒内ExecutorService未能关闭，则尝试立即关闭。
            if (!executor.awaitTermination(100, TimeUnit.MILLISECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            // 如果当前线程被中断，则设置中断状态并尝试立即关闭ExecutorService。
            Thread.currentThread().interrupt();
            executor.shutdownNow();
        }
    }

}

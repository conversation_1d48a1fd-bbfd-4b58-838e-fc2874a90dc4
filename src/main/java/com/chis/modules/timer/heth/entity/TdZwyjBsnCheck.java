package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.TsUnit;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-03,TdZwyjBsnCheck
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_BSN_CHECK")
public class TdZwyjBsnCheck extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwyjBsnBhk fkByMainId;

    @TableField("STATE")
    private String state;

    @TableField("IF_NORMAL")
    private String ifNormal;

    @TableField("COUNTY_SMT_DATE")
    private Date countySmtDate;

    @TableField("COUNTY_AUDIT_ADV")
    private String countyAuditAdv;

    @TableField(value = "COUNTY_CHK_ORGID" , el = "fkByCountyChkOrgid.rid")
    private TsUnit fkByCountyChkOrgid;

    @TableField("CITY_SMT_DATE")
    private Date citySmtDate;

    @TableField("CITY_RST")
    private String cityRst;

    @TableField("CITY_AUDIT_ADV")
    private String cityAuditAdv;

    @TableField(value = "CIYT_CHK_ORGID" , el = "fkByCiytChkOrgid.rid")
    private TsUnit fkByCiytChkOrgid;

    @TableField("PRO_SMT_DATE")
    private Date proSmtDate;

    @TableField("CITY_RST2")
    private String cityRst2;

    @TableField("PRO_AUDIT_ADV")
    private String proAuditAdv;

    @TableField(value = "PRO_CHK_ORGID" , el = "fkByProChkOrgid.rid")
    private TsUnit fkByProChkOrgid;


    public TdZwyjBsnCheck(Integer rid) {
        super(rid);
    }


}

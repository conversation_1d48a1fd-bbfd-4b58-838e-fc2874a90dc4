package com.chis.modules.timer.heth.job;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.comm.utils.ZoneUtil;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.service.TsSimpleCodeService;
import com.chis.modules.timer.heth.entity.*;
import com.chis.modules.timer.heth.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @ClassName OutRangeJob
 * @Description: 是否超范围服务工具
 * <AUTHOR>
 * @Date 2020-11-12
 * @Version V1.0
 **/
@Slf4j
@Component
public class OutRangeJob {

    @Autowired
    private TdTjBhkService bhkService;
    @Autowired
    private TdZwTjorginfoService tdZwTjorginfoService;
    @Autowired
    private TdZwTjorgRecordService tdZwTjorgRecordService;
    @Autowired
    private TdZwTjorgRcdItemService tdZwTjorgRcdItemService;
    @Autowired
    private TdTjBadrsnsService tdTjBadrsnsService;
    @Autowired
    private TdZwyjOtrRcdService tdZwyjOtrRcdService;
    @Autowired
    private TdZwyjOtrBhkService tdZwyjOtrBhkService;
    @Autowired
    private TdZwyjOtrServiceItemService tdZwyjOtrServiceItemService;
    @Autowired
    private TsSimpleCodeService simpleCodeService;

    @Value("${heth-timer.dataSize}")
    private Integer dataSize;
    @Value("${heth-timer.start-date}")
    private String startDate;


    @Scheduled(cron = "${heth-timer.sche-cron.outRangeTime}")
    public void start() {

        System.out.println("====OutRangeJob===start===");
        //所有机构的项目
        Map<Integer,TdZwTjorginfo> map = initTdZwTjorginfo();
        Map<String,TsSimpleCode> itemMap =initItem();

        List<TdTjBhk> bhkList = bhkService.selectOutRangeBhks(dataSize, startDate);

        while ( null != bhkList && bhkList.size() > 0){

            if(!CollectionUtils.isEmpty(bhkList)){
                for(TdTjBhk bhk:bhkList){
                    String errorMsg= "";
                    boolean ifOutZone = true;
                    boolean ifOutItem = false;

                    Set<String> outItemSet = new HashSet<>();
                    String badNames ="";
                    try{

                        //企业提取编码
                        String crptZoneCode = bhk.getCrptZoneCode();
                        //1:查找危害因素
                        List<TdTjBadrsns> badrsnsList = tdTjBadrsnsService.findTdTjBadrsnsExtends2ByBhkId(bhk.getRid());
                        if(!CollectionUtils.isEmpty(badrsnsList)){
                            for(TdTjBadrsns badrsns:badrsnsList){
                                badNames +=","+badrsns.getBadName();
                            }
                        }
                        TdZwTjorginfo tdZwTjorginfo = map.get(bhk.getFkByBhkorgId().getRid());
                        if(null != tdZwTjorginfo && null != tdZwTjorginfo.getTdZwTjorgRecordList()
                                && tdZwTjorginfo.getTdZwTjorgRecordList().size()>0){
                            List<TdZwTjorgRecord> tdZwTjorgRecordList = tdZwTjorginfo.getTdZwTjorgRecordList();



                            for(TdZwTjorgRecord record:tdZwTjorgRecordList){

                                if(crptZoneCode.startsWith(ZoneUtil.zoneSelect(record.getZoneGb()))){
                                    ifOutZone =false;

                                    //日期判断是否在有效区间内
                                    if(null != bhk.getBhkDate()){
                                        if(null !=record.getCertDate()){
                                            if(DateUtils.isDateAfter(DateUtils.parseDate(DateUtils.formatDate(record.getCertDate())),DateUtils.parseDate(DateUtils.formatDate(bhk.getBhkDate())))){
                                                ifOutZone =true;
                                            }
                                        }
                                        if(record.getStateMark().equals("1") && null != record.getLogoutDate()){
                                            if(DateUtils.isDateAfter(DateUtils.parseDate(DateUtils.formatDate(bhk.getBhkDate())),DateUtils.parseDate(DateUtils.formatDate(record.getLogoutDate())))){
                                                ifOutZone =true;
                                            }
                                        }
                                    }

                                    //验证项目
                                    if(!ifOutZone){
                                        Set<String> itemSet = new HashSet<>();
                                        if(!CollectionUtils.isEmpty(record.getTdZwTjorgRcdItemList())){
                                            List<TdZwTjorgRcdItem> items = record.getTdZwTjorgRcdItemList();
                                            for(TdZwTjorgRcdItem rcdItem:items){
                                                itemSet.add(rcdItem.getCodeNo());
                                            }
                                        }

                                        List<TdTjBadrsns> inbadrsns = new ArrayList<>();
                                        if(!CollectionUtils.isEmpty(badrsnsList)){
                                            for(TdTjBadrsns badrsn :badrsnsList){
                                                if(StringUtils.isNotBlank(badrsn.getBadExtends2())){
                                                    if(!itemSet.contains(badrsn.getBadExtends2())) {
                                                        ifOutItem =true;
                                                        inbadrsns.add(badrsn);
                                                    }
                                                }else{
                                                    errorMsg +=badrsn.getBadName()+"危害因素未维护扩展字段2；";
                                                }

                                            }
                                        }
                                        badrsnsList = new ArrayList<>();
                                        badrsnsList.addAll(inbadrsns);
                                    }

                                    if(!ifOutZone && !ifOutItem){
                                        break;
                                    }

                                }

                            }
                            if(!CollectionUtils.isEmpty(badrsnsList)){
                                for(TdTjBadrsns badrsn :badrsnsList){
                                    outItemSet.add(badrsn.getBadExtends2());
                                }
                            }else{
                                ifOutItem =false;
                            }
                        }else{
                            ifOutZone = true;
                        }

                    }catch (Exception e){
                        errorMsg =e.getMessage();
                        log.error(errorMsg);
                    }finally {
                        initOutRange(bhk,outItemSet, ifOutZone, ifOutItem, errorMsg, itemMap,StringUtils.isNotBlank(badNames)?badNames.substring(1):"");
                    }


                }
            }
            bhkList = bhkService.selectOutRangeBhks(dataSize, startDate);

        }
        System.out.print("暂无需要上传的数据！");
    }

    /**
     * @MethodName: initOutRange
     * @Description: 日志处理
     * @Param: [bhk, outItemSet, ifOutZone, ifOutItem]
     * @Return: void
     * @Author: maox
     * @Date: 2020-11-14
    **/
    private void initOutRange(TdTjBhk bhk,Set<String> outItemSet,boolean ifOutZone,boolean ifOutItem,String errorMsg
            ,Map<String,TsSimpleCode> itemMap,String badNames){
        //先保存记录表
        TdZwyjOtrRcd otrRcd = new TdZwyjOtrRcd();
        otrRcd.setBhkCode(bhk.getBhkCode());
        otrRcd.setFkByBhkorgId(bhk.getFkByBhkorgId());

        if(StringUtils.isNotBlank(errorMsg)){
            otrRcd.setIfOutRange("2");
            otrRcd.setProMsg(errorMsg);
        }else{
            if(ifOutZone || ifOutItem){
                otrRcd.setIfOutRange("1");
            }else{
                otrRcd.setIfOutRange("0");
            }
        }
        otrRcd.setCreateDate(new Date());
        otrRcd.setCreateManid(1);
        tdZwyjOtrRcdService.save(otrRcd);

        if(StringUtils.isNotBlank(errorMsg)){
            return;
        }
        //超范围体检记录表
        if(ifOutZone || ifOutItem){
            TdZwyjOtrBhk tdZwyjOtrBhk = new TdZwyjOtrBhk();
            List<TdZwyjOtrServiceItem> items = new ArrayList<>();

            tdZwyjOtrBhk.setBhkCode(bhk.getBhkCode());
            tdZwyjOtrBhk.setFkByBhkorgId(bhk.getFkByBhkorgId());
            TsZone zone = new TsZone();
            zone.setRid(bhk.getCrptZoneId());
            tdZwyjOtrBhk.setFkByCrptZoneId(zone);
            tdZwyjOtrBhk.setFkByCrptId(bhk.getEntrustId());
            tdZwyjOtrBhk.setPersonName(bhk.getPersonName());
            tdZwyjOtrBhk.setFkByCardTypeId(bhk.getFkByCardTypeId());
            tdZwyjOtrBhk.setIdc(bhk.getIdc());
            tdZwyjOtrBhk.setBhkDate(bhk.getBhkDate());
            tdZwyjOtrBhk.setBadrsn(badNames);
            tdZwyjOtrBhk.setHappenDate(new Date());
            tdZwyjOtrBhk.setCreateDate(new Date());
            tdZwyjOtrBhk.setCreateManid(1);

            if(ifOutItem){
                tdZwyjOtrBhk.setOtrType("2");
                for(String codeNo:outItemSet){
                    TdZwyjOtrServiceItem item = new TdZwyjOtrServiceItem();
                    item.setFkByMainId(tdZwyjOtrBhk);
                    item.setFkByServiceId(itemMap.get(codeNo));
                    item.setCreateDate(new Date());
                    item.setCreateManid(1);
                    items.add(item);
                }
            }else{
                tdZwyjOtrBhk.setOtrType("1");
            }

            tdZwyjOtrBhkService.save(tdZwyjOtrBhk);
            if(!CollectionUtils.isEmpty(items)){
                tdZwyjOtrServiceItemService.saveBatch(items);
            }

        }
    }


    /**
     * @MethodName: initTdZwTjorginfo
     * @Description: 查找所有机构信息
     * @Param: []
     * @Return: java.util.Map<java.lang.Integer,com.chis.modules.timer.heth.entity.TdZwTjorginfo>
     * @Author: maox
     * @Date: 2020-11-13
    **/
    private Map<Integer,TdZwTjorginfo> initTdZwTjorginfo(){
        Map<Integer,TdZwTjorginfo> map = new HashMap<>();
        List<TdZwTjorginfo> orgInfoList =tdZwTjorginfoService.selectAllOrgInfoList();
        if(!CollectionUtils.isEmpty(orgInfoList)){
            for(TdZwTjorginfo tdZwTjorginfo:orgInfoList){
                TdZwTjorgRecord record = new TdZwTjorgRecord();
                record.setFkByMainId(tdZwTjorginfo);
                //查找所有备案信息
                List<TdZwTjorgRecord> recordList = tdZwTjorgRecordService.selectTdZwTjorgRecord(tdZwTjorginfo.getRid());
                if(!CollectionUtils.isEmpty(recordList)){
                    for(TdZwTjorgRecord tdZwTjorgRecord:recordList){
                        TdZwTjorgRcdItem rcdItem = new TdZwTjorgRcdItem();
                        rcdItem.setFkByMainId(tdZwTjorgRecord);
                        //查找所有备案项目
                        List<TdZwTjorgRcdItem> rcdItems = tdZwTjorgRcdItemService.selectTdZwTjorgRcdItem(tdZwTjorgRecord.getRid());

                        //备案项目封装至备案信息
                        if(!CollectionUtils.isEmpty(rcdItems)){
                            tdZwTjorgRecord.setTdZwTjorgRcdItemList(rcdItems);
                        }
                    }

                    //备案信息封装至机构
                    tdZwTjorginfo.setTdZwTjorgRecordList(recordList);
                }

                map.put(tdZwTjorginfo.getSrvId(),tdZwTjorginfo);
            }
        }
        return map;
    }

    /**
     * @MethodName: initItem
     * @Description: 项目map
     * @Param: []
     * @Return: java.util.Map<java.lang.String,com.chis.modules.sys.entity.TsSimpleCode>
     * @Author: maox
     * @Date: 2020-11-16
    **/
    private Map<String,TsSimpleCode> initItem(){
        Map<String,TsSimpleCode> map = new HashMap<>();
        List<TsSimpleCode> list =simpleCodeService.findAllTsSimpleCodeList("5018");
        if(!CollectionUtils.isEmpty(list)){
            for(TsSimpleCode code:list){
                map.put(code.getCodeNo(),code);
            }
        }
        return map;
    }


}

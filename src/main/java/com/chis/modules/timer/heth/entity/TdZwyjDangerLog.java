package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-21,TdZwyjDangerLog
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_DANGER_LOG")
@KeySequence(value = "TD_ZWYJ_DANGER_LOG_SEQ",clazz = Integer.class)
public class TdZwyjDangerLog extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "BHK_ID" , el = "fkByBhkId.rid")
    private TdTjBhk fkByBhkId;

    @TableField("DATA_MARK")
    private String dataMark;

    @TableField("ERR_MSG")
    private String errMsg;


    public TdZwyjDangerLog(Integer rid) {
        super(rid);
    }


}

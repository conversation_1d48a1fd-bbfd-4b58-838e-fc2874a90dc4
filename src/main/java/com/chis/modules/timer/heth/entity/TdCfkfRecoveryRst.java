package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-12-28,TdCfkfRecoveryRst
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_CFKF_RECOVERY_RST")
public class TdCfkfRecoveryRst extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("ANALY_YEAR")
    private Integer analyYear;

    @TableField("PSN_NO")
    private String psnNo;

    @TableField("PSN_NAME")
    private String psnName;

    @TableField("IDC")
    private String idc;

    @TableField(value = "ZONE_ID" , el = "fkByZoneId.rid")
    private TsZone fkByZoneId;

    @TableField("SITE_NAME")
    private String siteName;

    @TableField("FST_RECOVR_DATE")
    private Date fstRecovrDate;

    @TableField("LST_RECOVR_DATE")
    private Date lstRecovrDate;

    @TableField("HAS_INST_REVOCERY")
    private Integer hasInstRevocery;

    @TableField("HAS_MED_REVOCERY")
    private Integer hasMedRevocery;

    @TableField("HAS_NUTRI_REVOCERY")
    private Integer hasNutriRevocery;

    @TableField("HAS_PSY_REVOCERY")
    private Integer hasPsyRevocery;

    @TableField("HAS_OTHER_REVOCERY")
    private Integer hasOtherRevocery;

    @TableField("REHABI_NUM")
    private Integer rehabiNum;

    @TableField("TOTAL_TIME")
    private Double totalTime;

    @TableField("AVG_TIME")
    private Double avgTime;

    @TableField("FST_BLOOD_OXYGEN")
    private String fstBloodOxygen;

    @TableField("LST_BLOOD_OXYGEN")
    private String lstBloodOxygen;

    @TableField("FST_ADL")
    private String fstAdl;

    @TableField("LST_ADL")
    private String lstAdl;

    @TableField("FST_ANXIETY")
    private String fstAnxiety;

    @TableField("LST_ANXIETY")
    private String lstAnxiety;

    @TableField("FST_DEPRESSION")
    private String fstDepression;

    @TableField("LST_DEPRESSION")
    private String lstDepression;


    public TdCfkfRecoveryRst(Integer rid) {
        super(rid);
    }


}

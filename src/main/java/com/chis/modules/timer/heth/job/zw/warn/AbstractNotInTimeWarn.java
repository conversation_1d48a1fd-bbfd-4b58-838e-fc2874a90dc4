package com.chis.modules.timer.heth.job.zw.warn;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.ObjectCopyUtil;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.timer.heth.entity.TbZwWarnModel;
import com.chis.modules.timer.heth.entity.TdZwWarnInfo;
import com.chis.modules.timer.heth.entity.TdZwWarnPsns;
import com.chis.modules.timer.heth.service.TbZwWarnModelService;
import com.chis.modules.timer.heth.service.TdZwWarnInfoService;
import com.chis.modules.timer.heth.service.TdZwWarnPsnsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 */
@Slf4j
@Service
public abstract class AbstractNotInTimeWarn {

    /**预警起始时间*/
    @Value("${warn-timer.zwWarn.beginDate}")
    protected String beginDate;

    @Value("${warn-timer.zwWarn.zoneCode}")
    protected String zoneCode;
    @Autowired
    private TbZwWarnModelService modelService;

    @Autowired
    private TdZwWarnInfoService warnInfoService;


    /**把区县，市级预警模型信息放在 map 中，方便后面匹配查询 key :1 区县 2：市 3：省级*/
    protected Map<Integer, TbZwWarnModel> warnModelMap;

    /**
    * <p>Description：定时开始初始化 </p>
    * <p>Author： yzz 2023-11-11 </p>
    */
    public void init(){
        //如果预警开始日期为空，则给默认值
        if(StringUtils.isBlank(beginDate)){
            throw new RuntimeException("配置的起始日期(warn-timer.zwWarn.beginDate)不能为空！");
        }
        try {
            DateUtils.parseDate(this.beginDate, "yyyy-MM-dd");
        } catch (ParseException e) {
            throw new RuntimeException("配置的起始日期(warn-timer.zwWarn.beginDate)格式错误！");
        }
        //预警地区
        if(StringUtils.isBlank(zoneCode)){
            throw new RuntimeException("配置的预警地区(warn-timer.zwWarn.zoneCode)不能为空！");
        }
        //预警模型初始化
        List<TbZwWarnModel> warnModeList = getWarnModeList();
        //模型匹配 缓存备用
        if(CollectionUtils.isEmpty(warnModeList)){
            log.error("请先配置"+getWarnName()+"的预警模型！");
            throw new RuntimeException("请先配置"+getWarnName()+"的预警模型！");
        }
        //把区县，市级，省级预警模型信息放在 map 中，方便后面匹配查询
         warnModelMap = warnModeList.stream()
                .collect(Collectors.toMap(TbZwWarnModel::getWarnLevel, v -> v, (p1, p2) -> p1));
    }
    
    /**
    * <p>Description：获取预警模型 </p>
    * <p>Author： yzz 2023-11-11 </p>
    */
    private List<TbZwWarnModel> getWarnModeList(){
        if (null==getWarnType()) {
            log.error("预警类型不能为空！");
        }
        return modelService.selectListByWarnType(getWarnType());
    }

    
    /**
    * <p>Description：匹配人员主表，保存人员表 </p>
    * <p>Author： yzz 2023-11-11 </p>
    */
    public void saveWarnInfo(List<TdZwWarnInfo> warnInfoList){
        warnInfoService.saveWarnInfo(warnInfoList);
    }

    /**
   * <p>Description：拼接预警内容 </p>
   * <p>Author： yzz 2023-11-11 </p>
   */
    public String getWarnCont(TbZwWarnModel model, String zoneName, Date beginDate, Date endDate, String crptName,
                               Integer nums) {
        StringBuffer warnCont = new StringBuffer();
        if (new Integer("1").equals(model.getWarnLevel())) {
            warnCont.append("区县");
        } else if (new Integer("2").equals(model.getWarnLevel())) {
            warnCont.append("市级");
        }else if (new Integer("3").equals(model.getWarnLevel())) {
            warnCont.append("省级");
        }
        warnCont.append("风险预警：").append(zoneName).append("在");
        warnCont.append(DateUtils.getChineseStringDate(beginDate)).append("到")
                .append(DateUtils.getChineseStringDate(endDate));
        warnCont.append("期间内发现").append(crptName).append("的");
        warnCont.append(getWarnName()).append("为").append(nums).append("例");
        warnCont.append("，请及时关注！");
        return warnCont.toString();
    }

    /**
    * <p>Description：预警规则匹配是否失败，只匹配下限</p>
    * <p>Author： yzz 2023-11-11 </p>
    */
    boolean calRuleIfFail(TbZwWarnModel model, int nums) {
        if (null == model) {
            return true;
        }
        Integer geInd = model.getGeInd();
        //是否满足条件
        boolean fail = false;
        if (geInd != null && nums < geInd) {
            fail = true;
        }
        Integer gtInd = model.getGtInd();
        if (gtInd != null && nums <= gtInd) {
            fail = true;
        }
        return fail;
    }

      /**
      * <p>Description： 预警类型</p>
      * <p>Author： yzz 2023-11-11 </p>
      */
    public abstract Integer getWarnType();

    /**
     * <p>Description： 预警类型名称</p>
     * <p>Author： yzz 2023-11-11 </p>
     */
    public abstract String getWarnName();

    /**
    * <p>Description：业务类型 </p>
    * <p>Author： yzz 2023-11-11 </p>
    */
    public abstract Integer getBusType();
}

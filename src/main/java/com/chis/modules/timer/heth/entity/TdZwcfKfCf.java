package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.beans.Transient;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人, 2022-09-02,TdZwcfKfCf
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWCF_KF_CF")
public class TdZwcfKfCf extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID", el = "fkByMainId.rid")
    private TdZwcfPatientInfo fkByMainId;

    @TableField("OPENER_DATE")
    private Date openerDate;

    @TableField("CF_PDF_PATH")
    private String cfPdfPath;

    @TableField(exist = false)
    private List<TdZwcfKfCfOper> kfCfOperList;


    public TdZwcfKfCf(Integer rid) {
        super(rid);
    }


}

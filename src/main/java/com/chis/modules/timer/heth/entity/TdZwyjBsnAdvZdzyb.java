package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnAdvZdzyb
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_BSN_ADV_ZDZYB")
@KeySequence(value ="TD_ZWYJ_BSN_ADV_ZDZYB_SEQ" ,clazz = Integer.class)
public class TdZwyjBsnAdvZdzyb extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "BHK_ID" , el = "fkByBhkId.rid")
    private TdZwyjBsnBhk fkByBhkId;

    @TableField(value = "ADV_ID" , el = "fkByAdvId.rid")
    private TdZwyjBsnCsionPdjl fkByAdvId;

    @TableField(value = "ITEM_ID" , el = "fkByItemId.rid")
    private TbTjItems fkByItemId;


    public TdZwyjBsnAdvZdzyb(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnClusionAdv
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_BSN_CLUSION_ADV")
public class TdZwyjBsnClusionAdv extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdZwyjBsnClusion fkByMainId;

    @TableField(value = "BHKRST_ID" , el = "fkByBhkrstId.rid")
    private TsSimpleCode fkByBhkrstId;


    public TdZwyjBsnClusionAdv(Integer rid) {
        super(rid);
    }


}

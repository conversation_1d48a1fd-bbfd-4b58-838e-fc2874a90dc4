package com.chis.modules.timer.heth.pojo;


import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 *  <p>类描述：放射人员token获取</p>
 * @ClassAuthor hsj 2022-09-05 17:13
 */
@Data
public class TokenReturnPojo {
	private String type;
	private String mess;
	//tokenid
	private String tokenId;
	//过期间隔
	private String validateTime;
	//过期时间
	private Date endDate;

	private Map<String, String> tsSystemParamMap;
}

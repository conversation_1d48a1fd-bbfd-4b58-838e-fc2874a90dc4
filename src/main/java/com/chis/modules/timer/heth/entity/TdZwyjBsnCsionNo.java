package com.chis.modules.timer.heth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnCsionNo
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_ZWYJ_BSN_CSION_NO")
public class TdZwyjBsnCsionNo extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("NO_DESC")
    private String noDesc;

    @TableField(value = "ONGUARD_STATEID" , el = "fkByOnguardStateid.rid")
    private TsSimpleCode fkByOnguardStateid;


    public TdZwyjBsnCsionNo(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.timer.heth.service;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.ObjectCopyUtil;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwWarnInfo;
import com.chis.modules.timer.heth.entity.TdZwWarnPsns;
import com.chis.modules.timer.heth.mapper.TdZwWarnInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人, 2023-11-10,TdZwWarnInfoService
 */
@Service
public class TdZwWarnInfoService extends ZwxBaseServiceImpl<TdZwWarnInfoMapper, TdZwWarnInfo> {

    @Autowired
    private TdZwWarnPsnsService warnPsnsService;

    /**
     * <p>方法描述：预比对完子表信息后调用的 存储预警信息
     * </p>
     * pw 2023/11/11
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateTdZwWarnInfoWithoutCompareSub (List<TdZwWarnInfo> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        //删除子表对应的主表rid集合
        List<Integer> mainRidList = dataList.stream()
                .filter(v -> null != v.getRid() && !CollectionUtils.isEmpty(v.getPsnList()))
                .mapToInt(TdZwWarnInfo::getRid).boxed().collect(Collectors.toList());
        //删除子表
        this.warnPsnsService.delWarnPsnByMainRids(mainRidList);
        List<TdZwWarnPsns> warnPsnsList = new ArrayList<>();
        for (TdZwWarnInfo warnInfo : dataList) {
            List<TdZwWarnPsns> subList = warnInfo.getPsnList();
            if (!CollectionUtils.isEmpty(subList)) {
                warnPsnsList.addAll(subList);
            }
            if (null == warnInfo.getRid()) {
                this.baseMapper.insertEntity(warnInfo);
            } else {
                this.baseMapper.updateFullById(warnInfo);
            }
        }
        if (CollectionUtils.isEmpty(warnPsnsList)) {
            return;
        }
        insertBatchWarnPsnsSplit(warnPsnsList);
    }

    /**
     * 分批插入子表
     *
     * @param warnPsnsList 子表
     */
    private void insertBatchWarnPsnsSplit(List<TdZwWarnPsns> warnPsnsList) {
        List<List<TdZwWarnPsns>> warnPsnsListList = StringUtils.splitListProxy(warnPsnsList, 100);
        if (CollectionUtils.isEmpty(warnPsnsListList)) {
            return;
        }
        for (List<TdZwWarnPsns> zwWarnPsnList : warnPsnsListList) {
            this.warnPsnsService.insertBatch(zwWarnPsnList);
        }
    }


    /**
     * 查询未处置数据
     *
     * @param modelId 模型ID
     * @param busType 业务类型
     * @param busId   业务ID
     * @return 未处置数据
     */
    public List<TdZwWarnInfo> selectNotDisposalList(Integer modelId, Integer busType, Integer busId) {
        return this.baseMapper.selectNotDisposalList(modelId, busType, busId);
    }

    /**
    * <p>Description： </p>
    * <p>Author： yzz 2023-11-11 </p>
    */
    public void saveWarnInfo(List<TdZwWarnInfo> warnInfoList) {
        if(CollectionUtils.isEmpty(warnInfoList)){
            return;
        }
        List<TdZwWarnInfo> warnInfoNewList=new ArrayList<>();
        warnInfoList.forEach(warn->{
            TdZwWarnInfo tdZwWarnInfo;
            //先去预警信息主表中匹配，是否有相同记录
            List<TdZwWarnInfo> cuurWarnInfos = this.selectNotDisposalList(warn.getFkByModelId().getRid(), warn.getBusType(), warn.getBusId());
            //匹配不到 预警主表直接保存
            if(CollectionUtils.isEmpty(cuurWarnInfos)){
                tdZwWarnInfo=warn;
            }else{
                //匹配到 更新
                 tdZwWarnInfo = cuurWarnInfos.get(0);
                try {
                    ObjectCopyUtil.copyPropertiesInclude(warn,tdZwWarnInfo,new String[]{"warnCont","happenDate","jcBeginDate","jcEndDate","happenNum","fkByWarnZone"});
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
            if (CollectionUtils.isEmpty(warn.getPsnList())){
                return;
            }
            for (TdZwWarnPsns warnPsns : warn.getPsnList()) {
                warnPsns.setFkByMainId(tdZwWarnInfo);
            }
            //获取需要删除的记录
            boolean bool = saveWarnPsn(tdZwWarnInfo, warn.getPsnList());
            if(bool){
                tdZwWarnInfo.setPsnList(warn.getPsnList());
            }else{
                tdZwWarnInfo.setPsnList(new ArrayList<>());
            }
            warnInfoNewList.add(tdZwWarnInfo);
        });
        if(CollectionUtils.isEmpty(warnInfoNewList)){
            return;
        }
        this.saveOrUpdateTdZwWarnInfoWithoutCompareSub(warnInfoNewList);
    }


    /**
     * <p>Description：保存子表 </p>
     * <p>Author： yzz 2023-11-11 </p>
     */
    private boolean saveWarnPsn(TdZwWarnInfo warnInfo,List<TdZwWarnPsns> psnList) {
        if (warnInfo.getPsnList() != null && psnList != null && warnInfo.getPsnList().size() != psnList.size()) {
            return true;
        }
        Map<String, TdZwWarnPsns> psnMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(psnList)) {
            psnList.forEach(psn -> {
                StringBuilder sb = new StringBuilder();
                if (psn.getBusId() != null) {
                    sb.append(psn.getBusId());
                }
                if (psn.getRcvDate() != null) {
                    sb.append("&").append(DateUtils.formatDate(psn.getRcvDate()));
                }
                if (psn.getDealDate() != null) {
                    sb.append("&").append(DateUtils.formatDate(psn.getDealDate()));
                }
                if (psn.getFkByDisId() != null && psn.getFkByDisId().getRid() != null) {
                    sb.append("&").append(psn.getFkByDisId().getRid());
                }
                if (!psnMap.containsKey(sb.toString())) {
                    psnMap.put(sb.toString(), psn);
                }
            });
        }

        warnInfo.getPsnList().forEach(psn -> {
            if (psn.getRid() == null) {
                return;
            }
            StringBuilder sb = new StringBuilder();
            if (psn.getBusId() != null) {
                sb.append(psn.getBusId());
            }
            if (psn.getRcvDate() != null) {
                sb.append("&").append(DateUtils.formatDate(psn.getRcvDate()));
            }
            if (psn.getDealDate() != null) {
                sb.append("&").append(DateUtils.formatDate(psn.getDealDate()));
            }
            if (psn.getFkByDisId() != null && psn.getFkByDisId().getRid() != null) {
                sb.append("&").append(psn.getFkByDisId().getRid());
            }
            psnMap.remove(sb.toString());
        });
        return !psnMap.isEmpty();
    }
}

package com.chis.modules.timer.heth.mapper;

import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.timer.heth.entity.TdTjBadrsns;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdTjBadrsnsMapper
 */
@Repository
public interface TdTjBadrsnsMapper extends ZwxBaseMapper<TdTjBadrsns> {

    public List<TdTjBadrsns> findTdTjBadrsnsExtends2ByBhkId(@Param("bhkId") Integer bhkId);

    /** 通过体检记录rid集合获取接触危害因素 */
    List<TdTjBadrsns> findTdTjBadrsnsByBhkIdList(@Param("bhkIdList") List<Integer> bhkIdList);
}

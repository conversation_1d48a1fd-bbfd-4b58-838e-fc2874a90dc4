package com.chis.modules.timer.heth.job.zw.warn;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.ObjectUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.entity.TsSysHoliday;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.service.TsSysHolidayService;
import com.chis.modules.timer.heth.entity.TbZwWarnModel;
import com.chis.modules.timer.heth.entity.TdZwWarnInfo;
import com.chis.modules.timer.heth.entity.TdZwWarnPsns;
import com.chis.modules.timer.heth.pojo.bo.warn.*;
import com.chis.modules.timer.heth.service.DisCaseJobService;
import com.chis.modules.timer.heth.service.TbZwWarnModelService;
import com.chis.modules.timer.heth.service.TdZwWarnInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public abstract class NotInTimeWarnBase {
    @Resource
    private TbZwWarnModelService modelService;
    @Resource
    private TdZwWarnInfoService warnInfoService;
    @Resource
    private TsSysHolidayService holidayService;
    @Resource
    protected DisCaseJobService disCaseJobService;
    /**
     * 预警起始时间参数
     */
    @Value("${warn-timer.zwWarn.beginDate}")
    private String beginDateStr;
    /**
     * 预警起始时间
     */
    protected Date beginDate;
    /**
     * 当前日期减去预警天数的时间
     */
    protected Date limitDate;
    /**
     * 预警天数
     */
    protected Integer warnDays;
    /**
     * 预警类型
     */
    protected Integer warnType;
    /**
     * 业务机构类型
     */
    protected Integer busType;
    /**
     * 预警类型名称
     */
    protected String warnName;
    /**
     * 预警级别及预警规则
     * <pre>key: 预警级别</pre>
     * <pre>value: 预警规则</pre>
     */
    protected Map<Integer, DisCaseWarnModelBO> warnModelMap;
    /**
     * 查询出的数据集
     */
    protected List<DisCaseNotInTime> dataList;
    /**
     * 筛选后的数据集 key 机构id value 机构数据对象
     * */
    private Map<Integer, DisCaseNotInTimeOrg> orgDataMap;
    /**预警业务地区*/
    @Value("${warn-timer.zwWarn.zoneCode}")
    protected String zoneCode;

    /**
     * 定时任务参数配置
     */
    public abstract void initJobParam();

    /**
     * <p>方法描述：初始化 </p>
     * pw 2023/11/12
     **/
    public boolean init() {
        boolean ifBaseValidate = true;
        this.initJobParam();
        if (null == this.warnDays) {
            log.error("定时任务：{} 未配置预警天数", this.warnName);
            ifBaseValidate = false;
        }
        if (!this.initBeginDate()) {
            log.error("定时任务：{} 参数配置有误，配置参数：{}", this.warnName, "warn-timer.zwWarn.beginDate");
            ifBaseValidate = false;
        }
        this.initWarnModelMap();
        if (CollectionUtils.isEmpty(this.warnModelMap)) {
            log.error("定时任务：{} 未配置预警模型", this.warnName);
            ifBaseValidate = false;
        }
        if (!this.checkCurYearHoliday()) {
            log.error("今年节假日未配置,当前日期：{}", DateUtils.formatStr(new Date()));
            ifBaseValidate = false;
        }
        if (ObjectUtils.isNull(this.zoneCode)) {
            log.error("定时任务：{} 未配置预警业务地区", this.zoneCode);
            ifBaseValidate = false;
        }
        this.limitDate = this.holidayService.calcuteWorkDayDate(DateUtils.getDateOnly(new Date()), -this.warnDays);
        return ifBaseValidate;
    }

    /**
     * <p>方法描述：查询待处理数据 </p>
     * pw 2023/11/12
     **/
    public abstract void queryDataList();

    /**
     * <p>方法描述：数据处理 </p>
     * pw 2023/11/12
     **/
    public void executeData() {
        if (!this.init()) {
            return;
        }
        log.info("{} 工具启动，当前版本：{} 当前时间：{}", this.warnName, 20231113,new Date());
        this.queryDataList();
        this.pakOrgDataMap();
        if (CollectionUtils.isEmpty(this.orgDataMap)) {
            return;
        }
        try {
            List<TdZwWarnInfo> saveOrUpdateList = new LinkedList<>();
            this.orgDataMap.forEach((orgId, org) ->
                    org.getBusDataMap().forEach((warnLevel, busDataList) ->
                            this.warnSingle(org, warnLevel, busDataList, saveOrUpdateList)));
            //存储
            this.warnInfoService.saveOrUpdateTdZwWarnInfoWithoutCompareSub(saveOrUpdateList);
        } catch (Exception e) {
            log.error(this.warnName+"，预警存储异常",e);
        }
    }

    /**
     * <p>方法描述：预警 </p>
     * pw 2023/11/12
     **/
    private void warnSingle(DisCaseNotInTimeOrg orgEntry, Integer warnLevel,
                            List<DisCaseNotInTime> busDataList,
                            List<TdZwWarnInfo> saveOrUpdateList) {
        orgEntry.setZwWarnInfoMap(new HashMap<>());
        DisCaseWarnModelBO warnModelBO = this.warnModelMap.get(warnLevel);
        Integer cycleDays = warnModelBO.getCycleDays();
        boolean ifCycle = null != cycleDays;
        List<List<DisCaseNotInTime>> allPossibleList = new ArrayList<>();
        //  getRcvDate按照日期排序
        busDataList = busDataList.stream().sorted(Comparator.comparing(DisCaseNotInTime::getRcvDate)) .collect(Collectors.toList());
        if (ifCycle) {
            this.pakAllPossibleListByCycle(allPossibleList, busDataList, cycleDays);
        } else {
            allPossibleList.add(busDataList);
        }

        Integer lowerLimit = warnModelBO.getLowerLimit();
        for (List<DisCaseNotInTime> curBusDataList : allPossibleList) {
            int num = curBusDataList.size();
            //不满足下限
            if (num < lowerLimit) {
                continue;
            }
            Date startDate = curBusDataList.get(0).getRcvDate();
            Date endDate = null;
            if (ifCycle) {
                endDate = DateUtils.addDays(startDate, cycleDays);
            }
            if (endDate == null || endDate.after(new Date())) {
                endDate = DateUtils.parseDate(DateUtils.getDate());
            }
            String warnCont = pakWarnCont(warnLevel, orgEntry.getZoneName(), startDate, endDate, orgEntry.getOrgName(), num);
            TdZwWarnInfo warnInfo = new TdZwWarnInfo();
            warnInfo.setFkByModelId(new TbZwWarnModel(warnModelBO.getRid()));
            warnInfo.setFkByWarnZone(new TsZone(orgEntry.getZoneId()));
            warnInfo.setBusType(this.busType);
            warnInfo.setBusId(orgEntry.getOrgId());
            warnInfo.setWarnCont(warnCont);
            warnInfo.setHappenDate(new Date());
            warnInfo.setJcBeginDate(startDate);
            warnInfo.setJcEndDate(endDate);
            warnInfo.setHappenNum(num);
            warnInfo.setStateMark(0);
            warnInfo.setViewLevel(warnLevel);
            warnInfo.setCreateDate(new Date());
            warnInfo.setCreateManid(1);
            warnInfo.setModifyDate(new Date());
            warnInfo.setModifyManid(1);

            saveOrUpdateList.add(warnInfo);

            pakWarnInfoMap(orgEntry, warnModelBO.getRid());

            //根据开始日期查找是否存在有未处置的预警
            String startDateStr = DateUtils.formatDate(startDate, "yyyy-MM-dd");
            //非周期性的 待处置的预警仅会有一条
            if (!ifCycle) {
                startDateStr = orgEntry.getZwWarnInfoMap().keySet().stream().findFirst().orElse("");
            }
            TdZwWarnInfo warnInfo1 = orgEntry.getZwWarnInfoMap().get(startDateStr);
            if (warnInfo1 != null && warnInfo1.getRid() != null) {
                warnInfo.setRid(warnInfo1.getRid());
                warnInfo.setCreateDate(warnInfo1.getCreateDate());
                log.info("找到主表TD_ZW_WARN_INFO，主表rid: {} 是否重新存储子表： {}",warnInfo1.getRid(),this.ifDifference(curBusDataList, warnInfo1.getPsnList()));
                //没有差异 continue
                if (!this.ifDifference(curBusDataList, warnInfo1.getPsnList())) {
                    continue;
                }
            }
            //封装子表
            this.fillPsnList(warnInfo, curBusDataList);
        }
    }

    /**
     * 封装所有周期
     *
     * @param allPossibleList 所有周期业务数据集合
     * @param busDataList     业务数据集合
     * @param cycleDays       周期
     */
    private void pakAllPossibleListByCycle(List<List<DisCaseNotInTime>> allPossibleList,
                                           List<DisCaseNotInTime> busDataList,
                                           int cycleDays) {
        int index = 0;
        Date startDate;
        List<DisCaseNotInTime> tempPossibleList = new LinkedList<>();
        List<List<DisCaseNotInTime>> tmpList = new ArrayList<>();
        for (int i = 0; i < busDataList.size(); i++) {
            startDate = busDataList.get(i).getRcvDate();
            Date endDate = DateUtils.addDays(startDate, cycleDays);
            index = findCombinationsHelper(busDataList, tempPossibleList, endDate, index);
            tmpList.add(new LinkedList<>(tempPossibleList));
            i = findCombinationsHelper(tempPossibleList, startDate, i);
        }
        //多周期 过滤掉周期末结束日期相同的记录 避免出现第一条预警13例 第二条10例 第一条的13例包含第二条的10例的情况
        Date rcvDate = null;
        for (List<DisCaseNotInTime> curList : tmpList) {
            int size = curList.size();
            Date curRcvDate = curList.get(size-1).getRcvDate();
            if (null != rcvDate && rcvDate.compareTo(curRcvDate) == 0) {
                continue;
            }
            allPossibleList.add(curList);
            rcvDate = curRcvDate;
        }
    }

    /**
     * 步进周期开始日期
     *
     * @param tempPossibleList 临时周期业务数据集合
     * @param startDate        周期开始日期
     * @param i                业务数据List开始数据下标
     * @return 业务数据List开始数据下标
     */
    public int findCombinationsHelper(List<DisCaseNotInTime> tempPossibleList, Date startDate, int i) {
        if (CollectionUtils.isEmpty(tempPossibleList)) {
            return i;
        }
        Iterator<DisCaseNotInTime> iterator = tempPossibleList.iterator();
        while (iterator.hasNext()) {
            if (iterator.next().compareDate(startDate) != 0) {
                return i - 1;
            }
            i++;
            iterator.remove();
        }
        return i - 1;
    }

    /**
     * 根据周期开始日期以及结束日期放入业务数据
     *
     * @param busDataList  业务数据集合
     * @param tempDateList 临时周期业务数据集合
     * @param endDate      周期结束日期
     * @param j            标记
     * @return 标记
     */
    public Integer findCombinationsHelper(List<DisCaseNotInTime> busDataList,
                                                 List<DisCaseNotInTime> tempDateList,
                                                 Date endDate, Integer j) {
        if (busDataList.size() <= j) {
            return j;
        }
        DisCaseNotInTime busDataBO = busDataList.get(j);
        if (busDataBO.compareDate(endDate) > 0) {
            return j;
        }
        tempDateList.add(busDataBO);
        return findCombinationsHelper(busDataList, tempDateList, endDate, ++j);
    }

    /**
     * <p>方法描述：子表填充 </p>
     * pw 2023/11/11
     **/
    private void fillPsnList(TdZwWarnInfo warnInfo, List<DisCaseNotInTime> curBusDataList) {
        List<TdZwWarnPsns> psnList = new ArrayList<>();
        warnInfo.setPsnList(psnList);
        if (CollectionUtils.isEmpty(curBusDataList)) {
            return;
        }
        for (DisCaseNotInTime dataBO : curBusDataList) {
            TdZwWarnPsns warnPsn = new TdZwWarnPsns();
            warnPsn.setFkByMainId(warnInfo);
            warnPsn.setBusId(dataBO.getId());
            warnPsn.setRcvDate(dataBO.getRcvDate());
            warnPsn.setDealDate(dataBO.getDealDate());
            TsSimpleCode disSimpleCode = null == dataBO.getDisId() ? null : new TsSimpleCode(dataBO.getDisId());
            warnPsn.setFkByDisId(disSimpleCode);
            warnPsn.setCreateDate(new Date());
            warnPsn.setCreateManid(0);
            psnList.add(warnPsn);
        }
    }

    /**
     * <p>方法描述：判断子表是否有差异 </p>
     * pw 2023/11/11
     **/
    private boolean ifDifference(List<DisCaseNotInTime> curBusDataList, List<TdZwWarnPsns> psnList) {
        int dataSize = CollectionUtils.isEmpty(curBusDataList) ? 0 : curBusDataList.size();
        int psnSize = CollectionUtils.isEmpty(psnList) ? 0 : psnList.size();
        if (dataSize != psnSize) {
            return true;
        }
        if (CollectionUtils.isEmpty(psnList)) {
            return true;
        }
        Set<String> differenceSet = new HashSet<>();
        for (DisCaseNotInTime dataBO : curBusDataList) {
            Integer id = dataBO.getId();
            Integer disId = dataBO.getDisId();
            if (null == id) {
                continue;
            }
            if (null == disId) {
                disId = 0;
            }
            String rcvDateStr = DateUtils.formatDate(dataBO.getRcvDate());
            String dealDateStr = null == dataBO.getDealDate() ? "" : DateUtils.formatDate(dataBO.getDealDate());
            differenceSet.add(id + "&" + disId+"#"+rcvDateStr+"&"+dealDateStr);
        }
        for (TdZwWarnPsns warnPsn : psnList) {
            Integer disId = null == warnPsn.getFkByDisId() ? 0 : warnPsn.getFkByDisId().getRid();
            String rcvDateStr = DateUtils.formatDate(warnPsn.getRcvDate());
            String dealDateStr = null == warnPsn.getDealDate() ? "" : DateUtils.formatDate(warnPsn.getDealDate());
            String key = warnPsn.getBusId() + "&" + disId+"#"+rcvDateStr+"&"+dealDateStr;
            if (!differenceSet.contains(key)) {
                return true;
            }
            differenceSet.remove(key);
        }
        return !CollectionUtils.isEmpty(differenceSet);
    }

    /**
     * 按监测开始日期封装未处置数据Map
     *
     * @param orgEntity  机构数据对象
     * @param modelId 预警模型ID
     */
    private void pakWarnInfoMap(DisCaseNotInTimeOrg orgEntity, Integer modelId) {
        if (!CollectionUtils.isEmpty(orgEntity.getZwWarnInfoMap())) {
            return;
        }
        Integer orgId = orgEntity.getOrgId();
        orgEntity.setZwWarnInfoMap(new HashMap<>());
        Map<String, TdZwWarnInfo> warnInfoMap = orgEntity.getZwWarnInfoMap();
        List<TdZwWarnInfo> warnInfoList = findZwWarnInfoList(modelId, orgId);
        for (TdZwWarnInfo warnInfo : warnInfoList) {
            Integer rid = warnInfo.getRid();
            Date jcBeginDate = warnInfo.getJcBeginDate();
            if (rid == null || jcBeginDate == null) {
                continue;
            }
            String key = DateUtils.formatDate(jcBeginDate, "yyyy-MM-dd");
            warnInfoMap.put(key, warnInfo);
        }
    }

    /**
     * 查询预警起始日期记录
     *
     * @return 预警起始日期记录
     */
    private List<TdZwWarnInfo> findZwWarnInfoList(Integer modelId, Integer orgId) {
        List<TdZwWarnInfo> warnInfoList = this.warnInfoService.selectNotDisposalList(modelId, this.busType, orgId);
        if (CollectionUtils.isEmpty(warnInfoList)) {
            return new ArrayList<>();
        }
        return warnInfoList;
    }

    /**
     * <p>方法描述：组装预警内容 </p>
     * pw 2023/11/12
     **/
    private String pakWarnCont(Integer warnLevel, String zoneName, Date startDate, Date endDate, String orgName, Integer num) {
        String msg = "<预警级别>风险预警：<机构地区>在<开始日期>到<结束日期>期间内发现<机构>的<标题>数为<例数>例，请及时关注！";
        if (new Integer(3).equals(warnLevel)) {
            msg = msg.replace("<预警级别>", "省级");
        } else if (new Integer(2).equals(warnLevel)) {
            msg = msg.replace("<预警级别>", "市级");
        } else {
            msg = msg.replace("<预警级别>", "区县");
        }
        zoneName = StringUtils.isBlank(zoneName) ? "" : zoneName.substring(zoneName.indexOf("_") + 1).replace("_", "");
        msg = msg.replace("<机构地区>", zoneName);
        msg = msg.replace("<开始日期>", DateUtils.formatDate(startDate, "yyyy-MM-dd"));
        msg = msg.replace("<结束日期>", DateUtils.formatDate(endDate, "yyyy-MM-dd"));
        msg = msg.replace("<机构>", orgName);
        msg = msg.replace("<例数>", num + "");
        msg = msg.replace("<标题>",this.warnName);
        return msg;
    }

    /**
     * <p>方法描述：打包待处理数据 </p>
     * pw 2023/11/12
     **/
    private void pakOrgDataMap() {
        this.orgDataMap = new HashMap<>();
        if (CollectionUtils.isEmpty(this.dataList)) {
            return;
        }
        for (DisCaseNotInTime data : this.dataList) {
            Integer id = data.getId();
            Integer orgId = data.getOrgId();
            Integer zoneId = data.getZoneId();
            Date rcvDate = data.getRcvDate();
            Integer warnLevel = data.getWarnLevel();
            boolean ifMarch = null == id || null == orgId || null == zoneId ||
                    null == rcvDate || null == warnLevel;
            if (ifMarch) {
                continue;
            }
            Date dealDate = data.getDealDate();
            //报告日期不为空并且业务日期与报告日期之间的工作日小于等于配置的预警天数 不需要预警
            ifMarch = null != dealDate &&
                    this.holidayService.findWorkDayByTwoDate(DateUtils.getDateOnly(rcvDate),
                            DateUtils.getDateOnly(dealDate),
                            false) <= this.warnDays;
            if (ifMarch) {
                continue;
            }
            String orgName = data.getOrgName();
            String zoneName = data.getZoneName();
            if (!this.orgDataMap.containsKey(orgId)) {
                this.orgDataMap.put(orgId, this.pakDisCaseOrg(orgId, orgName, zoneId, zoneName));
            }
            this.orgDataMap.get(orgId).getBusDataMap().get(warnLevel).add(data);
        }
    }

    /**
     * <p>方法描述： 组装机构信息 </p>
     * pw 2023/11/12
     **/
    private DisCaseNotInTimeOrg pakDisCaseOrg(Integer orgId, String orgName, Integer zoneId, String zoneName) {
        DisCaseNotInTimeOrg disCaseNotInTimeOrg = new DisCaseNotInTimeOrg(orgId, orgName, zoneId, zoneName);
        Map<Integer, List<DisCaseNotInTime>> busDataMap = disCaseNotInTimeOrg.getBusDataMap();
        for (Integer key : this.warnModelMap.keySet()) {
            busDataMap.put(key, new ArrayList<>());
        }
        return disCaseNotInTimeOrg;
    }

    /**
     * <p>方法描述：参数日期转换 </p>
     * pw 2023/11/12
     **/
    private boolean initBeginDate() {
        if (StringUtils.isBlank(this.beginDateStr)) {
            this.beginDateStr = "2000-01-01";
        }
        try {
            this.beginDate = DateUtils.parseDate(this.beginDateStr, "yyyy-MM-dd");
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * <p>方法描述：初始化预警模型Map </p>
     * pw 2023/11/12
     **/
    private void initWarnModelMap() {
        List<TbZwWarnModel> warnModeList = this.modelService.selectListByWarnType(this.warnType);
        this.warnModelMap = new HashMap<>();
        if (ObjectUtils.isEmpty(warnModeList)) {
            return;
        }
        for (TbZwWarnModel tbZwWarnModel : warnModeList) {
            Integer rid = tbZwWarnModel.getRid();
            Integer warnLevel = tbZwWarnModel.getWarnLevel();
            Integer geInd = tbZwWarnModel.getGeInd();
            Integer gtInd = tbZwWarnModel.getGtInd();
            if (rid == null || warnLevel == null || (geInd == null && gtInd == null)) {
                continue;
            }
            //下限
            int lowerLimit;
            if (geInd != null) {
                lowerLimit = geInd;
            } else {
                lowerLimit = gtInd + 1;
            }
            //监测周期天数
            Integer ifCycleTime = tbZwWarnModel.getIfCycleTime();
            Integer cycleDays = tbZwWarnModel.getCycleDays();
            if (cycleDays == null) {
                cycleDays = 0;
            }
            if (!new Integer(1).equals(ifCycleTime) || cycleDays <= 0) {
                cycleDays = null;
            }
            this.warnModelMap.put(warnLevel, new DisCaseWarnModelBO(rid, warnLevel, lowerLimit, cycleDays));
        }
    }

    /**
     * <p>方法描述：检测当年节假日是否有配置 </p>
     * pw 2023/11/12
     **/
    private boolean checkCurYearHoliday() {
        List<TsSysHoliday> holidayList = this.holidayService.findAllEnableHoliday();
        if (CollectionUtils.isEmpty(holidayList)) {
            return false;
        }
        Date curYearFirstDay = DateUtils.getYearFirstDay(new Date());
        return holidayList.stream().anyMatch(v -> null != v.getStartDate() && v.getStartDate().after(curYearFirstDay));
    }


}

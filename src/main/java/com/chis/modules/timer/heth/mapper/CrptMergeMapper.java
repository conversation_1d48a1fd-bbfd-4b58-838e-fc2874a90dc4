package com.chis.modules.timer.heth.mapper;

import com.chis.modules.timer.heth.logic.vo.CrptBadRsnVo;
import com.chis.modules.timer.heth.logic.vo.CrptMergeDynamicUpdatePO;
import com.chis.modules.timer.heth.logic.vo.TdTjCrptTaskSubVo;
import com.chis.modules.timer.heth.logic.vo.TdTjCrptTaskVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>类描述： 合并企业信息Mapper </p>
 * pw 2023/8/9
 **/
@Repository
public interface CrptMergeMapper {

    /**
     * <p>方法描述：获取待执行的同步业务数据异步任务 </p>
     * pw 2023/8/9
     **/
    List<TdTjCrptTaskVo> findTdTjCrptTaskVoListBySize(@Param("dataSize") Integer dataSize);

    /**
     * <p>方法描述：通过异步任务rid集合获取子表信息点集合 </p>
     * pw 2023/8/9
     **/
    List<TdTjCrptTaskSubVo> findTdTjCrptTaskSubVoListByMainIdList(@Param("mainRidList") List<Integer> mainRidList);

    /**
     * <p>方法描述：更新异步主表状态和失败原因 </p>
     * pw 2023/8/12
     **/
    void updateCrptTask(@Param("rid") Integer rid,@Param("state") Integer state,@Param("errMsg") String errMsg);

    /**
     * <p>方法描述： 批量处理数据 </p>
     * pw 2023/8/11
     **/
    void updateDynamicBatch(List<CrptMergeDynamicUpdatePO> updatePOList);

    /**
     * <p>方法描述：检测对应表是否存在 </p>
     * pw 2023/8/10
     **/
    int checkTableIfExist(@Param("tableName") String tableName);

    /**
     * <p>方法描述： 通过主体机构rid集合 获取分支机构的rid和单位名称 </p>
     * key rid,crptName
     * pw 2023/8/11
     **/
    List<Map<String,Object>> findSubCrptNameByFatherIdList(@Param("mainRidList") List<Integer> mainRidList);

    /**
     * <p>方法描述：通过单位名称与社会信用代码 获取企业rid集合 </p>
     * pw 2023/8/11
     **/
    List<Integer> findRidListByCrptNameAndInstitutionCode(@Param("crptName") String crptName,
                                                          @Param("institutionCode") String institutionCode);

    /**
     * <p>方法描述：通过社会信用代码获取主体机构企业rid集合 </p>
     * pw 2023/8/11
     **/
    List<Integer> findMainCrptRidListByInstitutionCode(@Param("institutionCode") String institutionCode);

    /**
     * <p>方法描述：校验最新企业是否存在报告卡最新状态数据 </p>
     * pw 2023/8/12
     **/
    int checkIfExistBgkLastSta(@Param("rid") Integer rid);

    /**
     * <p>方法描述：新增用人单位审核报告卡最新状态 </p>
     * pw 2023/8/12
     **/
    void insertBgkLastSta(@Param("busId") Integer busId, @Param("createManid") Integer createManid);

    /**
     * <p>方法描述：修订成功后更新停用企业记录 </p>
     * pw 2023/8/12
     **/
    void updateStopCrptInfo(@Param("list") List<Integer> list);

    /**
     * <p>方法描述：通过企业rid获取 企业信息—职业病危害因素明细 </p>
     * pw 2023/8/12
     **/
    List<CrptBadRsnVo> findDistinctCrptBadRsnVoList(@Param("crptRidList") List<Integer> crptRidList);

    /**
     * <p>方法描述：新增 企业信息—职业病危害因素明细 </p>
     * pw 2023/8/12
     **/
    void insertCrptBadRsnBatch(@Param("list") List<CrptBadRsnVo> list,@Param("createManid") Integer createManid,
                               @Param("crptId") Integer crptId);
}

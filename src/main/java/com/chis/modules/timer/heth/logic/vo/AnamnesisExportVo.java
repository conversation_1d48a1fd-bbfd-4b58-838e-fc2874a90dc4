package com.chis.modules.timer.heth.logic.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 异步导出既往病史VO
 * 
 * @ClassAuthor pw,2021年12月21日,AnamnesisExportVo
 */
@Data
public class AnamnesisExportVo implements Serializable {
    private static final long serialVersionUID = -5322736843292404987L;
    private Integer bhkRid;
    /** 疾病名称 */
    private String hstnam;
    /** 诊断日期 */
    private String hstdat;
    /** 诊断单位 */
    private String hstunt;
    /** 治疗经过 */
    private String hstcruprc;
    /** 转归 */
    private String hstlps;
}

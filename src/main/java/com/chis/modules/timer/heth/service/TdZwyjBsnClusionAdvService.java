package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TdZwyjBsnClusionAdv;
import com.chis.modules.timer.heth.entity.TdZwyjBsnClusionRsn;
import com.chis.modules.timer.heth.mapper.TdZwyjBsnClusionAdvMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-10-29,TdZwyjBsnClusionAdvService
 */
@Service
public class TdZwyjBsnClusionAdvService extends ZwxBaseServiceImpl<TdZwyjBsnClusionAdvMapper, TdZwyjBsnClusionAdv> {
    /**
     * <p>方法描述：查询建议结论</p>
     * @MethodAuthor qrr,2020-10-29,findClusionRsnMap
     * */
    public Map<Integer,List<TdZwyjBsnClusionAdv>> findClusionAdvMap(){
        List<TdZwyjBsnClusionAdv> advList = this.selectListByEntity(null);
        //key:配置主表Id,value:TdZwyjBsnClusionAdv
        Map<Integer,List<TdZwyjBsnClusionAdv>> advMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(advList)){
            for(TdZwyjBsnClusionAdv t:advList){
                if (null==t.getFkByMainId()){
                    continue;
                }
                if(null==advMap.get(t.getFkByMainId().getRid())){
                    List<TdZwyjBsnClusionAdv> list = new ArrayList<>();
                    list.add(t);
                    advMap.put(t.getFkByMainId().getRid(),list);
                }else {
                    List<TdZwyjBsnClusionAdv> list = advMap.get(t.getFkByMainId().getRid());
                    list.add(t);
                }
            }
        }
        return advMap;
    }
}

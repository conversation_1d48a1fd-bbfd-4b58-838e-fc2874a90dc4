package com.chis.modules.timer.heth.service;

import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.timer.heth.entity.TbZwOrgWarnConfig;
import com.chis.modules.timer.heth.mapper.TbZwOrgWarnConfigMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * <AUTHOR> 2024-07-11,TbZwOrgWarnConfigService
 */
@Service
public class TbZwOrgWarnConfigService extends ZwxBaseServiceImpl<TbZwOrgWarnConfigMapper, TbZwOrgWarnConfig> {

    /**
     * 通过资质类型查询资质机构预警配置
     *
     * @param busType 资质类型
     * @return 资质机构预警配置
     */
    public List<TbZwOrgWarnConfig> dataAndSubList(Integer busType) {
        List<TbZwOrgWarnConfig> configList = this.baseMapper.dataAndSubList(busType);
        if (CollectionUtils.isEmpty(configList)) {
            return new ArrayList<>();
        }
        return configList;
    }
}

package com.chis.modules.sys.enums.contrast;

/**
 * 码表对照枚举接口
 */
public interface ISimpleCodeEnum {
    /**
     * 码表类型
     *
     * @return String 码表类型
     */
    String getCodeType();

    /**
     * 数据获取类型枚举
     *
     * @return DataFetchCommEnum 数据获取类型枚举
     */
    DataFetchCommEnum getDataFetchEnum();

    /**
     * 对照类型枚举(Map的key)
     *
     * @return ContrastTypeCommEnum 对照类型枚举
     */
    ContrastTypeCommEnum getContrastType();
}

package com.chis.modules.sys.enums;

import com.chis.modules.webmvc.cache.enums.ICaffeineKeyEnum;


public enum JdbcCaf<PERSON>ineKeyEnum implements ICaffeineKeyEnum{
    CONTRA_CAFFEINE_KEY("CONTRA_CAFFEINE_KEY"),
    CONTRA_CAFFEINE_CONTRACON_KEY("CONTRA_CAFFEINE_CONTRACON_KEY"),
    CONTRA_CAFFEINE_SIMP_KEY("CONTRA_CAFFEINE_SIMP_KEY"),
    CONTRA_CAFFEINE_DECSR_KEY("CONTRA_CAFFEINE_DECSR_KEY"),
    CONTRA_CAFFEINE_TSSIMPLECODE_EXTENDS_KEY("CONTRA_CAFFEINE_TSSIMPLECODE_EXTENDS_KEY"),
    CONTRA_CAFFEINE_SPECIAL_DESC_KEY("CONTRA_CAFFEINE_SPECIAL_DESC_KEY"),
    CONTRA_CAFFEINE_MORE_KEY("CONTRA_CAFFEINE_MORE_KEY"),
    SIMPLE_CODE_CAFFEINE_KEY("SIMPLE_CODE_CAFFEINE_KEY"),
    ZONE_CODE_CAFFEINE_KEY("ZONE_CAFFEINE_KEY"),
    ZONE_CODE_CAFFEINE_SORT_KEY("ZONE_CODE_CAFFEINE_SORT_KEY"),
    ZONE_CODE_CAFFEINE_LIST_KEY("ZONE_CODE_CAFFEINE_LIST_KEY"),
    IF_HAVE_GS_UPLOAD("IF_HAVE_GS_UPLOAD"),
    /**尘肺病患接口对接中地区缓存*/
    ZONE_CODE_PNEU_KEY("ZONE_CODE_PNEU_KEY"),
    /**尘肺病患接口对接中码表缓存*/
    SIMPLE_CODE_PNEU_KEY("SIMPLE_CODE_PNEU_KEY"),
    /**
     * 多种码表类型码表Map缓存
     */
    MULTIPLE_SIMPLE_CODE_MAP_KEY("MULTIPLE_SIMPLE_CODE_MAP_KEY"),
    /**系统参数缓存*/
    SYSTEM_PARAM_TYPE("SYSTEM_PARAM_TYPE"),
    /**tokenid*/
    SX_FS_TOKEN("SX_FS_TOKEN"),
    /** 启用的码表按num排序的key */
    SIMPLE_CODE_LIST_ORDERBYNUM_KEY("SIMPLE_CODE_LIST_ORDERBYNUM_KEY"),
    /** 所有对应码表集合key 包含停用 */
    SIMPLE_CODE_LIST_ALL_KEY("SIMPLE_CODE_LIST_ALL_KEY"),
    SIMPLE_CODE_MAP_ALL_KEY("SIMPLE_CODE_MAP_ALL_KEY"),
    SIMPLE_CODE_MAP_ALL_KEY_NAME("SIMPLE_CODE_MAP_ALL_KEY_NAME"),
    SIMPLE_CODE_MAP_LAST_KEY_NAME("SIMPLE_CODE_MAP_LAST_KEY_NAME"),
    SIMPLE_CODE_MAP_ALL_KEY_LEVELNO("SIMPLE_CODE_MAP_ALL_KEY_LEVELNO"),
    /** 节假日 */
    SYSTEM_HOLIDAY("SYSTEM_HOLIDAY"),
    /**慢病码表*/
    SIMPLE_CODE_MB_CAFFEINE_KEY("SIMPLE_CODE_CAFFEINE_KEY"),
    SIMPLE_CODE_LIST_MB_ALL_KEY("SIMPLE_CODE_LIST_MB_ALL_KEY"),
    CONTRA_LEFT_DESC_KEY("CONTRA_LEFT_DESC_KEY"),
    SRVORG_ALL_KEY("SRVORG_ALL_KEY"),

    /**对照表维护的体检项目*/
    ITEM_ALL_KEY("ITEM_ALL_KEY"),
    /**体检项目 项目标记是15和16的体检项目*/
    ITEM_15And16_KEY("ITEM_15And16_KEY"),
    TJORGINFO_MAP_ALL_KEY("TJORGINFO_MAP_ALL_KEY"),
    TJORGINFO_UNIT_MAP_ALL_KEY("TJORGINFO_UNIT_MAP_ALL_KEY")
    ;



    private String key;

    JdbcCaffeineKeyEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }
}

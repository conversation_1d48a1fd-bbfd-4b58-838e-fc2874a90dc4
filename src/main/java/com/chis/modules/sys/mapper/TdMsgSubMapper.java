package com.chis.modules.sys.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.chis.modules.sys.entity.TdMsgSub;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2019-10-25,TdMsgSubMapper
 */
@Repository
public interface TdMsgSubMapper extends ZwxBaseMapper<TdMsgSub> {
	public List<TdMsgSub> selectListByMainIdAndZone(@Param("mainId")Integer mainId,@Param("zoneGb")String zoneGb,@Param("zoneType")String zoneType);
}

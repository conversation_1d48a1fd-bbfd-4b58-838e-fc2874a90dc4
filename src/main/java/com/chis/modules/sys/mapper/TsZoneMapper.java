package com.chis.modules.sys.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.chis.modules.sys.entity.TsZone;

/**
 * <p>
 * Mapper 接口：
 * </p>
 *
 * @ClassAuthor 机器人,2019-07-20,TsZoneMapper
 */
@Repository
public interface TsZoneMapper extends ZwxBaseMapper<TsZone> {
	public List<TsZone> findZoneList(@Param("zoneGb") String zoneGb, @Param("zoneTypeMin") Integer zoneTypeMin,
			@Param("zoneTypeMax") Integer zoneTypeMax, @Param("ifContainsProv") Boolean ifContainsProv);

    List<TsZone> selectTsZones();

	/**
	 * 查询地区
	 *
	 * @param zoneGb      地区ZONE_GB
	 * @param zoneTypeMin 最小地区级别
	 * @param zoneTypeMax 最大地区级别
	 * @param ifReveal    停用标记
	 * @return List
	 */
	List<TsZone> findTsZoneList(@Param("zoneGb") String zoneGb,
								@Param("zoneTypeMin") Integer zoneTypeMin,
								@Param("zoneTypeMax") Integer zoneTypeMax,
								@Param("ifReveal") Integer ifReveal);
}

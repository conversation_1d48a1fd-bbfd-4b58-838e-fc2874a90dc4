package com.chis.modules.sys.mapper;

import java.util.List;

import com.github.pagehelper.PageInfo;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/***
 * <p>类描述: 通用Mapper </p>
 *
 * @ClassAuthor mxp, 2018/12/26,ZwxBaseMapper
 */
public interface ZwxBaseMapper<T> extends BaseMapper<T> {

// 分页 //////////////////////////////////////////////////////////////////////////



    List<T> pageList(@Param("page") PageInfo<T> page, @Param("property") T entity);


// 查询 //////////////////////////////////////////////////////////////////////////

    /***
     * <p>方法描述: 单个查询：根据对象中的所有赋值字段（非空判断）进行查询</p>
     *
     * @MethodAuthor mxp, 2018/12/26,selectByEntity
     */
    T selectByEntity(T entity);

    /***
     * <p>方法描述: 批量查询：根据对象中的所有赋值字段（非空判断）进行查询</p>
     *
     * @MethodAuthor mxp, 2018/12/26,selectListByEntity
     */
    List<T> selectListByEntity(T entity);

// 更新 //////////////////////////////////////////////////////////////////////////

    /***
     * <p>方法描述: 单个更新：根据对象id更新，会更新全部字段</p>
     *
     * @MethodAuthor mxp, 2018/12/26,updateFullById
     */
    int updateFullById(T entity);

    /***
     * <p>方法描述: 批量更新：根据每个对象的id更新，会更新全部字段</p>
     *
     * @MethodAuthor mxp, 2018/12/26,updateFullBatchById
     */
    int updateFullBatchById(List<T> entityList);

// 删除 //////////////////////////////////////////////////////////////////////////

    /***
     * <p>方法描述: 删除：根据对象所有赋值字段（非空）进行删除</p>
     *
     * @MethodAuthor mxp,2018/12/26,removeByEntity
     */
    int removeByEntity(T entity);


    List<T> pageList(@Param("first") Integer first, @Param("pageSize") Integer pageSize, @Param("property") T entity);

    void insertBatch(List<T> entityList);

    int insertEntity(T t);
}

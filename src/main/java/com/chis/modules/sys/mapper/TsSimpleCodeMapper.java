package com.chis.modules.sys.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.chis.modules.sys.entity.TsSimpleCode;

/**
 * <p> Mapper 接口：编码表  </p>
 *
 * @ClassAuthor 机器人,2019-02-18,TsSimpleCodeMapper
 */
@Repository
public interface TsSimpleCodeMapper extends ZwxBaseMapper<TsSimpleCode> {
	public TsSimpleCode findTsSimpleCode(@Param("codeName")String codeName,@Param("codeNo")String codeNo);
	
	public List<TsSimpleCode> findTsSimpleCodeList(@Param("codeNo")String codeNo);

	public List<TsSimpleCode> findTsSimpleCodeAllList(@Param("codeNo")String codeNo);

	public List<TsSimpleCode> findAllTsSimpleCodeList(@Param("codeNo")String codeNo);

	public List<TsSimpleCode> findTsSimpleCodeListOrderby(@Param("codeNo")String codeNo);


	public TsSimpleCode findTsSimpleCodeExtends1(@Param("codeName")String codeName,@Param("extends1")String extends1);

	public List<TsSimpleCode> findAllTsSimpleCodeListByCodeNoList(@Param("codeNoList") List<String> codeNoList);

	List<TsSimpleCode> findAllTsSimpleCodeListByCodeName(@Param("codeName") String codeName);

    List<TsSimpleCode> selectListByQueId(@Param("queId") Integer queId);

	List<TsSimpleCode> findAllTsSimpleCodeLists(@Param("codeName") String codeName);
}

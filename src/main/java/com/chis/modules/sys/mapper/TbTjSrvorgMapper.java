package com.chis.modules.sys.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.chis.modules.sys.entity.TbTjSrvorg;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2019-07-31,TbTjSrvorgMapper
 */
@Repository
public interface TbTjSrvorgMapper extends ZwxBaseMapper<TbTjSrvorg> {
	/**
 	 * <p>方法描述：根据地区查询体检机构</p>
 	 * @MethodAuthor qrr,2019年7月31日,selectListByZoneCode
	 * */
	public List<TbTjSrvorg> selectListByZoneCode(@Param("zoneGb")String zoneGb,@Param("ifContainsProv")Boolean ifContainsProv);

	public List<TbTjSrvorg> pageListToFJ(@Param("first") Integer first, @Param("pageSize") Integer pageSize, @Param("property") TbTjSrvorg entity);

	public List<TbTjSrvorg> findGjSrvorg(@Param("paramName") String paramName);

	public List<TbTjSrvorg> findTbTjSrvorgAll();

	List<TbTjSrvorg> findTbTjSrvorgByUnitCode(@Param("unitList") List<String> unitList);

}

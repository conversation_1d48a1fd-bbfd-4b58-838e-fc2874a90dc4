package com.chis.modules.sys.mapper;

import com.chis.modules.sys.entity.TsProPoolOpt;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2021-09-27,TsProPoolOptMapper
 */
@Repository
public interface TsProPoolOptMapper extends ZwxBaseMapper<TsProPoolOpt> {
    /**
     * <p>方法描述： 通过题目rid集合 获取题库选项集合 </p>
     * @MethodAuthor： pw 2023/3/17
     **/
    List<TsProPoolOpt> findTsProPoolOptsByQuestIds(@Param("list") List<Integer> list);
}

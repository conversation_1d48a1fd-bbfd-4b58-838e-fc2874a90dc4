package com.chis.modules.sys.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.chis.modules.sys.entity.TsUserInfo;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2019-10-25,TsUserInfoMapper
 */
@Repository
public interface TsUserInfoMapper extends ZwxBaseMapper<TsUserInfo> {
	/**
 	 * <p>方法描述：查询有指定菜单权限的用户，接收人所在单位的单位属性为疾控中心</p>
 	 * @MethodAuthor qrr,2019年10月26日,selectMenuAuthUser
	 * */
	public List<Integer> selectMenuAuthUser(@Param("menuEn")String menuEn,@Param("zoneGb")String zoneGb,@Param("zoneType")String zoneType);

	public List<TsUserInfo> findTsUserAll();
}

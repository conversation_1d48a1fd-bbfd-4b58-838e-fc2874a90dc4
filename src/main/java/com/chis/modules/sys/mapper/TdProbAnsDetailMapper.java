package com.chis.modules.sys.mapper;

import com.chis.modules.sys.entity.TdProbAnsDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TdProbAnsDetailMapper
 */
@Repository
public interface TdProbAnsDetailMapper extends ZwxBaseMapper<TdProbAnsDetail> {
    public void deleteTdProbAnsDetail(@Param("busType") Integer busType,@Param("mainId") Integer mainId);

    public void deletePsnCheckTdProbAnsDetail(@Param("mainId") Integer mainId, @Param("mainType") Integer mainType);

    
    /**
     * @Description: 通过问卷类型 与 答卷主表Rid集合获取问卷答题详情列表
     * @param queTypeId 问卷类型
     * @param mainIdList  答卷主表rid集合
     *
     * @MethodAuthor pw,2021年10月15日
     */
    List<TdProbAnsDetail> findAnsDetailListByQueTypeIdAndMainIds(@Param("queTypeId") Integer queTypeId,
                                                                 @Param("mainIdList") List<Integer> mainIdList);
}

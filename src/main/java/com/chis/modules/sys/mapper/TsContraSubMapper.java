package com.chis.modules.sys.mapper;

import com.chis.modules.sys.entity.TsContraSub;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2020-11-14,TsContraSubMapper
 */
@Repository
public interface TsContraSubMapper extends ZwxBaseMapper<TsContraSub> {
    public List<TsContraSub> findTsContraSub(@Param("contraCode") String contraCode, @Param("busType") String busType);

    List<TsContraSub> selectContraSubToCountry();

    String selectParam(@Param("type") Integer type,@Param("name") String name);

    List<TsContraSub> findSimpByContraSub(@Param("contraCode")String contraCode, @Param("busiType")String busiType, @Param("codeTypeName")String codeTypeName);

    List<TsContraSub> findSimpLeftCodeByContraSub(@Param("contraCode")String contraCode, @Param("busiType")String busiType, @Param("codeTypeName")String codeTypeName);
}

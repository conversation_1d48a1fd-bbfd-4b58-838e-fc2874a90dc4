package com.chis.modules.sys.mapper;

import com.chis.modules.sys.entity.TbProbColsdefine;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TbProbColsdefineMapper
 */
@Repository
public interface TbProbColsdefineMapper extends ZwxBaseMapper<TbProbColsdefine> {

    List<TbProbColsdefine> findTbProbColsdefinesByRidList(@Param("ridList") List<Integer> ridList);

    List<TbProbColsdefine> findTbProbColsdefinesByTableId(@Param("tableId") Integer tableId);

    /**
     * <p>方法描述： 通过表格题定义rid集合 获取表格列定义对象集合 </p>
     * @MethodAuthor： pw 2023/3/17
     **/
    List<TbProbColsdefine> findTbProbColsdefinesByTableIdList(@Param("list") List<Integer> list);

}

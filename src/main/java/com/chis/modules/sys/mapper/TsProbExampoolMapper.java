package com.chis.modules.sys.mapper;

import com.chis.modules.sys.entity.TsProbExampool;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TsProbExampoolMapper
 */
@Repository
public interface TsProbExampoolMapper extends ZwxBaseMapper<TsProbExampool> {
    /**
     * <p>方法描述： 通过rid集合 获取TsProbExampool </p>
     * @MethodAuthor： pw 2022/11/25
     **/
    List<TsProbExampool> findExamPoolListByRids(@Param("list") List<Integer> list);
}

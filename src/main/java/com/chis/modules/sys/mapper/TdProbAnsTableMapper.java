package com.chis.modules.sys.mapper;

import com.chis.modules.sys.entity.TdProbAnsTable;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TdProbAnsTableMapper
 */
@Repository
public interface TdProbAnsTableMapper extends ZwxBaseMapper<TdProbAnsTable> {
    public void deleteTdProbAnsTable(@Param("busType") Integer busType,@Param("mainId") Integer mainId);

    public void deletePsnCheckTdProbAnsTable(@Param("mainId")Integer mainId, @Param("mainType")Integer mainType);

    /**
     * @Description: 通过问卷答题详情rid集合 批量查询
     *
     * @MethodAuthor pw,2021年08月26日
     */
    List<TdProbAnsTable> findTdProbAnsTablesByDetailRidList(@Param("detailRidList") List<Integer> detailRidList);


    List<Integer> selectPsnCheckTdProbAnsTableRids(@Param("mainId")Integer mainId, @Param("mainType")Integer mainType);
}

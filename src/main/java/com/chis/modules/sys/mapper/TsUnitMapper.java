package com.chis.modules.sys.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.chis.modules.sys.entity.TsUnit;

import java.util.List;

/**
 * <p> Mapper 接口：单位管理  </p>
 *
 * @ClassAuthor 机器人,2019-10-24,TsUnitMapper
 */
@Repository
public interface TsUnitMapper extends ZwxBaseMapper<TsUnit> {
    List<TsUnit> pageListToFj(@Param("first") Integer first, @Param("pageSize") Integer pageSize, @Param("property") TsUnit entity);

    public List<TsUnit> findTsUnitAll();

    List<TsUnit> findTsUnitAllByZoneCode(@Param("zoneCode") String zoneCode);

    List<TsUnit> findTsUnitByDiagOrg();
}

package com.chis.modules.sys.mapper;

import com.chis.modules.sys.entity.TbProbTabdefine;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p> Mapper 接口：  </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TbProbTabdefineMapper
 */
@Repository
public interface TbProbTabdefineMapper extends ZwxBaseMapper<TbProbTabdefine> {

    /**
     * <p>方法描述： 通过rid集合获取表格题定义对象集合 </p>
     * @MethodAuthor： pw 2023/3/17
     **/
    List<TbProbTabdefine> findTbProbTabdefineListByRids(@Param("list") List<Integer> list);
}

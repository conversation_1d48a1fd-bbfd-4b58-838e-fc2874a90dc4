package com.chis.modules.sys.config.mybatisplus;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.AbstractSqlInjector;
import com.baomidou.mybatisplus.core.injector.methods.*;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * MybatisPlusSql注入器
 * </p>
 *
 * <AUTHOR>
 */
public class MybatisPlusSqlInjector extends AbstractSqlInjector {

    @Override
    public boolean isInjectSqlRunner() {
        return false;
    }

    @Override
    public List<AbstractMethod> getMethodList() {
        return Stream.of(
                new Insert(),
                new Delete(),
                new DeleteById(),
                new Update(),
                new UpdateById(),
                new SelectById(),
                new SelectCount(),
                new SelectObjs(),
                new SelectList(),
                new SelectPage()
        ).collect(Collectors.toList());
    }

}

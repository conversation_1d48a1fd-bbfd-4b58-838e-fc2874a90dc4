package com.chis.modules.sys.config.mybatisplus;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/***
 * <p>类描述: 自动填充功能 </p>
 *  自定义实现类 ==> 实现元对象处理器接口
 * @ClassAuthor mxp, 2018/12/3,CommonMetaObjectHandler
 */
@Component
public class CommonMetaObjectHandler implements MetaObjectHandler {

    /**
     * 创建时间
     */
    private final String createDate = "createDate";
    /**
     * 修改时间
     */
    private final String modifyDate = "modifyDate";
    /**
     * 创建者ID
     */
    private final String createManid = "createManid";

    /**
     * 修改者ID
     */
    private final String modifyManid = "modifyManid";

    /***
     * <p>方法描述: 新增操作自动填充字段</p>
     *
     * @MethodAuthor mxp, 2018/12/3,insertFill
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        Object createManid = this.getFieldValByName(this.createManid, metaObject);
        if (createManid == null) {
            this.setFieldValByName(this.createManid, currentUserId(), metaObject);
        }
        Object createDate = this.getFieldValByName(this.createDate, metaObject);
        if (createDate == null) {
            this.setFieldValByName(this.createDate, new Date(), metaObject);
        }

        System.out.println("-----------------自动注入新增生效");
        System.out.println("-----------------自动注入新增生效");
        System.out.println("-----------------自动注入新增生效");
    }

    /***
     * <p>方法描述: 更新操作自动填充字段</p>
     *
     * @MethodAuthor mxp, 2018/12/3,updateFill
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        Object modifyManid = this.getFieldValByName(this.modifyManid, metaObject);
        if (modifyManid == null) {
            this.setFieldValByName(this.modifyManid, currentUserId(), metaObject);
        }
        Object modifyDate = this.getFieldValByName(this.modifyDate, metaObject);
        if (modifyDate == null) {
            this.setFieldValByName(this.modifyDate, new Date(), metaObject);
        }

        System.out.println("-----------------自动注入更新生效");
        System.out.println("-----------------自动注入更新生效");
        System.out.println("-----------------自动注入更新生效");

    }

    /***
     * <p>方法描述: 获取当前用户ID</p>
     *
     * @MethodAuthor mxp, 2018/12/3,currentUid
     */
    private Integer currentUserId() {
        Integer uid = null;
        try {
            // TODO: 2018/12/3 获取当前登录人id
            uid = 1;
//            uid = RequestKit.currentUid();
        } catch (Exception ignored) {
        }
        return uid;
    }

}

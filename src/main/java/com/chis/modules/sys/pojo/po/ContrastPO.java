package com.chis.modules.sys.pojo.po;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import com.chis.modules.sys.entity.TsContraSub;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.enums.contrast.IContrastEnum;
import com.chis.modules.sys.enums.contrast.ISimpleCodeEnum;

import java.util.Map;

/**
 * 对照缓存
 */
public class ContrastPO {
    /**
     * 码表
     */
    public static final Cache<ISimpleCodeEnum, Map<String, TsSimpleCode>> simpleCodeCache = CacheUtil.newTimedCache(86400000);
    /**
     * 对照
     */
    public static final Cache<IContrastEnum, Map<String, TsContraSub>> contrastCache = CacheUtil.newTimedCache(86400000);
}

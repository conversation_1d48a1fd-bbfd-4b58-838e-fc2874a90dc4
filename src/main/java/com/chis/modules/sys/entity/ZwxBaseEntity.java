package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/***
 * <p>类描述: 实体通用基类 </p>
 *
 * @ClassAuthor mxp,2018/12/4,ZwxBaseEntity
 */
@Data
public class ZwxBaseEntity implements Serializable {
    /**
     * 主键：默认自增
     */
    @TableId
    protected Integer rid;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    protected Integer createManid;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    protected Date createDate;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected Integer modifyManid;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected Date modifyDate;

    public ZwxBaseEntity() {
    }

    public ZwxBaseEntity(Integer rid) {
        this.rid = rid;
    }
}

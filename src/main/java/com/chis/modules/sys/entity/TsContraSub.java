package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-14,TsContraSub
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TS_CONTRA_SUB")
public class TsContraSub extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TsContraMain fkByMainId;

    @TableField("BUSI_TYPE")
    private String busiType;

    @TableField("LEFT_CODE")
    private String leftCode;

    @TableField("RIGHT_CODE")
    private String rightCode;

    @TableField("LEFT_DESC")
    private String leftDesc;

    @TableField("DESCR")
    private String descr;

    @TableField("DSF_TAG")
    private String dsfTag;

    @TableField("DSF_SPECIAL_DESC")
    private String dsfSpecialDesc;

    @TableField(exist = false)
    private Integer simpRid;

    @TableField(exist = false)
    private String extends2;

    @TableField(exist = false)
    private String extends1;
    @TableField(exist = false)
    private String extends3;

    public TsContraSub(Integer rid) {
        super(rid);
    }


}

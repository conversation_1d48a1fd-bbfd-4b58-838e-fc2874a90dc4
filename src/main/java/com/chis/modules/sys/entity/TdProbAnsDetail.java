package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TdProbAnsDetail
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_PROB_ANS_DETAIL")
@KeySequence(value = "TD_PROB_ANS_DETAIL_SEQ",clazz = Integer.class)
public class TdProbAnsDetail extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("MAIN_TYPE")
    private Integer mainType;

    @TableField(value = "QUE_TYPE_ID" , el = "fkByQueTypeId.rid")
    private TsSimpleCode fkByQueTypeId;

    @TableField("MAIN_ID")
    private Integer mainId;

    @TableField(value = "QUEST_ID" , el = "fkByQuestId.rid")
    private TsProbSubject fkByQuestId;

    @TableField("OPTION_VALUE")
    private String optionValue;

    @TableField("SCORE_VALUE")
    private String scoreValue;

    @TableField("FILL_VALUE")
    private String fillValue;

    @TableField("MULTI_NUM")
    private String multiNum;

    @TableField(value = "EXAMPOOL_ID" , el = "fkByExampoolId.rid")
    private TsProbExampool fkByExampoolId;


    public TdProbAnsDetail(Integer rid) {
        super(rid);
    }


}

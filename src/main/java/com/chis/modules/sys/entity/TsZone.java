package com.chis.modules.sys.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TS_ZONE")
public class TsZone extends ZwxBaseEntity {
	private static final long serialVersionUID = 1L;
	@TableField("ZONE_GB")
	private String zoneGb;
	@TableField("ZONE_NAME")
	private String zoneName;
	/**1：国家2：省级3：市级4：县区5：乡镇*/
	@TableField("ZONE_TYPE")
	private Integer zoneType;
	/**1:启用，0：停用*/
	@TableField("IF_REVEAL")
	private Integer ifReveal;
	@TableField("STOP_DATE")
	private Date stopDate;
	@TableField("ZONE_CODE")
	private String zoneCode;
	@TableField("FULL_NAME")
	private String fullName;
	@TableField("DSF_CODE")
	private String dsfCode;//第三方系统编码
	@TableField("IF_CITY_DIRECT")
	private String ifCityDirect;
	@TableField("REAL_ZONE_TYPE")
	private String realZoneType;
	@TableField("IF_PROV_DIRECT")
	private String ifProvDirect;
	
	public TsZone(Integer rid) {
		super(rid);
	}
}
package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-07-07,TsSystemParam
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TS_SYSTEM_PARAM")
public class TsSystemParam extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("PARAM_TYPE")
    private String paramType;

    @TableField("PARAM_NAME")
    private String paramName;

    @TableField("PARAM_VALUE")
    private String paramValue;

    @TableField("PARAM_RMK")
    private String paramRmk;

    @TableField("IF_REVEAL")
    private String ifReveal;

    @TableField("SPLSHT")
    private String splsht;

    @TableField("DATA_TYPE")
    private String dataType;

    @TableField("DICT_TYPE")
    private String dictType;

    @TableField("DICT_VALUE")
    private String dictValue;


    public TsSystemParam(Integer rid) {
        super(rid);
    }


}

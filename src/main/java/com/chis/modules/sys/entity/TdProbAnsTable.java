package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TdProbAnsTable
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TD_PROB_ANS_TABLE")
@KeySequence(value = "TD_PROB_ANS_TABLE_SEQ",clazz = Integer.class)
public class TdProbAnsTable extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "SUR_SUBJECTID" , el = "fkBySurSubjectid.rid")
    private TdProbAnsDetail fkBySurSubjectid;

    @TableField(value = "COL_ID" , el = "fkByColId.rid")
    private TbProbColsdefine fkByColId;

    @TableField("NUM")
    private String num;

    @TableField("COL_VALUE")
    private String colValue;


    public TdProbAnsTable(Integer rid) {
        super(rid);
    }


}

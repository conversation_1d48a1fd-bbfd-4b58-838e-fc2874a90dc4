package com.chis.modules.sys.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2019-10-25,TdMsgMain
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("TD_MSG_MAIN")
@KeySequence(value = "TD_MSG_MAIN_SEQ",clazz = Integer.class)
public class TdMsgMain{
    @TableId
    protected Integer rid;
    @TableField("INFO_TITLE")
    private String infoTitle;

    @TableField("MSG_TYPE")
    private String msgType;

    @TableField(value = "PUBLISH_MAN" , el = "fkByPublishMan.rid")
    private TsUserInfo fkByPublishMan;

    @TableField("PUBLISH_TIME")
    private Date publishTime;

    @TableField("NET_ADR")
    private String netAdr;

    @TableField("NET_NAME")
    private String netName;

    @TableField(value = "INFO_ID" , el = "fkByInfoId.rid")
    private TdInfoMain fkByInfoId;

    @TableField("APPEND_KEYS")
    private String appendKeys;

    @TableField("SUB_TYPE")
    private String subType;

    @TableField("MENU_ID")
    private String menuId;

    @TableField("IS_TODO")
    private String isTodo;

    @TableField("TODO_STATE")
    private String todoState;
    /**业务主键*/
    @TableField("BUS_ID")
	private Integer busId;


    public TdMsgMain(Integer rid) {
        this.rid = rid;
    }


}

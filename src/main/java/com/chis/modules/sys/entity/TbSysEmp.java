package com.chis.modules.sys.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2019-10-25,TbSysEmp
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TB_SYS_EMP")
public class TbSysEmp extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("NUM")
    private String num;

    @TableField("EMP_CODE")
    private String empCode;

    @TableField("EMP_NAME")
    private String empName;

    @TableField("EMP_SEX")
    private String empSex;

    @TableField("EMP_NATION")
    private String empNation;

    @TableField(value = "DEPT_ID" , el = "fkByDeptId.rid")
    private TsOffice fkByDeptId;

    @TableField("GROUP_ID")
    private String groupId;

    @TableField("IDC")
    private String idc;

    @TableField("WORK_TYPEID")
    private String workTypeid;

    @TableField("BIRTHDAY")
    private Date birthday;

    @TableField("IS_LEADER")
    private String isLeader;

    @TableField("MB_NUM")
    private String mbNum;

    @TableField("DUTY")
    private String duty;

    @TableField("POSITION")
    private String position;

    @TableField("EDU_DEGREE")
    private String eduDegree;

    @TableField("WORK_DAY")
    private Date workDay;

    @TableField("REGULAR_DAY")
    private Date regularDay;

    @TableField("POLITICS")
    private String politics;

    @TableField("USED_NAME")
    private String usedName;

    @TableField("MARITAL_STATUS")
    private String maritalStatus;

    @TableField("RELIGION")
    private String religion;

    @TableField("ONREG")
    private String onreg;

    @TableField("ONDUTY")
    private String onduty;

    @TableField(value = "PSN_PROP" , el = "fkByPsnProp.rid")
    private TsSimpleCode fkByPsnProp;

    @TableField("PSN_SIGN")
    private String psnSign;

    @TableField("PROF_LEVELID")
    private String profLevelid;

    @TableField("WORK_YEARS")
    private String workYears;

    @TableField("NEWCHIL_INOC")
    private String newchilInoc;

    @TableField("ADDWORK")
    private Date addwork;

    @TableField("FIRST_EDU")
    private String firstEdu;

    @TableField("FIRST_ACADEDU")
    private String firstAcadedu;

    @TableField("FIRST_PROF")
    private String firstProf;

    @TableField("FIRST_SCHL")
    private String firstSchl;

    @TableField("FIRSTGRD_TIME")
    private Date firstgrdTime;

    @TableField("ACAD_DEGREE")
    private String acadDegree;

    @TableField("LAST_PROF")
    private String lastProf;

    @TableField("LAST_SCHL")
    private String lastSchl;

    @TableField("LASTGRD_TIME")
    private Date lastgrdTime;

    @TableField("CREDITS_CARDNO")
    private String creditsCardno;

    @TableField("BIRTH_PLACE")
    private String birthPlace;

    @TableField("ADDRESS")
    private String address;


    public TbSysEmp(Integer rid) {
        super(rid);
    }


}

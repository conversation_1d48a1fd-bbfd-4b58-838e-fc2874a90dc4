package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TbProbColsdefine
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TB_PROB_COLSDEFINE")
public class TbProbColsdefine extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "TABLE_ID" , el = "fkByTableId.rid")
    private TbProbTabdefine fkByTableId;

    @TableField("NUM")
    private String num;

    @TableField("COL_NAME")
    private String colName;

    @TableField("COL_DESC")
    private String colDesc;

    @TableField("COL_TYPE")
    private String colType;

    @TableField("COLS")
    private String cols;

    @TableField("COL_EXPR")
    private String colExpr;

    @TableField("COL_LENTH")
    private String colLenth;

    @TableField("COL_PREC")
    private String colPrec;

    @TableField("COL_MUST")
    private String colMust;

    @TableField("COL_DEFVALUE")
    private String colDefvalue;

    @TableField("SCOPE_CONS")
    private String scopeCons;

    @TableField("MIN_VALUE")
    private String minValue;

    @TableField("MAX_VALUE")
    private String maxValue;

    @TableField("DS_TYPE")
    private String dsType;

    @TableField("DS_CDCODE")
    private String dsCdcode;

    @TableField("DS_SQL")
    private String dsSql;

    @TableField("ROW_INDEX")
    private String rowIndex;

    @TableField("COL_INDEX")
    private String colIndex;

    @TableField("ROWSPAN")
    private String rowspan;

    @TableField("STATE")
    private String state;


    public TbProbColsdefine(Integer rid) {
        super(rid);
    }


}

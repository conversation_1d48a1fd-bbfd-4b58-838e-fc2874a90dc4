package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2023-11-09,TsSysHoliday
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TS_SYS_HOLIDAY")
public class TsSysHoliday extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("HOLI_DESC")
    private String holiDesc;

    @TableField("HOLI_TYPE")
    private Integer holiType;

    @TableField("START_DATE")
    private Date startDate;

    @TableField("END_DATE")
    private Date endDate;

    @TableField("STATE")
    private Integer state;


    public TsSysHoliday(Integer rid) {
        super(rid);
    }


}

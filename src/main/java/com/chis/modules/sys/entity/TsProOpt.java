package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2023-03-04,TsProOpt
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TS_PRO_OPT")
public class TsProOpt extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "QUEST_ID" , el = "fkByQuestId.rid")
    private TsProbSubject fkByQuestId;

    @TableField("NUM")
    private Long num;

    @TableField("OPTION_DESC")
    private String optionDesc;

    @TableField("OPTION_IMG")
    private String optionImg;

    @TableField("OPTION_VALUE")
    private String optionValue;

    @TableField("OPTION_SCORE")
    private Integer optionScore;

    @TableField("NEED_FILL")
    private Integer needFill;

    @TableField("RMK")
    private String rmk;

    @TableField("STATE")
    private Integer state;

    @TableField("OTHER_DESC")
    private String otherDesc;

    @TableField("OTHER_IMG")
    private String otherImg;

    @TableField("JUMP_TYPE")
    private Integer jumpType;

    @TableField("JUMP_QUEST_CODE")
    private String jumpQuestCode;

    @TableField("IS_ALTER")
    private Integer isAlter;

    @TableField("IS_CORRECT")
    private Integer isCorrect;

    @TableField("IS_MULTI")
    private Integer isMulti;


    public TsProOpt(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.sys.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2019-10-25,TdInfoMain
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("TD_INFO_MAIN")
@KeySequence(value = "TD_INFO_MAIN_SEQ",clazz = Integer.class)
public class TdInfoMain {
    @TableId
    protected Integer rid;
    @TableField("INFO_TITLE")
    private String infoTitle;

    @TableField("INFO_CONTENT")
    private String infoContent;

    @TableField(value = "PUBLISH_MAN" , el = "fkByPublishMan.rid")
    private TsUserInfo fkByPublishMan;

    @TableField("PUBLISH_TIME")
    private Date publishTime;


    public TdInfoMain(Integer rid) {
        this.rid = rid;
    }


}

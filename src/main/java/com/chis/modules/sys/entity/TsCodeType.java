package com.chis.modules.sys.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 类描述：编码类型表 </p>
 *
 * @ClassAuthor 机器人,2019-02-18,TsCodeType
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("ts_code_type")
public class TsCodeType{
    @TableId
    protected Integer rid;
    @TableField("NUM")
    private Integer num;

    @TableField("CODE_TYPE_NAME")
    private String codeTypeName;

    @TableField("CODE_TYPE_DESC")
    private String codeTypeDesc;

    @TableField("TREE_TAG")
    private BigDecimal treeTag;

    @TableField("ISUSER_TYPE")
    private BigDecimal isuserType;

    @TableField("PARAM_TYPE")
    private BigDecimal paramType;


    public TsCodeType(Integer rid) {
        this.rid = rid;
    }


}

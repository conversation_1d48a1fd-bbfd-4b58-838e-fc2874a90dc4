package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2020-11-14,TsContraMain
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TS_CONTRA_MAIN")
public class TsContraMain extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("CONTRA_CODE")
    private String contraCode;

    @TableField("DESCR")
    private String descr;


    public TsContraMain(Integer rid) {
        super(rid);
    }


}

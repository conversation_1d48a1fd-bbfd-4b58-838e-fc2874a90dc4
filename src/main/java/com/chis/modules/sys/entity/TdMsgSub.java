package com.chis.modules.sys.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2019-10-25,TdMsgSub
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("TD_MSG_SUB")
@KeySequence(value = "TD_MSG_SUB_SEQ",clazz = Integer.class)
public class TdMsgSub{

    @TableId
    protected Integer rid;
    @TableField(value = "MAIN_ID" , el = "fkByMainId.rid")
    private TdMsgMain fkByMainId;

    @TableField(value = "PUBLISH_MAN" , el = "fkByPublishMan.rid")
    private TsUserInfo fkByPublishMan;

    @TableField("PUBLISH_TIME")
    private Date publishTime;

    @TableField("ACCEPT_STATE")
    private String acceptState;


    public TdMsgSub(Integer rid) {
        this.rid = rid;
    }


}

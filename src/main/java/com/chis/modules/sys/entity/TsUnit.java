package com.chis.modules.sys.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 类描述：单位管理 </p>
 *
 * @ClassAuthor 机器人,2019-10-24,TsUnit
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TS_UNIT")
public class TsUnit extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("ZONE_ID")
    private String zoneId;

    @TableField("UNITNAME")
    private String unitname;

    @TableField("UNIT_SIMPNAME")
    private String unitSimpname;

    @TableField("UNITZIP")
    private String unitzip;

    @TableField("UNITADDR")
    private String unitaddr;

    @TableField("UNITAERA")
    private String unitaera;

    @TableField("UNITTEL")
    private String unittel;

    @TableField("UNITFAX")
    private String unitfax;

    @TableField("UNITEMAIL")
    private String unitemail;

    @TableField("IF_REVEAL")
    private String ifReveal;

    @TableField("STOP_DATE")
    private Date stopDate;

    @TableField("SPLSHT")
    private String splsht;

    @TableField("UNIT_CODE")
    private String unitCode;

    @TableField("REG_CODE")
    private String regCode;

    @TableField("LNG")
    private String lng;

    @TableField("LAT")
    private String lat;

    @TableField("LINK_MAN")
    private String linkMan;

    @TableField("MEDI_LIC")
    private String mediLic;

    @TableField("RAD_LIC")
    private String radLic;

    @TableField("ORG_FZ")
    private String orgFz;

    @TableField("ORG_FZZW")
    private String orgFzzw;

    @TableField("ORG_TEL")
    private String orgTel;

    @TableField("CREDIT_CODE")
    private String creditCode;

    @TableField("SAFE_UNITNAME")
    private String safeUnitname;

    @TableField("JD_UNITNAME")
    private String jdUnitname;

    @TableField("PROVE_BAK")
    private String proveBak;

    @TableField("IF_COMPLETE")
    private String ifComplete;

    @TableField("UPDATETAG")
    private String updatetag;

    @TableField("ERROR_MSG")
    private String errorMsg;

    @TableField("GPY_VERSION")
    private String gpyVersion;

    @TableField(value = "MANAGE_ZONE_ID")
    private String manageZoneId;

    /**职业卫生数据上传[福建汇聚]rid*/
    private Integer uploadRcdId;

    @TableField(value = "WRITE_NO")
    private String writeNo;

    @TableField(value = "TRUST_WRITE_NO")
    private String trustWriteNo;

    @TableField(value = "PROVE_WRITE_NO")
    private String proveWriteNo;

    @TableField(value = "RED_UNIT_NAME")
    private String redUnitName;
    @TableField(value = "WRITE_NO_RULE")
    private String writeNoRule;

    public TsUnit(Integer rid) {
        super(rid);
    }



}

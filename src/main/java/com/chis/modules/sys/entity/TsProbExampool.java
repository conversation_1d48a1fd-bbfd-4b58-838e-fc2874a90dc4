package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TsProbExampool
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TS_PROB_EXAMPOOL")
public class TsProbExampool extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "TYPE_ID" , el = "fkByTypeId.rid")
    private TsSimpleCode fkByTypeId;

    @TableField("QES_CODE")
    private String qesCode;

    @TableField("QES_LEVEL_CODE")
    private String qesLevelCode;

    @TableField("TITLE_DESC")
    private String titleDesc;

    @TableField("QUEST_TYPE")
    private String questType;

    @TableField("MUST_ASK")
    private String mustAsk;

    @TableField("MIN_SELECT_NUM")
    private String minSelectNum;

    @TableField("OPT_LAYOUT")
    private String optLayout;

    @TableField("COLS")
    private String cols;

    @TableField("STATE")
    private String state;

    @TableField("OTHER_DESC")
    private String otherDesc;

    @TableField("OTHER_IMG")
    private String otherImg;

    @TableField("JUMP_TYPE")
    private String jumpType;

    @TableField("JUMP_QUEST_CODE")
    private String jumpQuestCode;

    @TableField("SLIDE_MINVAL")
    private String slideMinval;

    @TableField("SLIDE_MAXVAL")
    private String slideMaxval;

    @TableField("SLIDE_MAX_DESC")
    private String slideMaxDesc;

    @TableField("SLIDE_MIN_DESC")
    private String slideMinDesc;

    @TableField("INVOKE_SCRT")
    private String invokeScrt;

    @TableField("IS_MULTI")
    private String isMulti;

    @TableField("FILL_MAX_RANGE")
    private String fillMaxRange;

    @TableField(value = "TABLE_ID" , el = "fkByTableId.rid")
    private TbProbTabdefine fkByTableId;

    @TableField("SCORE_SCRIPT")
    private String scoreScript;

    private List<TsProPoolOpt> poolOpts;

    public TsProbExampool(Integer rid) {
        super(rid);
    }


}

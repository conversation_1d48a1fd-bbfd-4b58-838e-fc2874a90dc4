package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TbProbRowtitle
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TB_PROB_ROWTITLE")
public class TbProbRowtitle extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "TABLE_ID" , el = "fkByTableId.rid")
    private TbProbTabdefine fkByTableId;

    @TableField("TITLE")
    private String title;

    @TableField("ROW_INDEX")
    private String rowIndex;

    @TableField("COL_INDEX")
    private String colIndex;

    @TableField("COLSPAN")
    private String colspan;

    @TableField("ROWSPAN")
    private String rowspan;

    @TableField("STATE")
    private String state;


    public TbProbRowtitle(Integer rid) {
        super(rid);
    }


}

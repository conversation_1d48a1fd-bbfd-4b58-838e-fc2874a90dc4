package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2019-11-01,TsMenu
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TS_MENU")
public class TsMenu extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("MENU_LEVEL_NO")
    private String menuLevelNo;

    @TableField("MENU_CN")
    private String menuCn;

    @TableField("MENU_EN")
    private String menuEn;

    @TableField("MENU_SIMPLE")
    private String menuSimple;

    @TableField("ISFUNC")
    private String isfunc;

    @TableField("MENU_URI")
    private String menuUri;

    @TableField("MENU_ICON")
    private String menuIcon;

    @TableField("NUM")
    private String num;

    @TableField("BIG_ICON")
    private String bigIcon;

    @TableField("IF_POP")
    private String ifPop;


    public TsMenu(Integer rid) {
        super(rid);
    }


}

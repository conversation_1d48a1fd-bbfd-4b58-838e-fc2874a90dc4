package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TsProbLib
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TS_PROB_LIB")
public class TsProbLib extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "UNIT_ID" , el = "fkByUnitId.rid")
    private TsUnit fkByUnitId;

    @TableField(value = "QUEST_SORTID" , el = "fkByQuestSortid.rid")
    private TsSimpleCode fkByQuestSortid;

    @TableField("QUEST_NAME")
    private String questName;

    @TableField("RMK")
    private String rmk;

    @TableField("NUM")
    private String num;

    @TableField("BACK_IMAGE")
    private String backImage;

    @TableField("STATE")
    private String state;

    @TableField("HTML_NAME")
    private String htmlName;

    @TableField("LIB_SCRIPT")
    private String libScript;

    @TableField("LIB_INIT_SRC")
    private String libInitSrc;

    @TableField("VERIFY_SCRIPT")
    private String verifyScript;

    @TableField("PARAM_TYPE")
    private String paramType;


    public TsProbLib(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TbProbTabdefine
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TB_PROB_TABDEFINE")
public class TbProbTabdefine extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("TAB_NAME")
    private String tabName;

    @TableField("RMK")
    private String rmk;

    @TableField("ROW_FIXED")
    private String rowFixed;

    @TableField("DEFAULT_LINE_NUM")
    private String defaultLineNum;

    /** 列定义 */
    private List<TbProbColsdefine> cols = new ArrayList<>(0);
    /** 行标题 */
    private List<TbProbRowtitle> rowtitles = new ArrayList<>(0);


    public TbProbTabdefine(Integer rid) {
        super(rid);
    }


}

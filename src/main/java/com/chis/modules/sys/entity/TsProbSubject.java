package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TsProbSubject
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TS_PROB_SUBJECT")
public class TsProbSubject extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "QUESTLIB_ID" , el = "fkByQuestlibId.rid")
    private TsProbLib fkByQuestlibId;

    @TableField("SHOW_CODE")
    private String showCode;

    @TableField("QES_CODE")
    private String qesCode;

    @TableField("QES_LEVEL_CODE")
    private String qesLevelCode;

    @TableField("NUM")
    private String num;

    @TableField("TITLE_DESC")
    private String titleDesc;

    @TableField("QUEST_TYPE")
    private String questType;

    @TableField("MUST_ASK")
    private String mustAsk;

    @TableField("MIN_SELECT_NUM")
    private String minSelectNum;

    @TableField("OPT_LAYOUT")
    private String optLayout;

    @TableField("COLS")
    private String cols;

    @TableField("SHOW_SCRIPT")
    private String showScript;

    @TableField("QUEST_UNIT")
    private String questUnit;

    @TableField("STATE")
    private String state;

    @TableField("OTHER_DESC")
    private String otherDesc;

    @TableField("OTHER_IMG")
    private String otherImg;

    @TableField("JUMP_TYPE")
    private String jumpType;

    @TableField("JUMP_QUEST_CODE")
    private String jumpQuestCode;

    @TableField("SLIDE_MAXVAL")
    private String slideMaxval;

    @TableField("SLIDE_MINVAL")
    private String slideMinval;

    @TableField("SLIDE_MAX_DESC")
    private String slideMaxDesc;

    @TableField("SLIDE_MIN_DESC")
    private String slideMinDesc;

    @TableField(value = "POOL_ID" , el = "fkByPoolId.rid")
    private TsProbExampool fkByPoolId;

    @TableField("INVOKE_SCRT")
    private String invokeScrt;

    @TableField("OPTION_SCORE")
    private String optionScore;

    @TableField("IS_MULTI")
    private String isMulti;

    @TableField(value = "TABLE_ID" , el = "fkByTableId.rid")
    private TbProbTabdefine fkByTableId;

    @TableField("FILL_MAX_RANGE")
    private String fillMaxRange;

    @TableField("RIGHT_CODE")
    private String rightCode;


    public TsProbSubject(Integer rid) {
        super(rid);
    }


}

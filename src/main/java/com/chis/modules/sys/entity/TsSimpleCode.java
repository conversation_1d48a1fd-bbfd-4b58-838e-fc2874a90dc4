package com.chis.modules.sys.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 类描述：编码表 </p>
 *
 * @ClassAuthor 机器人,2019-02-18,TsSimpleCode
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("ts_simple_code")
public class TsSimpleCode extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("CODE_TYPE_ID")
    private TsCodeType fkByCodeTypeId;

    @TableField("CODE_NO")
    private String codeNo;

    @TableField("CODE_NAME")
    private String codeName;

    @TableField("CODE_DESC")
    private String codeDesc;

    @TableField("CODE_LEVEL_NO")
    private String codeLevelNo;

    @TableField("CODE_PATH")
    private String codePath;

    @TableField("PUBLISH_TAG")
    private BigDecimal publishTag;

    @TableField("IF_REVEAL")
    private BigDecimal ifReveal;

    @TableField("STOP_DATE")
    private Date stopDate;

    @TableField("SPLSHT")
    private String splsht;

    @TableField("NUM")
    private Integer num;

    @TableField("EXTENDS1")
    private String extends1;

    @TableField("EXTENDS2")
    private String extends2;

    @TableField("EXTENDS3")
    private String extends3;

    @TableField("EXTENDS4")
    private String extends4;

    @TableField("EXTENDS5")
    private String extends5;

    @TableField("EXTENDS6")
    private String extends6;

    public TsSimpleCode(Integer rid) {
        super(rid);
    }


}

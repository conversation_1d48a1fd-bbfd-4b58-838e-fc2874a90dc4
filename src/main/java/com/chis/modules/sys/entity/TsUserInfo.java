package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2019-10-25,TsUserInfo
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TS_USER_INFO")
public class TsUserInfo extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField("USER_TYPE")
    private String userType;

    @TableField(value = "EMP_ID" , el = "fkByEmpId.rid")
    private TbSysEmp fkByEmpId;

    @TableField("USER_NO")
    private String userNo;

    @TableField("USERNAME")
    private String username;

    @TableField("PASSWORD")
    private String password;

    @TableField("IF_MODPSW")
    private String ifModpsw;

    @TableField("RMK")
    private String rmk;

    @TableField("IF_REVEAL")
    private String ifReveal;

    @TableField(value = "UNIT_RID" , el = "fkByUnitRid.rid")
    private TsUnit fkByUnitRid;

    @TableField("USERADMIN")
    private String useradmin;

    @TableField("MB_NUM")
    private String mbNum;

    @TableField("DISP_KJMENU")
    private String dispKjmenu;

    @TableField("EMAIL")
    private String email;

    @TableField("IS_ADD")
    private String isAdd;

    @TableField("UPLOAD_TAG")
    private String uploadTag;

    @TableField("ERR_MSG")
    private String errMsg;


    public TsUserInfo(Integer rid) {
        super(rid);
    }


}

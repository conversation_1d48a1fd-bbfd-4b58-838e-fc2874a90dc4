package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2019-07-31,TbTjSrvorg
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TB_TJ_SRVORG")
public class TbTjSrvorg extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value="ZONE_ID")
    private String zoneId;

    private String zoneGb;

    @TableField("UNIT_CODE")
    private String unitCode;

    @TableField("UNIT_NAME")
    private String unitName;

    @TableField("APT_SORTID")
    private String aptSortid;

    @TableField("STOP_TAG")
    private String stopTag;

    @TableField("REG_CODE")
    private String regCode;

    @TableField("REG_ORGID")
    private String regOrgid;

    @TableField("UUID")
    private String uuid;

    /**职业卫生数据上传[福建汇聚]rid*/
    private Integer uploadRcdId;

    public TbTjSrvorg(Integer rid) {
        super(rid);
    }


}

package com.chis.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 类描述： </p>
 *
 * @ClassAuthor 机器人,2019-10-25,TsOffice
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("TS_OFFICE")
public class TsOffice extends ZwxBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableField(value = "UNIT_RID" , el = "fkByUnitRid.rid")
    private TsUnit fkByUnitRid;

    @TableField("NUM")
    private String num;

    @TableField("OFFICECODE")
    private String officecode;

    @TableField("OFFICENAME")
    private String officename;

    @TableField("SIMPL_NAME")
    private String simplName;

    @TableField("OFFICETYPE")
    private String officetype;

    @TableField("OFFICETEL")
    private String officetel;

    @TableField("OFFICEFAX")
    private String officefax;

    @TableField(value = "DEPT_LEADER_ID" , el = "fkByDeptLeaderId.rid")
    private TbSysEmp fkByDeptLeaderId;

    @TableField(value = "MANAGE_MANID" , el = "fkByManageManid.rid")
    private TbSysEmp fkByManageManid;

    @TableField("SPLSHT")
    private String splsht;

    @TableField("IF_REVEAL")
    private String ifReveal;

    @TableField(value = "UP_ID" , el = "fkByUpId.rid")
    private TsOffice fkByUpId;

    @TableField("IS_YJOFFICE")
    private String isYjoffice;


    public TsOffice(Integer rid) {
        super(rid);
    }


}

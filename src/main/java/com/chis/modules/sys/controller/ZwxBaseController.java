package com.chis.modules.sys.controller;

import com.alibaba.fastjson.JSON;
import com.chis.comm.utils.TypeUtil;
import com.chis.modules.sys.constants.ZwxPageCons;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.sys.utils.AntiSQLFilterUtil;
import com.chis.modules.webmvc.api.responses.ApiResponses;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
public class ZwxBaseController<S extends ZwxBaseServiceImpl,T extends ZwxBaseEntity> {

    @Autowired
    protected S baseService;

    @Autowired
    protected HttpServletRequest request;

    @Autowired
    protected HttpServletResponse response;

    /**
     * 成功的ApiResponses
     *
     * @return
     */
    public static <T> ApiResponses<T> success(T object) {
        return ApiResponses.<T>success(object);
    }

    /***
     * <p>方法描述: </p>
     *
     * @MethodAuthor mxp,2018/12/7,pageList
     */
    public String pageList(PageInfo<T> pageInfo){
        this.initPageInfo(pageInfo);
        PageInfo<T> page = baseService.pageList(pageInfo, null);
        return JSON.toJSONString(page);
    }

    /**
     * 初始化分页对象
     *
     */
    protected <T> PageInfo<T> initPageInfo(PageInfo<T> pageInfo) {
        if(pageInfo==null){
            pageInfo = new PageInfo<>();
        }
        // 页数
        Integer pageNum = TypeUtil.castToInt(request.getParameter(ZwxPageCons.PAGE_NUM), ZwxPageCons.DEFAULT_PAGE_NUM);
        // 分页大小
        Integer pageSize = TypeUtil.castToInt(request.getParameter(ZwxPageCons.PAGE_SIZE), ZwxPageCons.DEFAULT_PAGE_SIZE);
        pageSize = pageSize > ZwxPageCons.DEFAULT_PAGE_SIZE ? ZwxPageCons.DEFAULT_PAGE_SIZE : pageSize;

        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        return pageInfo;
    }

    /**
     * 获取安全参数(SQL ORDER BY 过滤)
     *
     * @param parameter
     * @return
     */
    protected String[] getParameterSafeValues(String parameter) {
        return AntiSQLFilterUtil.getSafeValues(request.getParameterValues(parameter));
    }
}

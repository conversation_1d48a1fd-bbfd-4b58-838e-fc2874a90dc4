package com.chis.modules.sys.utils;

import groovy.lang.GroovyShell;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

@Component
public class GroovyScriptEngine implements BeanPostProcessor {

	private GroovyBinding binding = new GroovyBinding();

	public void execute(String script, Map<String, Object> vars) {
		executeObject(script, vars);
	}

	private void setParameters(GroovyShell shell, Map<String, Object> vars) {
		if (vars == null) {
			return;
		}
		Set<Map.Entry<String, Object>> set = vars.entrySet();
		for (Iterator<Map.Entry<String, Object>> it = set.iterator(); it.hasNext();) {
			Map.Entry<String, Object> entry = (Map.Entry) it.next();
			shell.setVariable((String) entry.getKey(), entry.getValue());
		}
	}

	public boolean executeBoolean(String script, Map<String, Object> vars) {
		Boolean rtn = (Boolean) executeObject(script, vars);
		return rtn.booleanValue();
	}

	public String executeString(String script, Map<String, Object> vars) {
		String str = (String) executeObject(script, vars);
		return str;
	}
	
	

	public int executeInt(String script, Map<String, Object> vars) {
		Integer rtn = (Integer) executeObject(script, vars);
		return rtn.intValue();
	}

	public float executeFloat(String script, Map<String, Object> vars) {
		Float rtn = (Float) executeObject(script, vars);
		return rtn.floatValue();
	}

	public Object executeObject(String script, Map<String, Object> vars) {
		GroovyShell shell = new GroovyShell(this.binding);
		setParameters(shell, vars);

		script = script.replace("&apos;", "'").replace("&quot;", "\"").replace("&gt;", ">").replace("&lt;", "<").replace("&nuot;", "\n")
				.replace("&amp;", "&");

		Object rtn = shell.evaluate(script);
		this.binding.clearVariables();
		return rtn;
	}

	@Override
	public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
		if (IScript.class.isAssignableFrom(bean.getClass())) {
			this.binding.setProperty(beanName, bean);
		}
		return bean;
	}

	@Override
	public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
		return bean;
	}

	

	
	
	public static void main(String[] args) {

		GroovyScriptEngine g = new GroovyScriptEngine();
		StringBuilder s = new StringBuilder();
		
		Map<String, Object> vars = new HashMap<String, Object>();
		
		vars.put("q1", "2018");
		vars.put("q2", "11");
		vars.put("q3", "2018");
		vars.put("q4", "11");
		vars.put("q5", "12");
		
		
		s.append("import com.chis.common.utils.DateUtils; ");
		s.append("import com.chis.common.utils.StringUtils;");
		s.append("StringBuilder mess = new StringBuilder();");
		
		s.append("if( StringUtils.isNotBlank(q1)  && StringUtils.isNotBlank(q2) ");
		s.append("	&&	 StringUtils.isNotBlank(q3)  && StringUtils.isNotBlank(q4) && StringUtils.isNotBlank(q5))	{");

		s.append("	Date date1 = DateUtils.parseDate( q1+\"-\"+q2+\"-1\",\"yyyy-MM-dd\");");
		s.append("	Date date2 = DateUtils.parseDate(q3+\"-\"+q4+\"-\"+q5,\"yyyy-MM-dd\");");
		s.append("	if( date1.after(date2))	{");
		s.append("	mess.append(\";1001@出生日期不能大于诊断日期！\") ;");
		s.append("}");

		s.append("};");
		
		
		s.append("if( StringUtils.isNotBlank(q3)  && StringUtils.isNotBlank(q4) && StringUtils.isNotBlank(q5))	{");

		s.append("	Date date2 = DateUtils.parseDate(q3+\"-\"+q4+\"-\"+q5,\"yyyy-MM-dd\");");
		s.append("	if( date2.after(new Date()))	{");
		s.append("	mess.append(\";1001@诊断日期不能大于当前日期！\") ;");
		s.append("}");

		s.append("};");
		
		
		s.append("if( StringUtils.isNotBlank(q1)  && StringUtils.isNotBlank(q2) )	{");

		s.append("	Date date1 = DateUtils.parseDate( q1+\"-\"+q2+\"-1\",\"yyyy-MM-dd\");");
		s.append("	if( date1.after(new Date()))	{");
		s.append("	mess.append(\";1001@出生日期不能大于当前日期！\") ;");
		s.append("}");

		s.append("};");
		
		s.append("if( mess.length() > 0){ return mess.substring(1) };");
				 String executeString = g.executeString(s.toString(), vars);
				 
				 System.err.println(executeString);
				 
		
//		StringBuilder s = new StringBuilder();
//		Set<String> addSet = new HashSet<String>();
//		s.append("if( new BigDecimal(q4_1) >= 6.0) { addSet.add(\"1012\"); };if( new BigDecimal(q4_1) >= 6.0) { addSet.add(\"1009\"); };");
//		Map<String, Object> vars = new HashMap<String, Object>();
//		vars.put("q4_1", "7.1");
//		vars.put("addSet",addSet);
//		 g.executeObject(s.toString(), vars);
//		 System.err.println(addSet);
		
		
		
//		StringBuilder s = new StringBuilder();
////		try {
////			Date date1 = DateUtils.parseDate("q1001","yyyy-mm-dd");
////		} catch (ParseException e) {
////			// TODO Auto-generated catch block
////			e.printStackTrace();
////		}
//		s.append("import com.chis.common.utils.DateUtils; ");
//		
//		s.append("StringBuilder mess = new StringBuilder(); ");
////		s.append("if (null != q1001 && null != q2001) {");
////		
////		s.append("Date date1 = DateUtils.parseDate(q1001,\"yyyy-MM-dd\");");
////		s.append("Date date2 = DateUtils.parseDate(q2001,\"yyyy-MM-dd\");");
//		
////		s.append("if( date1.after(date2))	{");
////		s.append("mess.append(\";1001@发病日期不能大于出生日期！\") ;");
////		s.append("}else{");
//////		s.append("mess.append(\";2;发病日期不能大于出生日期！;\") ;}");
////		s.append("}");
////		s.append("};");
//		s.append("if (null != q1001 && null != q2001) {");
//		s.append("if(q1001 < q2001)	{");
//		s.append("mess.append(\";1001@发病日期不能大于出生日期！\") ;");
//		s.append("}else{");
////		s.append("mess.append(\";2;发病日期不能大于出生日期！;\") ;}");
//		s.append("}");
//		s.append("};");
//		s.append("if( mess.length() > 0){ return mess.substring(1) };");
//		
//		
//		Map<String, Object> vars = new HashMap<String, Object>();
//		vars.put("q2001","2016-5-1");
//		
//		String executeString = g.executeString(s.toString(), vars);
		
		
		
		// if(new BigDecimal("10") > 10.2 ){
		// System.err.println("====");
		// }
//		String s = "if( new BigDecimal(q1) > 10.2 && new BigDecimal(q1) < 11) { addSet.add(\"1002\"); }; ";

//		Integer q1001 = 30;
//		Integer q2001 = 40;
//		if (null != q1001 && null != q2001) {
//			if(q1001 > q2001)	{
//				
//				
//				return "q1;发病日期不能大于出生日期！";
//			}
//			
//		}

		// Map<String, Object> vars = new HashMap<String, Object>();
		// Set<String> addSet = new HashSet<>();
		// // vars.put("q1", "10.5");
		// vars.put("addSet", addSet);
		//
		// try{
//		 g.execute(s, vars);
		//
		// }catch(Exception e){
		// e.printStackTrace();
		// }
		//
		// try{
		// vars.put("q1", "10.5");
		// vars.put("addSet", addSet);
		// g.execute(s, vars);
		//
		// }catch(Exception e){
		// e.printStackTrace();
		// }
		//
		// System.err.println(addSet);

	}
}

package com.chis.modules.sys.utils;

import com.chis.comm.utils.DateUtils;
import com.chis.comm.utils.ObjectUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.ZwxBaseEntity;
import lombok.Data;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.regex.Pattern;

/**
 * 字符串校验工具类
 *
 * @version 1.0
 */
@Data
public class VerifyStringUtil {
    /**
     * 值
     */
    private String str;
    /**
     * 名称
     */
    private String name;
    /**
     * 是否校验必填
     */
    private boolean required;
    /**
     * 值的长度
     */
    private int len;
    /**
     * 值最大长度(为0不校验)
     */
    private int lenMax;
    /**
     * 是否校验日期格式
     */
    private boolean dateType;
    /**
     * 指定日期格式
     */
    private String dateFormat;
    /**
     * 校验后日期
     */
    private Date date;
    /**
     * 校验枚举类型(,分隔)(空不校验)
     */
    private String enumStr;
    /**
     * 错误信息
     */
    private List<String> errorMsgList;
    /**
     * 是否验证失败
     */
    private boolean fail;

    public VerifyStringUtil(String str, String name, List<String> errorMsgList) {
        this.str = StringUtils.objectToString(str);
        this.len = this.str.length();
        this.name = name;
        this.errorMsgList = errorMsgList;
        this.fail = Boolean.FALSE;
    }


    public VerifyStringUtil(String str, String name, boolean required, List<String> errorMsgList) {
        this.str = StringUtils.objectToString(str);
        this.len = this.str.length();
        this.name = name;
        this.required = required;
        this.errorMsgList = errorMsgList;
        this.fail = Boolean.FALSE;
    }


    public VerifyStringUtil(String str, String name, boolean required, int lenMax, List<String> errorMsgList) {
        this.str = StringUtils.objectToString(str);
        this.len = this.str.length();
        this.name = name;
        this.required = required;
        this.lenMax = lenMax;
        this.errorMsgList = errorMsgList;
        this.fail = Boolean.FALSE;
    }

    public VerifyStringUtil(String str, String name, boolean required, int lenMax, String enumStr, List<String> errorMsgList) {
        this.str = StringUtils.objectToString(str);
        this.len = this.str.length();
        this.name = name;
        this.required = required;
        this.lenMax = lenMax;
        this.enumStr = enumStr;
        this.errorMsgList = errorMsgList;
        this.fail = Boolean.FALSE;
    }

    public VerifyStringUtil(String str, String name, boolean required, int lenMax, boolean dateType, List<String> errorMsgList) {
        this.str = StringUtils.objectToString(str);
        this.len = this.str.length();
        this.name = name;
        this.required = required;
        this.lenMax = lenMax;
        this.dateType = dateType;
        this.errorMsgList = errorMsgList;
        this.fail = Boolean.FALSE;
    }

    public VerifyStringUtil(String str, String name, boolean required, int lenMax, boolean dateType, String dateFormat, List<String> errorMsgList) {
        this.str = StringUtils.objectToString(str);
        this.len = this.str.length();
        this.name = name;
        this.required = required;
        this.lenMax = lenMax;
        this.dateType = dateType;
        this.dateFormat = dateFormat;
        this.errorMsgList = errorMsgList;
        this.fail = Boolean.FALSE;
    }

    public VerifyStringUtil defaultVerify() {
        verifyRequired().verifyLen().verifyDateFormat().verifyEnum();
        return this;
    }

    /**
     * 检验是否为空
     *
     * @return VerifyStringUtil
     */
    public VerifyStringUtil verifyRequired() {
        if (StringUtils.isBlank(this.str)) {
            if (this.required) {
                this.errorMsgList.add(this.name + "不能为空");
            }
            this.fail = true;
        }
        return this;
    }

    /**
     * 校验 必须为空
     *
     * @return VerifyStringUtil
     **/
    public VerifyStringUtil verifyMustEmpty() {
        if (StringUtils.isNotBlank(this.str) && !this.required) {
            this.errorMsgList.add(this.name + "必须为空");
            this.fail = true;
        }
        return this;
    }

    /**
     * 检验长度
     *
     * @return VerifyStringUtil
     */
    public VerifyStringUtil verifyLen() {
        if (this.len == 0 || this.lenMax == 0) {
            return this;
        }
        if (this.len > this.lenMax) {
            this.errorMsgList.add(this.name + "长度不能大于" + this.lenMax);
            this.fail = true;
        }
        return this;
    }

    /**
     * 检验日期
     *
     * @return VerifyStringUtil
     */
    public VerifyStringUtil verifyDateFormat() {
        if (this.len == 0 || !this.dateType) {
            return this;
        }
        if (StringUtils.isBlank(this.dateFormat)) {
            this.date = DateUtils.parseDate(this.str);
            if (this.date == null) {
                this.errorMsgList.add(this.name + "格式错误");
                this.fail = true;
            }
            return this;
        }
        try {
            this.date = DateUtils.parseDate(this.str, this.dateFormat);
        } catch (ParseException e) {
            this.errorMsgList.add(this.name + "格式错误");
            this.fail = true;
        }
        return this;
    }

    /**
     * 与日期比较
     *
     * @param date1  比较的日期
     * @param name1  比较的日期名称
     * @param format 比较期望结果(>、>=、=、<、<=)
     * @return VerifyStringUtil
     */
    public VerifyStringUtil compareDate(Date date1, String name1, String format) {
        if (this.date == null || date1 == null) {
            return this;
        }
        int compare = this.date.compareTo(date1);
        if (">".equals(format) && compare <= 0) {
            this.errorMsgList.add(this.name + "应大于" + name1);
            this.fail = true;
        } else if (">=".equals(format) && compare < 0) {
            this.errorMsgList.add(this.name + "应大于等于" + name1);
            this.fail = true;
        } else if ("=".equals(format) && compare != 0) {
            this.errorMsgList.add(this.name + "应等于" + name1);
            this.fail = true;
        } else if ("<".equals(format) && compare >= 0) {
            this.errorMsgList.add(this.name + "应小于" + name1);
            this.fail = true;
        } else if ("<=".equals(format) && compare > 0) {
            this.errorMsgList.add(this.name + "应小于等于" + name1);
            this.fail = true;
        }
        return this;
    }

    /**
     * 校验枚举
     *
     * @return VerifyStringUtil
     */
    public VerifyStringUtil verifyEnum() {
        if (this.len == 0 || StringUtils.isBlank(this.enumStr)) {
            return this;
        }
        List<String> enumList = StringUtils.string2list(this.enumStr, ",");
        if (!enumList.contains(this.str)) {
            this.errorMsgList.add(this.name + "取值只能为" + StringUtils.list2string(enumList, "或"));
            this.fail = true;
        }
        return this;
    }

    /**
     * 校验正则
     *
     * @return VerifyStringUtil
     */
    public VerifyStringUtil verifyRegular(String regex, String tip) {
        if (this.len == 0 || StringUtils.isBlank(regex) || Pattern.matches(regex, this.str)) {
            return this;
        }
        this.errorMsgList.add(this.name + "只能" + tip);
        this.fail = true;
        return this;
    }

    /**
     * 校验 取值范围
     *
     * @return VerifyStringUtil
     */
    public VerifyStringUtil verifyValueArea(String... valArr) {
        if (StringUtils.isBlank(this.str) || null == valArr || valArr.length == 0) {
            return this;
        }
        StringJoiner joiner = new StringJoiner("、");
        boolean ifMarch = false;
        for (String curStr : valArr) {
            if (this.str.equals(curStr)) {
                ifMarch = true;
                break;
            }
            joiner.add(curStr);
        }
        if (!ifMarch) {
            this.errorMsgList.add(this.name + "只能是" + joiner);
            this.fail = true;
        }
        return this;
    }

    /**
     * 校验电话/手机号格式
     *
     * @return VerifyStringUtil
     */
    public VerifyStringUtil verifyPhone() {
        if (this.len == 0) {
            return this;
        }
        if (!StringUtils.vertyPhone(this.str)) {
            this.errorMsgList.add(this.name + "格式错误");
            this.fail = true;
            return this;
        }
        return this;
    }

    /**
     * 校验身份证号格式
     *
     * @return VerifyStringUtil
     */
    public VerifyStringUtil verifyIdc() {
        if (this.len == 0) {
            return this;
        }
        if (!StringUtils.vertyPhone(this.str)) {
            this.errorMsgList.add(this.name + "格式错误");
            this.fail = true;
            return this;
        }
        return this;
    }

    /**
     * 校验码表/对照(传入值)(errorMsg为空不返回失败)
     *
     * @return T
     */
    public Object verifyContrast(Map<String, ? extends ZwxBaseEntity> map, String str, String errorMsg) {
        if (this.len == 0) {
            return null;
        }
        str = StringUtils.objectToString(str);
        //判空
        if (ObjectUtils.isEmpty(map) || !map.containsKey(str) || map.get(str) == null || map.get(str).getRid() == null) {
            if (StringUtils.isNotBlank(errorMsg)) {
                this.errorMsgList.add(this.name + "【" + this.str + "】" + errorMsg);
                this.fail = true;
            }
            return null;
        }
        return map.get(str);
    }

    /**
     * 校验码表/对照(原始值)
     *
     * @return T
     */
    public Object verifyContrast(Map<String, ? extends ZwxBaseEntity> map, String errorMsg) {
        return verifyContrast(map, this.str, errorMsg);
    }

    /**
     * 校验码表/对照(原始值)(不返回失败)
     *
     * @return T
     */
    public Object verifyContrastNotFail(Map<String, ? extends ZwxBaseEntity> map) {
        return verifyContrast(map, this.str, "");
    }

    /**
     * 校验码表/对照(传入值)(不返回失败)
     *
     * @return T
     */
    public Object verifyContrastNotFail(Map<String, ? extends ZwxBaseEntity> map, String str) {
        return verifyContrast(map, str, "");
    }
}

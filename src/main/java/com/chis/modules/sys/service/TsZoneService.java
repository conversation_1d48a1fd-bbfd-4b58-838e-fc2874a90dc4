package com.chis.modules.sys.service;


import cn.hutool.core.util.ObjUtil;
import com.chis.comm.utils.ObjectUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.comm.utils.ZoneUtil;
import com.chis.modules.sys.entity.TsZone;
import com.chis.modules.sys.enums.JdbcCaffeineKeyEnum;
import com.chis.modules.sys.mapper.TsZoneMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-07-21,TsZoneService
 */
@Service
public class TsZoneService extends ZwxBaseServiceImpl<TsZoneMapper, TsZone> {
    /**
     * <p>方法描述：地区缓存</p>
     * @MethodAuthor qrr,2020-11-25,findTsZoneMap
     * */
    public Map<String,TsZone> findTsZoneMap(){
        Map<String,TsZone> map = (Map<String,TsZone>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.ZONE_CODE_CAFFEINE_KEY, null);
        if(null==map){
            map = new HashMap<>();
            List<TsZone> list = this.baseMapper.selectListByEntity(null);
            for(TsZone t:list){
                map.put(t.getZoneGb(),t);
            }
            CaffeineUtil.put(JdbcCaffeineKeyEnum.ZONE_CODE_CAFFEINE_KEY, null,map);
        }
        return map;
    }

    /**
     * <p>方法描述：地区缓存map中 key：rid </p>
     * @MethodAuthor： yzz
     * @Date：2022-01-10
     **/
    public Map<Integer,TsZone> findTsZoneMapByRid(){
        Map<Integer,TsZone> map = (Map<Integer,TsZone>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.ZONE_CODE_PNEU_KEY, null);
        if(null==map){
            map = new HashMap<>();
            List<TsZone> list = this.baseMapper.selectListByEntity(null);
            for(TsZone t:list){
                map.put(t.getRid(),t);
            }
            CaffeineUtil.put(JdbcCaffeineKeyEnum.ZONE_CODE_PNEU_KEY, null,map);
        }
        return map;
        
    }
    /**
     *  <p>方法描述：地区缓存有序</p>
     * @MethodAuthor hsj 2022-12-06 17:44
     */
    public Map<String, TsZone> findTsZoneSortMap() {
        Map<String,TsZone> map = (Map<String,TsZone>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.ZONE_CODE_CAFFEINE_SORT_KEY, null);
        if(null==map){
            map = new LinkedHashMap<>();
            List<TsZone> list = this.baseMapper.selectTsZones();
            for(TsZone t:list){
                if(!map.containsKey(t.getZoneGb())){
                    map.put(t.getZoneGb(),t);
                }
            }
            CaffeineUtil.put(JdbcCaffeineKeyEnum.ZONE_CODE_CAFFEINE_SORT_KEY, null,map);
        }
        return map;
    }

    /**
     * 查询地区
     *
     * @param zoneGb      地区ZONE_GB
     * @param zoneTypeMin 最小地区级别
     * @param zoneTypeMax 最大地区级别
     * @param ifReveal    停用标记
     * @return List
     */
    public List<TsZone> findTsZoneNext(String zoneGb, Integer zoneTypeMin, Integer zoneTypeMax, Integer ifReveal) {
        zoneGb = StringUtils.objectToString(zoneGb);
        if (ObjUtil.isEmpty(zoneGb)) {
            return new ArrayList<>();
        }
        zoneGb = ZoneUtil.zoneSelect(zoneGb) + "%";
        String key = zoneGb + "_" + zoneTypeMin + "_" + zoneTypeMax + "_" + ifReveal;
        List<TsZone> list = (List<TsZone>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.ZONE_CODE_CAFFEINE_LIST_KEY, key);
        if (ObjectUtils.isEmpty(list)) {
            list = this.baseMapper.findTsZoneList(zoneGb, zoneTypeMin, zoneTypeMax, ifReveal);
            CaffeineUtil.put(JdbcCaffeineKeyEnum.ZONE_CODE_CAFFEINE_LIST_KEY, key, list);
        }
        return list;
    }
}

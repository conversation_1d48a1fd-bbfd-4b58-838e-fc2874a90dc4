package com.chis.modules.sys.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TbProbColsdefine;
import com.chis.modules.sys.entity.TsProbLib;
import com.chis.modules.sys.entity.TsProbSubject;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.mapper.TsProbLibMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.sys.utils.GroovyScriptEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TsProbLibService
 */
@Service
public class TsProbLibService extends ZwxBaseServiceImpl<TsProbLibMapper, TsProbLib> {
    @Autowired
    private TsProbSubjectService subjectService;
    @Autowired
    private TbProbColsdefineService tbProbColsdefineService;
    @Autowired
    private GroovyScriptEngine groovyScriptEngine;
    @Autowired
    private TsSimpleCodeService tsSimpleCodeService;
    /**
     * 验证问卷保存脚本
     * @param probLib
     * @param quesCodeEntityMap
     * @param dataMap
     * @param varTs
     * @return
     */
    public String verifySaveScript(TsProbLib probLib, Map<String, TsProbSubject> quesCodeEntityMap, Map<String, String> dataMap, Map<String, Object> varTs) {
        if (null != probLib && null != quesCodeEntityMap) {
            // 执行脚本前，更新变量
            Map<String, Object> vars = new HashMap<String, Object>(); // 填空的后保存，有可能是更新记录
            String verifyScript = probLib.getVerifyScript();
            if (StringUtils.isNotBlank(verifyScript)) {
                Iterator<String> iterator = quesCodeEntityMap.keySet().iterator();
                Map<String, String> newdataMap=new HashMap<>();
                while (iterator.hasNext()) {
                    String next = iterator.next();
                    String key = new StringBuilder("q").append(next).toString();
                    if (StringUtils.contains(verifyScript, key)) {
                        TsProbSubject tsProbSubject = quesCodeEntityMap.get(next);
                        verifyScript = verifyScript.replaceAll(new StringBuilder("").append(key).toString(),
                                new StringBuilder("q").append(tsProbSubject.getNum()).toString());
                    }
                }

                System.err.println("verifyScript:" + verifyScript);
                Set<Map.Entry<String, String>> fillMapSet = dataMap.entrySet();
                for (Map.Entry<String, String> e : fillMapSet) {
                    if (StringUtils.isNotBlank(e.getKey()) && verifyScript.indexOf(e.getKey()) != -1 && StringUtils.isNotBlank(e.getValue())
                            && e.getKey().startsWith("q") && StringUtils.isNumeric(e.getKey().charAt(1) + "")) {
                        vars.put(e.getKey(), e.getValue());
                    }
                }
                vars.put("dataMap",dataMap);
                vars.put("state",dataMap.get("state"));
                vars.put("varTs",varTs);
                String mess = groovyScriptEngine.executeString(verifyScript, vars);
                if (StringUtils.isNotBlank(mess)) {
                    // 处理信息
                    StringBuilder proMess = new StringBuilder();
                    String[] split = mess.split(";");
                    for (String s : split) {
                        String[] split2 = s.split("@");
                        TsProbSubject tsProbSubject = quesCodeEntityMap.get(split2[0]);
                        proMess.append(";").append(tsProbSubject.getNum()).append("@").append(split2[1]);
                    }
                    if (proMess.length() > 0) {
                        return proMess.substring(1);
                    }
                }
            }
        }
        return null;
    }
    /**
     * 验证问卷答案
     *
     * @param queId
     * @param dataMap
     * @param
     * @return
     */
    public String verifyQueAns(Integer queId, Map<String, String> dataMap, Map<String, Object> varTs) {
        if (null != queId) {
            //问卷脚本验证
            TsProbLib probLib = this.selectByEntity(new TsProbLib(queId));
            // 问卷编码与序号对照Map
            Map<String, TsProbSubject> quesCodeEntityMap = subjectService.findQuesCodeEntity(queId);
            //验证长度脚本
            String verifyLength = this.verifyLength(quesCodeEntityMap, dataMap);
            //验证保存脚本
            String verifySaveScript = this.verifySaveScript(probLib, quesCodeEntityMap,dataMap,varTs);

            StringBuilder mess = new StringBuilder();
            if(StringUtils.isNotBlank(verifyLength)){
                mess.append(";").append(verifyLength);
            }
            if(StringUtils.isNotBlank(verifySaveScript)){
                mess.append(";").append(verifySaveScript);
            }
            if (mess.length() > 1) {
                return mess.substring(1);
            }
        }
        return null;
    }
    /**
     * 验证问卷控制脚本 脚本格式为 ： 1,1~2;2,1~3;
     *
     * @param quesCodeEntityMap
     * @param dataMap
     * @return
     */
    private String verifyLength(Map<String, TsProbSubject> quesCodeEntityMap, Map<String, String> dataMap) {
        if (null != quesCodeEntityMap) {
            // 错误信息
            StringBuilder mess = new StringBuilder();
            Iterator<String> iterator = quesCodeEntityMap.keySet().iterator();
            while (iterator.hasNext()) {
                String next = iterator.next();
                TsProbSubject tsProbSubject = quesCodeEntityMap.get(next);

                String fillMaxRange = tsProbSubject.getFillMaxRange();
                if (StringUtils.isNotBlank(fillMaxRange)) {
                    // 1,1~2;2,1~3; ==> 1,1~2 2,1~3
                    String[] split = fillMaxRange.split(";");
                    StringBuilder oneMess = new StringBuilder();
                    for (String scrip : split) {
                        if (StringUtils.isNotBlank(scrip)) {
                            // 1,1~2 ==> 1 1~2
                            String[] split2 = scrip.split(",");
                            // 题目答案标记
                            StringBuilder ansQusSign = new StringBuilder();
                            ansQusSign.append("q").append(tsProbSubject.getNum());
                            // 题目
                            StringBuilder ansName = new StringBuilder();

                            // 填空序号 如果为0 说明非多项
                            String xh = split2[0];
                            String[] split3 = split2[0].split("_");
                            if (split3.length > 1) {// 说明为选择题
                                String xxxh = split3[0];
                                ansQusSign.append("_").append(xxxh);
                                ansName.append("第").append(xxxh).append("个选项的");
                                xh = split3[1];

                                if (!"0".equals(xh)) {
                                    ansQusSign.append("_").append(xh);
                                    ansName.append("第").append(xh).append("个");
                                }
                                ansName.append("填空值");
                            } else {// 填空 或者 多项
                                if (!"0".equals(xh)) {
                                    ansQusSign.append("_").append(xh);
                                    ansName.append("第").append(xh).append("个");
                                }
                                ansName.append("填空值");
                            }
                            // 取值范围
                            String range = split2[1];

                            // 填空值
                            String fillVal = dataMap.get(ansQusSign.toString());
                            if (StringUtils.isNotBlank(fillVal) && StringUtils.isNotBlank(range)) {
                                String[] rangeArr = range.split("~");
                                if (rangeArr.length > 0) {
                                    // 最小值
                                    String minVal = rangeArr[0];
                                    if (StringUtils.isNotBlank(minVal)) {
                                        BigDecimal minDecimal = new BigDecimal(minVal);
                                        if (new BigDecimal(fillVal).subtract(minDecimal).doubleValue() < 0) {
                                            ansName.append("小于最小值").append(minDecimal.toPlainString()).append("!");

                                            oneMess.append(";").append(tsProbSubject.getNum()).append("@");
                                            oneMess.append(ansName);
                                            continue;
                                        }
                                    }
                                }
                                if (rangeArr.length > 1) {
                                    // 最小值
                                    String maxVal = rangeArr[1];
                                    BigDecimal maxDecimal = new BigDecimal(maxVal);
                                    if (new BigDecimal(fillVal).subtract(maxDecimal).doubleValue() > 0) {
                                        ansName.append("大于最大值").append(maxDecimal.toPlainString()).append("!");

                                        oneMess.append(";").append(tsProbSubject.getNum()).append("@");
                                        oneMess.append(ansName);
                                        continue;
                                    }
                                }
                            }
                        }
                    }
                    mess.append(oneMess);
                }

                /*添加表格中的校验*/
                if ("11".equals(tsProbSubject.getQuestType())) {
                    List<String> dataMapKeyList = new ArrayList<>(dataMap.keySet());
                    //对dataMapKeyList进行排序,例如：q题号_列_行 先按题目排，然后按行号排，再按列号排
                    Collections.sort(dataMapKeyList, new Comparator<String>() {
                        @Override
                        public int compare(String o1, String o2) {
                            String[] arr1 = o1.split("_");
                            String[] arr2 = o2.split("_");
                            if(arr1.length!=3 || arr2.length!=3){
                                return o1.compareTo(o2);
                            }
                            //题目排序
                            if(arr1[0].length()>arr2[0].length()){
                                return 1;
                            }else if(arr1[0].length()<arr2[0].length()){
                                return -1;
                            }else{
                                if(!arr1[0].equals(arr2[0])){
                                    return arr1[0].compareTo(arr2[0]);
                                }else{//题目一样
                                    if(arr1[2].equals(arr2[2])){
                                        return Integer.valueOf(arr1[1])-Integer.valueOf(arr2[1]);//按列排
                                    }else{
                                        return Integer.valueOf(arr1[2])-Integer.valueOf(arr2[2]);//按行排
                                    }
                                }
                            }
                        }
                    });

                    StringBuilder twoMess = new StringBuilder();
                    if (null != tsProbSubject.getFkByTableId() && null != tsProbSubject.getFkByTableId().getRid()) {
                        List<TbProbColsdefine> tbProbColsdefineList = tbProbColsdefineService.findTbProbColsdefinesByTableId(tsProbSubject.getFkByTableId().getRid());
                        if (tbProbColsdefineList.size() > 0) {
                            for (String key : dataMapKeyList) {
                                for (TbProbColsdefine colsdefine : tbProbColsdefineList) {
                                    if ("2".equals(colsdefine.getColType()) || "3".equals(colsdefine.getColType())) {
                                        String[] keyArr = key.split("_");//q8_11_1
                                        if (keyArr.length < 3) {
                                            continue;
                                        }
                                        if ((keyArr[0] + "_" + keyArr[1]).equals(colsdefine.getColName())) {
                                            String minVal = colsdefine.getMinValue();
                                            if (StringUtils.isNotBlank(minVal) && StringUtils.isNotBlank(dataMap.get(key))) {
                                                BigDecimal minDecimal = new BigDecimal(minVal);
                                                if (new BigDecimal(dataMap.get(key)).subtract(minDecimal).doubleValue() < 0) {
                                                    twoMess.append("第").append(key.split("_")[2]).append("行");
                                                    twoMess.append("第").append(key.split("_")[1]).append("列");
                                                    twoMess.append("【" + colsdefine.getColDesc() + "】");
                                                    twoMess.append("小于最小值").append(minDecimal.toPlainString()).append("！");
                                                }
                                            }
                                            String maxVal = colsdefine.getMaxValue();
                                            if (StringUtils.isNotBlank(maxVal) && StringUtils.isNotBlank(dataMap.get(key))) {
                                                BigDecimal maxDecimal = new BigDecimal(maxVal);
                                                if (new BigDecimal(dataMap.get(key)).subtract(maxDecimal).doubleValue() > 0) {
                                                    twoMess.append("第").append(key.split("_")[2]).append("行");
                                                    twoMess.append("第").append(key.split("_")[1]).append("列");
                                                    twoMess.append("【" + colsdefine.getColDesc() + "】");
                                                    twoMess.append("大于最大值").append(maxDecimal.toPlainString()).append("！");
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if(twoMess.length()>0){
                        twoMess.insert(0,";"+tsProbSubject.getNum()+"@");
                    }
                    mess.append(twoMess);
                }
            }
            if (mess.length() > 0) {
                return mess.substring(1);
            }
        }
        return null;
    }
    /**
     * @param queId
     * @param dataMap
     * @return
     */
    public <T> void newOrUpdateTarget(Integer queId, Map<String, String> dataMap,Boolean isSimple, T... target) {
        TsProbLib select = this.selectByEntity(new TsProbLib(queId));
        if (null != select) {
            // 执行脚本前，更新变量
            Map<String, Object> vars = new HashMap<String, Object>(); // 填空的后保存，有可能是更新记录
            String libScript = select.getLibScript();
            if (StringUtils.isNotBlank(libScript)) {
                // 慢病问卷编码与序号对照Map
                Map<String, String> codeMap =  subjectService.findQuesCodeList(queId);
                Map<String, TsSimpleCode> simpleMap = new HashMap<>();
                if(isSimple){
                    simpleMap = this.tsSimpleCodeService.findTsProOptSimple(queId);
                    vars.put("simpleMap", simpleMap);
                }
                Iterator<String> iterator = codeMap.keySet().iterator();
                while (iterator.hasNext()) {
                    String next = iterator.next();
                    String key = new StringBuilder("(q").append(next).toString();
                    if (StringUtils.contains(libScript, key)) {
                        String val = codeMap.get(next);
                        libScript = libScript.replaceAll(new StringBuilder("\\").append(key).toString(),
                                new StringBuilder("(q").append(val).toString());
                    }
                }
                Set<Map.Entry<String, String>> fillMapSet = dataMap.entrySet();
                for (Map.Entry<String, String> e : fillMapSet) {
                    if (StringUtils.isNotBlank(e.getKey()) && libScript.indexOf(e.getKey()) != -1
                            && StringUtils.isNotBlank(e.getValue()) && e.getKey().startsWith("q")
                            && StringUtils.isNumeric(e.getKey().charAt(1) + "")) {
                        vars.put(e.getKey(), e.getValue());
                    }
                }
                System.err.println("libScript" + libScript);
                vars.put("t", target[0]);
                if(target.length > 1){
                    for(int i = 1;i<target.length;i++){
                        vars.put("t"+i, target[i]);
                    }
                }
                groovyScriptEngine.execute(libScript, vars);
            }
        }
    }
}

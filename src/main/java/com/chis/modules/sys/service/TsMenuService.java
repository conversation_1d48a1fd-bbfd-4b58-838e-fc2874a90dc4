package com.chis.modules.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chis.modules.sys.entity.TsMenu;
import com.chis.modules.sys.mapper.TsMenuMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2019-11-01,TsMenuService
 */
@Service
public class TsMenuService extends ZwxBaseServiceImpl<TsMenuMapper, TsMenu> {

    public List<TsMenu> findTsMenuList(){
        QueryWrapper<TsMenu> queryWrapper = new QueryWrapper<TsMenu>();
        queryWrapper.orderByAsc("MENU_LEVEL_NO");
        List<TsMenu> menuList = baseMapper.selectList(queryWrapper);
        return menuList;
    }

}

package com.chis.modules.sys.service;

import com.chis.comm.utils.DateUtils;
import com.chis.modules.sys.entity.TsSysHoliday;
import com.chis.modules.sys.enums.JdbcCaffeineKeyEnum;
import com.chis.modules.sys.mapper.TsSysHolidayMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2023-11-09,TsSysHolidayService
 */
@Service
public class TsSysHolidayService extends ZwxBaseServiceImpl<TsSysHolidayMapper, TsSysHoliday> {
    /** 所有启用的节假日的key */
    private final String ALL_ENABLE_HOLIDAYKEY = "ALL_ENABLE_HOLIDAYKEY";
    /** 按日期类型分组节假日与补班 */
    private Map<Integer,List<Date>> holidayMap;
    /**
     * <p>方法描述：获取所有的启用的节假日 </p>
     * 注意：节假日只配置了特殊的国家法定节假日 平时的周末是没有配置的
     * pw 2023/11/9
     **/
    public List<TsSysHoliday> findAllEnableHoliday () {
        List<TsSysHoliday> resultList = new ArrayList<>();
        List<TsSysHoliday> cacheList = (List<TsSysHoliday>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.SYSTEM_HOLIDAY, this.ALL_ENABLE_HOLIDAYKEY);
        if (CollectionUtils.isEmpty(cacheList)) {
            TsSysHoliday queryEntity = new TsSysHoliday();
            queryEntity.setState(1);
            cacheList = this.baseMapper.selectListByEntity(queryEntity);
            if (!CollectionUtils.isEmpty(cacheList)) {
                CaffeineUtil.put(JdbcCaffeineKeyEnum.SYSTEM_HOLIDAY, this.ALL_ENABLE_HOLIDAYKEY, cacheList);
            }
        }
        if (!CollectionUtils.isEmpty(cacheList)) {
            resultList.addAll(cacheList);
        }
        return resultList;
    }

    /**
     * <p>方法描述：传递日期，获取传递工作日天数之前或者之后的日期 </p>
     * @param curDate
     * @param workDay 如果为负数 则获取传递日期前的天数对应日期
     * pw 2023/11/9
     **/
    public Date calcuteWorkDayDate (Date curDate, int workDay) {
        if (null == curDate) {
            return null;
        }
        if (0 == workDay) {
            return curDate;
        }
        Date resultDate = DateUtils.addDays(curDate, workDay);
        List<Date> dateList = this.getDuringDate(curDate, resultDate);
        dateList.remove(curDate);
        this.removeRestDay(dateList);
        int size = dateList.size();
        int betweenDay = workDay > 0 ? workDay - size : workDay+size;
        while (betweenDay != 0) {
            resultDate = DateUtils.addDays(resultDate, betweenDay);
            dateList = this.getDuringDate(curDate, resultDate);
            dateList.remove(curDate);
            this.removeRestDay(dateList);
            size = dateList.size();
            betweenDay = workDay > 0 ? workDay - size : workDay+size;
        }
        return resultDate;
    }

    /**
     * <p>方法描述： 获取两个日期间的工作日天数 </p>
     * pw 2023/11/9
     **/
    public Integer findWorkDayByTwoDate (Date date1, Date date2, boolean ifContantDate1) {
        if (null == date1 || null == date2 || date1.compareTo(date2) == 0) {
            return 0;
        }
        Date startDate = date1.after(date2) ? date2 : date1;
        Date endDate = date1.after(date2) ? date1 : date2;
        List<Date> dateList = this.getDuringDate(startDate, endDate);
        if (!ifContantDate1) {
            dateList.remove(date1);
        }
        this.removeRestDay(dateList);
        return dateList.size();
    }

    /**
     * <p>方法描述：去除休息日 </p>
     * pw 2023/11/10
     **/
    private void removeRestDay (List<Date> dateList) {
        Map<Integer,List<Date>> holidayMap = this.generateHoliTypeMap();
        //1节假日 2补班
        List<Date> hoDayList = holidayMap.get(1);
        List<Date> addDayList = holidayMap.get(2);
        if (CollectionUtils.isEmpty(hoDayList)) {
            hoDayList = new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(addDayList)) {
            addDayList = new ArrayList<>();
        }
        List<Date> removeList = new ArrayList<>();
        for (Date date : dateList) {
            //节假日的过滤掉
            if (hoDayList.contains(date)) {
                removeList.add(date);
                continue;
            }
            int weekDay = this.dayForWeek(date);
            //周末 并且不在补班中 需要移除
            boolean ifRemove = (6 == weekDay || 7 == weekDay) && !addDayList.contains(date);
            if (ifRemove) {
                removeList.add(date);
            }
        }
        if (CollectionUtils.isEmpty(removeList)) {
            return;
        }
        dateList.removeAll(removeList);
    }

    /**
     * <p>方法描述：节假日按日期类型分组 </p>
     * pw 2023/11/9
     **/
    private Map<Integer,List<Date>> generateHoliTypeMap () {
        if (null != this.holidayMap) {
            return this.holidayMap;
        }
        this.holidayMap = new HashMap<>();
        List<TsSysHoliday> holidayList = this.findAllEnableHoliday();
        if (CollectionUtils.isEmpty(holidayList)) {
            return this.holidayMap;
        }
        for (TsSysHoliday holiday : holidayList) {
            Date startDate = holiday.getStartDate();
            Date endDate = holiday.getEndDate();
            if (null == startDate || null == endDate) {
                continue;
            }
            Integer holiType = holiday.getHoliType();
            if (null == holiType || (1 != holiType && 2 != holiType)) {
                continue;
            }
            List<Date> curList = this.holidayMap.get(holiType);
            if (null == curList) {
                curList = new ArrayList<>();
            }
            curList.addAll(this.getDuringDate(startDate, endDate));
            this.holidayMap.put(holiType, curList);
        }
        return this.holidayMap;
    }

    /**
     * <p>方法描述：获取俩日期以及两个日期之间的日期 </p>
     * pw 2023/11/10
     **/
    private List<Date> getDuringDate (Date date1, Date date2) {
        if (null == date1 || null == date2) {
            return Collections.emptyList();
        }
        date1 = DateUtils.getDateOnly(date1);
        date2 = DateUtils.getDateOnly(date2);
        Date startDate = date1.after(date2) ? date2 : date1;
        Date endDate = date1.after(date2) ? date1 : date2;
        List<Date> dateList = new ArrayList<>();
        dateList.add(startDate);
        if (startDate.compareTo(endDate) == 0) {
            return dateList;
        }
        dateList.addAll(DateUtils.getDuringDate(startDate, endDate));
        return dateList;
    }

    /**
     * <p>方法描述：周日7 周一-周六 1-6 </p>
     * pw 2023/11/10
     **/
    public int dayForWeek(Date date) {
        try{
            return DateUtils.dayForWeek(date);
        }catch(Exception e){
            e.printStackTrace();
        }
        return -1;
    }
}

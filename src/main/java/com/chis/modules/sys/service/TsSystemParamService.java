package com.chis.modules.sys.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsSystemParam;
import com.chis.modules.sys.enums.JdbcCaffeineKeyEnum;
import com.chis.modules.sys.mapper.TsSystemParamMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2021-07-07,TsSystemParamService
 */
@Service
public class TsSystemParamService extends ZwxBaseServiceImpl<TsSystemParamMapper, TsSystemParam> {
    /**
     * <p>类描述：根据参数名称查询系统参数值</p>
     * @MethodAuthor qrr,2021-07-07,
     * */
    public TsSystemParam findTsSystemParam(String paramName){
        if(StringUtils.isBlank(paramName)){
            return null;
        }
        TsSystemParam param =(TsSystemParam) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.IF_HAVE_GS_UPLOAD,paramName);
        if(null==param){
            TsSystemParam search = new TsSystemParam();
            search.setParamName(paramName);
            List<TsSystemParam> list = this.baseMapper.selectListByEntity(search);
            if(CollectionUtils.isEmpty(list)){
                return null;
            }
            param = list.get(0);
            CaffeineUtil.put(JdbcCaffeineKeyEnum.IF_HAVE_GS_UPLOAD,paramName,list.get(0));
        }
        return param;
    }
    /**
     *  <p>方法描述：根据paramType 查询 </p>
     * @MethodAuthor hsj 2022-09-06 16:31
     */
    public Map<String,TsSystemParam> findTsSystemParamByType(String paramType){
        if(StringUtils.isBlank(paramType)){
            return null;
        }
        Map<String,TsSystemParam> param =(Map<String,TsSystemParam>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.SYSTEM_PARAM_TYPE,paramType);
        if(null==param){
            TsSystemParam search = new TsSystemParam();
            search.setParamType(paramType);
            List<TsSystemParam> list = this.baseMapper.selectListByEntity(search);
            if(CollectionUtils.isEmpty(list)){
                return null;
            }else {
                param = new HashMap<>();
                for(TsSystemParam systemParam : list){
                    param.put(systemParam.getParamName(),systemParam);
                }
            }
            CaffeineUtil.put(JdbcCaffeineKeyEnum.SYSTEM_PARAM_TYPE,paramType,param);
        }
        return param;
    }
}

package com.chis.modules.sys.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.chis.modules.sys.entity.TdMsgSub;
import com.chis.modules.sys.mapper.TdMsgSubMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2019-10-25,TdMsgSubService
 */
@Service
public class TdMsgSubService extends ZwxBaseServiceImpl<TdMsgSubMapper, TdMsgSub> {
	/**
 	 * <p>方法描述：根据主表Id与地区级别、编码查询接收人</p>
 	 * @MethodAuthor qrr,2019年10月30日,selectListByMainIdAndZone
	 * */
	public List<TdMsgSub> selectListByMainIdAndZone(Integer mainId,String zoneGb,String zoneType){
		return this.baseMapper.selectListByMainIdAndZone(mainId, zoneGb, zoneType);
	}
}

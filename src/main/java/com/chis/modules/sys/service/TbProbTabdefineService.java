package com.chis.modules.sys.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TbProbTabdefine;
import com.chis.modules.sys.mapper.TbProbTabdefineMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TbProbTabdefineService
 */
@Service
public class TbProbTabdefineService extends ZwxBaseServiceImpl<TbProbTabdefineMapper, TbProbTabdefine> {

    /**
     * <p>方法描述： 通过rid集合获取表格题定义对象集合 </p>
     * @MethodAuthor： pw 2023/3/17
     **/
    public List<TbProbTabdefine> findTbProbTabdefineListByRids(List<Integer> ridList){
        if(CollectionUtils.isEmpty(ridList)){
            return Collections.EMPTY_LIST;
        }
        ridList = ridList.stream().distinct().collect(Collectors.toList());
        List<TbProbTabdefine> resultList = new ArrayList<>(ridList.size());
        List<List<Integer>> groupList = StringUtils.splitListProxy(ridList, 1000);
        for(List<Integer> list : groupList){
            List<TbProbTabdefine> tmpList = this.baseMapper.findTbProbTabdefineListByRids(list);
            if(!CollectionUtils.isEmpty(tmpList)){
                resultList.addAll(tmpList);
            }
        }
        return resultList;
    }

}

package com.chis.modules.sys.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TbProbColsdefine;
import com.chis.modules.sys.mapper.TbProbColsdefineMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TbProbColsdefineService
 */
@Service
public class TbProbColsdefineService extends ZwxBaseServiceImpl<TbProbColsdefineMapper, TbProbColsdefine> {


    /**
     * @Description: 通过rid集合查询实体集合
     *
     * @MethodAuthor pw,2021年08月26日
     */
    public List<TbProbColsdefine> findTbProbColsdefineListByRidList(List<Integer> ridList){
        if(CollectionUtils.isEmpty(ridList)){
            return null;
        }
        List<TbProbColsdefine> resultList = new ArrayList<>();
        List<Integer> queryRidList = new ArrayList<>();
        //去重
        for(Integer rid : ridList){
            if(!queryRidList.contains(rid)){
                queryRidList.add(rid);
            }
        }
        List<Integer> executeQueryRidList = new ArrayList<>();
        if(queryRidList.size() < 1000){
            executeQueryRidList.addAll(queryRidList);
        }else{
            //避免出现 in 语句超过1000 导致执行失败的情况
            for(Integer rid : queryRidList){
                executeQueryRidList.add(rid);
                if(executeQueryRidList.size() >= 990){
                    List<TbProbColsdefine> tmpList = this.baseMapper.findTbProbColsdefinesByRidList(executeQueryRidList);
                    if(!CollectionUtils.isEmpty(tmpList)){
                        resultList.addAll(tmpList);
                    }
                    executeQueryRidList.clear();
                }
            }
        }
        if(!CollectionUtils.isEmpty(executeQueryRidList)){
            List<TbProbColsdefine> tmpList = this.baseMapper.findTbProbColsdefinesByRidList(executeQueryRidList);
            if(!CollectionUtils.isEmpty(tmpList)){
                resultList.addAll(tmpList);
            }
        }
        return resultList;
    }


    /**
     * 根据表格题定义id获取表格列定义
     * @param tableId
     * @return
     */
    public List<TbProbColsdefine> findTbProbColsdefinesByTableId(Integer tableId){
        return this.baseMapper.findTbProbColsdefinesByTableId(tableId);
    }

    /**
     * <p>方法描述： 通过表格题定义rid集合 获取表格列定义对象集合 </p>
     * @MethodAuthor： pw 2023/3/17
     **/
    public List<TbProbColsdefine> findTbProbColsdefinesByTableIdList(List<Integer> tableIdList){
        if(!CollectionUtils.isEmpty(tableIdList)){
            return Collections.EMPTY_LIST;
        }
        tableIdList = tableIdList.stream().distinct().collect(Collectors.toList());
        List<TbProbColsdefine> resultList = new ArrayList<>();
        List<List<Integer>> groupList = StringUtils.splitListProxy(tableIdList, 1000);
        for(List<Integer> list : groupList){
            List<TbProbColsdefine> tmpList = this.baseMapper.findTbProbColsdefinesByTableIdList(list);
            if(!CollectionUtils.isEmpty(tmpList)){
                resultList.addAll(tmpList);
            }
        }
        return resultList;
    }


}

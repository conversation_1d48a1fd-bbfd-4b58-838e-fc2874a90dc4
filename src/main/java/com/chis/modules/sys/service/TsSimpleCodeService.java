package com.chis.modules.sys.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.enums.JdbcCaffeineKeyEnum;
import com.chis.modules.sys.mapper.TsSimpleCodeMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p> 服务实现类：编码表  </p>
 *
 * @ClassAuthor 机器人,2019-02-18,TsSimpleCodeService
 */
@Service
public class TsSimpleCodeService extends ZwxBaseServiceImpl<TsSimpleCodeMapper, TsSimpleCode> {
	/*** 
	 * <AUTHOR>
	 * @param codeName
	 * @param codeNo
	 * @return
	 */
	public TsSimpleCode findTsSimpleCode(String codeName,String codeNo) {
		return baseMapper.findTsSimpleCode(codeName, codeNo);
	}
	
	/***
	 *  <p>方法描述：</p>所有码表
     *
     * @MethodAuthor maox,2019年3月14日,findTsSimpleCodeList
	 */
	public List<TsSimpleCode> findTsSimpleCodeAllList(String codeNo){
		//return baseMapper.findTsSimpleCodeAllList(codeNo);
		return this.findAllTsSimpleCodeList(codeNo);
	}

	/**
	 * <p>
	 *     方法描述：过滤停用
	 * </p>
	 *
	 * @MethodAuthor yph,2021年07月17日
	 */
	public List<TsSimpleCode> findTsSimpleCodeList(String codeNo){
		//return baseMapper.findTsSimpleCodeList(codeNo);
		return this.findTsSimpleCodeListOrderby(codeNo);
	}

	/**
	 * <p>方法描述： 查询所有的码表，包含停用，无排序 </p>
	 * @MethodAuthor： pw 2022/9/27
	 **/
	public List<TsSimpleCode> findAllTsSimpleCodeList(String codeNo){
		if(StringUtils.isBlank(codeNo)){
			return Collections.emptyList();
		}
		List<TsSimpleCode> resultList = new ArrayList<>();
		List<TsSimpleCode> tmpList = (List<TsSimpleCode>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.SIMPLE_CODE_LIST_ALL_KEY, codeNo);
		if(CollectionUtils.isEmpty(tmpList)){
			tmpList = baseMapper.findAllTsSimpleCodeList(codeNo);
			if(null != tmpList && !tmpList.isEmpty()){
				CaffeineUtil.put(JdbcCaffeineKeyEnum.SIMPLE_CODE_LIST_ALL_KEY, codeNo, tmpList);
			}
		}
		if(!CollectionUtils.isEmpty(tmpList)){
			resultList.addAll(tmpList);
		}
		return resultList;
	}
	/**
	 * <p>方法描述：码表缓存</p>
	 * @MethodAuthor qrr,2020-11-25,findTsSimpleCodeMap
	 * */
	public Map<String,TsSimpleCode> findTsSimpleCodeMap(String codeName){
		Map<String,TsSimpleCode> resultMap = new HashMap<>();
		Map<String,TsSimpleCode> map = (Map<String,TsSimpleCode>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.SIMPLE_CODE_CAFFEINE_KEY, codeName);
		if(CollectionUtils.isEmpty(map)){
			map = new HashMap<>();
			List<TsSimpleCode> list = this.findTsSimpleCodeAllList(codeName);
			for(TsSimpleCode t:list){
				map.put(t.getCodeNo(),t);
			}
			CaffeineUtil.put(JdbcCaffeineKeyEnum.SIMPLE_CODE_CAFFEINE_KEY, codeName,map);
		}
		if(!CollectionUtils.isEmpty(map)){
			resultMap.putAll(map);
		}
		return resultMap;
	}


	/**
	 * <p>方法描述：码表缓存 map中：key:rid</p>
	 * @MethodAuthor qrr,2020-11-25,findTsSimpleCodeMap
	 * */
	public Map<Integer,TsSimpleCode> findTsSimpleCodeMapByRid(){
		Map<Integer,TsSimpleCode> resultMap = new HashMap<>();
		Map<Integer,TsSimpleCode> map = (Map<Integer,TsSimpleCode>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.SIMPLE_CODE_PNEU_KEY, null);
		if(CollectionUtils.isEmpty(map)){
			map = new HashMap<>();
			List<TsSimpleCode> list =this.selectListByEntity(null);
			for(TsSimpleCode t:list){
				map.put(t.getRid(),t);
			}
			CaffeineUtil.put(JdbcCaffeineKeyEnum.SIMPLE_CODE_PNEU_KEY, null,map);
		}
		if(!CollectionUtils.isEmpty(map)){
			resultMap.putAll(map);
		}
		return resultMap;
	}

	/**
	 * <p>方法描述：码表缓存 map中：key:rid</p>
	 * @MethodAuthor qrr,2020-11-25,findTsSimpleCodeMap
	 * */
	public Map<Integer,TsSimpleCode> findTsSimpleCodeMapByRid(String codeName) {
		Map<Integer, TsSimpleCode> resultMap = new HashMap<>();
		Map<Integer, TsSimpleCode> map = (Map<Integer, TsSimpleCode>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.SIMPLE_CODE_PNEU_KEY, codeName);
		if (CollectionUtils.isEmpty(map)) {
			map = new HashMap<>();
			List<TsSimpleCode> list = this.findTsSimpleCodeAllList(codeName);
			for (TsSimpleCode t : list) {
				map.put(t.getRid(), t);
			}
			CaffeineUtil.put(JdbcCaffeineKeyEnum.SIMPLE_CODE_PNEU_KEY, codeName, map);
		}
		if (!CollectionUtils.isEmpty(map)) {
			resultMap.putAll(map);
		}
		return resultMap;
	}

	public TsSimpleCode findTsSimpleCodeExtends1(String codeName,String extends1) {
		return baseMapper.findTsSimpleCodeExtends1(codeName, extends1);
	}

	/**
	 * <p>方法描述： 码表  </p>
	 * @MethodAuthor  yzz，2021-06-15，findTsSimpleCodeListOrderby
	 **/
	public List<TsSimpleCode> findTsSimpleCodeListOrderby(String codeNo){
		if(StringUtils.isBlank(codeNo)){
			return Collections.emptyList();
		}
		codeNo = codeNo.trim();
		List<TsSimpleCode> resultList = new ArrayList<>();
		List<TsSimpleCode> tmpList = (List<TsSimpleCode>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.SIMPLE_CODE_LIST_ORDERBYNUM_KEY, codeNo);
		if(CollectionUtils.isEmpty(tmpList)){
			tmpList = baseMapper.findTsSimpleCodeListOrderby(codeNo);
			if(null != tmpList && !tmpList.isEmpty()){
				CaffeineUtil.put(JdbcCaffeineKeyEnum.SIMPLE_CODE_LIST_ORDERBYNUM_KEY, codeNo, tmpList);
			}
		}
		if(!CollectionUtils.isEmpty(tmpList)){
			resultList.addAll(tmpList);
		}
		return resultList;
	}

	/**
	 * 获取多种码表类型码表Map缓存
	 *
	 * @return map key:rid
	 */
	public Map<Integer, TsSimpleCode> findMultipleSimpleCode(List<String> codeNoList) {
		String codeNos = StringUtils.list2string(codeNoList, ",");
		Map<Integer, TsSimpleCode> resultMap = new HashMap<>();
		Map<Integer, TsSimpleCode> map =
				(Map<Integer, TsSimpleCode>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.MULTIPLE_SIMPLE_CODE_MAP_KEY, codeNos);
		if (CollectionUtils.isEmpty(map)) {
			map = new HashMap<>();
			List<TsSimpleCode> list = this.baseMapper.findAllTsSimpleCodeListByCodeNoList(codeNoList);
			for (TsSimpleCode t : list) {
				map.put(t.getRid(), t);
			}
			CaffeineUtil.put(JdbcCaffeineKeyEnum.MULTIPLE_SIMPLE_CODE_MAP_KEY, codeNos, map);
		}
		if(!CollectionUtils.isEmpty(map)){
			resultMap.putAll(map);
		}
		return resultMap;
	}
	/**
	 *  <p>方法描述：根据codeName 查询 所有 按照是否启用，RID排序(有序的map)</p>
	 * @MethodAuthor hsj 2022-12-03 10:43
	 */
	public Map<String, TsSimpleCode> findAllTsSimpleCodeListByCodeName(String codeName){
		Map<String, TsSimpleCode> map = new LinkedHashMap<>();
		if(StringUtils.isBlank(codeName)){
			return map;
		}
		Map<String, TsSimpleCode> tmpMap = (Map<String, TsSimpleCode>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.SIMPLE_CODE_MAP_ALL_KEY, codeName);
		if(!CollectionUtils.isEmpty(tmpMap)){
			return tmpMap;
		}
		List<TsSimpleCode> tmpList= baseMapper.findAllTsSimpleCodeListByCodeName(codeName);
		Optional.ofNullable(tmpList).orElse(new ArrayList<>()).forEach(item -> {
			map.put(item.getCodeNo(),item);
		});
		CaffeineUtil.put(JdbcCaffeineKeyEnum.SIMPLE_CODE_MAP_ALL_KEY, codeName, map);
		return map;
	}

	/**
	 *  <p>方法描述：获取题目选项的码表</p>
	 * @MethodAuthor hsj 2023-03-04 17:58
	 */
	public Map<String, TsSimpleCode> findTsProOptSimple(Integer queId) {
		if (null != queId) {
			List<TsSimpleCode> selectList = this.baseMapper.selectListByQueId(queId);
			Map<String, TsSimpleCode> simpleCodeMap = new HashMap<>();
			if (null != selectList && selectList.size() > 0) {
				for (TsSimpleCode tsSimpleCode : selectList) {
					if(tsSimpleCode.getRid() != null  && simpleCodeMap.containsKey(tsSimpleCode.getRid().toString())){
						simpleCodeMap.put(tsSimpleCode.getRid().toString(), tsSimpleCode);
					}
				}
				return simpleCodeMap;
			}
		}
		return null;
	}

	/**
	 *  <p>方法描述：码表最末级且上一级</p>
	 * @MethodAuthor hsj 2024-09-23 11:35
	 */
	public Map<Integer, Map<String, TsSimpleCode>> findSimpLastListByName(String codeName) {
		Map<Integer, Map<String, TsSimpleCode>> map = new LinkedHashMap<>();
		if(StringUtils.isBlank(codeName)){
			return map;
		}
		Map<Integer, Map<String, TsSimpleCode>> tmpMap = (Map<Integer, Map<String, TsSimpleCode>>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.SIMPLE_CODE_MAP_LAST_KEY_NAME, codeName);
		if(!CollectionUtils.isEmpty(tmpMap)){
			return tmpMap;
		}
		//按照启用且码表排序
		List<TsSimpleCode> tmpList= baseMapper.findAllTsSimpleCodeLists(codeName);
		Map<String,TsSimpleCode> lastMap = new HashMap<>();
		Map<String,List<TsSimpleCode>> levelMap = new HashMap<>();
		Map<String,TsSimpleCode> allCodeMap = new HashMap<>();
		dealLevelMap(tmpList,levelMap,allCodeMap);
		Optional.ofNullable(tmpList).orElse(new ArrayList<>()).forEach(simpleCode -> {
			if(CollectionUtils.isEmpty(levelMap.get(simpleCode.getCodeLevelNo())) && !lastMap.containsKey(simpleCode.getCodeName())){
				lastMap.put(simpleCode.getCodeName(),simpleCode);
			}
		});
		map.put(1,lastMap);
		map.put(2,allCodeMap);
		CaffeineUtil.put(JdbcCaffeineKeyEnum.SIMPLE_CODE_MAP_LAST_KEY_NAME, codeName, map);
		return map;
	}

	/**
	 *  <p>方法描述：码表级别map封装</p>
	 * @MethodAuthor hsj 2024-09-23 16:21
	 */
	private void dealLevelMap(List<TsSimpleCode> list, Map<String, List<TsSimpleCode>> levelMap, Map<String, TsSimpleCode> allCodeMap) {
		if (CollectionUtils.isEmpty(list)) {
   			return;
		}
		for (TsSimpleCode obj : list) {
			String codeLevelNo = obj.getCodeLevelNo();
			if (StringUtils.isBlank(codeLevelNo)) {
				continue;
			}
			allCodeMap.put(codeLevelNo, obj);
			try {
				List<String> codeList = new ArrayList<>(Arrays.asList(codeLevelNo.split("\\.")));
				if (CollectionUtils.isEmpty(codeList) || codeList.size() == 1) {
					continue;
				}
				codeList.remove(codeList.size() - 1);
				String code = StringUtils.join(codeList, ".");
				if (levelMap.containsKey(code)) {
					levelMap.get(code).add(obj);
				} else {
					List<TsSimpleCode> childs = new ArrayList<>();
					childs.add(obj);
					levelMap.put(code, childs);
				}
			} catch (NullPointerException e) {
				e.printStackTrace();
			}
		}
	}
}

package com.chis.modules.sys.service;

import com.chis.modules.sys.entity.TsUserInfo;
import com.chis.modules.sys.mapper.TsUserInfoMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2019-10-25,TsUserInfoService
 */
@Service
public class TsUserInfoService extends ZwxBaseServiceImpl<TsUserInfoMapper, TsUserInfo> {
	/**
 	 * <p>方法描述：查询有“预警处置”菜单权限的用户，接收人所在单位的单位属性为疾控中心</p>
 	 * @MethodAuthor qrr,2019年10月26日,selectWarnAuthUser
	 * */
	public List<Integer> selectWarnAuthUser(String zoneGb,String zoneType) {
		return this.baseMapper.selectMenuAuthUser("heth_yjcz",zoneGb, zoneType);
	}

	public List<TsUserInfo> findTsUserAll(){
		return baseMapper.findTsUserAll();
	}
}

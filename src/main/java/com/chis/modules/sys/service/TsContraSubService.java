package com.chis.modules.sys.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsContraSub;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.enums.JdbcCaffeineKeyEnum;
import com.chis.modules.sys.mapper.TsContraSubMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2020-11-14,TsContraSubService
 */
@Service
public class TsContraSubService extends ZwxBaseServiceImpl<TsContraSubMapper, TsContraSub> {
    /**
     * <p>方法描述：根据</p>
     * @MethodAuthor qrr,2020-11-14,findTsContraSub
     * */
    public Map<String,TsContraSub> findTsContraSub(String contraCode,String busType){
        String key = contraCode+"&"+busType;
        Map<String,TsContraSub> map = (Map<String,TsContraSub>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_KEY, key);
        if(null==map){
            map = new HashMap<>();
            List<TsContraSub> list = this.baseMapper.findTsContraSub(contraCode,busType);
            for(TsContraSub t:list){
                map.put(t.getLeftCode(),t);
            }
            CaffeineUtil.put(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_KEY, key,map);
        }
        return map;
    }

    public List<TsContraSub> findTsContraSubList(String contraCode, String busType) {
        return baseMapper.findTsContraSub(contraCode,busType);
    }

    @Transactional(readOnly = false)
    public List<TsContraSub> selectContraSubToCountry(){
        return baseMapper.selectContraSubToCountry();
    }

    @Transactional(readOnly = true)
    public String selectParam(Integer type, String name){
        return baseMapper.selectParam(type,name);
    }
    /**
     *  <p>方法描述：对照信息，加特殊标记</p>
     * @MethodAuthor hsj 2022/4/2 10:41
     */
    public TsContraSub findTsContraSubByDsfTag(String contraCode,String busType,String tag) {
        String key = contraCode+"&"+busType+"&"+tag;
        TsContraSub tsContraSubs = (TsContraSub) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_KEY, key);
        if(null == tsContraSubs){
            tsContraSubs = new TsContraSub();
            List<TsContraSub> list = this.baseMapper.findTsContraSub(contraCode,busType);
            for(TsContraSub t:list){
                if(tag.equals(t.getDsfTag())){
                    tsContraSubs = t;
                    break;
                }
            }
            CaffeineUtil.put(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_KEY, key,tsContraSubs);
        }
        return tsContraSubs;
    }
    /**
     *  <p>方法描述：根据 t.BUSI_TYPE 分组 Map<t.BUSI_TYPE,Map<t.RIGHT_CODE,List<t.LEFT_CODE>>></p>
     * @MethodAuthor hsj 2022-12-02 17:07
     */
    public Map<String,List<String>> findTsContraSubByContraCode(String contraCode,String type){
        String key = contraCode;
//        Map<String,Map<String,List<String>>> contraCodeMap =new LinkedHashMap<>();
        Map<String,Map<String,List<String>>> contraCodeMap = (Map<String,Map<String,List<String>>>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_CONTRACON_KEY, key);;
        if(CollectionUtils.isEmpty(contraCodeMap)){
            contraCodeMap = new LinkedHashMap<>();
            List<TsContraSub> list = this.baseMapper.findTsContraSub(contraCode,null);
            if(!CollectionUtils.isEmpty(list)){
                //根据BUSI_TYPE分组
                Map<String,List<TsContraSub>> contraMap =list.stream().collect(Collectors.groupingBy(TsContraSub::getBusiType));
                Map<String, Map<String, List<String>>> finalContraCodeMap = contraCodeMap;
                contraMap.forEach( (k, v)->{
                    Map<String,List<String>> codeMap = new HashMap<>();
                    v.forEach(o->{
                        if(codeMap.containsKey(o.getRightCode())){
                            codeMap.get(o.getRightCode()).add(o.getLeftCode());
                        }else{
                            List<String> strList = new ArrayList<>();
                            strList.add(o.getLeftCode());
                            codeMap.put(o.getRightCode(),strList);
                        }
                    });
                    finalContraCodeMap.put(k,codeMap);
                });
                contraCodeMap.putAll(finalContraCodeMap);
                CaffeineUtil.put(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_CONTRACON_KEY, key,contraCodeMap);
            }else {
                return null;
            }
        }
        return contraCodeMap.get(type);
    }

    /**
     * <p>方法描述：findSimpByRightCode
     * 注意 这里是用TS_CONTRA_SUB的LEFT_CODE关联的码表CODE_NO
     * </p>
     * @MethodAuthor： yzz
     * @Date：2022-12-07
     **/
    public Map<String, TsContraSub> findSimpByRightCode(String contraCode, String busiType, String codeTypeName) {
        String key=contraCode+"&"+busiType;
        Map<String,Map<String,TsContraSub>> contraCodeMap = (Map<String,Map<String,TsContraSub>>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_SIMP_KEY, key);
        if(contraCodeMap==null || contraCodeMap.isEmpty()){
            contraCodeMap=new HashMap<>();
            Map<String,TsContraSub> rightCodeMap = new HashMap<>();
            List<TsContraSub> simpByRightCode = this.baseMapper.findSimpLeftCodeByContraSub(contraCode, busiType, codeTypeName);
            if(simpByRightCode!=null && !simpByRightCode.isEmpty()){
                simpByRightCode.forEach((contraSub)->{
                    if(StringUtils.isNotBlank(contraSub.getRightCode().trim()) && !rightCodeMap.containsKey(contraSub.getRightCode().trim())){
                        rightCodeMap.put(contraSub.getRightCode().trim(),contraSub);
                    }
                });
                if(!rightCodeMap.isEmpty()){
                    contraCodeMap.put(key,rightCodeMap);
                    CaffeineUtil.put(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_SIMP_KEY, key,contraCodeMap);
                }
            }
        }
        return contraCodeMap.get(key);
    }

    /**
     * <p>方法描述：山东对照 通过编码对照</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-07
     **/
    public Map<String, TsContraSub> findSimpByLeftCode(String contraCode, String busiType, String codeTypeName) {
        String key = contraCode + "&" + busiType;
        Map<String, Map<String, TsContraSub>> contraCodeMap = (Map<String, Map<String, TsContraSub>>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_SIMP_KEY, key);
        if (contraCodeMap != null && !contraCodeMap.isEmpty()) {
            return contraCodeMap.get(key);
        }
        contraCodeMap = new HashMap<>();
        Map<String, TsContraSub> rightCodeMap = new HashMap<>();
        List<TsContraSub> simpByRightCode = this.baseMapper.findSimpLeftCodeByContraSub(contraCode, busiType, codeTypeName);
        if (CollectionUtils.isEmpty(simpByRightCode)) {
            return new HashMap<>();
        }
        simpByRightCode.forEach((contraSub) -> {
            if (StringUtils.isNotBlank(contraSub.getRightCode().trim()) && !rightCodeMap.containsKey(contraSub.getRightCode().trim())) {
                rightCodeMap.put(contraSub.getRightCode().trim(), contraSub);
            }
        });
        if (!rightCodeMap.isEmpty()) {
            contraCodeMap.put(key, rightCodeMap);
            CaffeineUtil.put(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_SIMP_KEY, key,contraCodeMap);
        }
        return contraCodeMap.get(key);
    }
    /**
     * <p>方法描述：山东对照 通过文本对照</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-07
     **/
    public Map<String, TsContraSub> findSimpLeftCodeByDescr(String contraCode, String busiType, String codeTypeName) {
        String key = contraCode + "&" + busiType;
        Map<String, Map<String, TsContraSub>> contraCodeMap = (Map<String, Map<String, TsContraSub>>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_DECSR_KEY, key);
        if (contraCodeMap != null && !contraCodeMap.isEmpty()) {
            return contraCodeMap.get(key);
        }
        contraCodeMap = new HashMap<>();
        Map<String, TsContraSub> rightCodeMap = new HashMap<>();
        List<TsContraSub> simpByRightCode = this.baseMapper.findSimpLeftCodeByContraSub(contraCode, busiType, codeTypeName);
        if (CollectionUtils.isEmpty(simpByRightCode)) {
            return new HashMap<>();
        }
        simpByRightCode.forEach((contraSub) -> {
            if (StringUtils.isNotBlank(contraSub.getDescr()) && !rightCodeMap.containsKey(contraSub.getDescr().trim())) {
                rightCodeMap.put(contraSub.getDescr().trim(), contraSub);
            }
        });
        if (!rightCodeMap.isEmpty()) {
            contraCodeMap.put(key, rightCodeMap);
            CaffeineUtil.put(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_DECSR_KEY, key,contraCodeMap);
        }
        return contraCodeMap.get(key);
    }

    /**
     * <p>方法描述：findSimpByDescr
     * 注意 这里是用TS_CONTRA_SUB的LEFT_CODE关联的码表CODE_NO
     * </p>
     * @MethodAuthor： yzz
     * @Date：2022-12-07
     **/
    public Map<String, TsContraSub> findSimpByDescr(String contraCode, String busiType, String codeTypeName) {
        String key=contraCode+"&"+busiType;
        Map<String,Map<String,TsContraSub>> contraCodeMap = (Map<String,Map<String,TsContraSub>>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_DECSR_KEY, key);
        if(contraCodeMap==null || contraCodeMap.isEmpty()){
            contraCodeMap=new HashMap<>();
            Map<String,TsContraSub> rightCodeMap = new HashMap<>();
            List<TsContraSub> simpByRightCode = this.baseMapper.findSimpLeftCodeByContraSub(contraCode, busiType, codeTypeName);
            if(simpByRightCode!=null && !simpByRightCode.isEmpty()){
                simpByRightCode.forEach((contraSub)->{
                    if(StringUtils.isNotBlank(contraSub.getDescr()) && !rightCodeMap.containsKey(contraSub.getDescr().trim())){
                        rightCodeMap.put(contraSub.getDescr().trim(),contraSub);
                    }
                });
                if(!rightCodeMap.isEmpty()){
                    contraCodeMap.put(key,rightCodeMap);
                    CaffeineUtil.put(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_DECSR_KEY, key,contraCodeMap);
                }
            }
        }
        return contraCodeMap.get(key);
    }


    /**
     * <p>方法描述：findSimpBySpecialDescr</p>
     * @MethodAuthor： yzz
     * @Date：2022-12-07
     **/
    public Map<String, List<TsContraSub>> findSimpBySpecialDescr(String contraCode, String busiType) {
        String key=contraCode+"&"+busiType;
        Map<String,Map<String,List<TsContraSub>>> contraCodeMap = (Map<String,Map<String,List<TsContraSub>>>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_SPECIAL_DESC_KEY, key);
        if(contraCodeMap==null || contraCodeMap.isEmpty()){
            contraCodeMap=new HashMap<>();
            Map<String,List<TsContraSub>> rightCodeMap = new HashMap<>();
            List<TsContraSub> simpByRightCode = this.baseMapper.findTsContraSub(contraCode, busiType);
            if(simpByRightCode!=null && !simpByRightCode.isEmpty()){
                simpByRightCode.forEach((contraSub)->{
                    if(StringUtils.isNotBlank(contraSub.getDsfSpecialDesc())){
                        if (!rightCodeMap.containsKey(contraSub.getDsfSpecialDesc().trim())) {
                            List<TsContraSub> tsContraSubList = new ArrayList<>();
                            tsContraSubList.add(contraSub);
                            rightCodeMap.put(contraSub.getDsfSpecialDesc().trim(), tsContraSubList);
                        } else {
                            rightCodeMap.get(contraSub.getDsfSpecialDesc().trim()).add(contraSub);
                        }
                    }
                });
                if(!rightCodeMap.isEmpty()){
                    contraCodeMap.put(key,rightCodeMap);
                    CaffeineUtil.put(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_SPECIAL_DESC_KEY, key,contraCodeMap);
                }
            }
        }
        return contraCodeMap.get(key);
    }

    /**
    * <p>Description： 根据对照后的码表扩展字段查询
     * 注意 这里是用TS_CONTRA_SUB的RIGHT_CODE关联的码表CODE_NO
     * </p>
    * <p>Author： yzz 2024-01-12 </p>
    */
    public TsContraSub findTsSimpleCodeExtends(String contraCode, String busiType,String codeTypeName) {
        String key = contraCode + "&" + busiType;
        TsContraSub tsContraSub = (TsContraSub) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_TSSIMPLECODE_EXTENDS_KEY, key);
        if (tsContraSub == null) {
            List<TsContraSub> simpByRightCode = this.baseMapper.findSimpByContraSub(contraCode, busiType, codeTypeName);
            if (simpByRightCode != null && !simpByRightCode.isEmpty()) {
                for (TsContraSub contraSub : simpByRightCode) {
                    if ("1".equals(contraSub.getExtends3())) {
                        CaffeineUtil.put(JdbcCaffeineKeyEnum.CONTRA_CAFFEINE_TSSIMPLECODE_EXTENDS_KEY, key,contraSub);
                        return contraSub;
                    }
                }
            }
        }else{
           return tsContraSub;
        }
        return null;
    }

    /**
     *  <p>方法描述：key：LEFT_DESC</p>
     * @MethodAuthor hsj 2024-01-15 9:33
     */
    public Map<String,TsContraSub> findLeftDescTsContraSub(String contraCode,String busType){
        String key = contraCode+"&"+busType;
        Map<String,TsContraSub> map = (Map<String,TsContraSub>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.CONTRA_LEFT_DESC_KEY, key);
        if(null==map){
            map = new HashMap<>();
            List<TsContraSub> list = this.baseMapper.findTsContraSub(contraCode,busType);
            for(TsContraSub t:list){
                if(StringUtils.isNotBlank(t.getLeftDesc())){
                    map.put(t.getLeftDesc(),t);
                }
            }
            CaffeineUtil.put(JdbcCaffeineKeyEnum.CONTRA_LEFT_DESC_KEY, key,map);
        }
        return map;
    }
}

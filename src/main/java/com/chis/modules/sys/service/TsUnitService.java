package com.chis.modules.sys.service;

import org.springframework.stereotype.Service;

import com.chis.modules.sys.entity.TsUnit;
import com.chis.modules.sys.mapper.TsUnitMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;

import java.util.List;

/**
 * <p> 服务实现类：单位管理  </p>
 *
 * @ClassAuthor 机器人,2019-10-24,TsUnitService
 */
@Service
public class TsUnitService extends ZwxBaseServiceImpl<TsUnitMapper, TsUnit> {
    public List<TsUnit> pageListToFj(Integer first, Integer pageSize, TsUnit entity) {
        List<TsUnit> list = baseMapper.pageListToFj(first,pageSize, entity);
        return list;
    }

    public List<TsUnit> findTsUnitAll(){
        return baseMapper.findTsUnitAll();
    }

    public List<TsUnit> findTsUnitAllByZoneCode(String zoneCode){
        return baseMapper.findTsUnitAllByZoneCode(zoneCode);
    }

    /**
     * 查找所有诊断机构对应的系统单位
     *
     * @return list 系统单位
     */
    public List<TsUnit> findTsUnitByDiagOrg() {
        return baseMapper.findTsUnitByDiagOrg();
    }
}

package com.chis.modules.sys.service;


import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;

/***
 * <p>类描述: 通用服务接口 </p>
 *
 * @ClassAuthor mxp,2018/12/26,ZwxBaseService
 */
public interface ZwxBaseService<T> extends IService<T> {

// 分页 //////////////////////////////////////////////////////////////////////////

    /***
     * <p>方法描述: 分页</p>
     *
     * @MethodAuthor mxp,2018/12/26,pageList
     */
    PageInfo<T> pageList(PageInfo<T> page, T entity);

// 查询 //////////////////////////////////////////////////////////////////////////


    /***
     * <p>方法描述: 单个查询：根据id查询</p>
     *
     * @MethodAuthor mxp,2018/12/26,selectById
     */
    T selectById(Serializable id);

    /***
     * <p>方法描述: 单个查询：根据对象中的所有赋值字段（非空判断）进行查询</p>
     *
     * @MethodAuthor mxp,2018/12/26,selectByEntity
     */
    T selectByEntity(T entity);


    /***
     * <p>方法描述: 批量查询：根据对象中的所有赋值字段（非空判断）进行查询</p>
     *
     * @MethodAuthor mxp,2018/12/26,selectListByEntity
     */
    List<T> selectListByEntity(T entity);

// 更新 //////////////////////////////////////////////////////////////////////////

    /***
     * <p>方法描述: 根据对象id更新，会更新全部字段</p>
     *
     * @MethodAuthor mxp,2018/12/26,updateFullById
     */
    int updateFullById(T entity);

    /***
     * <p>方法描述: 批量更新：根据每个对象的id更新，会更新全部字段</p>
     *
     * @MethodAuthor mxp,2018/12/26,updateFullBatchById
     */
    int updateFullBatchById(List<T> entityList);


// 删除 //////////////////////////////////////////////////////////////////////////

    /***
     * <p>方法描述: 删除：根据对象所有赋值字段（非空）进行删除</p>
     *
     * @MethodAuthor mxp,2018/12/26,removeByEntity
     */
    int removeByEntity(T entity);



    /***
     * <p>方法描述: 分页</p>
     *
     * @MethodAuthor mxp,2018/12/26,pageList
     */
    List<T> pageList(Integer first, Integer pageSize, T entity);




}

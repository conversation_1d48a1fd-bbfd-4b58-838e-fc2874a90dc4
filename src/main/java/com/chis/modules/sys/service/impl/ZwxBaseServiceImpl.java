package com.chis.modules.sys.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chis.comm.utils.Reflections;
import com.chis.modules.sys.mapper.ZwxBaseMapper;
import com.chis.modules.sys.service.ZwxBaseService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Date;
import java.util.List;

/***
 * <p>类描述: 通用服务接口实现类 </p>
 *
 * @ClassAuthor mxp, 2018/12/26,ZwxBaseServiceImpl
 */
@Slf4j
@Transactional(readOnly = true)
public class ZwxBaseServiceImpl<M extends ZwxBaseMapper<T>, T> extends ServiceImpl<M, T> implements ZwxBaseService<T> {


// 查询 //////////////////////////////////////////////////////////////////////////

    /***
     * <p>方法描述: 单个查询：根据id查询</p>
     *
     * @MethodAuthor mxp, 2018/12/26,selectById
     */
    public T selectById(Serializable id) {
        return baseMapper.selectById(id);
    }

    /***
     * <p>方法描述: 单个查询：根据对象中的所有赋值字段（非空判断）进行查询</p>
     *
     * @MethodAuthor mxp, 2018/12/26,selectByEntity
     */
    public T selectByEntity(T entity) {
        return baseMapper.selectByEntity(entity);
    }

    /***
     * <p>方法描述: 批量查询：根据对象中的所有赋值字段（非空判断）进行查询</p>
     *
     * @MethodAuthor mxp, 2018/12/26,selectListByEntity
     */
    public List<T> selectListByEntity(T entity) {
        return baseMapper.selectListByEntity(entity);
    }


// 更新 //////////////////////////////////////////////////////////////////////////

    /***
     * <p>方法描述: 单个更新：根据id更新，会更新全部字段</p>
     *
     * @MethodAuthor mxp, 2018/12/26,updateFullById
     */
    @Transactional
    public int updateFullById(T entity) {
        preUpdate(entity);
        return baseMapper.updateFullById(entity);
    }


    /***
     * <p>方法描述: 批量更新：根据每个对象的id更新，会更新全部字段</p>
     *
     * @MethodAuthor mxp, 2018/12/26,updateFullBatchById
     */
    @Transactional
    public int updateFullBatchById(List<T> entityList) {
        if(!CollectionUtils.isEmpty(entityList)){
            for(T t: entityList){
                preUpdate(t);
            }
        }
        return baseMapper.updateFullBatchById(entityList);
    }

    /***
     * <p>方法描述: 分页</p>
     *
     * @MethodAuthor mxp, 2018/12/26,pageList
     */
    @Override
    public PageInfo<T> pageList(PageInfo<T> page, T entity) {
        PageHelper.startPage(page.getPageNum(), page.getPageSize());
        List<T> list = baseMapper.pageList(page, entity);
        PageInfo<T> pageInfo = new PageInfo<>(list);

        return pageInfo;
    }

// 删除 //////////////////////////////////////////////////////////////////////////

    /***
     * <p>方法描述: 删除：根据对象所有赋值字段（非空）进行删除</p>
     *
     * @MethodAuthor mxp,2018/12/26,removeByEntity
     */
    @Transactional
    public int removeByEntity(T entity){
        return baseMapper.removeByEntity(entity);
    }

    /**
     * <p>方法描述：分页</p>
     *
     * @MethodAuthor qrr,2019年7月19日,pageList
     * */
    @Override
    public List<T> pageList(Integer first, Integer pageSize, T entity) {
        List<T> list = baseMapper.pageList(first,pageSize, entity);
        return list;
    }
    /**
     * <p>类描述：更新修改人、修改日期</p>
     * @MethodAuthor qrr,2020-11-28,preUpdate
     * */
    public static <T> void preUpdate(T entity){
        Field modifyManid = null;
        try {
            modifyManid = Reflections.getField("modifyManid", entity.getClass().getSuperclass());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (modifyManid != null) {
            Reflections.invokeSetter(entity,"modifyManid",1);
        }

        Field modifyDate = null;
        try {
            modifyDate = Reflections.getField("modifyDate",entity.getClass().getSuperclass());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (modifyDate != null) {
            Reflections.invokeSetter(entity,"modifyDate",new Date());
        }
    }
    /**@*
     * <p>类描述：新增</p>
     * @MethodAuthor qrr,2021-08-25,
     * */
    @Transactional
    public int insertEntity(T t) {
        preInsert(t);
       return baseMapper.insertEntity(t);
    }
    /**@*
     * <p>类描述：批量新增</p>
     * @MethodAuthor qrr,2021-08-25,
     * */
    @Transactional
    public void insertBatch(List<T> entityList) {
        if(!CollectionUtils.isEmpty(entityList)){
            for(T t: entityList){
                preInsert(t);
            }
        }
        baseMapper.insertBatch(entityList);
    }
    /**
     * <p>方法描述：新增</p>
     *
     * @MethodAuthor mxp, 2018-04-23,preInsert
     */
    public static <T> void preInsert(T entity) {
        Field createManid = null;
        try {
            createManid = Reflections.getField("createManid", entity.getClass().getSuperclass());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (createManid != null) {
            Reflections.invokeSetter(entity,"createManid",1);
        }

        Field createDate = null;
        try {
            createDate = Reflections.getField("createDate",entity.getClass().getSuperclass());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (createDate != null) {
            Reflections.invokeSetter(entity,"createDate",new Date());
        }
    }
}

package com.chis.modules.sys.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsProbExampool;
import com.chis.modules.sys.mapper.TsProbExampoolMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TsProbExampoolService
 */
@Service
public class TsProbExampoolService extends ZwxBaseServiceImpl<TsProbExampoolMapper, TsProbExampool> {

    /**
     * <p>方法描述： 通过rid集合 获取TsProbExampool </p>
     * @MethodAuthor： pw 2022/11/25
     **/
    public List<TsProbExampool> findExamPoolListByRids(List<Integer> ridList){
        if(CollectionUtils.isEmpty(ridList)){
            return Collections.EMPTY_LIST;
        }
        ridList = ridList.stream().distinct().collect(Collectors.toList());
        List<TsProbExampool> resultList = new ArrayList<>(ridList.size());
        List<List<Integer>> groupList = StringUtils.splitListProxy(ridList, 1000);
        for(List<Integer> list : groupList){
            List<TsProbExampool> tmpList = this.baseMapper.findExamPoolListByRids(list);
            if(!CollectionUtils.isEmpty(tmpList)){
                resultList.addAll(tmpList);
            }
        }
        return resultList;
    }
}

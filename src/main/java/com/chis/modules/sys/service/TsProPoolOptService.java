package com.chis.modules.sys.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsProPoolOpt;
import com.chis.modules.sys.mapper.TsProPoolOptMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2021-09-27,TsProPoolOptService
 */
@Service
public class TsProPoolOptService extends ZwxBaseServiceImpl<TsProPoolOptMapper, TsProPoolOpt> {
    /**
     * <p>方法描述： 通过题目rid集合 获取题库选项集合 </p>
     * @MethodAuthor： pw 2023/3/17
     **/
    public List<TsProPoolOpt> findTsProPoolOptsByQuestIds(List<Integer> questRids){
        if(CollectionUtils.isEmpty(questRids)){
            return Collections.EMPTY_LIST;
        }
        List<TsProPoolOpt> resultList = new ArrayList<>();
        List<List<Integer>> groupList = StringUtils.splitListProxy(questRids, 1000);
        for(List<Integer> list : groupList){
            List<TsProPoolOpt> tmpList = this.baseMapper.findTsProPoolOptsByQuestIds(list);
            if(!CollectionUtils.isEmpty(tmpList)){
                resultList.addAll(tmpList);
            }
        }
        return resultList;
    }
}

package com.chis.modules.sys.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.*;
import com.chis.modules.sys.mapper.TdProbAnsDetailMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TdProbAnsDetailService
 */
@Service
public class TdProbAnsDetailService extends ZwxBaseServiceImpl<TdProbAnsDetailMapper, TdProbAnsDetail> {
    @Autowired
    private TsProbSubjectService subjectService;
    @Autowired
    private TdProbAnsTableService tableService;

    /**@*
     * <p>类描述：删除问卷相关子表</p>
     * @MethodAuthor qrr,2021-08-24,
     * */
    @Transactional
    public void deleteProbAns(Integer busType, Integer mainId){
        tableService.deleteTdProbAnsTable(busType,mainId);
        this.deleteTdProbAnsDetail(busType,mainId);
    }

    /**@*
     * <p>类描述：删除问卷相关子表</p>
     * @MethodAuthor qrr,2021-08-24,
     * */
    @Transactional
    public void deletePsnCheckProbAns(Integer mainId, Integer mainType){
        TdProbAnsDetail queryDetail = new TdProbAnsDetail();
        queryDetail.setMainId(mainId);
        queryDetail.setMainType(mainType);
        List<TdProbAnsDetail> ansDetailList = this.baseMapper.selectListByEntity(queryDetail);
        if(CollectionUtils.isEmpty(ansDetailList)){
            return;
        }
        List<Integer> detailRidList = ansDetailList.stream()
                .mapToInt(v -> v.getRid()).distinct().boxed().collect(Collectors.toList());

        List<Integer> removeTableRids = this.tableService.selectPsnCheckTdProbAnsTableRids(mainId, mainType);
        if(!CollectionUtils.isEmpty(removeTableRids)){
            List<List<Integer>> groupList = StringUtils.splitListProxy(removeTableRids, 1000);
            for(List<Integer> tmpList : groupList){
                this.tableService.removeByIds(tmpList);
            }
        }
        List<List<Integer>> groupList = StringUtils.splitListProxy(detailRidList, 1000);
        for(List<Integer> tmpList : groupList){
            this.baseMapper.deleteBatchIds(tmpList);
        }
    }

    /**@*
     * <p>类描述：删除问卷答案</p>
     * @MethodAuthor qrr,2021-08-24,
     * */
    @Transactional
    public void deleteTdProbAnsDetail(Integer busType, Integer mainId){
        this.baseMapper.deleteTdProbAnsDetail(busType,mainId);
    }
    /**
     * @param queLibId
     * @param dataMap
     * @MethodReviser rj, 2018年3月1日, saveQuesAns
     */
    public void saveQuesAns(Integer queLibId, Map<String, String> dataMap, Integer busType, Integer mainId) {
        if(null==queLibId){
            return;
        }
        Map<String, TsProbSubject> qusMap = subjectService.findQuesMap(queLibId);
        if (null != qusMap && null != dataMap) {
            Set<Map.Entry<String, String>> entrySet = dataMap.entrySet();
            Map<String, String> fillMap = new HashMap<String, String>(); // 填空的后保存，有可能是更新记录
            // 问卷答案
            List<TdProbAnsDetail> ansList = new ArrayList<TdProbAnsDetail>();
            for (Map.Entry<String, String> e : entrySet) {
                if (StringUtils.isNotBlank(e.getKey()) && StringUtils.isNotBlank(e.getValue())
                        && e.getKey().startsWith("q") && StringUtils.isNumeric(e.getKey().charAt(1) + "")) {
                    if (e.getKey().indexOf("_") == -1) {
                        // 题目
                        TsProbSubject sub = qusMap.get(e.getKey().replace("q", "").trim());
                        if (null != sub) {// 题目Id
                            TdProbAnsDetail ans = new TdProbAnsDetail();
                            ans.setMainId(mainId);
                            TsSimpleCode tsSimpleCode=new TsSimpleCode();
                            tsSimpleCode.setRid(busType);
                            ans.setFkByQueTypeId(tsSimpleCode);
                            ans.setFkByQuestId(new TsProbSubject(sub.getRid()));
                            ans.setScoreValue(e.getValue());
                            ansList.add(ans);
                        }
                    } else {
                        // 选项填空
                        fillMap.put(e.getKey(), e.getValue());
                    }
                }
            }
            Map<Integer, List<TdProbAnsTable>> tabMap = new HashMap<>();
            if (null != fillMap && fillMap.size() > 0) {
                Set<Map.Entry<String, String>> fillEntrySet = fillMap.entrySet();
                for (Map.Entry<String, String> e : fillEntrySet) {
                    String queNum = e.getKey().split("_")[0].replace("q", "").trim();
                    TsProbSubject sub = qusMap.get(queNum);
                    if (null != sub) {
                        // 多项选择层级 ，如果数组长度为2，则为一级填空， 则使用选项序号
                        String[] split = e.getKey().split("_");
                        if (split.length == 2) {
                            TdProbAnsDetail ans = new TdProbAnsDetail();
                            ans.setMainId(mainId);
                            ans.setFkByQueTypeId(new TsSimpleCode(busType));
                            ans.setFkByQuestId(new TsProbSubject(sub.getRid()));
                            ans.setScoreValue(e.getValue());
                            ans.setOptionValue(split[1]);
                            ans.setFillValue(e.getValue());
                            ansList.add(ans);
                        } else if (split.length == 3) {// 如果长度为3，则为二级填空，则使用多项选择序号
                            // 表格题保存
                            if (sub.getFkByTableId() != null && sub.getFkByTableId().getRid() != null) {
                                for (TbProbColsdefine colsdefine : sub.getFkByTableId().getCols()) {
                                    if (colsdefine.getColName().equals(split[0] + "_" + split[1])) {
                                        TdProbAnsTable tabanswer = new TdProbAnsTable();
                                        tabanswer.setColValue(e.getValue());
                                        tabanswer.setFkByColId(colsdefine);
                                        tabanswer.setNum(split[2]);
                                        List<TdProbAnsTable> tabanswers = tabMap.get(sub.getRid());
                                        if (tabanswers == null) {
                                            tabanswers = new ArrayList<>();
                                            tabMap.put(sub.getRid(), tabanswers);
                                        }
                                        tabanswers.add(tabanswer);
                                    }
                                }
                            } else {
                                TdProbAnsDetail ans = new TdProbAnsDetail();
                                ans.setMainId(mainId);
                                ans.setFkByQueTypeId(new TsSimpleCode(busType));
                                ans.setFkByQuestId(new TsProbSubject(sub.getRid()));
                                ans.setScoreValue(e.getValue());
                                ans.setOptionValue(split[1]);
                                ans.setFillValue(e.getValue());
                                ans.setMultiNum(split[2]);// 多项填空序号
                                ansList.add(ans);
                            }
                        }
                    }
                }
            }
            if (null != ansList && ansList.size() > 0) {
                this.insertBatch(ansList);
            }
            /** 表格题保存 */
            if (tabMap.size() > 0) {
                for (Map.Entry<Integer, List<TdProbAnsTable>> e : tabMap.entrySet()) {
                    TdProbAnsDetail ans = new TdProbAnsDetail();
                    ans.setMainId(mainId);
                    ans.setFkByQueTypeId(new TsSimpleCode(busType));
                    ans.setFkByQuestId(new TsProbSubject(e.getKey()));
                    this.insertEntity(ans);
                    for (TdProbAnsTable tabanswer : e.getValue()) {
                        tabanswer.setFkBySurSubjectid(ans);
                    }
                    tableService.insertBatch(e.getValue());
                }
            }
        }
    }

    /**@*
     * <p>类描述：</p>
     * @MethodAuthor qrr,2021-09-26,saveExamPoolQuesAns
     * */
    public void saveExamPoolQuesAns(Map<String, TsProbExampool> qusMap, Map<String, String> dataMap,Integer mainId, Integer mainType) {
        if (null != qusMap && null != dataMap) {
            Set<Map.Entry<String, String>> entrySet = dataMap.entrySet();
            Map<String, String> fillMap = new HashMap<String, String>(); // 填空的后保存，有可能是更新记录
            // 问卷答案
            List<TdProbAnsDetail> ansList = new ArrayList<TdProbAnsDetail>();
            for (Map.Entry<String, String> e : entrySet) {
                if (StringUtils.isNotBlank(e.getKey()) && StringUtils.isNotBlank(e.getValue())
                        && e.getKey().startsWith("q")) {
                    if (e.getKey().indexOf("_") == -1) {
                        // 题目
                        TsProbExampool exampool = qusMap.get(e.getKey().replace("q", "").trim());
                        if (null != exampool) {// 题目Id
                            TdProbAnsDetail ans = new TdProbAnsDetail();
                            ans.setMainType(mainType);
                            ans.setMainId(mainId);
                            ans.setFkByExampoolId(exampool);
                            ans.setScoreValue(e.getValue());
                            ansList.add(ans);
                        }
                    } else {
                        // 选项填空
                        fillMap.put(e.getKey(), e.getValue());
                    }
                }
            }
            Map<Integer, List<TdProbAnsTable>> tabMap = new HashMap<>();
            if (null != fillMap && fillMap.size() > 0) {
                Set<Map.Entry<String, String>> fillEntrySet = fillMap.entrySet();
                for (Map.Entry<String, String> e : fillEntrySet) {
                    String queCode = e.getKey().split("_")[0].replace("q", "").trim();
                    TsProbExampool exampool = qusMap.get(queCode);
                    if (null != exampool) {
                        // 多项选择层级 ，如果数组长度为2，则为一级填空， 则使用选项序号
                        String[] split = e.getKey().split("_");
                        TdProbAnsDetail ans = new TdProbAnsDetail();
                        ans.setMainType(mainType);
                        ans.setMainId(mainId);
                        ans.setFkByExampoolId(exampool);
                        ans.setScoreValue(e.getValue());
                        ans.setOptionValue(split[1]);
                        ans.setFillValue(e.getValue());
                        if (split.length == 2) {
                            ansList.add(ans);
                        } else if (split.length == 3) {// 如果长度为3，则为二级填空，则使用多项选择序号
                            // 表格题保存
                            if (exampool.getFkByTableId() != null && exampool.getFkByTableId().getRid() != null) {
                                for (TbProbColsdefine colsdefine : exampool.getFkByTableId().getCols()) {
                                    if (colsdefine.getColName().equals(split[0] + "_" + split[1])) {
                                        TdProbAnsTable tabanswer = new TdProbAnsTable();
                                        tabanswer.setColValue(e.getValue());
                                        tabanswer.setFkByColId(colsdefine);
                                        tabanswer.setNum(split[2]);
                                        List<TdProbAnsTable> tabanswers = tabMap.get(exampool.getRid());
                                        if (tabanswers == null) {
                                            tabanswers = new ArrayList<>();
                                            tabMap.put(exampool.getRid(), tabanswers);
                                        }
                                        tabanswers.add(tabanswer);
                                    }
                                }
                            } else {
                                ans.setMultiNum(split[2]);// 多项填空序号
                                ansList.add(ans);
                            }
                        }
                    }
                }
            }
            if (null != ansList && ansList.size() > 0) {
                this.insertBatch(ansList);
            }
            /** 表格题保存 */
            if (tabMap.size() > 0) {
                for (Map.Entry<Integer, List<TdProbAnsTable>> e : tabMap.entrySet()) {
                    TdProbAnsDetail ans = new TdProbAnsDetail();
                    ans.setMainType(mainType);
                    ans.setMainId(mainId);
                    ans.setFkByExampoolId(new TsProbExampool(e.getKey()));
                    this.insertEntity(ans);
                    for (TdProbAnsTable tabanswer : e.getValue()) {
                        tabanswer.setFkBySurSubjectid(ans);
                    }
                    tableService.insertBatch(e.getValue());
                }
            }
        }
    }


    /**
     * @Description: 通过问卷类型 与 答卷主表Rid集合获取问卷答题详情列表
     *
     * @MethodAuthor pw,2021年10月15日
     */
    public List<TdProbAnsDetail> findAnsDetailListByQueTypeIdAndMainIds(Integer queTypeId, List<Integer> mainIdList){
        List<TdProbAnsDetail> resultList = new ArrayList<>();
        if(null == queTypeId || CollectionUtils.isEmpty(mainIdList)){
            return resultList;
        }
        List<Integer> executeQueryRidList = new ArrayList<>();
        if(mainIdList.size() < 1000){
            executeQueryRidList.addAll(mainIdList);
        }else{
            for(Integer rid : mainIdList){
                executeQueryRidList.add(rid);
                //避免出现 in 语句超过1000 导致执行失败的情况
                if(executeQueryRidList.size() >= 990){
                    List<TdProbAnsDetail> tmpList = this.baseMapper.findAnsDetailListByQueTypeIdAndMainIds(queTypeId,
                            executeQueryRidList);
                    if(!CollectionUtils.isEmpty(tmpList)){
                        resultList.addAll(tmpList);
                    }
                    executeQueryRidList.clear();
                }
            }
        }
        if(!CollectionUtils.isEmpty(executeQueryRidList)){
            List<TdProbAnsDetail> tmpList = this.baseMapper.findAnsDetailListByQueTypeIdAndMainIds(queTypeId,
                    executeQueryRidList);
            if(!CollectionUtils.isEmpty(tmpList)){
                resultList.addAll(tmpList);
            }
        }
        return resultList;
    }
}

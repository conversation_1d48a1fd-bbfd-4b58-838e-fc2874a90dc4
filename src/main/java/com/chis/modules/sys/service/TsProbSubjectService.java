package com.chis.modules.sys.service;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.*;
import com.chis.modules.sys.mapper.TsProbSubjectMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TsProbSubjectService
 */
@Service
public class TsProbSubjectService extends ZwxBaseServiceImpl<TsProbSubjectMapper, TsProbSubject> {
    @Autowired
    private TbProbTabdefineService tabdefineService;
    @Autowired
    private TbProbColsdefineService colsdefineService;
    /**
     * 根据问卷题库Id获取问卷题目对应的Id
     *
     * @param queId
     * @return
     */
    public Map<String, String> findQuesCodeList(Integer queId) {
        if (null != queId) {
            TsProbSubject subject = new TsProbSubject();
            subject.setFkByQuestlibId(new TsProbLib(queId));
            subject.setState("1");
            List<TsProbSubject> selectList = this.selectListByEntity(subject);
            Map<String, String> queIdMap = new HashMap<String, String>();
            if (null != selectList && selectList.size() > 0) {
                for (TsProbSubject tsProbSubject : selectList) {
                    queIdMap.put(tsProbSubject.getQesCode(), String.valueOf(tsProbSubject.getNum()));
                }
                return queIdMap;
            }
        }
        return null;
    }
    /**
     * 根据问卷题库Id获取问卷题目对应的Id
     *
     * @param queId
     * @return
     */
    public Map<String, TsProbSubject> findQuesCodeEntity(Integer queId) {
        if (null != queId) {
            TsProbSubject subject = new TsProbSubject();
            subject.setFkByQuestlibId(new TsProbLib(queId));
            subject.setState("1");
            List<TsProbSubject> selectList = this.selectListByEntity(subject);
            Map<String, TsProbSubject> queIdMap = new HashMap<String, TsProbSubject>();
            if (null != selectList && selectList.size() > 0) {
                for (TsProbSubject tsProbSubject : selectList) {
                    queIdMap.put(tsProbSubject.getQesCode(), tsProbSubject);
                }
                return queIdMap;
            }
        }
        return null;
    }
    /**
     * 根据问卷题库Id获取问卷题目
     *
     * @param queId
     * @return
     */
    public Map<String, TsProbSubject> findQuesMap(Integer queId) {
        if (null != queId) {
            TsProbSubject subject = new TsProbSubject();
            subject.setFkByQuestlibId(new TsProbLib(queId));
            subject.setState("1");
            List<TsProbSubject> selectList = this.selectListByEntity(subject);
            Map<String, TsProbSubject> queIdMap = new HashMap<String, TsProbSubject>();
            if (null != selectList && selectList.size() > 0) {
                for (TsProbSubject tsProbSubject : selectList) {
                    /** 表格题 */
                    if (tsProbSubject.getFkByTableId() != null && tsProbSubject.getFkByTableId().getRid() != null) {
                        TbProbTabdefine tabdefine = tabdefineService.selectByEntity(tsProbSubject.getFkByTableId());
                        tsProbSubject.setFkByTableId(tabdefine);
                        TbProbColsdefine colsdefine = new TbProbColsdefine();
                        colsdefine.setFkByTableId(tsProbSubject.getFkByTableId());
                        colsdefine.setState("1");
                        List<TbProbColsdefine> cols = colsdefineService.selectListByEntity(colsdefine);
                        tsProbSubject.getFkByTableId().setCols(cols);
                    }
                    queIdMap.put(String.valueOf(tsProbSubject.getNum()), tsProbSubject);
                }
                return queIdMap;
            }
        }
        return null;
    }
}

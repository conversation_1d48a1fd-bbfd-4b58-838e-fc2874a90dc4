package com.chis.modules.sys.service;

import cn.hutool.core.lang.func.Func0;
import com.chis.comm.utils.ObjectUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.sys.entity.TsContraSub;
import com.chis.modules.sys.entity.TsSimpleCode;
import com.chis.modules.sys.enums.contrast.IContrastEnum;
import com.chis.modules.sys.enums.contrast.ISimpleCodeEnum;
import com.chis.modules.sys.pojo.po.ContrastPO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用对照service
 *
 * @version 1.0
 */
@Service
public class ContrastCommService {
    @Resource
    protected TsSimpleCodeService simpleCodeService;
    @Resource
    protected TsContraSubService contraSubService;

    /**
     * 获取码表对照信息
     *
     * @param simpleCodeEnum 码表对照类别
     * @return Map 码表对照信息
     */
    public Map<String, TsSimpleCode> get(ISimpleCodeEnum simpleCodeEnum) {
        return ContrastPO.simpleCodeCache.get(
                simpleCodeEnum,
                (Func0<Map<String, TsSimpleCode>>) () -> pakTsSimpleCodeMap(simpleCodeEnum)
        );
    }

    /**
     * 获取对照信息
     *
     * @param contrastEnum 对照类别
     * @return Map 对照信息
     */
    public Map<String, TsContraSub> get(IContrastEnum contrastEnum) {
        return ContrastPO.contrastCache.get(
                contrastEnum,
                (Func0<Map<String, TsContraSub>>) () -> pakTsContraSubMap(contrastEnum)
        );
    }

    /**
     * 码表缓存封装
     *
     * @param simpleCodeCommEnum 码表类别
     * @return Map 缓存码表
     */
    private Map<String, TsSimpleCode> pakTsSimpleCodeMap(ISimpleCodeEnum simpleCodeCommEnum) {
        List<TsSimpleCode> list;
        switch (simpleCodeCommEnum.getDataFetchEnum()) {
            case ALL:
                list = this.simpleCodeService.findTsSimpleCodeAllList(simpleCodeCommEnum.getCodeType());
                break;
            case ENABLE:
                list = this.simpleCodeService.findTsSimpleCodeList(simpleCodeCommEnum.getCodeType());
                break;
            default:
                return new HashMap<>();
        }
        if (ObjectUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<String, TsSimpleCode> map = new HashMap<>();
        for (TsSimpleCode simpleCode : list) {
            String key;
            switch (simpleCodeCommEnum.getContrastType()) {
                case CODE_NO:
                    key = StringUtils.objectToString(simpleCode.getCodeNo());
                    break;
                case CODE_NAME:
                    key = StringUtils.objectToString(simpleCode.getCodeName());
                    break;
                case CODE_DESC:
                    key = StringUtils.objectToString(simpleCode.getCodeDesc());
                    break;
                case CODE_EXTENDS1:
                    key = StringUtils.objectToString(simpleCode.getExtends1());
                    break;
                default:
                    return new HashMap<>();
            }
            map.put(key, simpleCode);
        }
        return map;
    }

    /**
     * 对照缓存封装
     *
     * @param contrastEnum 对照类别
     * @return Map 对照
     */
    private Map<String, TsContraSub> pakTsContraSubMap(IContrastEnum contrastEnum) {
        List<TsContraSub> list = this.contraSubService.findTsContraSubList(contrastEnum.getContraCode(), contrastEnum.getBusiType());
        if (ObjectUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<String, TsContraSub> map = new HashMap<>();
        for (TsContraSub contraSub : list) {
            String key;
            switch (contrastEnum.getContrastType()) {
                case CONTRAST_LEFT_CODE:
                    key = StringUtils.objectToString(contraSub.getLeftCode());
                    break;
                default:
                    return new HashMap<>();
            }
            map.put(key, contraSub);
        }
        return map;
    }
}

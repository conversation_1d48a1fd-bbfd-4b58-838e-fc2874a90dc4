package com.chis.modules.sys.service;

import com.chis.modules.sys.entity.TbTjSrvorg;
import com.chis.modules.sys.enums.JdbcCaffeineKeyEnum;
import com.chis.modules.sys.mapper.TbTjSrvorgMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2019-07-31,TbTjSrvorgService
 */
@Service
public class TbTjSrvorgService extends ZwxBaseServiceImpl<TbTjSrvorgMapper, TbTjSrvorg> {
    public List<TbTjSrvorg> pageListToFj(Integer first, Integer pageSize, TbTjSrvorg entity) {
        List<TbTjSrvorg> list = baseMapper.pageListToFJ(first,pageSize, entity);
        return list;
    }

    public List<TbTjSrvorg> findTbTjSrvorgAll(){
        return baseMapper.findTbTjSrvorgAll();
    }

    public Map<String, TbTjSrvorg> findTbTjSrvorgByUnitCode(List<String> unitList) {
        Map<String, TbTjSrvorg> map = new LinkedHashMap<>();
        List<TbTjSrvorg> list = baseMapper.findTbTjSrvorgByUnitCode(unitList);
        if(!CollectionUtils.isEmpty(list)){
            for(TbTjSrvorg tbTjSrvorg : list){
                map.put(tbTjSrvorg.getUnitCode(),tbTjSrvorg);
            }
        }
        return map;
    }

    /**
     *  <p>方法描述：机构缓存</p>
     * @MethodAuthor hsj 2024-01-11 14:28
     */
    public Map<String, TbTjSrvorg> findTbTjSrvorgs() {
        Map<String, TbTjSrvorg> map = (Map<String,TbTjSrvorg>) CaffeineUtil.getIfPresent(JdbcCaffeineKeyEnum.SRVORG_ALL_KEY,"all");
        if(map != null && map.size() > 0){
            return map;
        }
        map = new HashMap<>();
        List<TbTjSrvorg> list = this.baseMapper.findTbTjSrvorgAll();
        for(TbTjSrvorg t:list){
            map.put(t.getUnitCode(),t);
        }
        CaffeineUtil.put(JdbcCaffeineKeyEnum.SRVORG_ALL_KEY, "all",map);
        return map;
    }
}

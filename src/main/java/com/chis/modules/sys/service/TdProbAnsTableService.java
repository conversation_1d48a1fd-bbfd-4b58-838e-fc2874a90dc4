package com.chis.modules.sys.service;

import com.chis.modules.sys.entity.TdProbAnsTable;
import com.chis.modules.sys.enums.QueBusType;
import com.chis.modules.sys.mapper.TdProbAnsTableMapper;
import com.chis.modules.sys.service.impl.ZwxBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p> 服务实现类：  </p>
 *
 * @ClassAuthor 机器人,2021-08-24,TdProbAnsTableService
 */
@Service
public class TdProbAnsTableService extends ZwxBaseServiceImpl<TdProbAnsTableMapper, TdProbAnsTable> {

    /**
     * <p>方法描述： 获取需要删除人员考核表格数据rid集合 </p>
     * @MethodAuthor： pw 2023/3/16
     **/
    public List<Integer> selectPsnCheckTdProbAnsTableRids(Integer mainId, Integer mainType){
        return this.baseMapper.selectPsnCheckTdProbAnsTableRids(mainId, mainType);
    }

    /**@*
     * <p>类描述：删除表格数据</p>
     * @MethodAuthor qrr,2021-08-24,
     * */
    @Transactional
    public void deleteTdProbAnsTable(Integer busType, Integer mainId){
        this.baseMapper.deleteTdProbAnsTable(busType,mainId);
    }
    /**@*
     * <p>类描述：删除人员考核表格数据</p>
     * @MethodAuthor qrr,2021-08-24,
     * */
    @Transactional
    public void deletePsnCheckTdProbAnsTable(Integer mainId, Integer mainType){
        this.baseMapper.deletePsnCheckTdProbAnsTable(mainId, mainType);
    }


    /**
     * @Description: 通过问卷答题详情rid集合获取对象
     *
     * @MethodAuthor pw,2021年08月26日
     */
    public List<TdProbAnsTable> findTdProbAnsTablesByDetailRidList(List<Integer> ridList){
        if(CollectionUtils.isEmpty(ridList)){
            return null;
        }
        List<TdProbAnsTable> resultList = new ArrayList<>();
        List<Integer> queryRidList = new ArrayList<>();
        //去重
        for(Integer rid : ridList){
            if(!queryRidList.contains(rid)){
                queryRidList.add(rid);
            }
        }
        List<Integer> executeQueryRidList = new ArrayList<>();
        if(queryRidList.size() < 1000){
            executeQueryRidList.addAll(queryRidList);
        }else{
            //避免出现 in 语句超过1000 导致执行失败的情况
            for(Integer rid : queryRidList){
                executeQueryRidList.add(rid);
                if(executeQueryRidList.size() >= 990){
                    List<TdProbAnsTable> tmpList = this.baseMapper.findTdProbAnsTablesByDetailRidList(executeQueryRidList);
                    if(!CollectionUtils.isEmpty(tmpList)){
                        resultList.addAll(tmpList);
                    }
                    executeQueryRidList.clear();
                }
            }
        }
        if(!CollectionUtils.isEmpty(executeQueryRidList)){
            List<TdProbAnsTable> tmpList = this.baseMapper.findTdProbAnsTablesByDetailRidList(executeQueryRidList);
            if(!CollectionUtils.isEmpty(tmpList)){
                resultList.addAll(tmpList);
            }
        }
        return resultList;
    }
}

package com.chis.modules.webmvc.cache.enums;

/***
 * <p>类描述: key的名称通用在这里管理，防止其他人覆盖 </p>
 *
 * @ClassAuthor mxp,2018/12/27,CaffeineKeyEnum
 */
public enum CaffeineKeyEnum implements ICaffeineKeyEnum{
    LOGIN_USER("login_user"),
    LOGIN_CFBUSER("login_cfbuser"),
    /**调用微信接口前获取access_token*/
	ACCESS_TOKEN("access_token"),
	/**获取用户认证code*/
    OAUTH2_CODE("oauth2_code"),
    /**获取用户认证code*/
    JS_API_TICKET("JS_API_TICKET"),
    /**慢病国家省市级对接*/
    MB_UPLOAD("MB_UPLOAD");



    private String key;

    CaffeineKeyEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }
}

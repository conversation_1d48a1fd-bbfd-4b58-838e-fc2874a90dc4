package com.chis.modules.webmvc.cache;

import com.chis.comm.utils.ObjectUtils;
import com.chis.modules.webmvc.cache.enums.ICaffeineKeyEnum;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/***
 * <p>类描述: caffeine缓存工具类 </p>
 *
 * @ClassAuthor mxp,2018/12/27,CaffeineUtil
 */
public class CaffeineUtil {


    /**
     * 无边界缓存：无数量上限、无时间上限
     */
    private static Cache<String, Object> outLimitCache;
    private static Cache<String, Object> limitCache;
    private static Map<Long, Cache<String, Object>> caches = new HashMap<>();


    static {
        //无边界缓存
        outLimitCache = Caffeine.newBuilder().build();

        //有边界缓存
        limitCache = Caffeine.newBuilder()
            .expireAfterWrite(86400, TimeUnit.SECONDS)
            .build();
    }

    /***
     * <p>方法描述:从缓存读取，没有返回null </p>
     *
     * @MethodAuthor mxp,2018/12/27,getIfPresent
     */
    public static Object getIfPresent(ICaffeineKeyEnum caffeineKeyEnum,String key){
       return outLimitCache.getIfPresent(caffeineKeyEnum.getKey()+"_"+key);
    }
    /***
     * <p>方法描述:从缓存读取，没有返回null </p>
     *
     * @MethodAuthor mxp,2018/12/27,getIfPresent
     */
    public static Object getLimitIfPresent(ICaffeineKeyEnum caffeineKeyEnum, String key){
       return limitCache.getIfPresent(caffeineKeyEnum.getKey()+"_"+key);
    }
    /***
     * <p>方法描述:存入缓存,null值不存 </p>
     *
     * @MethodAuthor mxp,2018/12/27,put
     */
    public static void put(ICaffeineKeyEnum caffeineKeyEnum,String key,Object object){
        if(object!=null){
            outLimitCache.put(caffeineKeyEnum.getKey()+"_"+key,object);
        }
    }
    /***
     * <p>方法描述:存入缓存,null值不存 </p>
     *
     * @MethodAuthor mxp,2018/12/27,put
     */
    public static void putLimit(ICaffeineKeyEnum caffeineKeyEnum, String key, Object object){
        if(object!=null){
        	limitCache.put(caffeineKeyEnum.getKey()+"_"+key,object);
        }
    }
    public static Cache<String, Object> getCacheForExpiration(long expireAfterWriteInSeconds) {
        Cache<String, Object> cache = caches.get(expireAfterWriteInSeconds);
        if (cache == null) {
            cache = Caffeine.newBuilder()
                    .expireAfterWrite(expireAfterWriteInSeconds, TimeUnit.SECONDS)
                    .build();
            caches.put(expireAfterWriteInSeconds, cache);
        }
        return cache;
    }
    /**
     *  <p>方法描述：有时间限制:且缓存要有值</p>
     * @MethodAuthor hsj 2024-06-19 9:35
     */
    public static void putForExpiration(long expireAfterWriteInSeconds,ICaffeineKeyEnum caffeineKeyEnum,String key,Object object){
        if(ObjectUtils.isNull(object)){
            return;
        }
        getCacheForExpiration(expireAfterWriteInSeconds);
        if(object!=null){
            caches.get(expireAfterWriteInSeconds).put(caffeineKeyEnum.getKey()+"_"+key,object);
        }
    }
    /**
     *  <p>方法描述：有时间限制</p>
     * @MethodAuthor hsj 2024-06-19 9:35
     */
    public static Object getForExpiration(ICaffeineKeyEnum caffeineKeyEnum,String key){
        for (Map.Entry<Long, Cache<String, Object>> entry : caches.entrySet()) {
            Object value = entry.getValue().getIfPresent(caffeineKeyEnum.getKey() + "_" + key);
            if (value != null) {
                return value;
            }
        }
        return null;
    }
}

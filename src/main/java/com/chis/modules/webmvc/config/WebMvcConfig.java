package com.chis.modules.webmvc.config;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import com.chis.comm.utils.PropertyUtils;
import com.chis.comm.utils.StringUtils;
import com.chis.modules.webmvc.spring.aspect.LogRecordAspect;
import com.chis.modules.webmvc.spring.interceptor.AuthenticationInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


@Configuration
@Slf4j
public class WebMvcConfig implements WebMvcConfigurer {

    @Bean
    public LogRecordAspect logRecordAspect() {
        return new LogRecordAspect();
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry){
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/META-INF/resources/").setCachePeriod(0);
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")//设置允许跨域的路径
                .allowedOrigins("*")//设置允许跨域请求的域名
                .allowCredentials(true)//是否允许证书 不再默认开启
                .allowedMethods("GET", "POST", "PUT", "DELETE")//设置允许的方法
                .maxAge(3600);//跨域允许时间
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authenticationInterceptor())
                .addPathPatterns("/**");    // 拦截所有请求，通过判断是否有 @LoginRequired 注解 决定是否需要登录
    }

    @Bean
    public AuthenticationInterceptor authenticationInterceptor() {
        return new AuthenticationInterceptor();
    }
    /**
     *  <p>方法描述：配置文件的读取</p>
     * @MethodAuthor hsj 2025-03-27 15:21
     */
    @Bean
    public static PropertySourcesPlaceholderConfigurer properties() throws IOException {
        PropertySourcesPlaceholderConfigurer configurer = new PropertySourcesPlaceholderConfigurer();
        List<Resource> allResources = loadConfigResources("chiscdc-*.yml", "chiscdc-*.properties");
        List<Properties> propertiesList = new ArrayList<>();
        for (Resource resource : allResources) {
            if (resource.getFilename().endsWith(".yml")) {
                YamlPropertiesFactoryBean yaml = new YamlPropertiesFactoryBean();
                InputStream inputStream = resource.getInputStream();
                yaml.setResources(new InputStreamResource(inputStream));
                propertiesList.add(yaml.getObject());
            } else if (resource.getFilename().endsWith(".properties")) {
                Properties props = new Properties();
                props.load(resource.getInputStream());
                propertiesList.add(props);
            }
        }
        configurer.setPropertiesArray(propertiesList.toArray(new Properties[0]));
        configurer.setLocations(allResources.toArray(new Resource[0]));
        return configurer;
    }

    @Bean
    public static PropertyPlaceholderConfigurer get() throws IOException {
        PropertyPlaceholderConfigurer placeholderConfigurer = new PropertyUtils();
        List<Resource> allResources = loadConfigResources("chiscdc-*.properties");
        placeholderConfigurer.setLocations(allResources.toArray(new Resource[0]));
        placeholderConfigurer.setIgnoreUnresolvablePlaceholders(true);
        return placeholderConfigurer;
    }

    /**
     * 加载配置文件资源,读取参数，若参数不为空时，读取指定的路径下的配置文件
     *
     * @param patterns 配置文件匹配模式，支持多个模式
     * @return 配置文件资源列表
     * @throws IOException 读取资源时可能抛出的异常
     */
    private static List<Resource> loadConfigResources(String... patterns) throws IOException {
        List<Resource> allResources = new ArrayList<>();
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        String customVariable = System.getenv("CUSTOM_VARIABLE");
        log.info("加载配置文件夹名称路径：{}", customVariable);
        if(StringUtils.isNotBlank(customVariable)){
            customVariable = customVariable.trim();
            for (String pattern : patterns) {
                Resource[] externalResources = resolver.getResources(customVariable +"/"+ pattern);
                for (Resource resource : externalResources) {
                    if (resource.exists()) {
                        allResources.add(resource);
                        log.info("加载配置文件：{}", resource.getURI());
                    }
                }
            }
        }else{
            for (String pattern : patterns) {
                Resource[] internalResources = resolver.getResources("classpath*:/" + pattern);
                for (Resource resource : internalResources) {
                    if (resource.exists()) {
                        allResources.add(resource);
                        log.info("加载配置文件：{}", resource.getURI());
                    }
                }
            }
        }
        return allResources;
    }
}

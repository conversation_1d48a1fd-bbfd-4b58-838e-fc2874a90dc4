package com.chis.modules.webmvc.exception;

import com.alibaba.fastjson.JSONObject;
import com.chis.modules.webmvc.api.enums.ReturnType;
import com.chis.modules.webmvc.api.pojo.ReturnDTO;
import com.chis.modules.webmvc.api.utils.ResponseUtil;
import com.chis.modules.webmvc.api.wrapper.ResponseWrapper;
import com.chis.modules.webmvc.encrypt.algorithm.AesEncryptAlgorithm;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 全程异常处理
 */
@Slf4j
@Component
public class ZwxHandlerExceptionResolver extends AbstractHandlerExceptionResolver {

    private static final ModelAndView MODEL_VIEW_INSTANCE = new ModelAndView();
    @Value("${spring.encrypt.key}")
    private String key;

    @Value("${spring.encrypt.debug}")
    private String debug;



    /**
     * Sets the {@linkplain #setOrder(int) order} to {@link #LOWEST_PRECEDENCE}.
     */
    public ZwxHandlerExceptionResolver() {
        setOrder(Ordered.LOWEST_PRECEDENCE);
    }

    @Override
    protected ModelAndView doResolveException(HttpServletRequest request, HttpServletResponse response, Object handler,
                                              Exception ex) {
        try {
            System.out.println("异常来了~~~~~~~~~~~~");
            if (ex instanceof ZwxApiException) {
                handleApi((ZwxApiException) ex, request, response);
            } else {
                handleException(ex, request, response);
            }
        } catch (Exception handlerException) {
            if (log.isWarnEnabled()) {
                log.warn("Handling of [" + ex.getClass().getName() + "] resulted in Exception", handlerException);
            }
        }
        log.error("Warn: doResolveException {}", Throwables.getStackTraceAsString(ex));
        return MODEL_VIEW_INSTANCE;
    }

    /**
     * Handle the case where exception
     *
     * @param ex       the ApiException to be handled
     * @param request  current HTTP request
     * @param response current HTTP response
     */
    protected void handleApi(ZwxApiException ex,
                             HttpServletRequest request, HttpServletResponse response) {
        ResponseUtil.writeValAsJson(request, new ResponseWrapper(response), ex.getReturnDTO());

    }


    /**
     * Handle the case where an other error.
     * <p>
     * The default implementation sends an HTTP 500 error.
     *
     * @param ex       the {@link Exception }to be handled
     * @param request  current HTTP request
     * @param response current HTTP response
     */
    protected void handleException(Exception ex, HttpServletRequest request, HttpServletResponse response) {
        AesEncryptAlgorithm algorithm = new AesEncryptAlgorithm();
        Object obj = new Object();
        try{
            if(debug.equals("true")){
                obj = ReturnDTO.builder().type(JSONObject.toJSONString(ReturnType.EXCEPTION_PROCESS.getTypeNo())).mess(JSONObject.toJSONString(ex.getMessage())).build();
            }else{
                obj =algorithm.encrypt(JSONObject.toJSONString(ReturnDTO.builder().type(JSONObject.toJSONString(ReturnType.EXCEPTION_PROCESS.getTypeNo())).mess(JSONObject.toJSONString(ex.getMessage())).build()), key);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        ResponseUtil.writeValAsJson(request, new ResponseWrapper(response),obj);
    }


}

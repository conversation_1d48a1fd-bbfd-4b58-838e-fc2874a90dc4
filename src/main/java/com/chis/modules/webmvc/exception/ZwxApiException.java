package com.chis.modules.webmvc.exception;

import com.chis.modules.webmvc.api.enums.ReturnType;
import com.chis.modules.webmvc.api.pojo.ReturnDTO;

/***
 * <p>类描述: API 业务异常类 </p>
 *
 * @ClassAuthor mxp,2019/1/2,ZwxApiException
 */
public class ZwxApiException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private ReturnDTO returnDTO;

    public ZwxApiException(ReturnDTO errorCode) {
        super(errorCode.getMess());
        this.returnDTO = errorCode;

    }
    public ZwxApiException(ReturnType type) {
        super(type.getTypeCN());
        this.returnDTO = ReturnDTO.builder().type(type.getTypeNo()).mess(type.getTypeCN()).build();
    }

    public ReturnDTO getReturnDTO() {
        return returnDTO;
    }

}

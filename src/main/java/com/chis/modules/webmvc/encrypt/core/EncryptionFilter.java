package com.chis.modules.webmvc.encrypt.core;

import com.chis.comm.utils.StringUtils;
import com.chis.modules.webmvc.encrypt.algorithm.AesEncryptAlgorithm;
import com.chis.modules.webmvc.encrypt.algorithm.EncryptAlgorithm;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据加解密过滤器
 * 
 * <AUTHOR>
 * 
 * @date 2019-01-12
 * 
 * @about http://cxytiandi.com/about
 *
 */
@Slf4j
public class EncryptionFilter implements Filter {


	private EncryptionConfig encryptionConfig;
	
	private EncryptAlgorithm encryptAlgorithm = new AesEncryptAlgorithm();
	
	public EncryptionFilter() {
		this.encryptionConfig = new EncryptionConfig();
	}
	
	public EncryptionFilter(EncryptionConfig config) {
		this.encryptionConfig = config;
	}
	
	public EncryptionFilter(EncryptionConfig config, EncryptAlgorithm encryptAlgorithm) {
		this.encryptionConfig = config;
		this.encryptAlgorithm = encryptAlgorithm;
	}
	
	public EncryptionFilter(String key) {
		EncryptionConfig config = new EncryptionConfig();
		config.setKey(key);
		this.encryptionConfig = config;
	}
	
	public EncryptionFilter(String key, List<String> responseEncryptUriList, List<String> requestDecyptUriList,
                            String responseCharset, boolean debug) {
		this.encryptionConfig = new EncryptionConfig(key, responseEncryptUriList, requestDecyptUriList, responseCharset, debug);
	}
	
	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		
	}
	
	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		
		HttpServletRequest req = (HttpServletRequest) request;
		HttpServletResponse resp = (HttpServletResponse)response;
		String uri = req.getRequestURI().replaceAll("//","/");
		log.info("RequestURI: {}", uri);
		
		// 调试模式不加解密
		if (encryptionConfig.isDebug()) {
			chain.doFilter(req, resp);
			return;
		}


		// 需要加密解密的URI
		boolean decryptionStatus = this.contains(encryptionConfig.getRequestDecyptUriList(), uri, req.getMethod());
		boolean encryptionStatus = this.contains(encryptionConfig.getResponseEncryptUriList(), uri, req.getMethod());


		// 不需要加密解密的URI
		boolean noDecryptionStatus = this.contains(encryptionConfig.getRequestNoDecyptUriList(), uri, req.getMethod());
		boolean noEncryptionStatus = this.contains(encryptionConfig.getResponseNoEncryptUriList(), uri, req.getMethod());
		
		// 没有配置具体加解密的URI默认全部都开启加解密
		if (encryptionConfig.getRequestDecyptUriList().size() == 0 
				&& encryptionConfig.getResponseEncryptUriList().size() == 0) {
			decryptionStatus = true;
			encryptionStatus = true;
		}

		// 配置了不需要解密的URI
		if(noDecryptionStatus){
			decryptionStatus = false;
		}
		// 配置了不需要加密的URI
		if(noEncryptionStatus){
			encryptionStatus = false;
		}


		
		// 没有加解密操作
		if (!decryptionStatus && !encryptionStatus) {
			chain.doFilter(req, resp);
			return;
		}
		
		
		EncryptionResponseWrapper responseWrapper = null;
		EncryptionReqestWrapper reqestWrapper = null;
		// 配置了需要解密才处理
		if (decryptionStatus) {
			Map<String, String[]> requestMap= req.getParameterMap();
			Map<String, String[]> decyptRequestMap = new HashMap<>();
			if(requestMap != null && requestMap.size() > 0){
				try {
					for(Map.Entry<String, String[]> entry : requestMap.entrySet()){
						String key = entry.getKey();
						String data = encryptAlgorithm.decrypt(key, encryptionConfig.getKey());
						//是否存在{}
						if(data.contains("{") && data.contains("}")){
							String[] parts = data.split("=", 2); // 使用限制参数2，确保只分割第一个
							String k = parts[0];
							String v = URLDecoder.decode(parts[1], "utf-8");
							decyptRequestMap.put(k,new String[]{v});
						}else {
							String[] sp = data.split("&");
							for(String str : sp){
								String[] parts = str.split("=");
								String k = parts[0];
								String v = null;
								if(parts.length == 2){
									v =  URLDecoder.decode(parts[1], "utf-8");
								}
								String[] curArr = decyptRequestMap.get(k);
								String[] fillArr = new String[null == curArr ? 1 : curArr.length+1];
								if (null != curArr) {
									int i = 0;
									for (String s : curArr) {
										fillArr[i++] = s;
									}
								}
								fillArr[null == curArr ? 0 : curArr.length] = v;
								decyptRequestMap.put(k,fillArr);
							}
						}
					}
				}catch (Exception e){
					log.error("请求数据解密失败", e);
					throw new RuntimeException(e);
				}
			}
			reqestWrapper = new EncryptionReqestWrapper(req,decyptRequestMap);
			String requestData = reqestWrapper.getRequestData();
			if(StringUtils.isNotBlank(requestData)){
				log.info("RequestData: {}", requestData);
				try {
					String decyptRequestData = encryptAlgorithm.decrypt(requestData, encryptionConfig.getKey());
					log.info("DecyptRequestData: {}", decyptRequestData);
					reqestWrapper.setRequestData(decyptRequestData);
				} catch (Exception e) {
					log.error("请求数据解密失败", e);
					throw new RuntimeException(e);
				}
			}

		}
		
		if (encryptionStatus) {
			responseWrapper = new EncryptionResponseWrapper(resp);
		}
		
		// 同时需要加解密
		if (encryptionStatus && decryptionStatus) {
			chain.doFilter(reqestWrapper, responseWrapper);
		} else if (encryptionStatus) { //只需要响应加密
			chain.doFilter(req, responseWrapper);
		} else if (decryptionStatus) { //只需要请求解密
			chain.doFilter(reqestWrapper, resp);
		}
		
		// 配置了需要加密才处理
		if (encryptionStatus) {
			String responeData = responseWrapper.getResponseData();
			log.info("ResponeData: {}", responeData);
			ServletOutputStream out = null;
			try {
				responeData = encryptAlgorithm.encrypt(responeData, encryptionConfig.getKey());
				log.info("EncryptResponeData: {}", responeData);
				response.setContentLength(responeData.length());
				response.setCharacterEncoding(encryptionConfig.getResponseCharset());
		        out = response.getOutputStream();
		        out.write(responeData.getBytes(encryptionConfig.getResponseCharset()));
			} catch (Exception e) {
				log.error("响应数据加密失败", e);
				throw new RuntimeException(e);
			} finally {
				if (out != null) {
					out.flush();
				    out.close();
				}
			}
			
		} 
		
	}

	private boolean contains(List<String> list, String uri, String methodType) {
		if (list.contains(uri)) {
			return true;
		}
		String prefixUri = methodType.toLowerCase() + ":" + uri;
		log.info("contains uri: {}", prefixUri);
		if (list.contains(prefixUri)) {
			return true;
		}
		return false;
	}
	
	@Override
	public void destroy() {
		
	}
}

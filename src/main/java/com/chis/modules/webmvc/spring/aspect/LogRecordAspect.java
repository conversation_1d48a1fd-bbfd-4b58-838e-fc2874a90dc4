package com.chis.modules.webmvc.spring.aspect;

import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

import com.chis.modules.webmvc.log.utils.ZwxLogUtil;

/***
 * <p>类描述: Controller统一切点日志处理类 </p>
 *
 * @ClassAuthor mxp,2018/12/20,LogRecordAspect
 */
@Aspect
public class LogRecordAspect {

    /***
     * <p>方法描述: 切点</p>
     *
     * @MethodAuthor mxp,2018/12/20,pointCut
     */
    @Pointcut("execution(public * com.chis.modules.*.*.controller.*Controller.*(..))")
    public void pointCut() {
    }

    @AfterReturning(returning = "ret", pointcut = "pointCut()")
    public void doAfterReturning(Object ret) {
        ZwxLogUtil.doAfterReturning(ret);
    }

}

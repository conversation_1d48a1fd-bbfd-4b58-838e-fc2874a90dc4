package com.chis.modules.webmvc.spring.utils;

import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;

/**
 * <p>类描述：统一验证有注解的请求参数</p>
 * @ClassAuthor qrr,2019年2月22日,ValidateResultUtils
 * */
public class ValidateResultUtils {
	/**
 	 * <p>方法描述：获取返回结果</p>
 	 * @MethodAuthor qrr,2019年2月22日,getErrors
	 * */
	public static String getErrors(BindingResult result) {
		StringBuffer sb = new StringBuffer();
		if (result.hasErrors()) {
			for (ObjectError error : result.getAllErrors()) {
				sb.append(error.getDefaultMessage());
			}
		}
		return sb.toString();
	}
}

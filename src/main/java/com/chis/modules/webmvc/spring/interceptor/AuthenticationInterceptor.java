package com.chis.modules.webmvc.spring.interceptor;

import com.chis.modules.webmvc.api.enums.ReturnType;
import com.chis.modules.webmvc.api.utils.ApiAssert;
import com.chis.modules.webmvc.cache.CaffeineUtil;
import com.chis.modules.webmvc.cache.enums.CaffeineKeyEnum;
import com.chis.modules.webmvc.jwt.annotation.ValidateToken;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;


/***
 * <p>类描述: 拦截器，拦截所有请求 </p>
 *
 * @ClassAuthor mxp,2019/1/2,AuthenticationInterceptor
 */
public class AuthenticationInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object object) throws Exception {
        String token = request.getHeader("tokenId");// 从 http 请求头中取出 token
        // 如果不是映射到方法直接通过
        if(!(object instanceof HandlerMethod)){
            return true;
        }
        HandlerMethod handlerMethod=(HandlerMethod)object;
        Method method=handlerMethod.getMethod();
        //检查是否含有需要验证token的注解
        if (method.isAnnotationPresent(ValidateToken.class)) {
            ValidateToken userLoginToken = method.getAnnotation(ValidateToken.class);
            if (userLoginToken.required()) {
                // 执行认证
                if (token == null) {
                    throw new RuntimeException("无tokenId，请重新登录");
                }

                // 获取 token 中的 user id
                Object obj = CaffeineUtil.getLimitIfPresent(CaffeineKeyEnum.LOGIN_USER, token);
                if(obj==null){
                    ApiAssert.failure(ReturnType.TOKENID_VALID);
                }
                return true;
            }
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object o, ModelAndView modelAndView) throws Exception {

    }
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object o, Exception e) throws Exception {

    }
}

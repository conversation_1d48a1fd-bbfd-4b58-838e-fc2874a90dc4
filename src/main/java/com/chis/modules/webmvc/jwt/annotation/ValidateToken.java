package com.chis.modules.webmvc.jwt.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/***
 * <p>类描述: token验证注解 </p>
 *
 * @ClassAuthor mxp,2019/1/2,ValidateToken
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidateToken {
    boolean required() default true;
}

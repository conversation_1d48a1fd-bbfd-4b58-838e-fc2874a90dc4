package com.chis.modules.webmvc.api.responses;

import java.io.Serializable;
import java.time.LocalDateTime;

import org.springframework.http.HttpStatus;

import com.chis.modules.webmvc.api.utils.ResponseUtil;

/**
 * 接口返回(多态)
 *
 */
public class ApiResponses<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 不需要返回结果
     */
    public static ApiResponses<Void> empty() {
        return SuccessResponse.<Void>builder().status(HttpStatus.OK.value()).build();

    }

    /**
     * 成功返回
     *
     * @param object
     */
    public static <T> ApiResponses<T> success(T object) {
        return SuccessResponse.<T>builder().status(HttpStatus.OK.value()).result(object).build();

    }

}

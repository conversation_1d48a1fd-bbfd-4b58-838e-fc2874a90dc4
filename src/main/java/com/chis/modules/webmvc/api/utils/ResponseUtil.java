package com.chis.modules.webmvc.api.utils;

import com.alibaba.fastjson.util.TypeUtils;
import com.chis.comm.utils.ObjectUtils;
import com.chis.modules.webmvc.api.wrapper.ResponseWrapper;
import com.chis.modules.webmvc.constants.APICons;
import com.chis.modules.webmvc.log.utils.ZwxLogUtil;
import com.chis.modules.webmvc.utils.IpUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;

/**
 * response输出工具类
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public abstract class ResponseUtil {

    /**
     * Portal输出json字符串
     *
     * @param response
     * @param obj      需要转换JSON的对象
     */
    public static void writeValAsJson(HttpServletRequest request, ResponseWrapper response, Object obj) {
        ZwxLogUtil.printLog((Long) request.getAttribute(APICons.API_BEGIN_TIME),
                TypeUtils.castToString(request.getAttribute(APICons.API_UID)),
                request.getParameterMap(),
                RequestUtil.getRequestBody(request),
                (String) request.getAttribute(APICons.API_REQURL),
                (String) request.getAttribute(APICons.API_MAPPING),
                (String) request.getAttribute(APICons.API_METHOD),
                IpUtils.getRemoteAddr(request),
                obj);
        if (ObjectUtils.isNotNull(response, obj)) {
            response.writeValueAsJson(obj);
        }
    }

    /**
     * 打印日志信息但是不输出到浏览器
     *
     * @param request
     * @param obj
     */
    public static void writeValAsJson(HttpServletRequest request, Object obj) {
        writeValAsJson(request, null, obj);
    }

}

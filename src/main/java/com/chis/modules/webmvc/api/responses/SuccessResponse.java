package com.chis.modules.webmvc.api.responses;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

/***
 * <p>类描述: 成功返回 </p>
 *
 * @ClassAuthor mxp,2018/12/27,SuccessResponse
 */
@Getter
@ToString
@Builder
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class SuccessResponse<T> extends ApiResponses<T> {

    private static final long serialVersionUID = 1L;
    /**
     * http 状态码
     */
    private Integer status;

    /**
     * 结果集返回
     */
    private T result;



}

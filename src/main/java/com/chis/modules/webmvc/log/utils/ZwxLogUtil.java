package com.chis.modules.webmvc.log.utils;

import java.util.Map;
import java.util.Optional;

import com.chis.modules.webmvc.api.utils.ResponseUtil;
import com.chis.modules.webmvc.log.entity.ZwxLog;
import com.chis.modules.webmvc.spring.utils.ApplicationUtils;
import com.chis.modules.webmvc.utils.JacksonUtils;

import lombok.extern.slf4j.Slf4j;

/***
 * <p>类描述: 请求日志工具类 </p>
 *
 * @ClassAuthor mxp,2018/12/20,ZwxLogUtil
 */
@Slf4j
public abstract class ZwxLogUtil {

    private boolean mongo;

    private boolean file;

    private String filePath;



    public void setMongo(boolean mongo) {
        this.mongo = mongo;
    }


    public void setFile(boolean file) {
        this.file = file;
    }


    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    /**
     * 获取日志对象
     *
     * @param beginTime
     * @param parameterMap
     * @param requestBody
     * @param url
     * @param mapping
     * @param method
     * @param ip
     * @param object
     * @return
     */
    public static void printLog(Long beginTime, String uid, Map<String, String[]> parameterMap, String requestBody, String url, String mapping, String method, String ip, Object object) {
        ZwxLog logInfo = ZwxLog.builder()
                //查询参数
                .parameterMap(parameterMap)
                .uid(uid)
                //请求体
                .requestBody(Optional.ofNullable(JacksonUtils.parse(requestBody)).orElse(requestBody))
                //请求路径
                .url(url)
                //请求mapping
                .mapping(mapping)
                //请求方法
                .method(method)
                .runTime((beginTime != null ? System.currentTimeMillis() - beginTime : 0) + "ms")
                .result(object)
                .ip(ip)
                .build();


    }

    public static void doAfterReturning(Object ret) {
        ResponseUtil.writeValAsJson(ApplicationUtils.getRequest(), ret);
    }

}

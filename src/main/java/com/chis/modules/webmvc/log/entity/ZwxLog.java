package com.chis.modules.webmvc.log.entity;

import java.util.Map;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/***
 * <p>类描述: app请求日志详情 </p>
 *
 * @ClassAuthor mxp,2018/12/20,ZwxLog
 */
@Getter
@ToString
@Builder
@EqualsAndHashCode(callSuper = false)
public class ZwxLog {

    /**
     * 参数
     */
    private Map<String, String[]> parameterMap;
    /**
     * requestBody
     */
    private Object requestBody;
    /**
     * 请求路径
     */
    private String url;
    /**
     * 请求mapping
     */
    private String mapping;
    /**
     * 请求方法
     */
    private String method;
    /**
     * 日志需要打印的json字符串
     */
    private Object result;
    /**
     * 接口运行时间 单位:ms
     */
    private String runTime;
    /**
     * IP地址
     */
    private String ip;
    /**
     * UID
     */
    private String uid;

}

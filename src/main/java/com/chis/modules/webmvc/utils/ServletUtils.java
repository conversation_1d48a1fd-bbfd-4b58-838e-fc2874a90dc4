package com.chis.modules.webmvc.utils;

import com.chis.comm.utils.FileUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;

/**
 * @Description : TODO
 * @ClassAuthor : anjing
 * @Date : 2020/8/21 11:28
 **/
public class ServletUtils {

    public static HttpServletResponse downLoadFiles(String tempzip, String srcpath, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        try {
            //压缩文件
            File zipfile = new File(tempzip);
            if (!zipfile.exists()){
                zipfile.createNewFile();
            }
            response.reset();
            //创建文件输出流
            FileOutputStream fous = new FileOutputStream(zipfile);
            java.util.zip.ZipOutputStream zipOut = new java.util.zip.ZipOutputStream(fous);
            File file=new File(srcpath);
            File[] tempList = file.listFiles();
            for(File srcfile : tempList) {
                FileUtils.zipFile(srcfile, zipOut);
            }
            zipOut.close();
            fous.close();
            return downloadZip(zipfile,response);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return response ;
    }

    public static HttpServletResponse downloadZip(File file, HttpServletResponse response) {
        try {
            // 以流的形式下载文件。
            InputStream fis = new BufferedInputStream(new FileInputStream(file.getPath()));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            // 清空response
            response.reset();

            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");

            //如果输出的是中文名的文件，在此处就要用URLEncoder.encode方法进行处理
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getName(), "UTF-8"));
            toClient.write(buffer);
            toClient.flush();
            toClient.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        }finally{
            try {
                File f = new File(file.getPath());
                f.delete();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return response;
    }

    /**
     * 在request中获取异常类
     * @param request
     * @return
     */
    public static Throwable getThrowable(HttpServletRequest request){
        Throwable ex = null;
        if (request.getAttribute("exception") != null) {
            ex = (Throwable) request.getAttribute("exception");
        } else if (request.getAttribute("javax.servlet.error.exception") != null) {
            ex = (Throwable) request.getAttribute("javax.servlet.error.exception");
        }
        return ex;
    }
}

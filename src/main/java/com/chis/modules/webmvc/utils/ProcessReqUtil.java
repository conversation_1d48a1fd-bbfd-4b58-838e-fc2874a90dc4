package com.chis.modules.webmvc.utils;

import com.chis.comm.utils.Encodes;
import com.chis.comm.utils.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 处理接收的请求
 * 
 * <AUTHOR>
 * @history 2014-09-22 创建文件
 * 
 */
public class ProcessReqUtil {
	/**
	 * 将http请求中的Map<String,String[]>转换成Map<String,String>
	 * 
	 * @param request
	 * @return
	 */
	public static Map<String, String> processRequestMap(HttpServletRequest request) {
		Map<String, String> map = new HashMap<String, String>();
		if (null != request) {
			// 获取request请求的参数集合
			Map<String, String[]> requestMap = request.getParameterMap();
			// 将request请求集合Map<String,String[]>格式化成Map<String,String>
			if (null != requestMap && requestMap.size() > 0) {
				Iterator<String> it = requestMap.keySet().iterator();
				while (it.hasNext()) {
					String next = it.next();
					String[] strArr = requestMap.get(next);
					StringBuilder val = new StringBuilder();
					if( null != strArr && strArr.length > 0)	{
						for( String s : strArr)	{
							if( StringUtils.isNotBlank(s)){
								val.append(",").append(s);
							}
						}
						if(val.length() > 0 ) val.deleteCharAt(0);
					}
					//处理值中含有%报错问题
					map.put(next,  Encodes.urlDecode(val.toString().replaceAll("%", "%25")));
				}
			}
		}
		return map;
	}
}

package com.chis.comm.pojo;

import java.util.List;

/**
 * @Description: Excel导出 动态sheet对象
 * 
 * @ClassAuthor pw,2022年04月12日,ExportDynamicSheetPO
 */
public class ExportDynamicSheetPO {
    /** 标题行 */
    private List<ExportDynamicRowPO> headRowList;
    /** 数据行 */
    private List<ExportDynamicRowPO> dataRowList;
    /** sheet名称 */
    private String sheetName;
    /** 是否固定标题行 默认固定 */
    private boolean ifFreezePane = Boolean.TRUE;
    /** 是否需要边框线 默认需要 */
    private boolean ifCellHasBorder = Boolean.TRUE;
    /** 数据行是否无合并行合并列的情况 默认有合并行或者合并列 */
    private boolean ifDataRowSingle = Boolean.FALSE;

    public List<ExportDynamicRowPO> getHeadRowList() {
        return headRowList;
    }

    public void setHeadRowList(List<ExportDynamicRowPO> headRowList) {
        this.headRowList = headRowList;
    }

    public List<ExportDynamicRowPO> getDataRowList() {
        return dataRowList;
    }

    public void setDataRowList(List<ExportDynamicRowPO> dataRowList) {
        this.dataRowList = dataRowList;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public boolean isIfFreezePane() {
        return ifFreezePane;
    }

    public void setIfFreezePane(boolean ifFreezePane) {
        this.ifFreezePane = ifFreezePane;
    }

    public boolean isIfCellHasBorder() {
        return ifCellHasBorder;
    }

    public void setIfCellHasBorder(boolean ifCellHasBorder) {
        this.ifCellHasBorder = ifCellHasBorder;
    }

    public boolean isIfDataRowSingle() {
        return ifDataRowSingle;
    }

    public void setIfDataRowSingle(boolean ifDataRowSingle) {
        this.ifDataRowSingle = ifDataRowSingle;
    }
}

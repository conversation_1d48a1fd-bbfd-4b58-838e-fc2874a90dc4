package com.chis.comm.pojo;

import org.apache.poi.ss.usermodel.CellStyle;

/**
 * @Description: Excel 导出 单元格数据对象
 * 
 * @ClassAuthor pw,2022年04月12日,ExportDynamicColPO
 */
public class ExportDynamicColPO {
    /** 数据对象 */
    private Object colVal;
    /** 数据单元格样式 */
    private CellStyle colStyle;
    /** 合并行数，默认为1 */
    private Integer rowspan = 1;
    /** 合并列数，默认为1 */
    private Integer colspan = 1;
    /** 单元格宽度 默认3000 */
    private Integer colWidth = 3000;
    // 是否需要边框线 注意 设置边框线是在colStyle中设置的样式
    // 要考虑样式被覆盖的情况，如果所有单元格都使用一个colStyle，那么如果第一个单元格没有边框线，最后一个设置边框线，那么生成Excel时，单元格会全部有边框线
    private Boolean ifHasBorder;

    public Object getColVal() {
        return colVal;
    }

    public void setColVal(Object colVal) {
        this.colVal = colVal;
    }

    public CellStyle getColStyle() {
        return colStyle;
    }

    public void setColStyle(CellStyle colStyle) {
        this.colStyle = colStyle;
    }

    public Integer getRowspan() {
        return rowspan;
    }

    public void setRowspan(Integer rowspan) {
        this.rowspan = rowspan;
    }

    public Integer getColspan() {
        return colspan;
    }

    public void setColspan(Integer colspan) {
        this.colspan = colspan;
    }

    public Integer getColWidth() {
        return colWidth;
    }

    public void setColWidth(Integer colWidth) {
        this.colWidth = colWidth;
    }

    public Boolean getIfHasBorder() {
        return ifHasBorder;
    }

    public void setIfHasBorder(Boolean ifHasBorder) {
        this.ifHasBorder = ifHasBorder;
    }
}

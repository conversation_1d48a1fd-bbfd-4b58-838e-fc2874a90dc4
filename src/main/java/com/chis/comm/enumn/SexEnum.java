package com.chis.comm.enumn;


/**
 * <p>类描述：性别枚举类</p>
 * @ClassAuthor qrr,2020年6月17日,SexEnum
 * */
public enum SexEnum {
	
	MALE(1, "男"),
	FEMALE(2,"女");
	
	private Integer type;
	private String name;
	
	
	private SexEnum(Integer type, String name) {
		this.type = type;
		this.name = name;
	}


	public Integer getType() {
		return type;
	}


	public String getName() {
		return name;
	}


	public void setType(Integer type) {
		this.type = type;
	}


	public void setName(String name) {
		this.name = name;
	}
	
	
}

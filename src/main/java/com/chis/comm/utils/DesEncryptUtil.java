package com.chis.comm.utils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.security.SecureRandom;

/**
 * 该加密工具兼容PHP
 */
public class DesEncryptUtil {

    /**
     * 密钥算法
     */
    private static final String ALGORITHM = "DES";
    /**
     * 加解密算法/工作模式/填充方式
     */
    private static final String ALGORITHM_STR = "DES/ECB/NoPadding";

    public static final String CIPHER_ALGORITHM = "DES/ECB/PKCS5Padding";

    private static final String CHARSET = "UTF-8";
    private static final String key = "chiscdc_tjpay_@%^";


    /**
     * 填充内容
     */
    private static final String PAD_STR = "\0";

    public static void main(String[] args) throws Exception {
//		String clearText = "{  \n" +
//                "\t\"uuid\" : \"12\",\n" +
//                "\t\"rptCode\" : \"放卫20180008\",\n" +
//                "\t\"checkUnitCode\": \"\",\n" +
//                "\t\"checkDate\": \"2018-08-28\",\n" +
//                "\t\"sendCheckUnitCode\": \"91320300MA1ME4WP4B\",\n" +
//                "\t\"jcObject\": \"2\",\n" +
//                "\t\"subList\":[{\n" +
//                "\t\t\"jcType\": \"3\",\n" +
//                "\t\t\"sampCode\": \"00111\",\n" +
//                "\t\t\"idc\": \"421125198209295822\",\n" +
//                "\t\t\"recheck\": \"0\",\n" +
//                "\t\t\"itemList\":[{\n" +
//                "\t\t\t\"itemCode\": \"\",\n" +
//                "\t\t\t\"hgTag\": \"1\",\n" +
//                "\t\t\t\"measureVal\": \"\",\n" +
//                "\t\t\t\"measureUnit\": \"mSv\",\n" +
//                "\t\t\t\"missTag\": \"0\"\n" +
//                "\t\t}]\n" +
//                "\t}]\n" +
//                "}";


        String clearText = "[{\"uuid\":\"15\",\"rptCode\":\"放卫20180011\",\"checkUnitCode\":\"1001\",\"checkDate\":\"2018-08-30\",\"sendCheckUnitCode\":\"91441581MA51MLXN2Q\",\"jcObject\":2,\"subList\":[{\"jcType\":3,\"sampCode\":\"00111\",\"idc\":\"421125198209295822\",\"recheck\":0,\"checkSubDate\":\"2018-08-30\",\"adornDate\":\"2018-04-01\",\"itemList\":[{\"itemCode\":\"1118\",\"hgTag\":1,\"measureVal\":\"0.02\",\"measureUnit\":\"mSv\",\"missTag\":0}]},{\"jcType\":3,\"sampCode\":\"0113\",\"idc\":\"************019779\",\"recheck\":0,\"checkSubDate\":\"2018-08-30\",\"adornDate\":\"2018-04-01\",\"itemList\":[{\"itemCode\":\"1118\",\"hgTag\":1,\"measureVal\":\"0.02\",\"measureUnit\":\"mSv\",\"missTag\":0}]},{\"jcType\":3,\"sampCode\":\"0114\",\"idc\":\"************013131\",\"recheck\":0,\"checkSubDate\":\"2018-08-30\",\"adornDate\":\"2018-04-01\",\"itemList\":[{\"itemCode\":\"1118\",\"hgTag\":1,\"measureVal\":\"0.02\",\"measureUnit\":\"mSv\",\"missTag\":0}]},{\"jcType\":3,\"sampCode\":\"0115\",\"idc\":\"************010627\",\"recheck\":0,\"checkSubDate\":\"2018-08-30\",\"adornDate\":\"2018-04-01\",\"itemList\":[{\"itemCode\":\"1118\",\"hgTag\":1,\"measureVal\":\"0.02\",\"measureUnit\":\"mSv\",\"missTag\":0}]},{\"jcType\":3,\"sampCode\":\"1015\",\"idc\":\"************017546\",\"recheck\":0,\"checkSubDate\":\"2018-08-30\",\"adornDate\":\"2018-04-01\",\"itemList\":[{\"itemCode\":\"1118\",\"hgTag\":1,\"measureVal\":\"0.02\",\"measureUnit\":\"mSv\",\"missTag\":0}]}]}]";
//		String clearText = "1abc你";
//		String key = "chiscdc_fsws_@%^";
//		System.out.println("明文：" + clearText + "\n密钥：" + key);
//        String encryptText = encrypt(clearText);
//        System.out.println("加密后：" + encryptText);
//		String decryptText = decrypt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
//		System.out.println("解密后：" + decryptText);
//        String decryptText = decrypt("F0E54500B08DDE8E5949F02C54D01E2C1B69AF980EA64652ADD9828647E26CE4494AB01A9B221B2765AB8A6779BF1FACFBE2CD38CA303B4BFB4E328D76B24DCF1B3E363246016D149F74C59EAC87545712C7AD7F3B6B0577FFF6465D8694B1E60E63FB41689F61B8C91884E2E5D308BDE78E675C3E1022A9229CE7BBDB8F91160FD1CD7BE8A8F55A52D29AA96CE5ED734EDA681AF93C9143DBF26D938C49F0FE92424EA3B1DB1A46792859CBF2E8CB673A13D7D46BD478E66F6F814001963F29535002F0EE5CFC3551ED64E07B22C872232C7C70D63061D9679EFB787CCECFCA73DBEF88E978548E4662770AAA65DF7768A895E2C113E2E76CDCE41A7FF9D1F629C3B4172864B05D2ACFCAAAAC99FA1FE771604148285E3F2C3987A533F95BE6EEBDD213959E035E3D8D400781741CBEECDEC26F5766E5CB437DFF2CBC50178438B08E0315929BA34A2DDF5BFD3055CDF960DADC77F6CF7F182F453AC888327CB360BACA0FF307665E94EDABEF2279E5D752C78B5690C64EB7274709C03B9F2BDB47FADB65D6CEF0EEBB895B849A7415D810E6D8CDF2FD8F6D14D0ECBDD9198322DDB82849D1AC2EB3477D0F74C4AE1907C1BADAF1774DBA");
//        System.out.println("解密后：" + decryptText);
        
        String json = "{\"bhkCode\":\"123456\",\"empName\":\"张三\",\"amo\":\"1245.36\"}";
        System.out.println(encrypt(json));
//
//		System.out.println(decryptText.trim().equals("123456"));
    }

    public static String encrypt(String souce) {
        try {
            return encryptByDes(souce, pkcs5Pad(key));
        } catch (Exception e) {
        }

        return "";
    }

    /**
     * <p>方法描述：根据自己的密钥实现des加密</p>
     *
     * @MethodAuthor yzd, 2018/7/23,encryptByKey
     *
     */
    public static String encryptByKey(String souce,String key) {
        try {
            return encryptByDes(pkcs5Pad(souce), pkcs5Pad(key));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String decrypt(final String souce) {
        try {
            return decryptByDes(souce, pkcs5Pad(key)).trim();
        } catch (Exception e) {
        }

        return "";
    }

    /**
     * <p>方法描述：根据密钥解密</p>
     *
     * @MethodAuthor yzd, 2018/7/23,decryptByKey
     *
     */
    public static String decryptByKey(String souce,String key) throws Exception{
    	return decryptByDes(souce, pkcs5Pad(key)).trim();
    }

    private static String encryptByDes(final String souce, final String key)
            throws Exception {
        // DES算法要求有一个可信任的随机数源
        SecureRandom sr = new SecureRandom();
        // 从原始密匙数据创建DESKeySpec对象
        DESKeySpec dks = new DESKeySpec(key.getBytes(CHARSET));
        // 创建一个密匙工厂，然后用它把DESKeySpec转换成 一个SecretKey对象
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        SecretKey key1 = keyFactory.generateSecret(dks);
        // Cipher对象实际完成加密操作
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        // 用密匙初始化Cipher对象
        cipher.init(Cipher.ENCRYPT_MODE, key1, sr);
        // 现在，获取数据并加密
        byte encryptedData[] = cipher.doFinal(souce.getBytes(CHARSET));

        return byteArr2HexStr(encryptedData);
    }

    public static String byteArr2HexStr(byte[] arrB) throws Exception {
        int iLen = arrB.length;
        // 每个byte用两个字符才能表示，所以字符串的长度是数组长度的两倍
        StringBuffer sb = new StringBuffer(iLen * 2);
        for (int i = 0; i < iLen; i++) {
            int intTmp = arrB[i];
            // 把负数转换为正数
            while (intTmp < 0) {
                intTmp = intTmp + 256;
            }
            // 小于0F的数需要在前面补0
            if (intTmp < 16) {
                sb.append("0");
            }
            sb.append(Integer.toString(intTmp, 16));
        }
        return sb.toString();
    }

    private static String decryptByDes(final String souce, final String key)
            throws Exception {
        // DES算法要求有一个可信任的随机数源
        SecureRandom sr = new SecureRandom();
        // 从原始密匙数据创建DESKeySpec对象
        DESKeySpec dks = new DESKeySpec(key.getBytes(CHARSET));
        // 创建一个密匙工厂，然后用它把DESKeySpec转换成 一个SecretKey对象
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        SecretKey key1 = keyFactory.generateSecret(dks);
        // Cipher对象实际完成加密操作
        Cipher cipher = Cipher.getInstance(ALGORITHM_STR);
        // 用密匙初始化Cipher对象
        cipher.init(Cipher.DECRYPT_MODE, key1, sr);

        // 将加密报文用BASE64算法转化为字节数组
        byte[] encryptedData = hexStr2ByteArr(souce);
        // 用DES算法解密报文
        byte decryptedData[] = cipher.doFinal(encryptedData);
        return new String(decryptedData, CHARSET);
    }

    public static byte[] hexStr2ByteArr(String strIn) throws Exception {
        byte[] arrB = strIn.getBytes();
        int iLen = arrB.length;

        // 两个字符表示一个字节，所以字节数组长度是字符串长度除以2
        byte[] arrOut = new byte[iLen / 2];
        for (int i = 0; i < iLen; i = i + 2) {
            String strTmp = new String(arrB, i, 2);
            arrOut[i / 2] = (byte) Integer.parseInt(strTmp, 16);
        }
        return arrOut;
    }

    private static String pkcs5Pad(final String souce) {
        // 密文和密钥的长度必须是8的倍数
        if (0 == souce.length() % 8) {
            return souce;
        }

        StringBuffer tmp = new StringBuffer(souce);

        while (0 != tmp.length() % 8) {
            tmp.append(PAD_STR);
        }
        return tmp.toString();
    }
}

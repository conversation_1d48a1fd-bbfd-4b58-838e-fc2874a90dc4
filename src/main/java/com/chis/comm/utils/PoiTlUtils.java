package com.chis.comm.utils;


import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.policy.RenderPolicy;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;

/**
 * @Description: Word模板引擎 http://deepoove.com/poi-tl
 *               用于生成Word报表
 *
 * @MethodAuthor pw,2021年06月4日
 */
public class PoiTlUtils {
    /**
     * @Description: 通过模板文件流，表格模板名称列表，数据对象 生成报表
     * @param inputStream  模板文件流
     * @param outputStream 接收文件流
     * @param dataObj 数据对象
     * @param policyMap 映射名称与策略Map key映射名称 value策略
     * @MethodAuthor pw,2021年06月10日
     */
    public static <T> void generateReport(InputStream inputStream, OutputStream outputStream, T dataObj,
                                          Map<String, RenderPolicy> policyMap) throws Exception{
        ConfigureBuilder configureBuilder = Configure.builder();
        // 表格数据对象映射
        if(!CollectionUtils.isEmpty(policyMap)){
            for(Map.Entry<String, RenderPolicy> map : policyMap.entrySet()){
                String tableModuleName = map.getKey();
                RenderPolicy renderPolicy = map.getValue();
                if(StringUtils.isNotBlank(tableModuleName) && null != renderPolicy){
                    configureBuilder = configureBuilder.bind(tableModuleName,renderPolicy);
                }
            }
        }
        try{
            Configure configure = configureBuilder.build();
            XWPFTemplate template = XWPFTemplate.compile(inputStream, configure);
            template.render(dataObj,outputStream);
            template.close();
        }catch(Exception e){
            throw e;
        }finally {
            //关闭流
            if(null != inputStream){
                inputStream.close();
            }
            if(null != outputStream){
                outputStream.close();
            }
        }
    }
}

package com.chis.comm.utils;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Random;

public class AesEncryptUtils {
	private static final String KEY = "abcdef0123456789";
	private static final String ALGORITHMSTR = "AES/ECB/PKCS5Padding";

	public static String base64Encode(byte[] bytes) {
		return Base64.encodeBase64String(bytes);
	}

	public static byte[] base64Decode(String base64Code) throws Exception {
		return Base64.decodeBase64(base64Code);
	}

	public static byte[] aesEncryptToBytes(String content, String encryptKey) throws Exception {
		KeyGenerator kgen = KeyGenerator.getInstance("AES");
		kgen.init(128);
		Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
		cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), "AES"));
		return cipher.doFinal(content.getBytes("utf-8"));
	}

	public static String aesEncrypt(String content, String encryptKey) throws Exception {
		return base64Encode(aesEncryptToBytes(content, encryptKey));
	}

	public static String aesDecryptByBytes(byte[] encryptBytes, String decryptKey) throws Exception {
		KeyGenerator kgen = KeyGenerator.getInstance("AES");
		kgen.init(128);
		Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
		cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), "AES"));
		byte[] decryptBytes = cipher.doFinal(encryptBytes);
		return new String(decryptBytes,StandardCharsets.UTF_8);
	}

	public static String aesDecrypt(String encryptStr, String decryptKey) throws Exception {
		return aesDecryptByBytes(base64Decode(encryptStr), decryptKey);
	}

	/**
	 * <p>方法描述：加密 key是Hex格式的</p>
	 * @MethodAuthor： yzz
	 * @Date：2021-12-13
	 **/
	public static String aesEncryptWithHexKey(String content, String key) throws Exception {
		KeyGenerator kgen = KeyGenerator.getInstance("AES");
		kgen.init(128);
		//AES:加密算法  ECB：加密模式  PKCS5Padding：补码方式
		Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
		//私钥key是16进制加密处理过的，需要解密
		cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(Hex.decodeHex(key.toCharArray()), "AES"));
		byte[] decryptBytes = cipher.doFinal(content.getBytes("utf-8"));
		return Base64.encodeBase64String(decryptBytes);
	}


	/**
	 * <p>方法描述：解密 key是Hex格式的</p>
	 * @MethodAuthor： yzz
	 * @Date：2021-12-13
	 **/
	public static String aesDecryptWithHexKey(String content, String key) throws Exception {
		KeyGenerator kgen = KeyGenerator.getInstance("AES");
		kgen.init(128);
		//AES:加密算法  ECB：加密模式  PKCS5Padding：补码方式
		Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
		//私钥key是16进制加密处理过的，需要解密
		cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(Hex.decodeHex(key.toCharArray()), "AES"));
		byte[] decryptBytes = cipher.doFinal(Base64.decodeBase64(content));
		return new String(decryptBytes, StandardCharsets.UTF_8);
	}


	/**生成16位的密钥**/
	public static String KeyValue16(){
		//定义一个字符串（A-Z，a-z，0-9）即62位；
		String str="zxcvbnmlkjhgfdsaqwertyuiopQWERTYUIOPASDFGHJKLZXCVBNM1234567890";
		//由Random生成随机数
		Random random=new Random();
		StringBuffer sb=new StringBuffer();
		//长度为几就循环几次
		for(int i=0; i<16; ++i){
			//产生0-61的数字
			int number=random.nextInt(62);
			//将产生的数字通过length次承载到sb中
			sb.append(str.charAt(number));
		}
		//将承载的字符转换成字符串
		return sb.toString();
	}
	/**
	 * AES 加密操作
	 *
	 * @param content 待加密内容
	 * @param key     加密密钥
	 * @param vipara     偏移量
	 * @return 返回Base64转码后的加密数据
	 */
	public static String encrypt(String content, String key, String vipara) {
		//偏移量
		byte[] iv = vipara.getBytes();
		try {
			Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
			int blockSize = cipher.getBlockSize();
			byte[] dataBytes = content.getBytes();
			int length = dataBytes.length;
			//计算需填充长度
			if (length % blockSize != 0) {
				length = length + (blockSize - (length % blockSize));
			}
			byte[] plaintext = new byte[length];
			//填充
			System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);
			SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), "AES");
			//设置偏移量参数
			IvParameterSpec ivSpec = new IvParameterSpec(iv);
			cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
			byte[] encryped = cipher.doFinal(plaintext);

			return Base64.encodeBase64String(encryped);

		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * AES 解密操作
	 *
	 * @param content 待解密内容
	 * @param key     加密密钥
	 * @param vipara     偏移量
	 * @return
	 */
	public static String desEncrypt(String content, String key, String vipara) {

		byte[] iv = vipara.getBytes();

		try {
			byte[] encryp = Base64.decodeBase64(content);
			Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
			SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), "AES");
			IvParameterSpec ivSpec = new IvParameterSpec(iv);
			cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
			byte[] original = cipher.doFinal(encryp);
			return new String(original,StandardCharsets.UTF_8);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public static void main(String[] args) throws Exception {
//		String content = "\"'{\"loginName\":\"展示\",\"password\":\"123\"}'\"";
		/*String content = "13666036";
		System.out.println("加密前：" + content);

		String encrypt = aesEncrypt(content, "afmi5pjDeKpSUaLU");
		System.out.println(encrypt.length() + ":加密后：" + encrypt);

		String decrypt = aesDecrypt(encrypt, "afmi5pjDeKpSUaLU");
		System.out.println("解密后：" + decrypt);
		System.out.println(KeyValue16());*/

		String real="IsPpgx7R5NVyyNyJycouFvyo4xQvyxQQ024liCKN1KFoOaDb2GtvfSL1aoCQjm++1uAahC3NfE9/h7E1A2/JD8l1xfO3ULHbYLe5HaFWJuQ=";

		String key = "259781CBD15E9A47098A96C52DA3391C";
		String data="{'id':'d369c893-a2de-4e6d-8044-892ff9949c96'}";
		System.out.println("加密前：" + data);
		String result= aesEncryptWithHexKey(data,key);
		System.out.println("加密后：" + result);
		String result1= aesDecryptWithHexKey(result,key);
		System.out.println("解密后：" + result1);
		System.out.println("aa：" + Hex.encodeHexString("abcdef0123456789".getBytes()));
		System.out.println("bb：" + Arrays.toString(Hex.encodeHex("abcdef0123456789".getBytes())));
	}
	
}
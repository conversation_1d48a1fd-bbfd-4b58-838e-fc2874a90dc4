package com.chis.comm.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 地区工具类 ，地区编码12位<br/>
 * 
 * 去零后，地区编码长度 <br/>
 * 1国家：0 <br/>
 * 2省：2 <br/>
 * 3市：4 <br/>
 * 4县区：6 <br/>
 * 5乡镇、街道：9 <br/>
 * 6村、社区：12 <br/>
 * 
 * <AUTHOR>
 * @createTime 2015年10月21日
 */
public class ZoneUtil {
	private static final int LEN_OF_ZONE_CODE = 12;
	private static final String ZERO_12 = "000000000000";
	private static final String ZERO_10 = "0000000000";
	private static final String ZERO_8 = "00000000";
	private static final String ZERO_6 = "000000";
	private static final String ZERO_3 = "000";


	/**
	 * 根据地区编码，计算地区级别
	 * 
	 * @param zoneCode
	 *            地区编码
	 * @return 1-国家 2-省 3-市 4-县区 5-乡镇/街道 6-村/社区
	 */
	public static int getZoneType(String zoneCode) {
		if (StringUtils.isNotBlank(zoneCode) && zoneCode.length() == 12) {
			if (ZERO_12.equals(zoneCode)) {
				return 1;
			} else if (zoneCode.endsWith(ZERO_10)) {
				return 2;
			} else if (zoneCode.endsWith(ZERO_8)) {
				return 3;
			} else if (zoneCode.endsWith(ZERO_6)) {
				return 4;
			} else if (zoneCode.endsWith(ZERO_3)) {
				return 5;
			} else {
				return 6;
			}
		} else if (zoneCode.length() == 10) {
			if (ZERO_10.equals(zoneCode)) {
				return 1;
			} else if (zoneCode.endsWith(ZERO_8)) {
				return 2;
			} else if (zoneCode.endsWith(ZERO_6)) {
				return 3;
			} else if (zoneCode.endsWith("0000")) {
				return 4;
			} else if (zoneCode.endsWith("00")) {
				return 5;
			} else {
				return 6;
			}
		} else {
			throw new RuntimeException("地区编码传入错误！");
		}
	}

	/**
	 * 根据地区编码和地区级别获取上级地区编码<br/>
	 * 
	 * @param zoneCode
	 *            地区编码<br/>
	 * @param zoneType
	 *            地区编码对应的地区级别<br/>
	 * @return 上级地区编码，如果当前编码是国家，返回NULL<br/>
	 */
	@Deprecated
	public static String getParentCode(String zoneCode, int zoneType) {
		if (StringUtils.isNotBlank(zoneCode)) {
			if (zoneCode.length() == 12) {
				if (zoneType == 2) {
					return ZERO_12;
				} else if (zoneType == 3) {
					return StringUtils.rightPad(zoneCode.substring(0, 2),
							LEN_OF_ZONE_CODE, '0');
				} else if (zoneType == 4) {
					return StringUtils.rightPad(zoneCode.substring(0, 4),
							LEN_OF_ZONE_CODE, '0');
				} else if (zoneType == 5) {
					return StringUtils.rightPad(zoneCode.substring(0, 6),
							LEN_OF_ZONE_CODE, '0');
				} else if (zoneType == 6) {
					return StringUtils.rightPad(zoneCode.substring(0, 9),
							LEN_OF_ZONE_CODE, '0');
				}
			} else if (zoneCode.length() == 10) {
				if (zoneType == 2) {
					return ZERO_10;
				} else if (zoneType == 3) {
					return StringUtils.rightPad(zoneCode.substring(0, 2), 10,
							'0');
				} else if (zoneType == 4) {
					return StringUtils.rightPad(zoneCode.substring(0, 4), 10,
							'0');
				} else if (zoneType == 5) {
					return StringUtils.rightPad(zoneCode.substring(0, 6), 10,
							'0');
				} else if (zoneType == 6) {
					return StringUtils.rightPad(zoneCode.substring(0, 8), 10,
							'0');
				}
			}
		}
		return null;
	}

	/**
	 * 根据地区编码和地区级别获取上级地区编码
	 * 
	 * @param zoneCode
	 *            地区编码
	 * @return 上级地区编码
	 */
	public static String getParentCode(String zoneCode) {
		int zoneType = getZoneType(zoneCode);
		return getParentCode(zoneCode, zoneType);
	}

	/**
	 * 根据地区编码和地区级别获取上级地区编码<br/>
	 * 
	 * @param zoneCode
	 *            地区编码<br/>
	 * @param zoneType
	 *            地区编码对应的地区级别<br/>
	 * @return 上级地区编码，如果当前编码是国家，返回NULL<br/>
	 */
	@Deprecated
	public static String getParentCode(String zoneCode, String zoneType) {
		int zoneTypeInt = Integer.parseInt(zoneType);
		return getParentCode(zoneCode, zoneTypeInt);
	}

	/**
	 * 根据地区编码，去掉末尾的0
	 * 地区编码非10位或12位不处理
	 * @param zoneCode
	 *            地区编码 32050801 
	 * @return 去除0的地区编码
	 */
	public static String zoneSelect(String zoneCode) {
		int epos = 0;
		int zoneLength = zoneCode.length();
		if(zoneLength != 10 && zoneLength != 12)
			return zoneCode;
		// 正规表达式
		String regpattern = "[0]*+";
		Pattern pattern = Pattern.compile(regpattern, Pattern.CASE_INSENSITIVE);

		// 去掉结尾的指定字符
		StringBuilder buffer = new StringBuilder(zoneCode).reverse();
		Matcher matcher = pattern.matcher(buffer);
		if (matcher.lookingAt()) {
			epos = matcher.end();
			zoneCode = new StringBuilder(buffer.substring(epos)).reverse().toString();
		}
		//截取后编码长度
		int len = zoneCode.length();
		if (zoneLength == 10) {
			// 判断长度是奇偶数，奇数要补个零d
			if (len % 2 == 1) {
				zoneCode = StringUtils.rightPad(zoneCode, len + 1, "0");
			}
		} else if (zoneLength == 12) {
			if (len <= 6) {
				if (len % 2 == 1) {
					zoneCode = StringUtils.rightPad(zoneCode, len + 1, "0");
				}
			} else {
				int leftLen = len % 3;
				if (leftLen % 3 != 0) {
					zoneCode = StringUtils.rightPad(zoneCode, len + (3-leftLen), "0");
				}
			}
		}
		return zoneCode;
	}

	public static String getZoneCodeLevel(String zoneCode) {
		return getZoneType(zoneCode)+"";
	}
}
package com.chis.comm.utils.sm;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Hex;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.IOException;
import java.security.Key;
import java.security.Security;

/***
 * <p>类描述: SM4BC模式 </p>
 *
 * @ClassAuthor mxp , 2018/11/2 , SM4BcUtil
 */
public class SM4BcUtil {
    String keyHex = "";

    private Cipher in, out;

    private Key key;

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    public SM4BcUtil(String keyHex) throws Exception {
        if (keyHex.length() > 32) {
            this.keyHex = keyHex.substring(0, 32);
        } else {
            this.keyHex = keyHex;
        }
        key = new SecretKeySpec(Hex.decode(this.keyHex), "SM4");
        out = Cipher.getInstance("SM4/ECB/NoPadding", "BC");
        in = Cipher.getInstance("SM4/ECB/NoPadding", "BC");
        out.init(Cipher.ENCRYPT_MODE, key);
        in.init(Cipher.DECRYPT_MODE, key);
    }

    public byte[] encrypt(byte[] plaintextBs) {
        CipherOutputStream cOut = null;
        ByteArrayOutputStream bOut = new ByteArrayOutputStream();
        cOut = new CipherOutputStream(bOut, out);
        byte[] bs = null;

        int len = plaintextBs.length;
        int mod = len % 16;
        int appendLen = 16 - mod;
        byte[] tbs = null;
        if (appendLen != 0 && appendLen != 16) {
            tbs = new byte[plaintextBs.length + appendLen];
            System.arraycopy(plaintextBs, 0, tbs, 0, plaintextBs.length);
            for (int i = 0; i < appendLen; i++) {
                tbs[plaintextBs.length + i] = (byte) 0x00;
            }
        } else {
            tbs = new byte[plaintextBs.length];
            System.arraycopy(plaintextBs, 0, tbs, 0, plaintextBs.length);
        }
        try {
            for (int i = 0; i != tbs.length / 2; i++) {
                cOut.write(tbs[i]);
            }
            cOut.write(tbs, tbs.length / 2, tbs.length - tbs.length / 2);
            bs = bOut.toByteArray();
        } catch (IOException e) {

        } finally {
            try {
                cOut.close();
            } catch (IOException e) {
            }
            try {
                bOut.close();
            } catch (IOException e) {
            }
        }
        return bs;
    }

    public byte[] decrypt(byte[] encryptBs) {
        ByteArrayInputStream bIn = new ByteArrayInputStream(encryptBs);
        CipherInputStream cIn = new CipherInputStream(bIn, in);
        byte[] tbs = null;
        DataInputStream dIn = new DataInputStream(cIn);
        try {
            tbs = new byte[encryptBs.length];
            int c = 0;
            int i = 0;
            while ((c = cIn.read()) != -1) {
                tbs[i] = (byte) c;
                i++;
            }
        } catch (Exception e) {
            System.out.println("SM4 failed encryption - " + e.toString());
        } finally {
            try {
                dIn.close();
            } catch (IOException e) {
            }
            try {
                cIn.close();
            } catch (IOException e) {
            }

            try {
                bIn.close();
            } catch (IOException e) {
            }
        }
        return tbs;
    }
}

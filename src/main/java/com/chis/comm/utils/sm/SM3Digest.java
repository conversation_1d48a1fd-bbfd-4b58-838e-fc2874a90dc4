package com.chis.comm.utils.sm;

import org.bouncycastle.util.encoders.Hex;

/***
 * <p>类描述: SM3 </p>
 *
 * @ClassAuthor mxp , 2018/11/2 , SM3Digest
 */
public class SM3Digest {
    /**
     * SM3值的长度
     */
    private static final int BYTE_LENGTH = 32;

    /**
     * SM3分组长度
     */
    private static final int BLOCK_LENGTH = 64;

    /**
     * 缓冲区长度
     */
    private static final int BUFFER_LENGTH = BLOCK_LENGTH * 1;

    /**
     * 缓冲区
     */
    private byte[] xBuf = new byte[BUFFER_LENGTH];

    /**
     * 缓冲区偏移量
     */
    private int xBufOff;

    /**
     * 初始向量
     */
    private byte[] V = SM3.iv.clone();

    private int cntBlock = 0;

    public SM3Digest() {
    }

    public SM3Digest(SM3Digest t) {
        System.arraycopy(t.xBuf, 0, this.xBuf, 0, t.xBuf.length);
        this.xBufOff = t.xBufOff;
        System.arraycopy(t.V, 0, this.V, 0, t.V.length);
    }

    /**
     * SM3结果输出
     *
     * @param out    保存SM3结构的缓冲区
     * @param outOff 缓冲区偏移量
     * @return
     */
    public int doFinal(byte[] out, int outOff) {
        byte[] tmp = doFinal();
        System.arraycopy(tmp, 0, out, 0, tmp.length);
        return BYTE_LENGTH;
    }

    public void reset() {
        xBufOff = 0;
        cntBlock = 0;
        V = SM3.iv.clone();
    }

    /**
     * 明文输入
     *
     * @param in    明文输入缓冲区
     * @param inOff 缓冲区偏移量
     * @param len   明文长度
     */
    public void update(byte[] in, int inOff, int len) {
        int partLen = BUFFER_LENGTH - xBufOff;
        int inputLen = len;
        int dPos = inOff;
        if (partLen < inputLen) {
            System.arraycopy(in, dPos, xBuf, xBufOff, partLen);
            inputLen -= partLen;
            dPos += partLen;
            doUpdate();
            while (inputLen > BUFFER_LENGTH) {
                System.arraycopy(in, dPos, xBuf, 0, BUFFER_LENGTH);
                inputLen -= BUFFER_LENGTH;
                dPos += BUFFER_LENGTH;
                doUpdate();
            }
        }

        System.arraycopy(in, dPos, xBuf, xBufOff, inputLen);
        xBufOff += inputLen;
    }

    private void doUpdate() {
        byte[] B = new byte[BLOCK_LENGTH];
        for (int i = 0; i < BUFFER_LENGTH; i += BLOCK_LENGTH) {
            System.arraycopy(xBuf, i, B, 0, B.length);
            doHash(B);
        }
        xBufOff = 0;
    }

    private void doHash(byte[] B) {
        byte[] tmp = SM3.CF(V, B);
        System.arraycopy(tmp, 0, V, 0, V.length);
        cntBlock++;
    }

    private byte[] doFinal() {
        byte[] B = new byte[BLOCK_LENGTH];
        byte[] buffer = new byte[xBufOff];
        System.arraycopy(xBuf, 0, buffer, 0, buffer.length);
        byte[] tmp = SM3.padding(buffer, cntBlock);
        for (int i = 0; i < tmp.length; i += BLOCK_LENGTH) {
            System.arraycopy(tmp, i, B, 0, B.length);
            doHash(B);
        }
        return V;
    }

    public void update(byte in) {
        byte[] buffer = new byte[]{in};
        update(buffer, 0, 1);
    }

    public int getDigestSize() {
        return BYTE_LENGTH;
    }

    public static void main1(String[] args) {
		/*String appId = "cc7d55cea21646a88f2fa6ba950a66d2";
		System.out.println("appId : " + appId);
		String orgkey = "b48f46c491c1369fe2a0ebb2e97dd8797ef833e";
		System.out.println("orgkey : " + orgkey);
		String txt = appId + orgkey;*/
		/*byte[] md = new byte[32];
		byte[] msg1 = "1234567".getBytes();
		System.out.println("content" + SMStringUtil.byteArray2hex(msg1));
		SM3Digest sm3 = new SM3Digest();
		sm3.update(msg1, 0, msg1.length);
		sm3.doFinal(md, 0);
		System.out.println(md);
		String s = new String(Hex.encode(md));
		System.out.println(s.toUpperCase());
		System.out.println(VasUtil.encrypt4SM4("0123456789ABCDEFFEDCBA9876543210", "123456".getBytes()));
		System.out.println(VasUtil.decrypt4SM4("0123456789ABCDEFFEDCBA9876543210", SMStringUtil.hex2byteArray418("8F1836E60B0AD2A64802BAAA0214F82D2677F46B09C122CC975533105BD4A22A")));
		byte[] a = "123456".getBytes();
		for(int i=0;i<a.length ;i++ )
		    System.out.print(a[i]+"\t");*/
        byte[] md = new byte[32];
        byte[] msg1 = "biz_content={\"Data\":\"0D56D5AAA4A1BE44047C57940FF66107B2CCA97880B2DC54E5A1940B8C4002A0%3a0%3a9581F644F012E9B5915E686D57653CE6\",\"Time\":\"201809051532757\",\"TerminalID\":\"1000000000000072\",\"InstitutionID\":\"10000142\"}&digest_type=SM3&enc_type=SM4&method=ehc.ehealthcode.verify&term_id=1000000000000072&timestamp=1536132477208&version=X.M.0.1&key=90C9D5307ABD4CFF869F26A71F2D42F2".getBytes();
        System.out.println("content ==== hex" + SMStringUtil.byteArray2hex(msg1));
        SM3Digest sm3 = new SM3Digest();
        sm3.update(msg1, 0, msg1.length);
        sm3.doFinal(md, 0);
        System.out.println(SMStringUtil.byteArray2hex(md).toUpperCase());

    }

    public static void main(String[] args) {
        String jsonStr = "app_id=83A6D3CB90E64E98B45FDDD767A5F0AF&biz_content={\"birthday\":\"20180303\",\"AppVersion\":\"1.0\",\"address\":\"%E8%B4%B5%E5%B7%9E%E7%9C%81%E6%B5%8B%E8%AF%95A\",\"gender\":\"1\",\"CertID\":\"134213175547446544\",\"cellphone\":\"13232323232\",\"CertType\":\"01\",\"imei\":\"0000\",\"Time\":\"20180710093231\",\"AppUserID\":\"1531215151446\",\"Name\":\"%E6%B5%8B%E8%AF%95A\",\"AppPackageName\":\"com.hengbao.vasdemo\"}&digest_type=SM3&enc_type=SM4&method=ehc.ehealthcode.GzRegisterReqPO&term_id=0000&timestamp=1531215151446&version=X.M.0.1&key=03B68A739213EF9C3DB689C068B0E22E776826E0DCB1CDA1382D37B53F2DE1C2";
        SM3Digest sm3 = new SM3Digest();
        byte[] msg1 = jsonStr.getBytes();
        sm3.update(msg1, 0, msg1.length);
        byte[] md = new byte[32];
        sm3.doFinal(md, 0);
        System.out.println(md);
        String s = new String(Hex.encode(md));
        //DED0490893D8D33F4924D70E226E29D2A89534944460B0024A410D79B78BF1D7
        System.out.println(s.toUpperCase());
        //AA82A3295E79AC786811B58D04D8A4EA283690B5D64310809E11F5410811820C
        //AA82A3295E79AC786811B58D04D8A4EA283690B5D64310809E11F5410811820C
    }
}

package com.chis.comm.utils;

import cn.hutool.core.convert.Convert;
import com.chis.comm.pojo.ExportDynamicColPO;
import com.chis.comm.pojo.ExportDynamicRowPO;
import com.chis.comm.pojo.ExportDynamicSheetPO;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Description: Excel 导出工具类
 * 参考ExcelExportUtil 兼容合并行合并列以及多sheet
 * @ClassAuthor pw, 2022年04月12日, DynamicExcelExportUtil
 */
public class DynamicExcelExportUtil {
    /**
     * 是否生成xls版的Workbook 默认生成xlsx版的Workbook
     */
    private boolean ifHssfWorkBook = Boolean.FALSE;
    /**
     * sheet数据列表
     */
    private List<ExportDynamicSheetPO> sheetList;
    /**
     * Workbook对象
     */
    private Workbook wBook;
    /**
     * 数据达到flushRowNum倍数则刷新缓存
     */
    private final int flushRowNum = 1000;

    /**
     * @Description: 生成Workbook对象
     * @MethodAuthor pw, 2022年04月12日
     */
    public Workbook generateWorkbook() {
        if (CollectionUtils.isEmpty(this.sheetList)) {
            return null;
        }
        try {
            if (null == this.wBook) {
                this.wBook = ifHssfWorkBook ? new HSSFWorkbook() : new SXSSFWorkbook(this.flushRowNum);
            }
            for (ExportDynamicSheetPO sheetPO : this.sheetList) {
                //标题行
                List<ExportDynamicRowPO> headRowList = sheetPO.getHeadRowList();
                //数据行
                List<ExportDynamicRowPO> dataRowList = sheetPO.getDataRowList();
                //无数据不处理
                if (CollectionUtils.isEmpty(headRowList) && CollectionUtils.isEmpty(dataRowList)) {
                    continue;
                }
                Sheet sheet = this.wBook.createSheet(null == sheetPO.getSheetName() ? "" : sheetPO.getSheetName());
                headRowList = this.fillRowSpanColumns(headRowList);
                if (sheetPO.isIfFreezePane() && !CollectionUtils.isEmpty(headRowList)) {
                    //固定标题行
                    sheet.createFreezePane(0, headRowList.size());
                }
                if (!sheetPO.isIfDataRowSingle()) {
                    dataRowList = this.fillRowSpanColumns(dataRowList);
                }
                this.defaultCellStyle(headRowList, true, sheetPO.isIfCellHasBorder());
                this.defaultCellStyle(dataRowList, false, sheetPO.isIfCellHasBorder());
                int startRow = 0;
                this.createRow(sheet, headRowList, startRow, true);
                startRow += CollectionUtils.isEmpty(headRowList) ? 0 : headRowList.size();
                this.createRow(sheet, dataRowList, startRow, false);
            }
            return this.wBook;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * <p>方法描述：将数据行填充到对应sheet中 </p>
     * 注意 该方法用于向已经存在的Sheet中加入新的数据行
     * 如果已经有标题行或者已经有数据行 那么startRow可以传递null
     * 如果sheet不需要标题行 那么第一次添加数据行的时候startRow要赋值0 因sheet.getLastRowNum()在无数据时 也返回0
     * 不考虑未传递CellStyle的情况 数据量大 如果创建大量CellStyle 会导致内存溢出
     *
     * @param sheet
     * @param startRow
     * @param ifCellHasBorder
     * @param dataRowList
     * @param ifSingle        是否都是普通单行数据没有合并列合并行的情况
     * @MethodAuthor： pw 2022/8/8
     **/
    public void appendSheetDataByDataRow(Sheet sheet, Integer startRow, boolean ifCellHasBorder,
                                         List<ExportDynamicRowPO> dataRowList, boolean ifSingle) {
        if (CollectionUtils.isEmpty(dataRowList)) {
            return;
        }
        if (null == startRow) {
            startRow = sheet.getLastRowNum() + 1;
        }
        try {
            if (!ifSingle) {
                dataRowList = this.fillRowSpanColumns(dataRowList);
            }
            this.createRow(sheet, dataRowList, startRow, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @Description: 创建Workbook对象 若标题需要自定义设置字体 那么需要用到Workbook对象
     * @MethodAuthor pw, 2022年04月12日
     */
    public void createWorkbook() {
        this.wBook = ifHssfWorkBook ? new HSSFWorkbook() : new SXSSFWorkbook(this.flushRowNum);
    }

    /**
     * @Description: 单元格CellStyle 默认值设置
     * 注意 如果大量创建CellStyle 会出现内存溢出的情况
     * @MethodAuthor pw, 2022年04月12日
     */
    private void defaultCellStyle(List<ExportDynamicRowPO> rowList, boolean ifHeadCell, boolean ifCellHasBorder) {
        if (CollectionUtils.isEmpty(rowList)) {
            return;
        }

        for (int i = 0; i < rowList.size(); i++) {
            ExportDynamicRowPO rowPO = rowList.get(i);
            List<ExportDynamicColPO> cols = rowPO.getCols();
            if (CollectionUtils.isEmpty(cols)) {
                continue;
            }
            //默认标题字体大小11
            Short fontHeightInPoints = (short) 11;
            for (ExportDynamicColPO colPO : cols) {
                if (colPO == null) {
                    colPO = new ExportDynamicColPO();
                }
                CellStyle colStyle = colPO.getColStyle();
                if (null == colStyle) {
                    colStyle = this.customDataLeftStyle(HorizontalAlignment.CENTER, VerticalAlignment.CENTER, false,
                            (ifHeadCell ? fontHeightInPoints : (short) 11), ifHeadCell);
                }
                Boolean ifHasBorder = colPO.getIfHasBorder();
                //有边框线
                if (ifCellHasBorder && (null == ifHasBorder || ifHasBorder)) {
                    this.customBorderSizeStyle(colStyle);
                }
                colPO.setColStyle(colStyle);
            }
        }
    }

    /**
     * 自定义单元格内容样式
     *
     * @param align              单元格样式-水平
     * @param vertical           单元格样式-垂直
     * @param wrapText           单元格样式-自动换行
     * @param fontHeightInPoints 单元格样式-字体大小
     * @param boldWeightBold     单元格样式-字体是否加粗
     * @return {@link CellStyle 单元格样式}
     */
    public CellStyle customDataLeftStyle(HorizontalAlignment align, VerticalAlignment vertical, boolean wrapText, short fontHeightInPoints, boolean boldWeightBold) {
        CellStyle style = this.wBook.createCellStyle();
        style.setAlignment(align);
        style.setVerticalAlignment(vertical);
        if (wrapText) {
            style.setWrapText(true);
        }
        Font font = this.wBook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints(fontHeightInPoints);
        font.setBold(boldWeightBold);
        style.setFont(font);
        return style;
    }

    /**
     * 自定义单元格边框线大小样式
     */
    private CellStyle customBorderSizeStyle(CellStyle style) {
        if (null == style) {
            return null;
        }
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        return style;
    }

    /**
     * @Description: 创建行数据
     * @MethodAuthor pw, 2022年04月12日
     */
    private void createRow(Sheet sheet, List<ExportDynamicRowPO> rowList, int startRow, boolean ifHeadRow) throws IOException {
        if (CollectionUtils.isEmpty(rowList)) {
            return;
        }
        int rowSize = rowList.size();
        for (int i = 0; i < rowSize; i++) {
            if (i == 0 && startRow == 0 && !ifHeadRow) {
                ifHeadRow = true;
            }
            int rowIndex = i + startRow;
            Row row = sheet.createRow(rowIndex);
            ExportDynamicRowPO rowPO = rowList.get(i);
            if (!rowPO.getIfAutoRowHeight()) {
                row.setHeightInPoints(rowPO.getHeightInPoints());
            }
            List<ExportDynamicColPO> colPOList = rowPO.getCols();
            int colSize = null == colPOList ? 0 : colPOList.size();
            for (int j = 0; j < colSize; j++) {
                ExportDynamicColPO colPO = colPOList.get(j);
                if (colPO == null) {
                    continue;
                }
                this.createCell(sheet, row, colPO, j, ifHeadRow);
                Integer rowspan = Convert.toInt(colPO.getRowspan(), 1);
                Integer colspan = Convert.toInt(colPO.getColspan(), 1);
                //合并行
                if (rowspan > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + rowspan - 1, j, j));
                }
                //合并列
                if (colspan > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, j, j + colspan - 1));
                }
            }
            //如果是循环添加数据 不要正好在最后一行刷新缓存 否则sheet.getLastRowNum()不准确
            boolean ifFlushCache = sheet instanceof SXSSFSheet &&
                    0 != rowIndex &&
                    ((startRow + rowSize - 1) % this.flushRowNum == 0 ?
                            rowIndex % (this.flushRowNum - 1) == 0 :
                            rowIndex % this.flushRowNum == 0);
            if (ifFlushCache) {
                //刷新缓存
                ((SXSSFSheet) sheet).flushRows();
            }
        }
    }

    /**
     * @Description: 创建单元格
     * @MethodAuthor pw, 2022年04月12日
     */
    private void createCell(Sheet sheet, Row row, ExportDynamicColPO colPO, int columnIndex, boolean ifHeadRow) {
        Cell cell = row.createCell(columnIndex);
        if (colPO == null) {
            return;
        }
        cell.setCellStyle(colPO.getColStyle());
        this.fillCellVal(colPO.getColVal(), cell);
        //仅需要考虑最后一列标题列的宽度即可（无标题时，取第一行数据的宽度）
        if (ifHeadRow && null != colPO.getColWidth()) {
            sheet.setColumnWidth(columnIndex, colPO.getColWidth());
        }
    }

    /**
     * @Description: 填充单元格数值
     * @MethodAuthor pw, 2022年04月12日
     */
    private void fillCellVal(Object obj, Cell cell) {
        if (null == obj) {
            cell.setCellValue("");
            return;
        }
        String result;
        if (obj instanceof BigDecimal) {
            result = ((BigDecimal) obj).toPlainString();
        } else if (obj instanceof Date) {
            cell.setCellValue((Date) obj);
            return;
        } else {
            result = obj.toString();
        }
        cell.setCellValue(result);
    }

    /**
     * @Description: rowspan ExportDynamicColPO
     * 合并单元格的时候 rowspan大于1的时候 让rowspan的行都有对应的单元格
     * 保证每一行列数相同 方便合并 第一行第一列rowspan为2 那么第二行第一列rowspan为1
     * @MethodAuthor pw, 2022年04月12日
     */
    private List<ExportDynamicRowPO> fillRowSpanColumns(List<ExportDynamicRowPO> rowList) throws Exception {
        rowList = this.flatRowColumns(rowList);
        if (CollectionUtils.isEmpty(rowList)) {
            return rowList;
        }
        List<ExportDynamicRowPO> resultRowList = new ArrayList<>();
        //缓存上一行 rowspan>1的 key 下标 value 对应的单元格对象
        Map<Integer, ExportDynamicColPO> map = new HashMap<>();
        int resultSize = rowList.size();
        int size = this.extraSize(rowList);
        //需要将行数置为1
        Map<Integer,List<Integer>> rowspanMap = new HashMap<>();
        for (int i = 0; i < resultSize; i++) {
            List<Integer> rowspanList = new ArrayList<>();
            ExportDynamicRowPO baseRow = rowList.get(i);
            ExportDynamicRowPO transferRow = this.fillDynamicRowPO(map, baseRow, size,rowspanList);
            if(!CollectionUtils.isEmpty(rowspanList)){
                rowspanMap.put(i,rowspanList);
            }
            if (null == transferRow) {
                continue;
            }
            map = new HashMap<>();
            this.fillRowspanMap(map, transferRow);
            resultRowList.add(transferRow);
        }
        this.dealPowspanMap(rowspanMap,resultRowList);
        return resultRowList;
    }

    /**
     * <p>方法描述：获取最大单元格列数 </p>
     * pw 2025/1/14
     **/
    private Integer extraSize(List<ExportDynamicRowPO> rowList) {
        int size = 0;
        if (CollectionUtils.isEmpty(rowList)) {
            return size;
        }
        for (ExportDynamicRowPO rowPO : rowList) {
            int innerSize = rowPO.getCols().size();
            if (innerSize > size) {
                size = innerSize;
            }
        }
        return size;
    }

    /**
     *  <p>方法描述：将行数置为1</p>
     * @MethodAuthor hsj 2024-12-10 14:55
     */
    private void dealPowspanMap(Map<Integer, List<Integer>> rowspanMap, List<ExportDynamicRowPO> resultRowList) {
        if(rowspanMap.isEmpty() || resultRowList.isEmpty()){
            return;
        }
        for (Map.Entry<Integer, List<Integer>> entry : rowspanMap.entrySet()) {
            int rowIndex = entry.getKey();
            ExportDynamicRowPO baseRow = resultRowList.get(rowIndex);
            List<ExportDynamicColPO> cols = baseRow.getCols();
            List<Integer> rowspanList = entry.getValue();
            for (Integer rowspan : rowspanList) {
                cols.get(rowspan).setRowspan(1);
            }
        }
    }

    /**
     * @Description: 填充上一行数据对应的map
     * map 缓存上一行 rowspan>1的 key 下标 value 对应的单元格对象
     * @MethodAuthor pw, 2022年04月12日
     */
    private void fillRowspanMap(Map<Integer, ExportDynamicColPO> map, ExportDynamicRowPO transferRow) throws Exception {
        if (null == transferRow || CollectionUtils.isEmpty(transferRow.getCols())) {
            return;
        }
        List<ExportDynamicColPO> tmpList = transferRow.getCols();
        int size = tmpList.size();
        for (int i = 0; i < size; i++) {
            ExportDynamicColPO colEntity = tmpList.get(i);
            ExportDynamicColPO curColEntity = new ExportDynamicColPO();
            if (colEntity != null) {
                ObjectCopyUtil.copyProperties(colEntity, curColEntity);
            }
            curColEntity.setRowspan(Convert.toInt(curColEntity.getRowspan(), 1));
            if (curColEntity.getRowspan() > 1) {
                curColEntity.setRowspan(curColEntity.getRowspan() - 1);
                curColEntity.setColVal("");
                map.put(i, curColEntity);
            }
        }
    }

    /**
     * @Description: 处理rowspan的单元格
     * @MethodAuthor pw, 2022年04月12日
     */
    private ExportDynamicRowPO fillDynamicRowPO(Map<Integer, ExportDynamicColPO> map, ExportDynamicRowPO baseRow,
                                                int size,List<Integer> rowspanList) {
        ExportDynamicRowPO rowResult = new ExportDynamicRowPO();
        List<ExportDynamicColPO> cols = baseRow.getCols();
        if (CollectionUtils.isEmpty(cols)) {
            return null;
        }
        Queue<ExportDynamicColPO> queue = new LinkedList<>(cols);
        rowResult.setCols(new ArrayList<ExportDynamicColPO>());
        if (!baseRow.getIfAutoRowHeight()) {
            rowResult.setHeightInPoints(baseRow.getHeightInPoints());
        }
        rowResult.setIfAutoRowHeight(baseRow.getIfAutoRowHeight());
        for (int i = 0; i < size; i++) {
            ExportDynamicColPO colPO = map.get(i);
            if (null == colPO) {
                colPO = queue.poll();
            }else{
                if (colPO.getRowspan() > 1) {
                    rowspanList.add(i);
                }
            }
            rowResult.getCols().add(colPO);
        }
        return rowResult;
    }

    /**
     * @Description: 平铺colspan ExportDynamicColPO
     * 合并单元格时候 让每一行的列数都相等
     * @MethodAuthor pw, 2022年04月12日
     */
    private List<ExportDynamicRowPO> flatRowColumns(List<ExportDynamicRowPO> rowList) throws Exception {
        if (CollectionUtils.isEmpty(rowList)) {
            return rowList;
        }
        List<ExportDynamicRowPO> resultRowList = new ArrayList<>();
        for (ExportDynamicRowPO rowPO : rowList) {
            ExportDynamicRowPO tmpRowPO = new ExportDynamicRowPO();
            List<ExportDynamicColPO> colPOList = rowPO.getCols();
            if (CollectionUtils.isEmpty(colPOList)) {
                continue;
            }
            tmpRowPO.setCols(new ArrayList<ExportDynamicColPO>());
            tmpRowPO.setIfAutoRowHeight(rowPO.getIfAutoRowHeight());
            if (!rowPO.getIfAutoRowHeight()) {
                tmpRowPO.setHeightInPoints(rowPO.getHeightInPoints());
            }
            for (ExportDynamicColPO colPO : colPOList) {
                ExportDynamicColPO copyCol = new ExportDynamicColPO();
                ObjectCopyUtil.copyProperties(colPO, copyCol);
                int colspan = Convert.toInt(copyCol.getColspan(), 1);
                tmpRowPO.getCols().add(copyCol);
                while (colspan-- > 1) {
                    ExportDynamicColPO innerCopyCol = new ExportDynamicColPO();
                    ObjectCopyUtil.copyProperties(copyCol, innerCopyCol);
                    //注意 这里要设置成1
                    innerCopyCol.setColspan(1);
                    innerCopyCol.setColVal("");
                    tmpRowPO.getCols().add(innerCopyCol);
                }
            }
            resultRowList.add(tmpRowPO);
        }
        return resultRowList;
    }

    public boolean isIfHssfWorkBook() {
        return ifHssfWorkBook;
    }

    public void setIfHssfWorkBook(boolean ifHssfWorkBook) {
        this.ifHssfWorkBook = ifHssfWorkBook;
    }

    public List<ExportDynamicSheetPO> getSheetList() {
        return sheetList;
    }

    public void setSheetList(List<ExportDynamicSheetPO> sheetList) {
        this.sheetList = sheetList;
    }

    public Workbook getwBook() {
        return wBook;
    }

    public void setwBook(Workbook wBook) {
        this.wBook = wBook;
    }
}

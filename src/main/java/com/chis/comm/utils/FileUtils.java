package com.chis.comm.utils;

import java.io.*;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.CompressionLevel;
import net.lingala.zip4j.model.enums.EncryptionMethod;
import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.springframework.util.ResourceUtils;

import lombok.extern.slf4j.Slf4j;
import sun.misc.BASE64Decoder;

/**
 * 文件操作工具类 实现文件的创建、删除、复制、压缩、解压以及目录的创建、删除、复制、压缩解压等功能<br/>
 * 
 * <AUTHOR>
 * @createTime 2015年11月5日
 */
@Slf4j
public class FileUtils extends org.apache.commons.io.FileUtils {

	private static int FILE_UPLOAD_SINGLE_BYTE = 4096;
	private static final int  BUFFER_SIZE = 2 * 1024;

	public static final int cache = 10 * 1024;
	

	/**
	 * 创建单个文件
	 * 
	 * @param descFileName
	 *            文件名，包含路径
	 * @return 如果创建成功，则返回true，否则返回false
	 */
	public static boolean createFile(String descFileName) {
		File file = new File(descFileName);
		if (file.exists()) {
			log.debug("文件 " + descFileName + " 已存在!");
			return false;
		}
		if (descFileName.endsWith(File.separator)) {
			log.debug(descFileName + " 为目录，不能创建目录!");
			return false;
		}
		if (!file.getParentFile().exists()) {
			// 如果文件所在的目录不存在，则创建目录
			if (!file.getParentFile().mkdirs()) {
				log.debug("创建文件所在的目录失败!");
				return false;
			}
		}

		// 创建文件
		try {
			if (file.createNewFile()) {
				log.debug(descFileName + " 文件创建成功!");
				return true;
			} else {
				log.debug(descFileName + " 文件创建失败!");
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.debug(descFileName + " 文件创建失败!");
			return false;
		}

	}

	/**
	 * 创建目录
	 * 
	 * @param descDirName
	 *            目录名,包含路径
	 * @return 如果创建成功，则返回true，否则返回false
	 */
	public static boolean createDirectory(String descDirName) {
		String descDirNames = descDirName;
		if (!descDirNames.endsWith(File.separator)) {
			descDirNames = descDirNames + File.separator;
		}
		File descDir = new File(descDirNames);
		if (descDir.exists()) {
			log.debug("目录 " + descDirNames + " 已存在!");
			return false;
		}
		// 创建目录
		if (descDir.mkdirs()) {
			log.debug("目录 " + descDirNames + " 创建成功!");
			return true;
		} else {
			log.debug("目录 " + descDirNames + " 创建失败!");
			return false;
		}

	}

	/**
	 * 写入文件
	 * @param fileName 生成的文件路径 父级文件夹不存在 会自动生成
	 * @param content 文件内容
	 * @param append 如果文件已经存在 true则追加文件内容 false则覆写文件内容
	 */
	public static void writeToFile(String fileName, String content, boolean append) {
		try {
			org.apache.commons.io.FileUtils.write(new File(fileName), content, "utf-8", append);
			log.info("文件 " + fileName + " 写入成功!");
		} catch (IOException e) {
			log.debug("文件 " + fileName + " 写入失败! " + e.getMessage());
		}
	}

	/**
	 * 写入文件
	 * 
	 * @param file
	 *            要写入的文件
	 */
	public static void writeToFile(String fileName, String content, String encoding, boolean append) {
		try {
			org.apache.commons.io.FileUtils.write(new File(fileName), content, encoding, append);
			log.debug("文件 " + fileName + " 写入成功!");
		} catch (IOException e) {
			log.debug("文件 " + fileName + " 写入失败! " + e.getMessage());
		}
	}


	/**
	 * 修复路径，将 \\ 或 / 等替换为 File.separator
	 * 
	 * @param path
	 * @return
	 */
	public static String path(String path) {
		String p = StringUtils.replace(path, "\\", "/");
		p = StringUtils.join(StringUtils.split(p, "/"), "/");
		if (!StringUtils.startsWithAny(p, "/") && StringUtils.startsWithAny(path, "\\", "/")) {
			p += "/";
		}
		if (!StringUtils.endsWithAny(p, "/") && StringUtils.endsWithAny(path, "\\", "/")) {
			p = p + "/";
		}
		return p;
	}

	/**
	 * Enhancement of java.io.File#createNewFile() Create the given file. If the
	 * parent directory don't exists, we will create them all.
	 * 
	 * @param file
	 *            the file to be created
	 * @return true if the named file does not exist and was successfully
	 *         created; false if the named file already exists
	 * @see java.io.File#createNewFile
	 * @throws IOException
	 */
	public static boolean createFile(File file) throws IOException {
		if (!file.exists()) {
			makeDir(file.getParentFile());
		}
		return file.createNewFile();
	}

	/**
	 * Enhancement of java.io.File#mkdir() Create the given directory . If the
	 * parent folders don't exists, we will create them all.
	 * 
	 * @see java.io.File#mkdir()
	 * @param dir
	 *            the directory to be created
	 */
	public static void makeDir(File dir) {
		if (!dir.getParentFile().exists()) {
			makeDir(dir.getParentFile());
		}
		dir.mkdir();
	}

	/**
	 * 文件转化为字节数组
	 * 
	 * @param file
	 *            文件
	 * @return 字节数组
	 */
	public static byte[] convertFile2Bytes(File file) {
		byte[] ret = null;
		try {
			if (file == null) {
				return null;
			}
			FileInputStream in = new FileInputStream(file);
			ByteArrayOutputStream out = new ByteArrayOutputStream(4096);
			byte[] b = new byte[4096];
			int n;
			while ((n = in.read(b)) != -1) {
				out.write(b, 0, n);
			}
			in.close();
			out.close();
			ret = out.toByteArray();
		} catch (IOException e) {
			log.error(e.getMessage(),e);
		}
		return ret;
	}
	
	/**
	 * 根据相对路径读取文件 <br/>
	 * @param relativePath spring环境中classpath下的相对路径，不要以/开头 <br/>
	 * @return
	 */
	public static String getFileContentRelativePath(String relativePath) {
		if (StringUtils.isNotBlank(relativePath)) {
			try {
				//地址根目录
				File file = ResourceUtils.getFile("classpath:" + relativePath);
				InputStream is = new FileInputStream(file);
				return getFileContentStream(is);
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			} 
		}
		return null;
	}
	
	/**
	 * 直接根据路径读取文件
	 * @param absolutePaht
	 * @return
	 */
	public static String getFileContentAbsPath(String absolutePaht) {
		if (StringUtils.isNotBlank(absolutePaht)) {
			try {
				//地址根目录
				File file = new File(absolutePaht);
				InputStream is = new FileInputStream(file);
				return getFileContentStream(is);
			} catch (Exception e) {
				log.error(e.getMessage(),e);
				return null;
			} 
		}
		return null;
	}
	
	/**
	 * 根据输入流读取文件内容
	 * @param is
	 * @return
	 */
	public static String getFileContentStream(InputStream is) {
		BufferedReader br = null;
		try {
			//地址根目录
			br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			StringBuilder sb = new StringBuilder();
			while (true) {
				String line = br.readLine();
				if (line == null) {
					break;
				}
				sb.append(line).append("\r\n");
			}
			return sb.toString();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		} finally {
			try {
				if (is != null) {
					is.close();
				}
				if (br != null) {
					br.close();
				}
			} catch (IOException e1) {
				e1.printStackTrace();
			}
		}
	}

	/**
	 * 从网页地址下载文件，返回是否下载成功
	 * 
	 * @param fileUrl 
	 * @param filePath
	 * @return
	 */
	public static boolean downLoadUrlFile(String fileUrl, String filePath) {
		try {
			String ml = filePath.substring(0, filePath.lastIndexOf("/"));
			File file = new File(ml);
			if (!file.exists()) {
				file.mkdirs();
			}
			FileUtils.copyURLToFile(new URL(fileUrl), new File(filePath));
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	/**
	 * 根据url下载文件，保存到filepath中,
	 *文件格式是通过解析响应头中的Content-Disposition来获取
	 * @param url 文件的url
	 * @param diskUrl 本地存储路径
	 * @return
	 */
	public static String downloadByOuterUrl(String url, String diskUrl) {
		String filepath = "";
		String filename = "";
		try {
			HttpClient client = HttpClients.createDefault();
			HttpGet httpget = new HttpGet(url);
			// 加入Referer,防止防盗链
			httpget.setHeader("Referer", url);
			HttpResponse response = client.execute(httpget);
			HttpEntity entity = response.getEntity();
			InputStream is = entity.getContent();
			if (StringUtils.isBlank(filepath)){
				Map<String,String> map =  getFilePath(response, url,diskUrl);
				filepath = map.get("filepath");
				filename = map.get("filename");
			}
			File file = new File(filepath);
			file.getParentFile().mkdirs();
			FileOutputStream fileout = new FileOutputStream(file);
			byte[] buffer = new byte[cache];
			int ch = 0;
			while ((ch = is.read(buffer)) != -1) {
				fileout.write(buffer, 0, ch);
			}
			is.close();
			fileout.flush();
			fileout.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return filename;
	}

	/**
	 * 获取response要下载的文件的默认路径
	 *
	 * @param response
	 * @return
	 */
	public static Map<String,String> getFilePath(HttpResponse response, String url, String diskUrl) {
		Map<String,String> map = new HashMap<>();
		String filepath = diskUrl;
		String suffix = getSuffix(response, url);
		if(StringUtils.isBlank(suffix)){
			suffix="pdf";
		}
		String filename=StringUtils.uuid()+"."+suffix;
		if (StringUtils.isNotBlank(filename)) {
			filepath += filename;
		}
		map.put("filename", filename);
		map.put("filepath", filepath);
		return map;
	}

	/**
	 * 获取response header中Content-Disposition中的filename值
	 * @param response
	 * @param url
	 * @return
	 */
	public static String getSuffix(HttpResponse response, String url) {
		Header contentHeader = response.getFirstHeader("Content-Disposition");
		String suffix = null;
		if (contentHeader != null) {
			// 如果contentHeader存在
			HeaderElement[] values = contentHeader.getElements();
			if (values.length == 1) {
				NameValuePair param = values[0].getParameterByName("filename");
				if (param != null) {
					try {
						String filename = param.getValue();
						if(StringUtils.isNotBlank(filename) && filename.contains(".")){
							suffix = filename.split("\\.")[1];
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}
		return suffix;
	}


	public static void zipFile(File inputFile, java.util.zip.ZipOutputStream ouputStream) {
		try {
			if(inputFile.exists()) {
				/**如果是目录的话这里是不采取操作的，
				 * 至于目录的打包正在研究中*/
				if (inputFile.isFile()) {
					FileInputStream IN = new FileInputStream(inputFile);
					BufferedInputStream bins = new BufferedInputStream(IN, 512);
					//org.apache.tools.zip.ZipEntry
					java.util.zip.ZipEntry entry = new java.util.zip.ZipEntry(inputFile.getName());
					ouputStream.putNextEntry(entry);
					// 向压缩文件中输出数据
					int nNumber;
					byte[] buffer = new byte[512];
					while ((nNumber = bins.read(buffer)) != -1) {
						ouputStream.write(buffer, 0, nNumber);
					}
					// 关闭创建的流对象
					bins.close();
					IN.close();
				} else {
					try {
						File[] files = inputFile.listFiles();
						for (int i = 0; i < files.length; i++) {
							zipFile(files[i], ouputStream);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	

	/**
	 * 上传文件，将文件写入指定虚拟路径目录下
	 * @param realFilePath
	 * @param in
	 */
	public static void copyFile(String realFilePath, InputStream in) {
		if(StringUtils.isNotBlank(realFilePath))	{
			//父级目录。首先判断父级目录是否存在，如不存在，则需要新建文件夹
			String parentFilePath = realFilePath.substring(0, realFilePath.lastIndexOf("/"));
			File tempFile = new File(parentFilePath);
			if (!tempFile.exists()) {
				tempFile.mkdirs();
			}
			File outFile = new File(realFilePath);
			FileOutputStream out = null;
			try {
				out = new FileOutputStream(outFile);
				byte[] buffer = new byte[FILE_UPLOAD_SINGLE_BYTE];
				int i = 0;
				while ((i = in.read(buffer)) != -1) {
					out.write(buffer, 0, i);
				}
				out.flush();// 将缓存中的数据写入文件
			} catch (Exception e) {
				e.printStackTrace();
			} finally {// 关闭输入输出流
				try {
					in.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
				try {
					out.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}
	
	/**
	 * Base64字符串保存成图片，格式为Jpg
	 * @param imgStr 图片的二进制字节码转成的Base64字符串
	 * @param imgFilePath 保存到的完整的图片路径
	 */
    public static void base64ToImage(String imgStr, String imgFilePath) {// 对字节数组字符串进行Base64解码并生成图片
        OutputStream out = null;
        BASE64Decoder decoder = new BASE64Decoder();
        try {
            if (StringUtils.isNotBlank(imgStr)) {
                // Base64解码
                byte[] bytes = decoder.decodeBuffer(imgStr);
                for (int i = 0; i < bytes.length; ++i) {
                    if (bytes[i] < 0) {// 调整异常数据
                        bytes[i] += 256;
                    }
                }
                // 生成jpeg图片
                out = new FileOutputStream(imgFilePath);
                out.write(bytes);
                out.flush();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return;
        } finally {
            try {
                if (null != out) {
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * @Description: 文件以及文件夹压缩
	 * 参考 https://www.cnblogs.com/zeng1994/p/7862288.html
     * @param srcDir 输入文件路径
	 * @param out 输出流
	 * @param KeepDirStructure 是否保留原目录结构 true 保留，如果不保留原目录结构 压缩文件里不会包含文件夹
     * @MethodAuthor pw,2022年12月31日
     */
	public static void toZip(String srcDir, OutputStream out, boolean KeepDirStructure)
			throws RuntimeException{
		ZipOutputStream zos = null ;
		try {
			zos = new ZipOutputStream(out);
			File sourceFile = new File(srcDir);
			generateZipFile(sourceFile,zos,sourceFile.getName(),KeepDirStructure);
		} catch (Exception e) {
			throw new RuntimeException("压缩文件失败toZip11111",e);
		}finally{
			if(zos != null){
				try {
					zos.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * <p>方法描述：文件组压缩 </p>
	 * pw 2025/7/10
	 **/
	public static void toZip(List<File> srcFiles, OutputStream out)throws RuntimeException {
		ZipOutputStream zos = null ;
		try {
			zos = new ZipOutputStream(out);
			for (File srcFile : srcFiles) {
				byte[] buf = new byte[BUFFER_SIZE];
				zos.putNextEntry(new ZipEntry(srcFile.getName()));
				int len;
				FileInputStream in = new FileInputStream(srcFile);
				while ((len = in.read(buf)) != -1){
					zos.write(buf, 0, len);
				}
				zos.closeEntry();
				in.close();
			}
		} catch (Exception e) {
			throw new RuntimeException("压缩文件失败toZip22222",e);
		}finally{
			if(zos != null){
				try {
					zos.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * <p>方法描述：文件组压缩，自定义文件名 </p>
	 * pw 2025/7/10
	 **/
	public static void toZip(Map<String, File> fileMap, OutputStream out)throws RuntimeException {
		ZipOutputStream zos = null ;
		try {
			zos = new ZipOutputStream(out);
			for (Map.Entry<String, File> fileEntity : fileMap.entrySet()) {
				byte[] buf = new byte[BUFFER_SIZE];
				zos.putNextEntry(new ZipEntry(fileEntity.getKey()));
				int len;
				FileInputStream in = new FileInputStream(fileEntity.getValue());
				while ((len = in.read(buf)) != -1){
					zos.write(buf, 0, len);
				}
				zos.closeEntry();
				in.close();
			}
		} catch (Exception e) {
			throw new RuntimeException("压缩文件失败toZip33333",e);
		}finally{
			if(zos != null){
				try {
					zos.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * @Description: 文件以及文件夹压缩
	 *
	 * @MethodAuthor pw,2022年12月31日
	 */
	private static void generateZipFile(File sourceFile, ZipOutputStream zos, String name,
								 boolean KeepDirStructure) throws Exception{
		byte[] buf = new byte[BUFFER_SIZE];
		if(sourceFile.isFile()){
			// 向zip输出流中添加一个zip实体，构造器中name为zip实体的文件的名字
			zos.putNextEntry(new ZipEntry(name));
			// copy文件到zip输出流中
			int len;
			FileInputStream in = new FileInputStream(sourceFile);
			while ((len = in.read(buf)) != -1){
				zos.write(buf, 0, len);
			}
			// Complete the entry
			zos.closeEntry();
			in.close();
		} else {
			File[] listFiles = sourceFile.listFiles();
			if(listFiles == null || listFiles.length == 0){
				// 需要保留原来的文件结构时,需要对空文件夹进行处理
				if(KeepDirStructure){
					// 空文件夹的处理
					zos.putNextEntry(new ZipEntry(name + "/"));
					// 没有文件，不需要文件的copy
					zos.closeEntry();
				}
			}else {
				for (File file : listFiles) {
					// 判断是否需要保留原来的文件结构
					if (KeepDirStructure) {
						// 注意：file.getName()前面需要带上父文件夹的名字加一斜杠,
						// 不然最后压缩包中就不能保留原来的文件结构,即：所有文件都跑到压缩包根目录下了
						generateZipFile(file, zos, name + File.separator + file.getName(),KeepDirStructure);
					} else {
						generateZipFile(file, zos, file.getName(),KeepDirStructure);
					}
				}
			}
		}
	}

	/**
	 * <p>方法描述： 将文件转成字节数组返回 </p>
	 * pw 2023/8/21
	 **/
	public static byte[] getFileByte(File file) throws Exception {
		byte[] bytes = null;
		if (file != null) {
			InputStream is = new FileInputStream(file);
			int length = (int) file.length();
			if (length > Integer.MAX_VALUE) {
				return null;
			}
			bytes = new byte[length];
			int offset = 0;
			int numRead = 0;
			while (offset < bytes.length && (numRead = is.read(bytes, offset, bytes.length - offset)) >= 0) {
				offset += numRead;
			}
			if (offset < bytes.length) {
				return null;
			}
			is.close();
		}
		return bytes;
	}

	/**
	 * <p>方法描述： 将文件转成字节数组返回 </p>
	 * pw 2023/8/21
	 **/
	public static byte[] getFileByte(String filePath) throws Exception{
		File file = StringUtils.isBlank(filePath) ? null : new File(filePath);
		if(null == file || !file.exists()){
			return null;
		}
		return getFileByte(file);
	}

	/**
	 * <p>方法描述：以utf8读取文件 </p>
	 * pw 2023/8/21
	 **/
	public static String readUtf8String(String filePath) {
		try(BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(filePath),"utf-8"))){
			StringBuilder sb = new StringBuilder();
			while (true) {
				String line = br.readLine();
				if (line == null) {
					break;
				}
				sb.append(line).append("\r\n");
			}
			return sb.toString();
		}catch(Exception e){
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * <p>方法描述：压缩加密码 </p>
	 * pw 2023/8/21
	 **/
	public static void zipFileWithPassword(String sourceFilePath, String outFilePath, String password) throws Exception {
		File source = new File(sourceFilePath);
		if(!source.exists()){
			return;
		}
		ZipParameters zipParameters = new ZipParameters();
		zipParameters.setEncryptFiles(StringUtils.isNotBlank(password));
		zipParameters.setCompressionLevel(CompressionLevel.HIGHER);
		zipParameters.setEncryptionMethod(EncryptionMethod.AES);

		ZipFile zipFile = StringUtils.isBlank(password) ? new ZipFile(outFilePath) :
				new ZipFile(outFilePath, password.toCharArray());
		if(source.isFile()){
			zipFile.addFile(source, zipParameters);
		}else{
			zipFile.addFolder(source, zipParameters);
		}
		zipFile.close();
	}
}
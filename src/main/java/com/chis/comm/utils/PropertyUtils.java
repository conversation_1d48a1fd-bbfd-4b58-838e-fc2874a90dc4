package com.chis.comm.utils;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;

import java.io.UnsupportedEncodingException;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * 属性工具类
 * <AUTHOR>
 */
public class PropertyUtils extends PropertyPlaceholderConfigurer {


	private static Map<String, String> propertyMap;

	@Override
	protected void processProperties(
			ConfigurableListableBeanFactory beanFactoryToProcess,
			Properties props) throws BeansException {
		super.processProperties(beanFactoryToProcess, props);
		propertyMap = new HashMap<String, String>();
		for (Object key : props.keySet()) {
			String keyStr = key.toString();
			String value = props.getProperty(keyStr);
            try {
                value = new String(value.getBytes("iso-8859-1"),"utf-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            propertyMap.put(keyStr, value);
		}
	}

	public static String getValue(String name) throws RuntimeException {
		String value = propertyMap.get(name);
		if (StringUtils.isBlank(value)) {
			String error = "属性[" + name + "]的值为空";
			throw new RuntimeException(error);
		} else {
			return value;
		}
	}
	public static String getValueWithoutException(String name){
		return  propertyMap.get(name);
	}

	public static List<String> getValuesByStartWith(String name){
		List<String> values = Lists.newArrayList();
		for(String key:propertyMap.keySet()){
			if(!key.startsWith(name))
				continue;
			values.add(propertyMap.get(key));
		}
		if ( values.size()==0) {
			String error = "以[" + name + "]开头的值为空";
//			throw new RuntimeException(error);
		}
		return values;
	}

	public static String resolveMsg(String name, Object... params) {
		try {
			return MessageFormat.format(getValue(name), params);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

}

package com.chis.comm.utils;

import cn.hutool.core.util.IdcardUtil;
import com.deepoove.poi.data.ParagraphRenderData;
import com.deepoove.poi.data.Texts;
import com.google.common.collect.Lists;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.util.CollectionUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtils extends org.apache.commons.lang3.StringUtils {
	private static final char SEPARATOR = '_';
	private static final String CHARSET_NAME = "UTF-8";
	public static String[] Chinese = new String[] { "零", "一", "二", "三", "四",
			"五", "六", "七", "八", "九" };
	/**固定的验证正则表达式*/
	public static final String PHONE = "^\\s*$|^0(([1,2]\\d)|([3-9]\\d{2}))[-]?\\d{7,8}$";
    /**手机号的验证正则表达式*/
    public static final String MOBILE_REGEX = "^\\s*$|^[1][3-9]\\d{9}";
    /**邮箱的验证正则表达式*/
    public static final String  email= "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";
 	/**身份证的验证正则表达式*/
    public static final String IDC_REGEX = "^(\\d{6})(18|19|20)?(\\d{2})([01]\\d)([0123]\\d)(\\d{3})(\\d|X|x)?$";
    /**各种固定电话验证*/
    public static final String PhoneChat = "^(0(([1,2]\\d)|([3-9]\\d{2}))[-]?)?\\d{7,8}$";
	/**社会信用代码*/
    public static final String  CreditCode="^([0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\\d{14})$";
	/**邮编的验证正则表达式*/
	public static final String  post= "^[0-9]{6}$";

	/**字符串去除空格/换行/回车/制表符/换页符*/
	public static final String regx = "\\s*|\t|\r|\n|\u00a0";

	/** 港澳通行证 */
	public static final String HM_CARDREG = "^[HM]{1}([0-9]{8})$";
	/** 护照  */
	public static final String PASS_PORTREG = "^([A-Z]|[0-9]){5,17}$";
	/** 军官证 */
	public static final String MILITARYREG = "^[\\u4E00-\\u9FA5](字第)([0-9A-Z]{4,8})(号?)$";
	/** 户口本 */
	public static final String HOUSEHOLDREG = "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X)$)";
	/** 台胞证 */
	public static final String TAIWAN_ENTRYREG = "(^\\d{8}$)|(^[a-zA-Z0-9]{10}$)|(^\\d{18}$)|(^[A-Z]\\d{6}\\([DBA]\\)$)|(^\\d{10}\\(B\\)$)";

	/**
	 * 转换为字节数组
	 * 
	 * @param str
	 * @return
	 */
	public static byte[] getBytes(String str) {
		if (str != null) {
			try {
				return str.getBytes(CHARSET_NAME);
			} catch (UnsupportedEncodingException e) {
				return null;
			}
		} else {
			return null;
		}
	}

	/**
	 * 按字符编码转换为字节数组
	 * 
	 * @param str
	 * @return
	 */
	public static byte[] getBytes(String str, String encoding) {
		if (str != null) {
			try {
				return str.getBytes(encoding);
			} catch (UnsupportedEncodingException e) {
				return null;
			}
		} else {
			return null;
		}
	}

	/**
	 * 获取字符串的字节数，默认"UTF-8"编码，汉字3个字节
	 * 
	 * @param str
	 */
	public static int getBytesLength(String str) {
		if (StringUtils.isBlank(str)) {
			return 0;
		} else {
			return getBytes(str, CHARSET_NAME).length;
		}
	}

	/**
	 * 按字符编码获取字符串的字节数
	 * 
	 * @param str
	 */
	public static int getBytesLength(String str, String encoding) {
		if (StringUtils.isBlank(str)) {
			return 0;
		} else {
			return getBytes(str, encoding).length;
		}
	}

	/**
	 * 转换为字节数组
	 * 
	 * @param str
	 * @return
	 */
	public static String toString(byte[] bytes) {
		try {
			return new String(bytes, CHARSET_NAME);
		} catch (UnsupportedEncodingException e) {
			return EMPTY;
		}
	}

	/**
	 * 是否包含字符串
	 * 
	 * @param str
	 *            验证字符串
	 * @param strs
	 *            字符串组
	 * @return 包含返回true
	 */
	public static boolean inString(String str, String... strs) {
		if (str != null) {
			for (String s : strs) {
				if (str.equals(trim(s))) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 替换掉HTML标签方法
	 */
	public static String replaceHtml(String html) {
		if (isBlank(html)) {
			return "";
		}
		String regEx = "<.+?>";
		Pattern p = Pattern.compile(regEx);
		Matcher m = p.matcher(html);
		String s = m.replaceAll("");
		return s;
	}

	/**
	 * 替换为手机识别的HTML，去掉样式及属性，保留回车。
	 * 
	 * @param html
	 * @return
	 */
	public static String replaceMobileHtml(String html) {
		if (html == null) {
			return "";
		}
		return html.replaceAll("<([a-z]+?)\\s+?.*?>", "<$1>");
	}

	/**
	 * 替换为手机识别的HTML，去掉样式及属性，保留回车。
	 * 
	 * @param txt
	 * @return
	 */
	public static String toHtml(String txt) {
		if (txt == null) {
			return "";
		}
		return replace(replace(Encodes.escapeHtml(txt), "\n", "<br/>"), "\t",
				"&nbsp; &nbsp; ");
	}

	/**
	 * 缩略字符串（不区分中英文字符）
	 * 
	 * @param str
	 *            目标字符串
	 * @param length
	 *            截取长度
	 * @return
	 */
	public static String abbr(String str, int length) {
		if (str == null) {
			return "";
		}
		try {
			StringBuilder sb = new StringBuilder();
			int currentLength = 0;
			for (char c : replaceHtml(StringEscapeUtils.unescapeHtml4(str))
					.toCharArray()) {
				currentLength += String.valueOf(c).getBytes("GBK").length;
				if (currentLength <= length - 3) {
					sb.append(c);
				} else {
					sb.append("...");
					break;
				}
			}
			return sb.toString();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return "";
	}

	public static String abbr2(String param, int length) {
		if (param == null) {
			return "";
		}
		StringBuffer result = new StringBuffer();
		int n = 0;
		char temp;
		boolean isCode = false; // 是不是HTML代码
		boolean isHTML = false; // 是不是HTML特殊字符,如&nbsp;
		for (int i = 0; i < param.length(); i++) {
			temp = param.charAt(i);
			if (temp == '<') {
				isCode = true;
			} else if (temp == '&') {
				isHTML = true;
			} else if (temp == '>' && isCode) {
				n = n - 1;
				isCode = false;
			} else if (temp == ';' && isHTML) {
				isHTML = false;
			}
			try {
				if (!isCode && !isHTML) {
					n += String.valueOf(temp).getBytes("GBK").length;
				}
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}

			if (n <= length - 3) {
				result.append(temp);
			} else {
				result.append("...");
				break;
			}
		}
		// 取出截取字符串中的HTML标记
		String temp_result = result.toString().replaceAll("(>)[^<>]*(<?)",
				"$1$2");
		// 去掉不需要结素标记的HTML标记
		temp_result = temp_result
				.replaceAll(
						"</?(AREA|BASE|BASEFONT|BODY|BR|COL|COLGROUP|DD|DT|FRAME|HEAD|HR|HTML|IMG|INPUT|ISINDEX|LI|LINK|META|OPTION|P|PARAM|TBODY|TD|TFOOT|TH|THEAD|TR|area|base|basefont|body|br|col|colgroup|dd|dt|frame|head|hr|html|img|input|isindex|li|link|meta|option|p|param|tbody|td|tfoot|th|thead|tr)[^<>]*/?>",
						"");
		// 去掉成对的HTML标记
		temp_result = temp_result.replaceAll("<([a-zA-Z]+)[^<>]*>(.*?)</\\1>",
				"$2");
		// 用正则表达式取出标记
		Pattern p = Pattern.compile("<([a-zA-Z]+)[^<>]*>");
		Matcher m = p.matcher(temp_result);
		List<String> endHTML = Lists.newArrayList();
		while (m.find()) {
			endHTML.add(m.group(1));
		}
		// 补全不成对的HTML标记
		for (int i = endHTML.size() - 1; i >= 0; i--) {
			result.append("</");
			result.append(endHTML.get(i));
			result.append(">");
		}
		return result.toString();
	}

	/**
	 * 转换为Double类型
	 */
	public static Double toDouble(Object val) {
		if (val == null) {
			return 0D;
		}
		try {
			return Double.valueOf(trim(val.toString()));
		} catch (Exception e) {
			return 0D;
		}
	}

	/**
	 * 转换为Float类型
	 */
	public static Float toFloat(Object val) {
		return toDouble(val).floatValue();
	}

	/**
	 * 转换为Long类型
	 */
	public static Long toLong(Object val) {
		return toDouble(val).longValue();
	}

	/**
	 * 转换为Integer类型
	 */
	public static Integer toInteger(Object val) {
		return toLong(val).intValue();
	}

	/**
	 * 驼峰命名法工具
	 * 
	 * @return toCamelCase("hello_world") == "helloWorld"
	 *         toCapitalizeCamelCase("hello_world") == "HelloWorld"
	 *         toUnderScoreCase("helloWorld") = "hello_world"
	 */
	public static String toCamelCase(String s) {
		if (s == null) {
			return null;
		}

		s = s.toLowerCase();

		StringBuilder sb = new StringBuilder(s.length());
		boolean upperCase = false;
		for (int i = 0; i < s.length(); i++) {
			char c = s.charAt(i);

			if (c == SEPARATOR) {
				upperCase = true;
			} else if (upperCase) {
				sb.append(Character.toUpperCase(c));
				upperCase = false;
			} else {
				sb.append(c);
			}
		}

		return sb.toString();
	}

	/**
	 * 驼峰命名法工具
	 * 
	 * @return toCamelCase("hello_world") == "helloWorld"
	 *         toCapitalizeCamelCase("hello_world") == "HelloWorld"
	 *         toUnderScoreCase("helloWorld") = "hello_world"
	 */
	public static String toCapitalizeCamelCase(String s) {
		if (s == null) {
			return null;
		}
		s = toCamelCase(s);
		return s.substring(0, 1).toUpperCase() + s.substring(1);
	}

	/**
	 * 驼峰命名法工具
	 * 
	 * @return toCamelCase("hello_world") == "helloWorld"
	 *         toCapitalizeCamelCase("hello_world") == "HelloWorld"
	 *         toUnderScoreCase("helloWorld") = "hello_world"
	 */
	public static String toUnderScoreCase(String s) {
		if (s == null) {
			return null;
		}

		StringBuilder sb = new StringBuilder();
		boolean upperCase = false;
		for (int i = 0; i < s.length(); i++) {
			char c = s.charAt(i);

			boolean nextUpperCase = true;

			if (i < (s.length() - 1)) {
				nextUpperCase = Character.isUpperCase(s.charAt(i + 1));
			}

			if ((i > 0) && Character.isUpperCase(c)) {
				if (!upperCase || !nextUpperCase) {
					sb.append(SEPARATOR);
				}
				upperCase = true;
			} else {
				upperCase = false;
			}

			sb.append(Character.toLowerCase(c));
		}

		return sb.toString();
	}

	/**
	 * 如果不为空，则设置值
	 * 
	 * @param target
	 * @param source
	 */
	public static void setValueIfNotBlank(String target, String source) {
		if (isNotBlank(source)) {
			target = source;
		}
	}

	/**
	 * 转换为JS获取对象值，生成三目运算返回结果
	 * 
	 * @param objectString
	 *            对象串 例如：row.user.id
	 *            返回：!row?'':!row.user?'':!row.user.id?'':row.user.id
	 */
	public static String jsGetVal(String objectString) {
		StringBuilder result = new StringBuilder();
		StringBuilder val = new StringBuilder();
		String[] vals = split(objectString, ".");
		for (int i = 0; i < vals.length; i++) {
			val.append("." + vals[i]);
			result.append("!" + (val.substring(1)) + "?'':");
		}
		result.append(val.substring(1));
		return result.toString();
	}

	/**
	 * String根据分隔符转List
	 * 
	 * @param value
	 *            String字符串
	 * @param splitStr
	 *            分隔符
	 * @return List
	 */
	public static List<String> string2list(String value, String splitStr) {
		List<String> list = Lists.newArrayList();
		if (StringUtils.isNotBlank(value)) {
			String[] arr = value.split(splitStr);
			for (String s : arr) {
				list.add(s);
			}
		}
		return list;
	}

	/**
	 * List转String
	 * 
	 * @param list
	 *            List
	 * @param splitStr
	 *            分隔符
	 * @return String
	 */
	public static String list2string(List list, String splitStr) {
		if (null != list && list.size() > 0) {
			StringBuilder sb = new StringBuilder();
			for (Object s : list) {
				sb.append(splitStr).append(s);
			}
			return sb.toString().replaceFirst(splitStr, "");
		} else {
			return null;
		}
	}

	/**
	 * 字符串如果是NULL，返回空串，否则去空格返回
	 * 
	 * @param str
	 *            字符串
	 * @return 字符串
	 */
	public static String convertNull2Empty(String str) {
		if (null == str) {
			return "";
		} else {
			return str.trim();
		}
	}

	/**
	 * 对象的toString
	 * 
	 * @param object
	 *            对象为null返回空串，否则toString
	 * @return 对象的toString
	 */
	public static String objectToString(Object object) {
		return null == object ? "" : object.toString();
	}

	/**
	 * 解决页面模糊查询时，输入% 或 ％查询数据有误问题
	 * 
	 * @param str
	 *            页面传入的值
	 * @return
	 */
	public static String convertBFH(String str) {
		if (str.indexOf("%") != -1 || str.indexOf("％") != -1) {
			str = str.replaceAll("%", "\\\\%").replaceAll("％", "\\\\％");
		}
		return str;
	}

	/**
	 * base64解码
	 * 
	 * @param data
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("restriction")
	public static String decode(String data) {
		if (isBlank(data)) {
			return "";
		}
		try {
			return toString(new BASE64Decoder().decodeBuffer(data));
		} catch (IOException e) {
			return "";
		}
	}

	/**
	 * base64编码
	 * 
	 * @param data
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("restriction")
	public static String encode(byte[] data) {
		if (data == null) {
			return "";
		}
		return (new BASE64Encoder()).encodeBuffer(data);
	}

	/**
	 * base64编码
	 * 
	 * @param data
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("restriction")
	public static String encode(String source) {
		if (isBlank(source)) {
			return "";
		}
		return (new BASE64Encoder()).encodeBuffer(getBytes(source));
	}

	/**
	 * 解析url中param的值
	 * 
	 * @param url
	 *            url地址
	 * @param param
	 *            参数名
	 * @return 返回url中param的值，如果没有返回NULL
	 */
	public static String parseUrl(String url, String param) {
		if (StringUtils.isNotBlank(url) && StringUtils.isNotBlank(param)) {
			if (url.indexOf("?") != -1 && url.indexOf(param) != -1) {
				url = url.substring(url.indexOf("?") + 1);
				String[] arr = url.split("&");
				for (String s : arr) {
					if (s.startsWith(param)) {
						return s.replaceAll(param, "").replaceAll("=", "");
					}
				}
			}
		}
		return null;
	}

	/**
	 * 将字符串数组转换成按分隔符隔开的字符串如数组"1","2","3"则返回1,2,3
	 * 
	 * @param array
	 *            字符串数组
	 * @param splitStr
	 *            分隔符
	 * @return 按分隔符隔开的字符串
	 */
	public static String array2string(String[] array, String splitStr) {
		if (null != array && array.length > 0) {
			StringBuilder sb = new StringBuilder();
			for (String s : array) {
				if (StringUtils.isNotBlank(s)) {
					sb.append(splitStr).append(s);
				}
			}
			return sb.toString().replaceFirst(splitStr, "");
		} else {
			return null;
		}
	}

	public static final String convertNumToChar(int num) {
		if (num >= 0 && num <= 99) {
			String chiStr = "";
			char[] numStr = String.valueOf(num).toCharArray();
			if (numStr.length == 1) {
				chiStr = Chinese[(new Integer(num)).intValue()];
			} else if (numStr.length == 2) {
				if (numStr[0] != 49) {
					chiStr = Chinese[Integer.valueOf(String.valueOf(numStr[0]))
							.intValue()];
				}

				if (numStr[1] != 48) {
					chiStr = chiStr
							+ "十"
							+ Chinese[Integer
									.valueOf(String.valueOf(numStr[1]))
									.intValue()];
				} else {
					chiStr = chiStr + "十";
				}
			}

			return chiStr;
		} else {
			return null;
		}
	}

	/**
	 * 字符串匹配
	 * 
	 * @param regEx
	 *            正则表达式
	 * @param value
	 *            要匹配的字符串
	 * @return 匹配成功返回true
	 */
	public static boolean pattern(String regEx, String value) {
		Pattern p = Pattern.compile(regEx);
		Matcher m = p.matcher(value);
		return m.find();
	}

	public static String dealZero(String zoneCode) {
		if (zoneCode.length() != 8) {
			return zoneCode;
		} else {
			if (zoneCode.substring(zoneCode.length() - 2).equals("00")) {
				zoneCode = zoneCode.substring(0, 6);
			}

			if (zoneCode.substring(zoneCode.length() - 2).equals("00")) {
				zoneCode = zoneCode.substring(0, 4);
			}

			if (zoneCode.substring(zoneCode.length() - 2).equals("00")) {
				zoneCode = zoneCode.substring(0, 2);
			}

			return zoneCode;
		}
	}

	/**
	 * 获取HTML文件中的图片路径数组
	 * 
	 * <AUTHOR>
	 * @createDate 2015年6月5日
	 */
	public static String[] getImgsPath(String content) {
		String img = "";
		Pattern p_image;
		Matcher m_image;
		String str = "";
		String[] images = null;
		String regEx_img = "(<img.*src\\s*=\\s*(.*?)[^>]*?>)";
		p_image = Pattern.compile(regEx_img, Pattern.CASE_INSENSITIVE);
		m_image = p_image.matcher(content);
		while (m_image.find()) {
			img = m_image.group();
			Matcher m = Pattern.compile("src\\s*=\\s*\"?(.*?)(\"|>|\\s+)")
					.matcher(img);
			while (m.find()) {
				String tempSelected = m.group(1);
				if ("".equals(str)) {
					str = tempSelected;
				} else {
					String temp = tempSelected;
					str = str + "," + temp;
				}
			}
		}
		regEx_img = "(<)(input).*?(type)(=)(\")(image)(\").*?(\\/)(>)";
		p_image = Pattern.compile(regEx_img, Pattern.CASE_INSENSITIVE);
		m_image = p_image.matcher(content);
		while (m_image.find()) {
			img = m_image.group();
			Matcher m = Pattern.compile("src\\s*=\\s*\"?(.*?)(\"|>|\\s+)")
					.matcher(img);
			while (m.find()) {
				String tempSelected = m.group(1);
				if ("".equals(str)) {
					str = tempSelected;
				} else {
					String temp = tempSelected;
					str = str + "," + temp;
				}
			}
		}

		if (!"".equals(str)) {
			images = str.split(",");
		}
		return images;
	}


	/**
	 * 验证是否为小数
	 * 
	 * @return isNumber(null); false isNumber(""); true isNumber("11.5"); true
	 *         isNumber("0.5"); true isNumber("011.5"); false isNumber("115");
	 *         true
	 */
	public static boolean isNumber(String value) {
		if (null == value) {
			return false;
		}
		if (isEmpty(value)) {
			return true;
		}
		if (!value.matches("^(-?\\d+)(\\.\\d+)?$")) {
			return false;
		}
		if ("0".equals(value)) {
			return true;
		}
		if (!value.startsWith("0")) {
			return true;
		}
		return value.startsWith("0.");
	}

	/**
	 * 随机生成大写的uuid
	 * 
	 * @return
	 */
	public static String uuid() {
		return UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
	}

	public static boolean isInteger(String value) {
		if (isEmpty(value)) {
			return true;
		}
		if (!isType(value, "\\d+")) {
			return false;
		}
		return (!value.startsWith("0")) || (value.length() <= 1);
	}

	public static boolean isType(String str, String regex) {
		return str.matches(regex);
	}

	public static boolean is10Date(String date) {
		if (isEmpty(date)) {
			return true;
		}
		if (!isSpecifiedLength(date, "10")) {
			return false;
		}

		return isType(
				date,
				"(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)");
	}

	public static boolean is19Datetime(String datetime) {
		if (isEmpty(datetime)) {
			return true;
		}
		if (!isSpecifiedLength(datetime, "19")) {
			return false;
		}
		return isType(
				datetime,
				"^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s((([0-1][0-9])|(2?[0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$");
	}

	public static boolean isSpecifiedLength(String value, String length) {
		if (isEmpty(value)) {
			return true;
		}
		if (isEmpty(length)) {
			return false;
		}
		String[] len = length.split("\\|");
		for (int i = 0; i < len.length; i++) {
			if (getStringLength(value) == Integer.parseInt(len[i])) {
				return true;
			}
		}
		return false;
	}

	public static int getStringLength(String str) {
		int len = str.length();
		int clen = 0;
		for (int i = 0; i < len; i++) {
			if (gbValue(str.charAt(i)) > 0) {
				clen += 2;
			} else {
				clen++;
			}
		}
		return clen;
	}

	private static int gbValue(char ch) {
		String str = new String();
		str = str + ch;
		try {
			byte[] bytes = str.getBytes("GBK");
			if (bytes.length < 2) {
				return 0;
			}
			return (bytes[0] << 8 & 0xFF00) + (bytes[1] & 0xFF);
		} catch (Exception e) {
		}
		return 0;
	}

	/**
	 * 验证组织机构代码是否合法
	 * 
	 * @param code
	 * @return
	 */
	public static boolean isOrganizationCode(String code) {
		if (isEmpty(code))
			return true;

		if (isPdyOrganizationCode(code)) {
			return true;
		}
		return orgCodeValidate(code);
	}

	/**
	 * 验证社会信用代码是否合法
	 * 
	 * @param cCode
	 * @return
	 */
	public static boolean isCreditCode(String cCode) {
		if (isEmpty(cCode))
			return true;

		if (cCode.length() != 18)
			return false;

		if(!Pattern.matches(CreditCode, cCode)){
			return false;
		}
		return true;
	}

	private static boolean isPdyOrganizationCode(String code) {
		if (code.length() != 10)
			return false;

		if (code.indexOf("-") != 8) {
			return false;
		}

		return (code.toLowerCase().startsWith("pdy"));
	}

	private static boolean orgCodeValidate(String code) {
		if (isEmpty(code)) {
			return false;
		}

		int[] ws = { 3, 7, 9, 10, 5, 8, 4, 2 };
		String str = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
		String regex = "^([0-9A-Z]){8}-[0-9|X]$";

		if (!(code.matches(regex)))
			return false;

		int sum = 0;
		for (int i = 0; i < 8; ++i) {
			sum += str.indexOf(String.valueOf(code.charAt(i))) * ws[i];
		}

		int c9 = 11 - sum % 11;

		String sc9 = String.valueOf(c9);
		if (11 == c9)
			sc9 = "0";
		else if (10 == c9)
			sc9 = "X";

		return sc9.equals(String.valueOf(code.charAt(9)));
	}
	/**邮箱验证
	 * qrr 20171101
	 * */
	public static boolean checkEmail(String value) {
	    if(StringUtils.isNotBlank(value)){
	        if(!Pattern.matches(email, value)){
	            return false;
	        }
	    }
        return true;
	}
	/**邮编验证
	 * qrr 20171101
	 * */
	public static boolean vertyPost(String value) {
		if (!Pattern.matches(post, value)) {
			return false;
		}
		return true;
	}

	/**
	 * <p>方法描述：手机号码验证</p>
	 * @MethodAuthor qrr,2018年5月16日,vertyPhone
	 * */
	public static boolean vertyMobilePhone(String value) {
		if (!Pattern.matches(MOBILE_REGEX,
				value)) {
			return false;
		}
		return true;
	}

	/**
	 * <p>方法描述：固话验证</p>
	 * @MethodAuthor qrr,2018年5月16日,vertyFixedPhone
	 * */
	public static boolean vertyFixedPhone(String value) {
		if (!Pattern.matches(PHONE, value)) {
			return false;
		}
		return true;
	}

	/**手机验证
	 * qrr 20171101
	 * */
	public static boolean vertyPhone(String value) {
		if (StringUtils.isNotBlank(value)&& !Pattern.matches("^0(([1,2]\\d)|([3-9]\\d{2}))[-]?\\d{7,8}$", value)
				&&!Pattern.matches("^0?[1]\\d{10}$", value)) {
			return false;
		}
        return true;
	}

	/**
	 * <p>方法描述：返回最大为maxLength长度的字符串</p>
	 * 中文和中文标点符号算2个长度，英文和英文标点符号算1个长度；
	 * maxLength：为中文算2个的长度和英文算1个的长度加起来的值
	 * defaultEndWith:默认超过maxLength长度后，在末尾拼接的字符串
	 * 默认拼接的字符串，同样计入maxLength的长度
	 * @MethodAuthor mxp, 2018/2/23,getStringByLength
	 */
	public static String getStringByLength(String str, int maxLength, String defaultEndWith) {
		str = StringUtils.trimToEmpty(str);
		if ("".equals(str)) {
			return null;
		}
		//小于等于maxLength直接返回
		int stringLength = getStringLength(str);
		if(stringLength<=maxLength){
			return str;
		}
		int len = str.length();
		defaultEndWith = StringUtils.trimToEmpty(defaultEndWith);
		int cLen = getStringLength(defaultEndWith);
		if (cLen >= maxLength) {
			return null;
		}
		int i;
		for (i = 0; i < len; i++) {
			if (gbValue(str.charAt(i)) > 0) {
				cLen += 2;
			} else {
				cLen++;
			}
			if (cLen == maxLength) {
				i++;
				break;
			} else if (cLen > maxLength) {
				break;
			}
		}
		return i == 0?null:str.substring(0, i) + defaultEndWith;
	}

	/**
	 * <p>方法描述：方法同getStringByLength，默认结尾是英文标点 ...</p>
	 *
	 * @MethodAuthor mxp, 2018/2/23,getStringByLengthWithDefaultEnd
	 */
	public static String getStringByLengthWithDefaultEnd(String str, int maxLength) {
		return getStringByLength(str, maxLength, "...");
	}
	public static String genUUID(){
        return UUID.randomUUID().toString().replaceAll("-","");
    }

    /**
     * @Description: 不建议使用的list按长度分组
	 *  清空或者移除返回的结果元素 原先传递的lists也会改变 导致tmpList不可用 ConcurrentModificationException
     *  List<String> tmpList = new ArrayList<>(3);
	 *         tmpList.add("1");
	 *         tmpList.add("2");
	 *         tmpList.add("2");
	 *         tmpList.add("2");
	 *         tmpList.add("5");
	 *         tmpList.add("6");
	 *         tmpList.add("7");
	 *         List<List<String>> newList = splitList(tmpList, 2);
	 *         tmpList.clear();
	 *         System.out.println(newList.get(0).size()); //ConcurrentModificationException
	 * @deprecated replaced by  StringUtils.splitListProxy
     * @MethodAuthor pw,2022年01月21日
     */
    @Deprecated
	public static <T> List<List<T>> splitList(List<T> lists, Integer num) {
		int maxCount;
		if (num == null || num == 0) {
			maxCount = 5;
		} else {
			maxCount = num.intValue();
		}
		List<List<T>> result = new ArrayList<>();
		if (lists != null) {
			//记录数量
			int length = lists.size();
			int time = length / num;
			//计算拆分几条
			time = length % num > 0 ? time + 1 : time;
			int end;
			List<T> tmpList;
			for (int i = 1, start = 0; i <= time; i++) {
				if (i == time) {
					end = length;
				} else {
					end = i * maxCount;
				}
				tmpList = lists.subList(start, end);
				start = end;
				result.add(tmpList);
			}
		}
		return result;
	}


	/**
	 * @Description: list按长度分组
	 *
	 * @MethodAuthor pw,2022年01月21日
	 */
	public static <T> List<List<T>> splitListProxy(List<T> lists, Integer num){
		if(CollectionUtils.isEmpty(lists) || null == num || num < 1){
			return null;
		}
		int capacity = lists.size()/num + 1;
		List<List<T>> result = new ArrayList<>(capacity);
		List<T> list = new ArrayList<>(num);
		for(T t : lists){
			list.add(t);
			if(list.size() == num){
				result.add(list);
				list = new ArrayList<>(num);
			}
		}
		if(null != list && list.size() > 0){
			result.add(list);
		}
		return result;
	}

	/**@
	 * <p>类描述：html数值转为ParagraphRenderData</p>
	 * @MethodAuthor qrr,2021-06-10,htmlTextToParagraph
	 * */
	public static ParagraphRenderData htmlTextToParagraph(String val){
		if(StringUtils.isBlank(val)){
			return null;
		}
		ParagraphRenderData paragraph = new ParagraphRenderData();
		String[] split = val.split("<sub>");
		for (String s: split) {
			int i = s.indexOf("</sub>");
			if (i==-1){
				if(s.indexOf("<sup>")==-1){
					if(s.indexOf("<i>")==-1){
						paragraph.addText(s);
					}else {
						italicToParagraph(s,paragraph);
					}
				}else {
					superToParagraph(s,paragraph);
				}
			}else {
				paragraph.addText(Texts.of(s.substring(0,i)).sub().create());//下标
				if(isNotBlank(s.substring(i))){
					String sub = s.substring(i).replace("</sub>", "");
					if(sub.indexOf("<sup>")==-1){
						paragraph.addText(sub);
					}else {
						superToParagraph(sub,paragraph);
					}
				}
			}
		}
		return paragraph;
	}


	/**
	 *  <p>方法描述：斜体<i></i></p>
	 * @MethodAuthor hsj
	 */
	private static void italicToParagraph(String val, ParagraphRenderData paragraph) {
		String[] split = val.split("<i>");
		for (String s: split) {
			int i = s.indexOf("</i>");
			if (i==-1){
				paragraph.addText(s);
			}else {
				paragraph.addText(Texts.of(s.substring(0,i)).italic().create());
				if(isNotBlank(s.substring(i))){
					paragraph.addText(s.substring(i).replace("</i>",""));
				}
			}
		}
	}

	/**@
	 * <p>类描述：上标</p>
	 * @MethodAuthor qrr,2021-06-10,super1
	 * */
	public static void superToParagraph(String val,ParagraphRenderData paragraph){
		String[] split = val.split("<sup>");
		for (String s: split) {
			int i = s.indexOf("</sup>");
			if (i==-1){
				if(s.indexOf("<i>") == -1){
					paragraph.addText(s);
				}else{
					italicToParagraph(s,paragraph);
				}
			}else {
				paragraph.addText(Texts.of(s.substring(0,i)).sup().create());//上标
				if(isNotBlank(s.substring(i))){
					String sup = s.substring(i).replace("</sup>", "");
					if(sup.indexOf("<i>")==-1){
						paragraph.addText(sup);
					}else {
						italicToParagraph(sup,paragraph);
					}
				}
			}
		}
	}
	/**
	 * <p>方法描述：比较两个List集合是否相等
	 * 1. 如果一个List的引用为null，或者其包含的元素个数为0，那么该List在本逻辑处理中都算作空；
	 * 2. 泛型参数T涉及到对象，所以需要确保正确实现了对应对象的equal()方法。
	 * </p>
	 * @MethodAuthor qrr,2019年6月21日,isListEqual
	 * */
	public static <T>boolean isListEqual(List<T> list1, List<T> list2) {
		// 两个list引用相同（包括两者都为空指针的情况）
		if (list1 == list2) {
			return true;
		}

		// 两个list都为空（包括空指针、元素个数为0）
		if ((list1 == null && list2 != null && list2.size() == 0)
				|| (list2 == null && list1 != null && list1.size() == 0)) {
			return true;
		}
		//一个list为空，另一个list有值
		if ((list1 == null && list2 != null && list2.size()>0)
				|| (list2 == null && list1 != null && list1.size()>0)) {
			return false;
		}
		// 两个list元素个数不相同
		if (list1.size() != list2.size()) {
			return false;
		}

		// 两个list元素个数已经相同，再比较两者内容
		// 采用这种可以忽略list中的元素的顺序
		// 涉及到对象的比较是否相同时，确保实现了equals()方法
		if (!list1.containsAll(list2)) {
			return false;
		}

		return true;
	}


	/**
	 * @Description: 加密证件号码
	 *
	 * @MethodAuthor pw,2021年12月21日
	 */
	public static String encryptIdc(String idcCard){
		if(StringUtils.isNoneBlank(idcCard)){
			String trim = idcCard.trim();
			if(idcCard.trim().length()==15){
				idcCard = trim.substring(0,6)+"******"+trim.substring(12);
			}else if(idcCard.trim().length()==18){
				idcCard = trim.substring(0,6)+"********"+trim.substring(14);
			}else if (idcCard.trim().length()>4) {
				idcCard = idcCard.trim().substring(0,idcCard.trim().length()-4)+"****";
			}else {
				idcCard = "****";
			}
			return idcCard;
		}
		return idcCard;
	}

	/**
	 * @Description: 加密电话或手机号
	 *
	 * @MethodAuthor pw,2021年12月21日
	 */
	public static String encryptPhone(String phoneNo){
		if(StringUtils.isNotBlank(phoneNo)&& phoneNo.length()>4){
			StringBuffer buffer = new StringBuffer();
			int length = phoneNo.length()-4;
			for(int i=0; i<length; i++){
				buffer.append("*");
			}
			buffer.append(phoneNo.substring(length));
			return buffer.toString();
		}
		return phoneNo;
	}


	/**
	* <p>Description：去除字符串的空格，换行符，水平制表符，回车 </p>
	* <p>Author： yzz 2023-11-16 </p>
	*/
	public static String stringTrimAll(final String input) {
		if (null == input) {
			return "";
		}
		Pattern patt = Pattern.compile(regx);
		Matcher m = patt.matcher(input);
		return m.replaceAll("");
	}

	/**
	 * <p>方法描述：校验证件号码 </p>
	 * pw 2024/1/16
	 **/
	public static boolean verifyCardType(String cardTypeCode, String idc) {
		if (StringUtils.isBlank(cardTypeCode) || StringUtils.isBlank(idc)) {
			return false;
		}
		boolean ifIdcValidateSuccess = "88".equals(cardTypeCode);
		if("01".equals(cardTypeCode)){
			ifIdcValidateSuccess = IdcardUtil.isValidCard(idc);
		}else if("02".equals(cardTypeCode)){
			ifIdcValidateSuccess = isHousehold(idc);
		}else if("03".equals(cardTypeCode)){
			ifIdcValidateSuccess = isPassPort(idc);
		}else if("04".equals(cardTypeCode)){
			ifIdcValidateSuccess = isMilitary(idc);
		}else if("06".equals(cardTypeCode)){
			ifIdcValidateSuccess = isHmCard(idc);
		}else if("07".equals(cardTypeCode)){
			ifIdcValidateSuccess = isTaiWanEntry(idc);
		}
		return ifIdcValidateSuccess;
	}

	/** 港澳通行证 */
	public static boolean isHmCard(String idc){
		return pattern(HM_CARDREG, idc);
	}
	/** 护照  */
	public static boolean isPassPort(String idc){
		return pattern(PASS_PORTREG, idc);
	}
	/** 军官证 */
	public static boolean isMilitary(String idc){
		return pattern(MILITARYREG, idc);
	}
	/** 户口本 */
	public static boolean isHousehold(String idc){
		return pattern(HOUSEHOLDREG, idc);
	}
	/** 台胞证 */
	public static boolean isTaiWanEntry(String idc){
		return pattern(TAIWAN_ENTRYREG, idc);
	}
	/**
	 * <p>方法描述：通过身份证号获取出生日期 </p>
	 * @MethodAuthor： pw 2022/7/25
	 **/
	public static Date calBirthdayFromIdc(String idc) {
		if (isBlank(idc) || !IdcardUtil.isValidCard(idc)) {
			return null;
		}
		Date brithday = null;
		// 日期转换格式
		SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
		try {
			// 18位身份证号
			if (idc.length() == 18) {
				brithday = df.parse(idc.substring(6, 14));
			} else {
				// 15位身份证号前加上"19"
				brithday = df.parse(new StringBuilder("19").append(
						idc.substring(6, 12)).toString());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return brithday;
	}

	/**
	 * <p>方法描述：通过身份证号码获取性别 1男 2女 </p>
	 * pw 2024/1/16
	 **/
	public static String getSexFromIdc(String idc) {
		if (StringUtils.isBlank(idc) || !IdcardUtil.isValidCard(idc)) {
			return null;
		}
		String val = null;
		if (idc.length()==15) {
			val = idc.substring(
					idc.length() - 1);
		}else {
			val = idc.substring(
					idc.length() - 2,
					idc.length() - 1);
		}
		if (Integer.valueOf(val) % 2 == 0) {
			return "2";
		} else {
			return "1";
		}
	}

	/**
	 * <p>方法描述： 计算年龄 </p>
	 * pw 2024/1/16
	 **/
	public static Integer getAgeByBirthDay(Date bhkDate,Date birthday) {
		if (null == bhkDate || null == birthday) {
			return null;
		}
		// 当前时间
		Calendar curr = Calendar.getInstance();
		curr.setTime(bhkDate);
		// 生日
		Calendar born = Calendar.getInstance();
		born.setTime(birthday);
		// 年龄 = 当前年 - 出生年
		int age = curr.get(Calendar.YEAR) - born.get(Calendar.YEAR);
		if (age <= 0) {
			return 0;
		}
		// 如果当前月份小于出生月份: age-1
		// 如果当前月份等于出生月份, 且当前日小于出生日: age-1
		int currMonth = curr.get(Calendar.MONTH);
		int currDay = curr.get(Calendar.DAY_OF_MONTH);
		int bornMonth = born.get(Calendar.MONTH);
		int bornDay = born.get(Calendar.DAY_OF_MONTH);
		if ((currMonth < bornMonth) || (currMonth == bornMonth && currDay < bornDay)) {
			age--;
		}
		return age < 0 ? 0 : age;
	}

	/**
	 * <p>方法描述：去除字符串中不能作为xml内容的特殊字符 </p>
	 * pw 2024/11/7
	 **/
	public static String filterSpecialCharOfXml(String txt){
		if (isBlank(txt)) {
			return txt;
		}
		StringBuffer buffer = new StringBuffer();
		int size = txt.length();
		for(int i = 0; i < size; ++i){
			char ch = txt.charAt(i);
			boolean ifMarch = Character.isDefined(ch) &&  ch!= '&' &&
					!Character.isHighSurrogate(ch) &&
					!Character.isISOControl(ch) &&
					!Character.isLowSurrogate(ch);
			if(ifMarch){
				buffer.append(ch);
			}
		}
		return buffer.toString();
	}
}

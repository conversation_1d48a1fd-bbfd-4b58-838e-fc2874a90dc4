package com.chis.comm.utils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * OkHttp工具类
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class OkHttpUtils {
    private final static int READ_TIMEOUT = 300;
    private final static int CONNECT_TIMEOUT = 300;
    private final static int WRITE_TIMEOUT = 300;

    private static OkHttpClient okHttpClient;

    public OkHttpUtils(OkHttpClient okHttpClient) {
        OkHttpUtils.okHttpClient = okHttpClient;
    }

    private static void buildOkHttpClient() {
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();
        //设置读取超时时间
        clientBuilder.readTimeout(READ_TIMEOUT, TimeUnit.SECONDS);
        //设置超时连接时间
        clientBuilder.connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS);
        //设置写入超时时间
        clientBuilder.writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS);
        //支持HTTPS请求，跳过证书验证
        //clientBuilder.sslSocketFactory(createSSLSocketFactory());
        //添加拦截器
        //clientBuilder.addInterceptor(new HttpLogInterceptor());
        okHttpClient = clientBuilder.build();
    }

    /**
     * Get请求
     *
     * @param url   请求的url
     * @param param 请求的参数
     * @return response.body().string()
     */
    public static String get(String url, Map<String, String> param) {
        buildOkHttpClient();
        StringBuilder sb = new StringBuilder(url);
        sb.append("?");
        if (!ObjectUtils.isEmpty(param)) {
            param.forEach((k, v) -> sb.append(k).append("=").append(v).append("&"));
        }
        url = sb.substring(0, sb.length() - 1);
        Request request = new Request.Builder().url(url).build();
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.body() != null) {
                log.info("okhttp3 get status: {}, url: {}, resp: {}.", response.code(), url, response.body().string());
                if (response.isSuccessful()) {
                    return response.body().string();
                }
            } else {
                log.info("okhttp3 get status: {}, url: {}.", response.code(), url);
            }
        } catch (Exception e) {
            log.error("okhttp3 get exception：", new Throwable(e));
        }
        return "";
    }

    /**
     * Post请求
     *
     * @param url       请求的url
     * @param params    post form 提交的参数
     * @param headerMap headerMap
     * @return response.body().string()
     */
    public static String post(String url, Map<String, String> params, Map<String, String> headerMap) throws Exception {
        buildOkHttpClient();
        FormBody.Builder builder = new FormBody.Builder();
        if (!ObjectUtils.isEmpty(params)) {
            params.forEach(builder::add);
        }
        return post(url, builder.build(), headerMap);
    }

    public static Map<Integer, String> postMap(String url, Map<String, String> params, Map<String, String> headerMap) throws Exception {
        buildOkHttpClient();
        FormBody.Builder builder = new FormBody.Builder();
        if (!ObjectUtils.isEmpty(params)) {
            params.forEach(builder::add);
        }
        return postMap(url, builder.build(), headerMap);
    }

    /**
     * Post请求发送JSON数据
     *
     * @param url        请求Url
     * @param jsonParams 请求的JSON
     * @param headerMap  headerMap
     * @return response.body().string()
     */
    public static String postJsonParams(String url, String jsonParams, Map<String, String> headerMap) throws Exception {
        buildOkHttpClient();
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), jsonParams);
        return post(url, requestBody, headerMap);
    }

    /**
     * <p>Description：Post请求发送加密JSON数据，可返回失败后的返回结果 </p>
     * <p>Author： yzz 2024-06-05 </p>
     */
    public static Map<Integer,String> postJsonParamsMap(String url, String jsonParams, Map<String, String> headerMap) throws Exception {
        buildOkHttpClient();
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), jsonParams);
        return postMap(url, requestBody, headerMap);
    }


    /**
     * Post请求发送xml类型数据
     *
     * @param url       请求Url
     * @param xml       请求的xml
     * @param headerMap headerMap
     * @return response.body().string()
     */
    public static String postXmlParams(String url, String xml, Map<String, String> headerMap) throws Exception {
        buildOkHttpClient();
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/xml; charset=utf-8"), xml);
        return post(url, requestBody, headerMap);
    }


    /**
     * Post请求发送string以及file类型数据
     *
     * @param url       请求Url
     * @param paramMap  请求的参数
     * @param headerMap headerMap
     * @return response.body().string()
     */
    public static String postManyParams(String url, Map<String, Object> paramMap, Map<String, String> headerMap) throws Exception {
        buildOkHttpClient();
        MediaType mediaType = MediaType.parse("application/octet-stream");
        MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        paramMap.forEach((k, v) -> {
            if (v instanceof String) {
                builder.addFormDataPart(k, StringUtils.objectToString(v));
            } else if (v instanceof File) {
                if (!ObjectUtils.isEmpty(((File) v).getName())) {
                    builder.addFormDataPart(k, ((File) v).getName(), RequestBody.create(mediaType, (File) v));
                }
            }
        });
        return post(url, builder.build(), headerMap);
    }

    /**
     * post
     *
     * @param url         请求的url
     * @param requestBody requestBody
     * @param headerMap   headerMap
     * @return response.body().string()
     */
    private static String post(String url, RequestBody requestBody, Map<String, String> headerMap) throws Exception {
        Request.Builder builder = new Request.Builder().url(url).post(requestBody);
        if (!ObjectUtils.isEmpty(headerMap)) {
            addHeaders(headerMap, builder);
        }
        Request request = builder.build();
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.body() != null) {
                String responseStr = response.body().string();
                log.info("okhttp3 post status: {}, url: {}, resp: {}.", response.code(), url, responseStr);
                if (response.isSuccessful()) {
                    return responseStr;
                }
            } else {
                log.info("okhttp3 post status: {}, url: {}.", response.code(), url);
            }
        } catch (Exception e) {
            log.error("okhttp3 post exception：", new Throwable(e));
            throw e;
        }
        return "";
    }

    /**
     * <p>Description：可返回失败编码和失败信息 </p>
     * <p>Author： yzz 2024-06-05 </p>
     */
    private static Map<Integer,String> postMap(String url, RequestBody requestBody, Map<String, String> headerMap) throws Exception {
        Request.Builder builder = new Request.Builder().url(url).post(requestBody);
        if (!ObjectUtils.isEmpty(headerMap)) {
            addHeaders(headerMap, builder);
        }
        Request request = builder.build();
        Map<Integer,String> respMap = new HashMap<>();
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.body() != null) {
                String responseStr = response.body().string();
                log.info("okhttp3 post status: {}, url: {}, resp: {}.", response.code(), url, responseStr);
                respMap.put(response.code(),responseStr);
            } else {
                log.info("okhttp3 post status: {}, url: {}.", response.code(), url);
                respMap.put(response.code(),null);
            }
        } catch (Exception e) {
            log.error("okhttp3 post exception：", new Throwable(e));
            throw e;
        }
        return respMap;
    }

    /**
     * 添加header信息
     *
     * @param headerMap header
     * @param builder   Request.Builder
     */
    private static void addHeaders(Map<String, String> headerMap, Request.Builder builder) {
        if (!ObjectUtils.isEmpty(headerMap)) {
            headerMap.forEach(builder::addHeader);
        }
    }

    /**
     *  <p>方法描述：post请求</p>
     * @MethodAuthor hsj 2024-06-18 18:23
     */
    public static String postMultipartBody(String url, MultipartBody requestBody,Map<String, String> headerMap)  {
        buildOkHttpClient();
        try {
            Request.Builder builder = new Request.Builder() .url(url) .post(requestBody) ;
            if (!ObjectUtils.isEmpty(headerMap)) {
                addHeaders(headerMap, builder);
            }
            Request request = builder.build();
            Response response =okHttpClient.newCall(request).execute();
            if (response.body() != null) {
                String responseStr = response.body().string();
                log.info("okhttp3 post status: {},  resp: {}.", response.code(),  responseStr);
                if (response.isSuccessful()) {
                    return responseStr;
                }
            } else {
                log.info("okhttp3 post status: {}", response.code());
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("okhttp3 post exception：", e);
        }
      return null;
    }

    /**
     *  <p>方法描述：get请求</p>
     * @MethodAuthor hsj 2024-06-18 18:38
     */
    public static String okGet(String url, Map<String, String> param, Map<String, String> headerMap) {
        buildOkHttpClient();
        try {
            StringBuilder sb = new StringBuilder(url);
            sb.append("?");
            if (!ObjectUtils.isEmpty(param)) {
                param.forEach((k, v) -> sb.append(k).append("=").append(v).append("&"));
            }
            url = sb.substring(0, sb.length() - 1);
            log.info("okhttp3 get url: {}", url);
            Request.Builder builder = new Request.Builder().url(url).get();
            if (!ObjectUtils.isEmpty(headerMap)) {
                addHeaders(headerMap, builder);
            }
            Request request = builder.build();
            Response response = okHttpClient.newCall(request).execute();
            if (response.body() != null) {
                String responseStr = response.body().string();
                log.info("okhttp3 post status: {},  resp: {}.", response.code(), responseStr);
                if (response.isSuccessful()) {
                    return responseStr;
                }
            } else {
                log.info("okhttp3 post status: {}", response.code());
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("okhttp3 post exception：", e);
        }
        return null;
    }
    /**
     *  <p>方法描述：post application/x-www-form-urlencoded 请求</p>
     * @MethodAuthor hsj 2024-06-19 9:58
     */
    public static String postRequestBody(String url, RequestBody  requestBody,Map<String, String> headerMap)  {
        buildOkHttpClient();
        try {
            Request.Builder builder = new Request.Builder() .url(url) .post(requestBody) ;
            if (!ObjectUtils.isEmpty(headerMap)) {
                addHeaders(headerMap, builder);
            }
            Request request = builder.build();
            Response response =okHttpClient.newCall(request).execute();
            if (response.body() != null) {
                String responseStr = response.body().string();
                log.info("okhttp3 post status: {},  resp: {}.", response.code(),  responseStr);
                if (response.isSuccessful()) {
                    return responseStr;
                }
            } else {
                log.info("okhttp3 post status: {}", response.code());
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("okhttp3 post exception：", e);
        }
        return null;
    }


}

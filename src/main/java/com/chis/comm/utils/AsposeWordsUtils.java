package com.chis.comm.utils;

import com.aspose.words.Document;
import com.aspose.words.SaveFormat;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
/**
 * <p>类描述：aspose-words的文件转换工具类 </p>
 * @ClassAuthor： pw 2022/9/3
 **/
public class AsposeWordsUtils {

    /**
     * <p>方法描述：将word转换成PDF </p>
     * @MethodAuthor： pw 2022/8/28
     **/
    public static String word2Pdf(String virtualDir, String docFilePath, String pdfFilePath, boolean ifDeleteDoc) {
        FileOutputStream fileOS = null;
        FileInputStream fileIn = null;
        if(StringUtils.isBlank(docFilePath)){
            return null;
        }
        File docFile = new File(virtualDir+File.separator+docFilePath);
        //word文件不存在 返回
        if(null == docFile || !docFile.exists()){
            return null;
        }
        //有提供的路径使用提供的路径 无提供的路径 直接使用word文件路径
        if(StringUtils.isBlank(pdfFilePath)){
            pdfFilePath = docFilePath.substring(0,docFilePath.lastIndexOf(".")+1)+"pdf";
        }
        try {
            fileIn = new FileInputStream(docFile);
            Document doc = new Document(fileIn);
            //样式异常 加入格式清除
            /*ParagraphFormat pf = doc.getStyles().getDefaultParagraphFormat();
            pf.clearFormatting();*/
            fileOS = new FileOutputStream(virtualDir+File.separator+pdfFilePath);
            // 保存转换的pdf文件
            doc.save(fileOS, SaveFormat.PDF);
        } catch (Exception e) {
            e.printStackTrace();
            pdfFilePath = null;
        } finally {
            try {
                if (fileOS != null) {
                    fileOS.close();
                }

                if (fileIn != null) {
                    fileIn.close();
                }
            } catch (IOException e) {
            }
        }
        if(ifDeleteDoc && StringUtils.isNotBlank(pdfFilePath)){
            docFile.delete();
        }
        return pdfFilePath;
    }
}

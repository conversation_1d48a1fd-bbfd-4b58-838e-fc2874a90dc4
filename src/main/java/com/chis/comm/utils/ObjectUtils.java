package com.chis.comm.utils;

import java.io.Reader;
import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Clob;
import java.util.Collection;
import java.util.Date;
import java.util.Map;

public class ObjectUtils {
	 /**
     * 判断object是否不为空,集合会校验size
     */
    public static boolean isNotNull(Object... obj) {
        return !ObjectUtils.isNull(obj);
    }
    /**
     * 判断object是否为空,集合会校验size
     */
    public static boolean isNull(Object... objs) {
        for (Object obj : objs) {
            if (ObjectUtils.isEmpty(obj)) {
                return true;
            }
        }
        return false;
    }
    /**
     * 对象空判断
     */
    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }

        if (obj.getClass().isArray()) {
            return Array.getLength(obj) == 0;
        }
        if (obj instanceof CharSequence) {
            return ((CharSequence) obj).length() == 0;
        }
        if (obj instanceof Collection) {
            return ((Collection) obj).isEmpty();
        }
        if (obj instanceof Map) {
            return ((Map) obj).isEmpty();
        }
        // else
        return false;
    }

    /**
     * 转换值为指定类型(转换失败返回null)
     *
     * @param <T>   转换的目标类型
     * @param type  类型目标
     * @param value 被转换值
     * @return 转换后的值
     */
    public static <T> T convert(Class<T> type, Object value) {
        return convert(type, value, null);
    }

    /**
     * 转换值为指定类型(转换失败返回默认值)
     *
     * @param <T>          转换的目标类型
     * @param type         类型目标
     * @param value        被转换值
     * @param defaultValue 默认值
     * @return 转换后的值
     */
    @SuppressWarnings("unchecked")
    public static <T> T convert(Class<T> type, Object value, T defaultValue) {
        if (isEmpty(value)) {
            return defaultValue;
        }
        T t = null;
        try {
            if (type == String.class) {
                t = (T) toStr(value);
            } else if (type == Double.class) {
                t = (T) toDouble(value);
            } else if (type == Float.class) {
                t = (T) toFloat(value);
            } else if (type == Long.class) {
                t = (T) toLong(value);
            } else if (type == Short.class) {
                t = (T) toShort(value);
            } else if (type == Integer.class) {
                t = (T) toInteger(value);
            } else if (type == BigDecimal.class) {
                t = (T) toBigDecimal(value);
            } else if (type == Date.class) {
                t = (T) toDate(value);
            } else if (type == Boolean.class) {
                t = (T) toBoolean(value);
            }
        } catch (Exception e) {
        }
        if (t == null) {
            return defaultValue;
        } else {
            return t;
        }
    }

    /**
     * 转换为String类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static String toStr(Object value) {
        return null == value ? null : value.toString();
    }

    /**
     * 转换为Double类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Double toDouble(Object value) {
        return Double.valueOf(StringUtils.trim(value.toString()));
    }

    /**
     * 转换为Float类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Float toFloat(Object value) {
        return Float.valueOf(StringUtils.trim(value.toString()));
    }

    /**
     * 转换为Long类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Long toLong(Object value) {
        return Long.valueOf(StringUtils.trim(value.toString()));
    }

    /**
     * 转换为Short类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Short toShort(Object value) {
        return Short.valueOf(StringUtils.trim(value.toString()));
    }

    /**
     * 转换为Integer类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Integer toInteger(Object value) {
        return Integer.valueOf(StringUtils.trim(value.toString()));
    }

    /**
     * 转换为BigDecimal类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static BigDecimal toBigDecimal(Object value) {
        BigDecimal ret = null;
        if (value != null) {
            if (value instanceof BigDecimal) {
                ret = (BigDecimal) value;
            } else if (value instanceof String) {
                ret = new BigDecimal((String) value);
            } else if (value instanceof BigInteger) {
                ret = new BigDecimal((BigInteger) value);
            } else if (value instanceof Number) {
                ret = BigDecimal.valueOf(((Number) value).doubleValue());
            }
        }
        return ret;
    }

    /**
     * 转换为Date类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Date toDate(Object value) {
        Date date = null;
        if (value != null) {
            if (value instanceof Date) {
                date = (Date) value;
            } else if (value instanceof Long) {
                date = new Date((Long) value * 1000L);
            } else if (value instanceof String) {
                Long aLong = convert(Long.class, value);
                if (aLong != null) {
                    date = new Date(aLong * 1000L);
                } else {
                    date = new Date((String) value);
                }
            }
        }
        return date;
    }

    /**
     * 转换为Date类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Boolean toBoolean(Object value) {
        Boolean data = null;
        if (value != null) {
            if (value instanceof Boolean) {
                data = (Boolean) value;
            } else if (value instanceof String) {
                if ("true".equals(value.toString())) {
                    data = Boolean.TRUE;
                } else if ("false".equals(value.toString())) {
                    data = Boolean.FALSE;
                }
            }
        }
        return data;
    }
}

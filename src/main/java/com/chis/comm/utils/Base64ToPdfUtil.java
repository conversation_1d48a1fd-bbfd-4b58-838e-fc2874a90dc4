package com.chis.comm.utils;

import sun.misc.BASE64Decoder;
import java.io.*;

/**
 * <AUTHOR>
 * @description:
 */
public class Base64ToPdfUtil {
    /**
     * Description: 将base64编码内容转换为Pdf
     *
     * @param base64Content 编码内容，文件的存储路径（含文件名）
     * <AUTHOR>
     * Create Date: 2015年7月30日 上午9:40:23
     */
    public static Boolean base64StringToPdf(String base64Content, String filePath) throws IOException {
        BASE64Decoder decoder = new BASE64Decoder();
        BufferedInputStream bis = null;
        FileOutputStream fos = null;
        BufferedOutputStream bos = null;
        try {
            //base64编码内容转换为字节数组
            byte[] bytes = decoder.decodeBuffer(base64Content);
            ByteArrayInputStream byteInputStream = new ByteArrayInputStream(bytes);
            bis = new BufferedInputStream(byteInputStream);
            File file = new File(filePath);
            File path = file.getParentFile();
            if (!path.exists()) {
                path.mkdirs();
            }
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);

            byte[] buffer = new byte[1024];
            int length = bis.read(buffer);
            while (length != -1) {
                bos.write(buffer, 0, length);
                length = bis.read(buffer);
            }
            bos.flush();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            bis.close();
            fos.close();
            bos.close();
        }
        return true;
    }
}

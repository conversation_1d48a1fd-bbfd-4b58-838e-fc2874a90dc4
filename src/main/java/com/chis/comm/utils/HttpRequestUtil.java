package com.chis.comm.utils;

import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.*;
import java.net.URLConnection;
import java.security.cert.X509Certificate;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;


public class HttpRequestUtil {

	/**
	 * <p>方法描述：https请求
	 * 参考 https://cloud.tencent.com/developer/ask/sof/86690
	 * </p>
	 * @param requestUrl  请求地址
	 * @param outputStr  需要传递的请求参数对象json字符串
	 * @param requestMethod GET或者POST
	 * @param headMap 请求头Map
	 * @MethodAuthor： pw 2022/9/6
	 **/
	public static String httpSSLRequest(String requestUrl, String requestMethod,
										String outputStr, Map<String,String> headMap) throws Exception {
		/* Start of Fix */
		TrustManager[] trustAllCerts = new TrustManager[] { new X509TrustManager() {
			@Override
			public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {

			}

			@Override
			public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {

			}

			@Override
			public java.security.cert.X509Certificate[] getAcceptedIssuers() { return null; }

		} };

		SSLContext sc = SSLContext.getInstance("SSL");
		sc.init(null, trustAllCerts, new java.security.SecureRandom());
		HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

		// Create all-trusting host name verifier
		HostnameVerifier allHostsValid = new HostnameVerifier() {
			@Override
			public boolean verify(String hostname, SSLSession session) { return true; }
		};
		// Install the all-trusting host verifier
		HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
		/* End of the fix*/
		URL url = new URL(requestUrl);
		HttpsURLConnection httpUrlConn = (HttpsURLConnection)url.openConnection();
		StringBuffer buffer = new StringBuffer();
		httpUrlConn.setDoOutput(true);
		httpUrlConn.setDoInput(true);
		httpUrlConn.setUseCaches(false);
		// 设置请求方式（GET/POST）
		httpUrlConn.setRequestMethod(requestMethod);
		//设置请求头
		if(null != headMap && !headMap.isEmpty()){
			for(Map.Entry<String,String> mapEntity : headMap.entrySet()){
				httpUrlConn.setRequestProperty(mapEntity.getKey(), mapEntity.getValue());
			}
		}
		httpUrlConn.connect();

		// 当有请求对象需要传递
		if (null != outputStr) {
			DataOutputStream out = new DataOutputStream(httpUrlConn.getOutputStream());
			out.write(outputStr.getBytes("UTF-8"));
			// 刷新、关闭
			out.flush();
			out.close();
		}

		// 将返回的输入流转换成字符串
		InputStream inputStream = httpUrlConn.getInputStream();
		InputStreamReader inputStreamReader = new InputStreamReader(
				inputStream, "utf-8");
		BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

		String str = null;
		while ((str = bufferedReader.readLine()) != null) {
			buffer.append(str);
		}
		bufferedReader.close();
		inputStreamReader.close();
		// 释放资源
		inputStream.close();
		httpUrlConn.disconnect();
		return buffer.toString();
	}

	/**
	 * 发起http请求并获取结果
	 * 
	 * @param requestUrl
	 *            请求地址
	 * @param requestMethod
	 *            请求方式（GET、POST）
	 * @param outputStr
	 *            提交的数据
	 * @return JSONObject(通过JSONObject.get(key)的方式获取json对象的属性值)
	 * @throws IOException
	 */
	public static String httpRequest(String requestUrl, String requestMethod,
			String outputStr) throws Exception {
		StringBuffer buffer = new StringBuffer();
		URL url = new URL(requestUrl);
		HttpURLConnection httpUrlConn = (HttpURLConnection) url
				.openConnection();

		httpUrlConn.setDoOutput(true);
		httpUrlConn.setDoInput(true);
		httpUrlConn.setUseCaches(false);
		// 设置请求方式（GET/POST）
		httpUrlConn.setRequestMethod(requestMethod);

		if ("GET".equalsIgnoreCase(requestMethod))
			httpUrlConn.connect();

		// 当有数据需要提交时
		if (null != outputStr) {
			OutputStream outputStream = httpUrlConn.getOutputStream();
			// 注意编码格式，防止中文乱码
			outputStream.write(outputStr.getBytes("UTF-8"));
			outputStream.close();
		}

		// 将返回的输入流转换成字符串
		InputStream inputStream = httpUrlConn.getInputStream();
		InputStreamReader inputStreamReader = new InputStreamReader(
				inputStream, "utf-8");
		BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

		String str = null;
		while ((str = bufferedReader.readLine()) != null) {
			buffer.append(str);
		}
		bufferedReader.close();
		inputStreamReader.close();
		// 释放资源
		inputStream.close();
		inputStream = null;
		httpUrlConn.disconnect();
		return buffer.toString();
	}
	
	/**
	 * 发起https请求并获取结果
	 * 模拟附件发送
	 * @param requestUrl
	 * @param fis
	 * @return
	 * @throws IOException
	 */
	public static String httpRequest(String requestUrl, FileInputStream fis) throws IOException{
		URL urlObj = new URL(requestUrl);
		// 连接
		HttpURLConnection con = (HttpURLConnection) urlObj.openConnection();
		/**
		 * 设置关键值
		 */
		con.setRequestMethod("POST"); // 以Post方式提交表单，默认get方式
		con.setDoInput(true);
		con.setDoOutput(true);
		con.setUseCaches(false); // post方式不能使用缓存
		// 设置请求头信息
		con.setRequestProperty("Connection", "Keep-Alive");
		con.setRequestProperty("Charset", "UTF-8");
		// 设置边界
		String BOUNDARY = "----------" + System.currentTimeMillis();
		con.setRequestProperty("Content-Type", "multipart/form-data; boundary="
				+ BOUNDARY);
		// 请求正文信息
		StringBuilder sb = new StringBuilder();

		// 附件部分：
		sb.append("--");
		sb.append(BOUNDARY);
		sb.append("\r\n");
		sb.append("Content-Disposition: form-data;name=\"dataFile\";filename=\""
				+ "Data.zip" + "\"\r\n");
		sb.append("Content-Type:application/octet-stream\r\n\r\n");

		byte[] head = sb.toString().getBytes("utf-8");

		// 获得输出流
		OutputStream out = new DataOutputStream(con.getOutputStream());
		// 输出表头
		out.write(head);

		// 文件正文部分
		// 把文件已流文件的方式 推入到url中
		int bytes = 0;
		byte[] bufferOut = new byte[1024];
		while ((bytes = fis.read(bufferOut)) != -1) {
			out.write(bufferOut, 0, bytes);
		}
		fis.close();

		// 结尾部分
		byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义最后数据分隔线

		out.write(foot);
		out.flush();
		out.close();

		// 将返回的输入流转换成字符串
		InputStream inputStream = con.getInputStream();
		InputStreamReader inputStreamReader = new InputStreamReader(
				inputStream, "utf-8");
		BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

		String str = null;
		StringBuffer buffer = new StringBuffer();
		while ((str = bufferedReader.readLine()) != null) {
			buffer.append(str);
		}
		bufferedReader.close();
		inputStreamReader.close();
		// 释放资源
		inputStream.close();
		inputStream = null;
		con.disconnect();
		return buffer.toString();
	}
	/***
	 * <p>方法描述: 发起https请求并获取结果</p>
	 * compressed：压缩的字节数组
	 * @MethodAuthor mxp,2019/1/12,httpRequest
	 */
	public static String httpRequest(String requestUrl, byte[] compressed,boolean ifZip) throws IOException{
		URL urlObj = new URL(requestUrl);
		// 连接
		HttpURLConnection con = (HttpURLConnection) urlObj.openConnection();
		/**
		 * 设置关键值
		 */
		con.setRequestMethod("POST"); // 以Post方式提交表单，默认get方式
		con.setDoInput(true);
		con.setDoOutput(true);
		con.setUseCaches(false); // post方式不能使用缓存
		// 设置请求头信息
		con.setRequestProperty("Connection", "Keep-Alive");
		con.setRequestProperty("Charset", "UTF-8");
		// 设置边界
		String BOUNDARY = "----------" + System.currentTimeMillis();
		con.setRequestProperty("Content-Type", "multipart/form-data; boundary="
				+ BOUNDARY);
		// 请求正文信息
		StringBuilder sb = new StringBuilder();

		// 附件部分：
		sb.append("--");
		sb.append(BOUNDARY);
		sb.append("\r\n");
		sb.append("Content-Disposition: form-data;name=\"dataJson\";filename=\""
				+ "dataJson.zip" + "\"\r\n");
		sb.append("Content-Type:application/octet-stream\r\n\r\n");

		byte[] head = sb.toString().getBytes("utf-8");

		// 获得输出流
		OutputStream out = new DataOutputStream(con.getOutputStream());
		// 输出表头
		out.write(head);

		// 文件正文部分
		out.write(compressed);
		// 结尾部分
		byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义最后数据分隔线

		out.write(foot);
		out.flush();
		out.close();

		// 将返回的输入流转换成字符串
		InputStream inputStream = con.getInputStream();
		StringBuffer buffer = new StringBuffer();
		if(ifZip){
			ZipInputStream zis = new ZipInputStream(inputStream);
			ZipEntry zipEntry = zis.getNextEntry();
			while (zipEntry != null) {
				InputStreamReader isr = new InputStreamReader(zis, "UTF-8");
				BufferedReader br = new BufferedReader(isr);
				String str = null;
				while ((str = br.readLine()) != null) {
					buffer.append(str);
				}
				zipEntry = null;
			}
			zis.close();
		}else{
			InputStreamReader inputStreamReader = new InputStreamReader(
					inputStream, "utf-8");
			BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

			String str = null;

			while ((str = bufferedReader.readLine()) != null) {
				buffer.append(str);
			}
			bufferedReader.close();
			inputStreamReader.close();
		}

		// 释放资源
		inputStream.close();
		inputStream = null;
		con.disconnect();
		return buffer.toString();
	}

	/**
	 * <p>方法描述: 发起http请求并获取结果</p>
	 *
	 * @MethodAuthor mxp, 2018/9/5,httpRequest
	 *
	 * @param requestUrl
	 *            请求地址
	 * @param requestMethod
	 *            请求方式（GET、POST）
	 * @param outputStr
	 *            提交的数据
	 * @param connectionTimeout
	 *            连接超时
	 * @param readTimeOut
	 *            读取超时
	 * @return JSONObject(通过JSONObject.get(key)的方式获取json对象的属性值)
	 */
	public static String httpRequest(String requestUrl, String requestMethod,
									 String outputStr,int connectionTimeout, int readTimeOut) throws IOException {
		StringBuffer buffer = new StringBuffer();
		URL url = new URL(requestUrl);
		HttpURLConnection httpUrlConn = (HttpURLConnection) url
				.openConnection();

		httpUrlConn.setDoOutput(true);
		httpUrlConn.setDoInput(true);
		httpUrlConn.setUseCaches(false);
		httpUrlConn.setConnectTimeout(connectionTimeout);
		httpUrlConn.setReadTimeout(readTimeOut);
		// 设置请求方式（GET/POST）
		httpUrlConn.setRequestMethod(requestMethod);

		if ("GET".equalsIgnoreCase(requestMethod))
			httpUrlConn.connect();

		// 当有数据需要提交时
		if (null != outputStr) {
			OutputStream outputStream = httpUrlConn.getOutputStream();
			// 注意编码格式，防止中文乱码
			outputStream.write(outputStr.getBytes("UTF-8"));
			outputStream.close();
		}

		// 将返回的输入流转换成字符串
		InputStream inputStream = httpUrlConn.getInputStream();
		InputStreamReader inputStreamReader = new InputStreamReader(
				inputStream, "utf-8");
		BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

		String str = null;
		while ((str = bufferedReader.readLine()) != null) {
			buffer.append(str);
		}
		bufferedReader.close();
		inputStreamReader.close();
		// 释放资源
		inputStream.close();
		inputStream = null;
		httpUrlConn.disconnect();
		return buffer.toString();
	}
	/**
 	 * <p>方法描述：以raw方式实现post请求</p>
 	 * @MethodAuthor qrr,2018年11月5日,httpTest
	 * */
	public static String httpRequestByRaw(String url,String msg) throws ClientProtocolException,IOException {
		HttpClient httpClient = HttpClients.createDefault();
		HttpPost post = new HttpPost(url);
		StringEntity postingString = new StringEntity(msg, "UTF-8");// json传递
		postingString.setContentEncoding("UTF-8");
		post.setEntity(postingString);
		post.setHeader("Content-type", "application/json; charset=UTF-8");
		HttpResponse response = httpClient.execute(post);
		String content = EntityUtils.toString(response.getEntity());
		return content;
	}
	/**
 	 * <p>方法描述：发起https请求并获取ZIP压缩包结果
 	 * @param requestUrl
	 * @param fis
	 * @return
	 * @throws IOException
 	 * </p>
 	 * @MethodAuthor qrr,2019年1月14日,httpRequest
	 */
	public static String httpRequestByZip(String requestUrl, FileInputStream fis) throws IOException{
		URL urlObj = new URL(requestUrl);
		// 连接
		HttpURLConnection con = (HttpURLConnection) urlObj.openConnection();
		/**
		 * 设置关键值
		 */
		con.setRequestMethod("POST"); // 以Post方式提交表单，默认get方式
		con.setDoInput(true);
		con.setDoOutput(true);
		con.setUseCaches(false); // post方式不能使用缓存
		// 设置请求头信息
		con.setRequestProperty("Connection", "Keep-Alive");
		con.setRequestProperty("Charset", "UTF-8");
		// 设置边界
		String BOUNDARY = "----------" + System.currentTimeMillis();
		con.setRequestProperty("Content-Type", "multipart/form-data; boundary="
				+ BOUNDARY);
		// 请求正文信息
		StringBuilder sb = new StringBuilder();

		// 附件部分：
		sb.append("--");
		sb.append(BOUNDARY);
		sb.append("\r\n");
		sb.append("Content-Disposition: form-data;name=\"dataFile\";filename=\""
				+ "Data.zip" + "\"\r\n");
		sb.append("Content-Type:application/octet-stream\r\n\r\n");

		byte[] head = sb.toString().getBytes("utf-8");

		// 获得输出流
		OutputStream out = new DataOutputStream(con.getOutputStream());
		// 输出表头
		out.write(head);

		// 文件正文部分
		// 把文件已流文件的方式 推入到url中
		int bytes = 0;
		byte[] bufferOut = new byte[1024];
		while ((bytes = fis.read(bufferOut)) != -1) {
			out.write(bufferOut, 0, bytes);
		}
		fis.close();

		// 结尾部分
		byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义最后数据分隔线

		out.write(foot);
		out.flush();
		out.close();

		// 将返回的输入流转换成字符串
		InputStream inputStream = con.getInputStream();
		
		ZipInputStream zin = new ZipInputStream(inputStream);

		StringBuffer buffer = new StringBuffer();
		ZipEntry zipEntry = zin.getNextEntry();
		while (zipEntry != null) {
			InputStreamReader isr = new InputStreamReader(zin, "UTF-8");
			BufferedReader br = new BufferedReader(isr);
			String str = null;
			while ((str = br.readLine()) != null) {
				buffer.append(str);
			}
			zipEntry = null;
			zipEntry = zin.getNextEntry();
		}
		
		zin.close();
		// 释放资源
		inputStream.close();
		inputStream = null;
		con.disconnect();
		return buffer.toString();
	}
	/**
	 * <p>方法描述：发起https请求并获取反馈结果结果
	 * @param requestUrl
	 * @param fis
	 * @return
	 * @throws IOException
	 * </p>
	 * @MethodAuthor qrr,2019年1月14日,httpRequest
	 */
	public static String httpRequestByBackJson(String requestUrl, FileInputStream fis) throws IOException{
		URL urlObj = new URL(requestUrl);
		// 连接
		HttpURLConnection con = (HttpURLConnection) urlObj.openConnection();
		/**
		 * 设置关键值
		 */
		con.setRequestMethod("POST"); // 以Post方式提交表单，默认get方式
		con.setDoInput(true);
		con.setDoOutput(true);
		con.setUseCaches(false); // post方式不能使用缓存
		// 设置请求头信息
		con.setRequestProperty("Connection", "Keep-Alive");
		con.setRequestProperty("Charset", "UTF-8");
		// 设置边界
		String BOUNDARY = "----------" + System.currentTimeMillis();
		con.setRequestProperty("Content-Type", "multipart/form-data; boundary="
				+ BOUNDARY);
		// 请求正文信息
		StringBuilder sb = new StringBuilder();
		
		// 附件部分：
		sb.append("--");
		sb.append(BOUNDARY);
		sb.append("\r\n");
		sb.append("Content-Disposition: form-data;name=\"dataFile\";filename=\""
				+ "Data.zip" + "\"\r\n");
		sb.append("Content-Type:application/octet-stream\r\n\r\n");
		
		byte[] head = sb.toString().getBytes("utf-8");
		
		// 获得输出流
		OutputStream out = new DataOutputStream(con.getOutputStream());
		// 输出表头
		out.write(head);
		
		// 文件正文部分
		// 把文件已流文件的方式 推入到url中
		int bytes = 0;
		byte[] bufferOut = new byte[1024];
		while ((bytes = fis.read(bufferOut)) != -1) {
			out.write(bufferOut, 0, bytes);
		}
		fis.close();
		
		// 结尾部分
		byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义最后数据分隔线
		
		out.write(foot);
		out.flush();
		out.close();
		
		// 将返回的输入流转换成字符串
		InputStream inputStream = con.getInputStream();
		StringBuffer buffer = new StringBuffer();
		InputStreamReader inputStreamReader = new InputStreamReader(
				inputStream, "utf-8");
		BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
	
		String str = null;
		while ((str = bufferedReader.readLine()) != null) {
			buffer.append(str);
		}
		bufferedReader.close();
		inputStreamReader.close();
		// 释放资源
		inputStream.close();
		inputStream = null;
		con.disconnect();
		return buffer.toString();
	}
	
	
	
	
	/**
 	 * <p>方法描述：发起https请求并发送ZIP压缩包结果,并且指定压缩包key名称
 	 * @param requestUrl
	 * @param fis
	 * @return
	 * @throws IOException
 	 * </p>
 	 * @MethodAuthor rcj,2019年1月14日,httpRequest
	 */
	public static String httpRequestByZipName(String requestUrl, FileInputStream fis,String zipName) throws IOException{
		URL urlObj = new URL(requestUrl);
		// 连接
		HttpURLConnection con = (HttpURLConnection) urlObj.openConnection();
		/**
		 * 设置关键值
		 */
		con.setRequestMethod("POST"); // 以Post方式提交表单，默认get方式
		con.setDoInput(true);
		con.setDoOutput(true);
		con.setUseCaches(false); // post方式不能使用缓存
		// 设置请求头信息
		con.setRequestProperty("Connection", "Keep-Alive");
		con.setRequestProperty("Charset", "UTF-8");
		// 设置边界
		String BOUNDARY = "----------" + System.currentTimeMillis();
		con.setRequestProperty("Content-Type", "multipart/form-data; boundary="
				+ BOUNDARY);
		// 请求正文信息
		StringBuilder sb = new StringBuilder();

		// 附件部分：
		sb.append("--");
		sb.append(BOUNDARY);
		sb.append("\r\n");
		sb.append("Content-Disposition: form-data;name=\"").append(zipName).append("\";filename=\""
				+ "Data.zip" + "\"\r\n");
		sb.append("Content-Type:application/octet-stream\r\n\r\n");

		byte[] head = sb.toString().getBytes("utf-8");

		// 获得输出流
		OutputStream out = new DataOutputStream(con.getOutputStream());
		// 输出表头
		out.write(head);

		// 文件正文部分
		// 把文件已流文件的方式 推入到url中
		int bytes = 0;
		byte[] bufferOut = new byte[1024];
		while ((bytes = fis.read(bufferOut)) != -1) {
			out.write(bufferOut, 0, bytes);
		}
		fis.close();

		// 结尾部分
		byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义最后数据分隔线

		out.write(foot);
		out.flush();
		out.close();

		StringBuffer buffer = new StringBuffer();
		// 将返回的输入流转换成字符串
		InputStream inputStream = con.getInputStream();
		InputStreamReader inputStreamReader = new InputStreamReader(
				inputStream, "utf-8");
		BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
		
		String str = null;
		while ((str = bufferedReader.readLine()) != null) {
			buffer.append(str);
		}
		bufferedReader.close();
		inputStreamReader.close();
		// 释放资源
		inputStream.close();
		inputStream = null;
		con.disconnect();
		return buffer.toString();
	}

	/**
	 * <p>类描述：get请求</p>
	 * @MethodAuthor qrr,2020-11-26,httpGetRequestByRaw
	 * */
	public static String httpGetRequestByRaw(String url,String tokenId) throws IOException {
		HttpClient httpClient = HttpClients.createDefault();
		HttpGet post = new HttpGet(url);
		post.setHeader("Content-type", "application/json; charset=UTF-8");
		if(StringUtils.isNotBlank(tokenId)){
			post.setHeader("tokenId",tokenId);
		}
		HttpResponse response = httpClient.execute(post);
		String content = EntityUtils.toString(response.getEntity());
		return content;
	}

	/**
	 * <p>方法描述：以raw方式实现post请求</p>
	 * @MethodAuthor qrr,2018年11月5日,httpTest
	 * */
	public static String httpRequestByRaw(String url, String msg, Header[] headers) throws IOException {
		HttpClient httpClient = HttpClients.createDefault();
		HttpPost post = new HttpPost(url);
		StringEntity postingString = new StringEntity(msg, "UTF-8");// json传递
		postingString.setContentEncoding("UTF-8");
		post.setEntity(postingString);
		post.setHeader("Content-type", "application/json; charset=UTF-8");
		post.setHeaders(headers);
		HttpResponse response = httpClient.execute(post);
		String content = EntityUtils.toString(response.getEntity());
		return content;
	}
	/**
	 *  <p>方法描述：以raw方式实现post请求解决乱码</p>
	 * @MethodAuthor hsj
	 */
	public static String httpRequestByRawNew(String url, String msg, Header[] headers) throws IOException {
		HttpClient httpClient = HttpClients.createDefault();
		HttpPost post = new HttpPost(url);
		StringEntity postingString = new StringEntity(msg, StandardCharsets.UTF_8);// json传递
		postingString.setContentEncoding("UTF-8");
		postingString.setContentType("application/json");
		post.setEntity(postingString);
		post.setHeader("Content-type", "application/json; charset=UTF-8");
		post.setHeaders(headers);
		HttpResponse response = httpClient.execute(post);
		String content = EntityUtils.toString(response.getEntity(),StandardCharsets.UTF_8);
		return content;
	}

	/**
	 * <p>方法描述： form-data方式提交参数 </p>
	 * 参考https://www.cnblogs.com/supiaopiao/p/12513148.html
	 * @MethodAuthor： pw 2022/12/23
	 **/
	public static String httpRequestByFormData(String url,Map<String,String> paramMap) throws Exception{
		SSLContext sslcontext = createIgnoreVerifySSL();
		// 设置协议http和https对应的处理socket链接工厂的对象
		Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
				.register("http", PlainConnectionSocketFactory.INSTANCE)
				.register("https", new SSLConnectionSocketFactory(sslcontext))
				.build();
		PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
		HttpClient httpClient = HttpClients.custom().setConnectionManager(connManager).build();
		List<NameValuePair> nvps = new ArrayList<>();
		if(null != paramMap && !paramMap.isEmpty()){
			for(Map.Entry<String,String> mapEntity : paramMap.entrySet()){
				nvps.add(new BasicNameValuePair(mapEntity.getKey(), mapEntity.getValue()));
			}
		}
		UrlEncodedFormEntity entity = new UrlEncodedFormEntity(nvps,"utf-8");
		HttpPost post = new HttpPost(url);
		post.setEntity(entity);
		HttpResponse response = httpClient.execute(post);
		String content = EntityUtils.toString(response.getEntity(),StandardCharsets.UTF_8);
		return content;
	}
	/**
	 *  <p>方法描述：云掌通短信发送</p>
	 * @MethodAuthor hsj 2023-10-14 13:56
	 */
	public static String httpRequestByYzt(String url, String param) throws IOException {
		URL realUrl = new URL(url);
		// 打开和URL之间的连接
		URLConnection conn = realUrl.openConnection();
		// 设置通用的请求属性
		conn.setRequestProperty("accept", "*/*");
		conn.setRequestProperty("connection", "Keep-Alive");
		conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
		conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
		// 发送POST请求必须设置如下两行
		conn.setDoOutput(true);
		conn.setDoInput(true);
		// 获取URLConnection对象对应的输出流
		PrintWriter out = new PrintWriter(conn.getOutputStream());
		// 发送请求参数
		out.print(param);
		// flush输出流的缓冲
		out.flush();
		// 定义BufferedReader输入流来读取URL的响应
		BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
		String line;
		StringBuilder response = new StringBuilder();
		while ((line = in.readLine()) != null) {
			response.append(line);
		}
		return response.toString();
	}
	public static SSLContext createIgnoreVerifySSL() {
		try {
			SSLContext sc = SSLContext.getInstance("SSLv3");

			// 实现一个X509TrustManager接口，用于绕过验证，不用修改里面的方法
			X509TrustManager trustManager = new X509TrustManager() {
				@Override
				public void checkClientTrusted(
						X509Certificate[] paramArrayOfX509Certificate,
						String paramString) throws CertificateException {
				}

				@Override
				public void checkServerTrusted(
						X509Certificate[] paramArrayOfX509Certificate,
						String paramString) throws CertificateException {
				}

				@Override
				public X509Certificate[] getAcceptedIssuers() {
					return null;
				}
			};

			sc.init(null, new TrustManager[]{trustManager}, null);
			return sc;

		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;

	}

}

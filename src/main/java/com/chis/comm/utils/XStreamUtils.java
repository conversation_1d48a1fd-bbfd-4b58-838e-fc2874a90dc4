package com.chis.comm.utils;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.core.util.QuickWriter;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;
import com.thoughtworks.xstream.io.xml.DomDriver;
import com.thoughtworks.xstream.io.xml.PrettyPrintWriter;
import com.thoughtworks.xstream.io.xml.XmlFriendlyNameCoder;
import com.thoughtworks.xstream.io.xml.XppDriver;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.io.Writer;

/**
 * 模块说明：xml转换工具类<br/>
 *
 * <AUTHOR>
 * @XStreamAlias xml转换注解<br   />
 * @XStreamOmitField xml字段不生成注解<br   />
 * @XStreamImplicit 注解在集合上，不生成集合的标签<br/>
 * @createDate 2016年12月24日
 */
public class XStreamUtils {

    /**
     * 转换成xml
     *
     * @param obj
     * @return
     */
    public static String toXml(Object obj) {
        if (null == obj)
            return null;
        // 转义--为-
        XStream xStream = new XStream(new XppDriver(new XmlFriendlyNameCoder("_-", "_")));
        xStream.autodetectAnnotations(true);
        xStream.aliasSystemAttribute(null, "class"); // 去掉 class 属性

        String xml = xStream.toXML(obj);
        return xml;
    }

    /**
     * <p>方法描述：转换成被CDATA标签包裹的xml </p>
     * @MethodAuthor： pw 2022/8/8
     **/
    public static String toCDATAXml(Object obj) {
        if (null == obj){
            return null;
        }
        XppDriver xppDriver = new XppDriver() {
            @Override
            public HierarchicalStreamWriter createWriter(Writer out) {
                return new PrettyPrintWriter(out, new XmlFriendlyNameCoder("_-", "_")) {
                    boolean cdata = true;
                    @Override
                    protected void writeText(QuickWriter writer, String text) {
                        text =  XMLChars(text);
                        if (cdata) {
                            if(StringUtils.isNotBlank(text)){
                                writer.write("<![CDATA[");
                                writer.write(text);
                                writer.write("]]>");
                            }else{
                                writer.write(text);
                            }
                        } else {
                            writer.write(text);
                        }
                    }
                };
            }
        };
        // 转义--为-
        XStream xStream = new XStream(xppDriver);
        xStream.autodetectAnnotations(true);
        xStream.aliasSystemAttribute(null, "class"); // 去掉 class 属性

        String xml = xStream.toXML(obj);
        return xml;
    }
    /**
     *  <p>类描述： 处理解析XML时非法字符</p>
     * @ClassAuthor hsj 2022-09-19 14:36
     */
    public static String XMLChars(String s) {
        if (StringUtils.isBlank(s)) {
            return s;
        }
        return s.replaceAll("[\\x00-\\x08\\x0b-\\x0c\\x0e-\\x1f]", "");
    }
    /**
     * @Description: 转换成有头信息的xml
     *
     * @MethodAuthor pw,2022年04月6日
     */
    public static String toXmlWithHead(Object obj){
        String xml = toXml(obj);
        if(StringUtils.isNotBlank(xml)){
            xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?> " + xml;
        }
        return xml;
    }

    /**
     * <p>方法描述：转换成有头信息并且内容包裹在CDATA中的xml</p>
     * @MethodAuthor： pw 2022/8/8
     **/
    public static String toCDATAXmlWithHead(Object obj){
        String xml = toCDATAXml(obj);
        if(StringUtils.isNotBlank(xml)){
            xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?> " + xml;
        }
        return xml;
    }

    /**
     * xml转换对象
     * 注意 如果clazz节点下子节点类存在XStreamAlias注解并且子节点类和root类不在同一个包下 会出现异常 使用fromXmlWithPackage处理
     * @param s
     * @param clazz
     * @return <p>修订内容：设置当前线程的类加载器</p>
     * @MethodReviser qrr, 2019年5月13日, fromXml
     */
    public static Object fromXml(String s, Class clazz) {
        return fromXmlWithPackage(s, clazz, null);
    }

    /**
     * <p>方法描述：xml转换对象 </p>
     * @param s
     * @param clazz
     * @param classNameArr 类存在XStreamAlias注解的类名 如果为空则获取clazz节点对应的包下所有的类
     * 注意 只要clazz节点涉及到的类有XStreamAlias注解 并且有数据的 都需要加入到packageArr中
     * @MethodAuthor： pw 2023/1/12
     **/
    public static Object fromXmlWithPackage(String s, Class clazz, String[] classNameArr) {
        if (StringUtils.isBlank(s))
            return null;
        XStream stream = new XStream(new DomDriver());
        if(null == classNameArr || classNameArr.length == 0){
            String packageName = clazz.getPackage().getName();
            classNameArr = new String[]{packageName+".*"};
        }
        /*
         * com.thoughtworks.xstream.security.ForbiddenClassException
         * stream.addPermission(AnyTypePermission.ANY)或者stream.allowTypesByWildcard(packageArr)解决
         * */
        stream.allowTypesByWildcard(classNameArr);
        stream.processAnnotations(clazz);
        stream.ignoreUnknownElements();// 忽略多余的xml节点
        stream.setClassLoader(Thread.currentThread().getContextClassLoader());
        Object obj = stream.fromXML(s);
        return obj;
    }

    /**
     * 合并两个xml的内容
     *
     * @param xml1
     * @param xml2
     * @param nodeName1
     * @param nodeName2
     * @return
     */
    public static String uniteXml(String xml1, String xml2, String nodeName1, String nodeName2) throws Exception {
        Document doc_1 = DocumentHelper.parseText(xml1);
        Document doc_2 = DocumentHelper.parseText(xml2);

        Element ele1 = (Element) doc_1.selectSingleNode(nodeName1);
        Element ele2 = (Element) doc_2.selectSingleNode(nodeName2);

        ele1.appendContent(ele2);
        return doc_1.asXML();
    }

    /**
     * 合并两个xml的内容
     *
     * @param xml1
     * @param xml2
     * @param nodeName1
     * @param nodeName2
     * @return
     */
    public static Document uniteXml(Document doc_1, Document doc_2, String nodeName1, String nodeName2) throws Exception {
        Element ele1 = (Element) doc_1.selectSingleNode(nodeName1);
        Element ele2 = (Element) doc_2.selectSingleNode(nodeName2);
        ele1.appendContent(ele2);
        return doc_1;
    }

    /**
     * 合并多个xml
     *
     * @param xmls     多个xml以@zwx_split@隔开
     * @param nodeName
     * @return
     * @throws Exception
     */
    public static String uniteXmls(String xmls, String nodeName) throws Exception {
        String[] arrs = xmls.split("@zwx_split@");
        Document doc_1 = DocumentHelper.parseText(arrs[0]);
        for (int i = 1; i < arrs.length; i++) {
            Document doc_i = DocumentHelper.parseText(arrs[i]);
            uniteXml(doc_1, doc_i, nodeName, nodeName);
        }
        return doc_1.asXML();
    }
}

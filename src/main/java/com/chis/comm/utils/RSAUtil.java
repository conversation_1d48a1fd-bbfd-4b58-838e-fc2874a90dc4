package com.chis.comm.utils;

import org.apache.commons.codec.binary.Base64;
import sun.misc.BASE64Decoder;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Map;

public class RSAUtil {
 
	public static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
	public static final String ENCODE_ALGORITHM = "SHA-256";
	public static final String PLAIN_TEXT = "hello world";
 
	public static void main(String[] args) throws UnsupportedEncodingException, SignatureException {
		// 公私钥对
//		Map<String, byte[]> keyMap = RSA.generateKeyBytes();
//		PublicKey publicKey = RSA.restorePublicKey(keyMap.get(RSA.PUBLIC_KEY));
//		PrivateKey privateKey = RSA.restorePrivateKey(keyMap.get(RSA.PRIVATE_KEY));
		
		
		PublicKey publicKey = getPubKey();
		PrivateKey privateKey = getPrivateKey("MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCA3VwWVZvuFwqz3FYAHflpyfyhmOf1ugfYQ2EJ1rlYe+5AOXbJR+9jQWz/pukYgUAE3qVl8aX87VOZlU+uxqfqNqFZFRQ/VJktCtVj4B06HDocKzK9QVawY3gUlyQteLNZi+GlXYgNImZyVhBjaf7b0pvU+9Mu/HCSZH7fO6xmAH4BlEMUVgTaHsEsJ51EKuJZ46LWlQ2oK2hRpIA5S+l+/YQXt27VK1JyI23jiqlFMxE6P3LSMWDZPVXpBd3WYcoANGwMl28sTeEtmBzvN7K9CMs3ByzOxfRFt7hdXmEUJNZRtBVawTU9rBIqzDdEpEc1vTV2jZ3RzaeuRLomqMpNAgMBAAECggEAR13QbRIWXEJoi/mfq0ruYTca8UfkVsMjKxMNv8wqM6XW6bypW4m6Owb13ZPLjeWu1cOUaCPvIGczIGB/k7SzM9PzgYlaAETS3A4e5cQti4wsZoNKA2QXJXLBAu49e+1XrmT4n4NwII4HnJSrWNNP8N/vq4GXkHL/ySNc3DaJIKelxwp4Z78S47a+kBY5Rp6TQEyh6lD6LZtyAgYn9QYyL9uBYXd1lkj2E4PLbf1LwKfXWFYscr3Yx25DYS3sbZXTNzPzTtpg55F2AAoT6eGlFuJxHB6fOaaaMTw4TlMb2zrYZIYn0hzeBCUf5h16cSRsiLt89YT2JVoSKadabCoLAQKBgQC9TzXgjcPFEi272f7VIqKTCGcth83NpIn6UmuslcIgAei2Wzibx8/SLO9zKYs3HNCTTYn0thV/dGaNk/ctCGa1aHYFQI0U/Mutb1OHvtrtCS9QaQqKUq0MPq178zlWehIbgNryAmuzEeLUGQw+C3OOvgqhmriDP5EbaIGiz3ktjQKBgQCuQveHwjJVbdvxi205fGMh+wGdwHUTUf0OD8D4SyqLcLKkGc4IYlMniUoiXiFfypB+XFaMqx6rfZMdSUpSdoSWECu9CD/uU6IScOZJkEbF8EkenSbPo11C2NvpvvcEa7a+O+uxYf4BES4VV4EnHWwgOH/uDnKUE5JToueMmSb/wQKBgFGbArOhCOI7+uD+kP6ZppFRTwDuiMX8BsqUmvvTttILYKCzZAyrLqgAXNw87Vk9n5/QDgciMtQ1fjvLrAKKm5PZyv5qnCaO7tH9qnlayY7ipbW5AiUsjJI5tpk2+OGRT5rxifBHINwZerlJbuCUGSVQQM73HlDdpxS0kSV44FjFAoGADPGkeU0PeBEE+Vgl8NMweeGvKQyWpay9Y0IAT3El492nJ1FTYIQNMS6ddxgO2pKtUPWC6Ef9qzdUqiQ7hQyQpKTm2s+2D/neXAhO0sAIwcMraz65F0tHLiP1kdNBfe6SUGcbBr4EpAemqKcrTMB+u5X2yrQABse9YsEC+gqWPwECgYEAjkw5KQUO+Uz0SOqWpRk/z5CU+tMuVDwRtZ9pc4h20OsnUU+zb1hA8DvntsKTsr1s1XPKB1nFqNyA0jWaPht5RuqRyx8kNwOvdHUmI0+nJD94+wg4V5xVxzGTHjKEyOC/gZKrJIcrloGMKO3R/zpgE32Cv3e2UVGlpd0BjrQRiBw=");
//		String pubKeyStr = new String(Base64.encode(publicKey.getEncoded())); //pkcs8格式
//		String priKeyStr = new String(Base64.encode(privateKey.getEncoded())); //pkcs8格式
//		 System.out.println("pubKeyStr:\r"+pubKeyStr);
//	     System.out.println("priKeyStr:\r"+priKeyStr);
		
		
		// 签名
		String val ="<body>\n" +
				"    <employerList>\n" +
				"      <employer>\n" +
				"        <creditCode>91110302801786752A</creditCode>\n" +
				"        <employerName>北京北方华创微电子装备有限公司</employerName>\n" +
				"        <areaCode>4201000000</areaCode>\n" +
				"        <economicTypeCode>110</economicTypeCode>\n" +
				"        <industryCategoryCode>200751</industryCategoryCode>\n" +
				"        <enterpriseSizeCode>10000</enterpriseSizeCode>\n" +
				"        <address>北京市北京经济技术开发区文昌大道8号</address>\n" +
				"        <contactPerson>赵晋荣</contactPerson>\n" +
				"        <contactPhone>01057846999</contactPhone>\n" +
				"        <isSubsidiary>false</isSubsidiary>\n" +
				"        <createAreaCode>4201020000</createAreaCode>\n" +
				"        <writeUnit>武汉市职业病防治院</writeUnit>\n" +
				"        <writePerson>院长</writePerson>\n" +
				"        <writePersonTel>18888888888</writePersonTel>\n" +
				"        <writeDate>2020-12-03</writeDate>\n" +
				"        <reportUnit>武汉市职业病防治院</reportUnit>\n" +
				"        <reportPerson>院长</reportPerson>\n" +
				"        <reportPersonTel>18888888888</reportPersonTel>\n" +
				"        <reportDate>2020-12-03</reportDate>\n" +
				"        <auditStatus>05</auditStatus>\n" +
				"        <auditInfo>通过</auditInfo>\n" +
				"        <auditTime>2021-01-15 11:53:32</auditTime>\n" +
				"        <auditorName>张三</auditorName>\n" +
				"      </employer>\n" +
				"    </employerList>\n" +
				"  </body>";
		String val1 ="<body>\n" +
				"    <employerList>\n" +
				"      <employer>\n" +
				"        <creditCode>91110302801786752A</creditCode>\n" +
				"        <employerName>北京北方华创微电子装备有限公司</employerName>\n" +
				"        <areaCode>4201000000</areaCode>\n" +
				"        <economicTypeCode>110</economicTypeCode>\n" +
				"        <industryCategoryCode>200751</industryCategoryCode>\n" +
				"        <enterpriseSizeCode>10000</enterpriseSizeCode>\n" +
				"        <address>北京市北京经济技术开发区文昌大道8号</address>\n" +
				"        <contactPerson>赵晋荣</contactPerson>\n" +
				"        <contactPhone>01057846999</contactPhone>\n" +
				"        <isSubsidiary>false</isSubsidiary>\n" +
				"        <createAreaCode>4201020000</createAreaCode>\n" +
				"        <writeUnit>武汉市职业病防治院</writeUnit>\n" +
				"        <writePerson>院长</writePerson>\n" +
				"        <writePersonTel>18888888888</writePersonTel>\n" +
				"        <writeDate>2020-12-03</writeDate>\n" +
				"        <reportUnit>武汉市职业病防治院</reportUnit>\n" +
				"        <reportPerson>院长</reportPerson>\n" +
				"        <reportPersonTel>18888888888</reportPersonTel>\n" +
				"        <reportDate>2020-12-03</reportDate>\n" +
				"        <auditStatus>05</auditStatus>\n" +
				"        <auditInfo>通过</auditInfo>\n" +
				"        <auditTime>2021-01-15 11:53:32</auditTime>\n" +
				"        <auditorName>张三</auditorName>\n" +
				"      </employer>\n" +
				"    </employerList>\n" +
				"  </body>";
		System.out.println(val1);
		String sing_byte = sign(privateKey, val1);
		System.out.println("SHA256withRSA签名后-----》" + sing_byte);

//		String val = "<body>\n" +
//				"    <employerList>\n" +
//				"      <employer>\n" +
//				"        <creditCode>91110302801786752A</creditCode>\n" +
//				"        <employerName>北京北方华创微电子装备有限公司</employerName>\n" +
//				"        <areaCode>4201000000</areaCode>\n" +
//				"        <economicTypeCode>110</economicTypeCode>\n" +
//				"        <industryCategoryCode>200751</industryCategoryCode>\n" +
//				"        <enterpriseSizeCode>10000</enterpriseSizeCode>\n" +
//				"        <address>北京市北京经济技术开发区文昌大道8号</address>\n" +
//				"        <contactPerson>赵晋荣</contactPerson>\n" +
//				"        <contactPhone>01057846999</contactPhone>\n" +
//				"        <isSubsidiary>false</isSubsidiary>\n" +
//				"        <createAreaCode>4201020000</createAreaCode>\n" +
//				"        <writeUnit>武汉市职业病防治院</writeUnit>\n" +
//				"        <writePerson>院长</writePerson>\n" +
//				"        <writePersonTel>18888888888</writePersonTel>\n" +
//				"        <writeDate>2020-12-03</writeDate>\n" +
//				"        <reportUnit>武汉市职业病防治院</reportUnit>\n" +
//				"        <reportPerson>院长</reportPerson>\n" +
//				"        <reportPersonTel>18888888888</reportPersonTel>\n" +
//				"        <reportDate>2020-12-03</reportDate>\n" +
//				"        <auditStatus>05</auditStatus>\n" +
//				"        <auditInfo>通过</auditInfo>\n" +
//				"        <auditTime>11:28:30</auditTime>\n" +
//				"        <auditorName>张三</auditorName>\n" +
//				"      </employer>\n" +
//				"    </employerList>\n" +
//				"  </body>";
//		byte[] sing_byte = sign1(privateKey, val);
//
//		// 验签
//		boolean fail = verifySign(publicKey, val, sing_byte);
//		System.out.println(fail);
	}
 
	/**
	 * 签名
	 * 
	 * @param privateKey
	 *            私钥
	 * @param plain_text
	 *            明文
	 * @return
	 * @throws UnsupportedEncodingException 
	 * @throws SignatureException 
	 */
	public static String sign(PrivateKey privateKey, String plain_text) throws UnsupportedEncodingException, SignatureException {
		Signature sign = null;
		try {
			sign = Signature.getInstance(SIGNATURE_ALGORITHM);  
			sign.initSign(privateKey);  
			sign.update(plain_text.getBytes("utf-8")); 
//			System.out.println("SHA256withRSA签名后-----》" + Base64.getEncoder().encodeToString(sign.sign()));
		} catch (NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
			e.printStackTrace();
		}
		return Base64.encodeBase64String(sign.sign());
	}

	public static byte[] sign1(PrivateKey privateKey, String plain_text) throws UnsupportedEncodingException, SignatureException {
		Signature sign = null;
		try {
			sign = Signature.getInstance(SIGNATURE_ALGORITHM);
			sign.initSign(privateKey);
			sign.update(plain_text.getBytes("utf-8"));
//			System.out.println("SHA256withRSA签名后-----》" + Base64.getEncoder().encodeToString(sign.sign()));
		} catch (NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
			e.printStackTrace();
		}
		return sign.sign();
	}
 
	/**
	 * 验签
	 * 
	 * @param publicKey
	 *            公钥
	 * @param plain_text
	 *            明文
	 * @param signed
	 *            签名
	 * @throws UnsupportedEncodingException 
	 */
	public static boolean verifySign(PublicKey publicKey, String plain_text, byte[] signed) throws UnsupportedEncodingException {
		Signature sign = null;  
		boolean SignedSuccess=false;
		try {
			sign = Signature.getInstance(SIGNATURE_ALGORITHM);  
			sign.initVerify(publicKey);  
			sign.update(plain_text.getBytes("utf-8")); 
			SignedSuccess = sign.verify(signed);
			System.out.println("验证成功？---" + SignedSuccess);
			
		} catch (NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
			e.printStackTrace();
		}
		return SignedSuccess;
	}
 
	/**
	 * bytes[]换成16进制字符串
	 * 
	 * @param src
	 * @return
	 */
	public static String bytesToHexString(byte[] src) {
		StringBuilder stringBuilder = new StringBuilder("");
		if (src == null || src.length <= 0) {
			return null;
		}
		for (int i = 0; i < src.length; i++) {
			int v = src[i] & 0xFF;
			String hv = Integer.toHexString(v);
			if (hv.length() < 2) {
				stringBuilder.append(0);
			}
			stringBuilder.append(hv);
		}
		return stringBuilder.toString();
	}
	
	
	

	/**
	  * 实例化公钥
	  * 
	  * @return
	  */
	public static PublicKey getPubKey() {
	  PublicKey publicKey = null;
	  try {

	   // 自己的公钥(测试)
	    String pubKey ="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgN1cFlWb7hcKs9xWAB35acn8oZjn9boH2ENhCda5WHvuQDl2yUfvY0Fs/6bpGIFABN6lZfGl/O1TmZVPrsan6jahWRUUP1SZLQrVY+AdOhw6HCsyvUFWsGN4FJckLXizWYvhpV2IDSJmclYQY2n+29Kb1PvTLvxwkmR+3zusZgB+AZRDFFYE2h7BLCedRCriWeOi1pUNqCtoUaSAOUvpfv2EF7du1StSciNt44qpRTMROj9y0jFg2T1V6QXd1mHKADRsDJdvLE3hLZgc7zeyvQjLNwcszsX0Rbe4XV5hFCTWUbQVWsE1PawSKsw3RKRHNb01do2d0c2nrkS6JqjKTQIDAQAB";
	      java.security.spec.X509EncodedKeySpec bobPubKeySpec = new java.security.spec.X509EncodedKeySpec(
	     new BASE64Decoder().decodeBuffer(pubKey));
	   // RSA对称加密算法
	   KeyFactory keyFactory;
	   keyFactory = KeyFactory.getInstance("RSA");
	   // 取公钥匙对象
	   publicKey = keyFactory.generatePublic(bobPubKeySpec);
	  } catch (NoSuchAlgorithmException e) {
	   e.printStackTrace();
	  } catch (InvalidKeySpecException e) {
	   e.printStackTrace();
	  } catch (IOException e) {
	   e.printStackTrace();
	  }
	  return publicKey;
	 }

	 

	 


	 /**
	  * 实例化私钥
	  * 
	  * @return
	  */
	 public static PrivateKey getPrivateKey(String s) {
	  PrivateKey privateKey = null;
	  String priKey = s;
	  PKCS8EncodedKeySpec priPKCS8;
	  try {
	   priPKCS8 = new PKCS8EncodedKeySpec(
	     new BASE64Decoder().decodeBuffer(priKey));
	   KeyFactory keyf = KeyFactory.getInstance("RSA");
	   privateKey = keyf.generatePrivate(priPKCS8);
	  } catch (IOException e) {
	   e.printStackTrace();
	  } catch (NoSuchAlgorithmException e) {
	   e.printStackTrace();
	  } catch (InvalidKeySpecException e) {
	   e.printStackTrace();
	  }
	  return privateKey;
	 }
}

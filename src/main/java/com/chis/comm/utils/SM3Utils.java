package com.chis.comm.utils;

import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;

import java.security.Security;

/**
 * <p>类描述：SM3加密工具类，加密和加密结果验证（不可逆算法）</p>
 * @ClassAuthor qrr,2020-11-16,SM3Utils
 * */
public class SM3Utils {
    private static final String ENCODING = "UTF-8";
    static {
        Security.addProvider(new BouncyCastleProvider());
    }
    /**
     * <p>方法描述：</p>
     * @MethodAuthor qrr,2020-11-16,SM3Utils
     * */
    public static String encrypt(String src){
        String result = null;
        try {
            byte[] srcBytes = src.getBytes(ENCODING);
            byte[] hash = hash(srcBytes);
            result = ByteUtils.toHexString(hash);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
    /**
     * <p>方法描述：摘要算法</p>
     * @MethodAuthor qrr,2020-11-16,SM3Utils
     * */
    private static byte[] hash(byte[] src){
        SM3Digest digest = new SM3Digest();
        digest.update(src, 0, src.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }

    public static void main(String[] args) {
        System.out.println(encrypt("7OZV4XWCLSQSD1XM|1605492181077"));
    }
}

V1.0
    1、新增体检机构实体（齐然然20190731）
    2、系统单位实体创建（齐然然20191024）
    3、消息相关表创建（齐然然20191025）
    4、发送消息查询业务地区的上级地区关联所在管辖地区的用户（齐然然20191224）
    5、发送消息给用户，该用户的角色为质控员（齐然然20200107）
    6、ZwxBaseController去除接口方法定义（齐然然20200818）
    7、兼容12c数据库（齐然然20201103）
    8、新增接口对照实体等（齐然然20201116）
V1.1
    1、码表、地区缓存新增方法（齐然然20201126）
    2、公用service类新增更新创建人、创建日期（齐然然20201201）
    3、TS_ZONE增加省直属属性（潘伟20210517）
    4、码表增加拓展字段4（杨攀华20210608）
    5、新增系统参数实体等（齐然然20210707）
    6、新增查询所有基础码表方法-不过滤停用（杨攀华20210717）
    7、新增问卷相关实体和执行脚本工具类（齐然然20210824）
    8、新增insert和insertBatch方法（齐然然20210825）
    9、表格题目答题详情与表格题列定义新增批量查询方法（潘伟20210826）
    10、问卷类型新增碘治疗人员内照射剂量监测调查（齐然然20210828）
    11、【武汉职卫】问卷调查详情接口调整，问卷保存接口，问卷答题详情表新增字段调整（闫壮壮20210911）
    12、江苏职卫技术人员考核题目相关调整（齐然然20210927）
    13、新增技术人员考核问卷答案子表（齐然然20211009）
    14、新增通过问卷类型与答卷主表Rid集合批量获取问卷答题详情列表（潘伟20211018）
    15、调整优化表格列最大最小值验证以及错误返回提示（龚哲20211019）
    16、脚本验证新增state状态参数（龚哲20211020）
    17、新增查询所有用户方法（龚哲20211117）
    18、新增获取多种码表类型码表Map缓存方法（陈晨20220825）
    19、新增节假日查询，获取两个日期间工作日天数，以及获取参数日期前后参数天数的工作日日期（潘伟20231110）
    20、新增全部机构查询且缓存（侯思杰20240111）
    21、TS_UNIT表新增字段WRITE_NO_RULE（侯思杰20240201）
    22、TsContraSub对照表封装空指针异常处理（侯思杰20240402）
    23、【职卫通用】springboot项目创建人和修改人存储为1（潘伟20240621）
    34、226335码表缓存新增key为名称的map方法（侯思杰20240923）
V1.1.1
    1、码表查询，非单条数据返回的方法加入缓存，返回结果非单个码表结果，都是有缓存的（潘伟20220927）
V1.1.2
    1、删除问卷答案相关方法新增参数MAIN_TYPE（陈晨20221125）
    2、增加通过rid集合批量获取题库方法（潘伟20221125）
V1.1.3
    1、问卷题目增加对照编码（潘伟20230302）
    2、【吉林职卫】尘肺病康复管理-【评估】-【ADL评估表等】问卷接口（侯思杰20230306）
V1.1.4
    1、专业技术人员考核问卷删除锁表处理（潘伟20230315）
    2、新增 查找所有诊断机构对应的系统单位方法（陈晨20240926）
    3、新增 码表、对照、校验等方法（陈晨20241115）
    4、252199新增 查询地区List findTsZoneNext 方法（陈晨20241205）
V1.0
    1、添加工具类XStreamUtils、PropertyUtils，添加对应pom依赖；（安静20200508）
    2、新增ObjectCopyUtil等工具类（齐然然20200509）
    3、新增http请求工具类（齐然然20200721）
    4、http请求HttpClient初始化优化（齐然然20200915）
    5、SM3加密工具类（齐然然20201116）
V1.1
    1、新增http请求方法（齐然然20201126）
    2、新增MD5、SHA256加密工具类（齐然然20210116）
    3、html数值转ParagraphRenderData（齐然然20210610）
    4、优化用于生成Word报表的工具（潘伟20210610）
    5、新增判断2个集合是否相等方法（齐然然20210927）
    6、增加依据出生日期与依据身份证号计算年龄两个方法，增加返回日期的年方法（潘伟20211126）
    7、统一社会信用代码校验工具，去除社会信用代码校验位的校验（龚哲20121203）
    8、httpRequestByRaw支持多个head封装（齐然然20211215）
    9、添加DES加解密工具类（潘伟20211216）
    10、增加加密证件号码与手机号（潘伟20211221）
    11、增加文件夹压缩ZIP文件（潘伟20211231）
    12、新增splitListProxy替换原先的splitList方法（潘伟20220125）
    13、XStreamUtils新增转换成有头信息的xml（潘伟20220406）
    14、新增根据身份证号获取性别（侯思杰20220408）
    15、xml转换工具增加特殊字符用<![CDATA[]]>包裹（潘伟20220806）
    16、增加户口簿、护照、军官证、港澳通行证、台胞证验证（潘伟20220806）
    17、新增word转pdf工具依赖（潘伟20220827）
    18、新增aspose-words的文件转换工具类（潘伟20220903）
    19、工具类增加https请求方法（潘伟20220908）
    20、处理解析XML时非法字符（侯思杰20220919）
    21、增加职业/放射卫生技术服务信息报送卡 国家接口DES加解密工具类（潘伟20221222）
    22、HttpRequestUtil工具类增加，form-data方式提交参数（潘伟20221223）
    23、新增OkHttp工具类（陈晨20221228）
    24、xstream版本调整成1.4.20（潘伟20230111）
    25、FileUtils增加读取文件返回字节码，通过utf8读取文件返回字符串方法（潘伟20230821）
    26、FileUtils增加密码压缩ZIP文件（潘伟20230822）
    27、HttpRequestUtil新增云掌通短息发送方法（侯思杰20231014）
    28、ObjectUtils新增类型转换方法（陈晨20231117）
    29、新增SM4加密工具类（陈晨20231130）
    30、StringUtils支持手机号和电话号码验证修改（侯思杰20240304）
    31、OkHttpUtils新增请求方式方法（侯思杰20240619）
    32、DateUtils新增严格地解析日期方法（陈晨20240823）
    33、245477【福建职卫】汇聚平台接口工具更新方法名（StringUtils增加去除字符串中不能作为xml内容的特殊字符方法）（潘伟20241107）
    34、DateUtils日期格式新增yyyyMMdd格式（侯思杰20241116）
    35、252199平台工具类DynamicExcelExportUtil迁移（陈晨20241205）
    36、252201DynamicExcelExportUtil行合并优化（侯思杰20241211）
    37、265686【江苏职卫】风险评估-个案数（全部导出）支持单个单元格去除边框，解决列数多的行后生成的时候丢失多出的列的问题（潘伟20250114）
    38、313921 FileUtils新增文件列表压缩，文件组内的文件以新名称压缩（潘伟20250710）
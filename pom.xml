<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
    <groupId>chis-spr</groupId>
    <artifactId>spring-jdbc</artifactId>
    <version>1.1-SNAPSHOT</version>
	<packaging>jar</packaging>
	<parent>
        <groupId>chis-spr</groupId>
        <artifactId>springboot-pro</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <properties> 
	    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>  
	    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>  
	    <zwx.spring-webmvc.version>1.1-SNAPSHOT</zwx.spring-webmvc.version>  
	</properties>
    <dependencies>
        <dependency>
            <groupId>chis-spr</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${zwx.spring-webmvc.version}</version>
        </dependency>
		<!--druid -->  
	    <dependency> 
	      <groupId>com.alibaba</groupId>  
	      <artifactId>druid-spring-boot-starter</artifactId>  
	      <version>1.1.9</version> 
	    </dependency>
        <!--jdbc-->
        <dependency>
            <groupId>ojdbc6</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>11.2.0.4</version>
        </dependency>
        <!--mybatis-plus -->  
	    <dependency> 
	      <groupId>com.baomidou</groupId>  
	      <artifactId>mybatis-plus-boot-starter</artifactId>  
	      <version>3.0.6</version> 
	    </dependency>
        <!--pagehelper -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.2.5</version>
        </dependency>
        <dependency>
            <groupId>groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>2.0.1</version>
        </dependency>
    </dependencies>
	<build>
        <plugins>
	            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>**/*.yml</exclude>
                    </excludes>
                </configuration>
            </plugin>
          <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
           <version>3.7.0</version>
           <configuration>
              <source>8</source>
              <target>8</target>
           </configuration> 
      </plugin>  
        </plugins>
    </build>
  <distributionManagement> 
    <repository> 
      <id>JxcDomain</id>  
      <name>JxcDomainName</name>  
      <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url> 
    </repository> 
  </distributionManagement>  
  <repositories> 
    <repository> 
      <id>JxcDomain</id>  
      <name>JxcDomainName</name>  
      <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url>  
      <releases> 
        <enabled>true</enabled> 
      </releases>  
      <snapshots> 
        <enabled>false</enabled>  
        <updatePolicy>always</updatePolicy>  
        <checksumPolicy>warn</checksumPolicy> 
      </snapshots> 
    </repository> 
  </repositories>  
  <pluginRepositories> 
    <pluginRepository> 
      <id>JxcDomain</id>  
      <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url>  
      <releases> 
        <enabled>true</enabled> 
      </releases>  
      <snapshots> 
        <enabled>false</enabled>  
        <updatePolicy>always</updatePolicy>  
        <checksumPolicy>warn</checksumPolicy> 
      </snapshots> 
    </pluginRepository> 
  </pluginRepositories> 
</project>
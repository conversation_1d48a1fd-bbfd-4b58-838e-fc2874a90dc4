<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>chis-spr</groupId>
    <artifactId>spring-base</artifactId>
    <version>1.1-SNAPSHOT</version>
	<packaging>jar</packaging>

  <parent>
	    <groupId>chis-spr</groupId>
        <artifactId>springboot-pro</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
            <version>2.11.5</version>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.20</version>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <groupId>fakepath</groupId>
            <artifactId>xmlpull</artifactId>
            <version>*******</version>
        </dependency>
        <dependency> 
	      <groupId>commons-io</groupId>  
	      <artifactId>commons-io</artifactId>  
	      <version>2.6</version> 
	    </dependency>
        <dependency>
            <groupId>httpclient</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.4.1</version>
        </dependency>
        <dependency>
            <groupId>httpcore</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.1</version>
        </dependency>
        <!-- sm加密 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>1.55</version>
        </dependency>

        <!-- poi-tl Word模板引擎 http://deepoove.com/poi-tl -->
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.10.0-beta</version>
        </dependency>
        <!-- 转换工具，当前用于word转pdf -->
        <dependency>
            <groupId>aspose-words</groupId>
            <artifactId>aspose-words</artifactId>
            <version>21.1</version>
            <classifier>jdk17</classifier>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.14.8</version>
        </dependency>
        <!--判断pdf是否破损-->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.24</version>
        </dependency>
    </dependencies>
  <build> 
    <plugins> 
      <plugin> 
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-javadoc-plugin</artifactId>  
        <version>2.10.3</version>  
        <configuration> 
          <excludePackageNames>*.*</excludePackageNames> 
        </configuration> 
      </plugin>  
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
           <version>3.7.0</version>
           <configuration>
              <source>8</source>
              <target>8</target>
           </configuration> 
      </plugin>  
      <plugin> 
        <groupId>org.jacoco</groupId>  
        <artifactId>jacoco-maven-plugin</artifactId>  
        <executions> 
          <execution> 
            <id>pre-test</id>  
            <goals> 
              <goal>prepare-agent</goal> 
            </goals> 
          </execution>  
          <execution> 
            <id>post-test</id>  
            <phase>test</phase>  
            <goals> 
              <goal>report</goal> 
            </goals> 
          </execution> 
        </executions> 
      </plugin> 
    </plugins> 
  </build>  
	  <distributionManagement> 
    <repository> 
      <id>JxcDomain</id>  
      <name>JxcDomainName</name>  
      <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url> 
    </repository> 
  </distributionManagement>  
  <repositories> 
    <repository> 
      <id>JxcDomain</id>  
      <name>JxcDomainName</name>  
      <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url>  
      <releases> 
        <enabled>true</enabled> 
      </releases>  
      <snapshots> 
        <enabled>false</enabled>  
        <updatePolicy>always</updatePolicy>  
        <checksumPolicy>warn</checksumPolicy> 
      </snapshots> 
    </repository> 
  </repositories>  
  <pluginRepositories> 
    <pluginRepository> 
      <id>JxcDomain</id>  
      <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url>  
      <releases> 
        <enabled>true</enabled> 
      </releases>  
      <snapshots> 
        <enabled>false</enabled>  
        <updatePolicy>always</updatePolicy>  
        <checksumPolicy>warn</checksumPolicy> 
      </snapshots> 
    </pluginRepository> 
  </pluginRepositories> 
</project>
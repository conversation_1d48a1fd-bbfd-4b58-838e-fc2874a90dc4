<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
    <groupId>chis-spr</groupId>
    <artifactId>spring-webmvc</artifactId>
    <version>1.1-SNAPSHOT</version>
	<packaging>jar</packaging>
    <parent>
        <groupId>chis-spr</groupId>
        <artifactId>springboot-pro</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <properties> 
	    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>  
	    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>  
	    <zwx.spring-base.version>1.1-SNAPSHOT</zwx.spring-base.version>  
	</properties>
    <dependencies>
    	<dependency>
            <groupId>chis-spr</groupId>
            <artifactId>spring-base</artifactId>
            <version>${zwx.spring-base.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
        </dependency>

        <!--这是Spring Boot的核心启动器，包含了自动配置、日志和YAML-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--S支持全栈式Web开发，包括Tomcat和spring-webmvc。-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.module</groupId>
                    <artifactId>jackson-module-parameter-names</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                    <artifactId>jackson-datatype-jdk8</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                    <artifactId>jackson-datatype-jsr310</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <!-- 排除漏洞jar -->
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
        	<groupId>org.aspectj</groupId>
	        <artifactId>aspectjrt</artifactId>
	     </dependency>
	     <dependency> 
	      <groupId>commons-io</groupId>  
	      <artifactId>commons-io</artifactId>  
	      <version>2.6</version> 
	    </dependency>
	    <dependency> 
	      <groupId>com.alibaba</groupId>  
	      <artifactId>fastjson</artifactId>  
	      <version>1.2.83</version> 
	    </dependency>
	    <dependency> 
	      <groupId>org.apache.shiro</groupId>  
	      <artifactId>shiro-spring</artifactId>  
	      <version>1.13.0</version>
	    </dependency>
    </dependencies>

	<build>
        <plugins>
	            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                    <!--打jar包时候排除掉配置文件信息-->
                        <exclude>**/*.yml</exclude>
                    </excludes>
                </configuration>
        </plugin>
        <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
           <version>3.7.0</version>
           <configuration>
              <source>8</source>
              <target>8</target>
           </configuration> 
      </plugin>  
        </plugins>
    </build>

  <distributionManagement> 
    <repository> 
      <id>JxcDomain</id>  
      <name>JxcDomainName</name>  
      <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url> 
    </repository> 
  </distributionManagement>  
  <repositories> 
    <repository> 
      <id>JxcDomain</id>  
      <name>JxcDomainName</name>  
      <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url>  
      <releases> 
        <enabled>true</enabled> 
      </releases>  
      <snapshots> 
        <enabled>false</enabled>  
        <updatePolicy>always</updatePolicy>  
        <checksumPolicy>warn</checksumPolicy> 
      </snapshots> 
    </repository> 
  </repositories>  
  <pluginRepositories> 
    <pluginRepository> 
      <id>JxcDomain</id>  
      <url>http://10.88.99.21:8081/nexus/content/repositories/JxcDomain/</url>  
      <releases> 
        <enabled>true</enabled> 
      </releases>  
      <snapshots> 
        <enabled>false</enabled>  
        <updatePolicy>always</updatePolicy>  
        <checksumPolicy>warn</checksumPolicy> 
      </snapshots> 
    </pluginRepository> 
  </pluginRepositories> 
</project>